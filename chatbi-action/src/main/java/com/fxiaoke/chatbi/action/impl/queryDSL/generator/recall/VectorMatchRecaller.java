package com.fxiaoke.chatbi.action.impl.queryDSL.generator.recall;

import com.fxiaoke.chatbi.common.model.enums.DataTypeEnum;
import com.fxiaoke.chatbi.integration.model.ch.KnowledgeEmbedding;
import com.fxiaoke.chatbi.knowledge.embedding.service.FieldVectorMatchService;
import com.fxiaoke.common.Pair;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 向量匹配召回器
 * 通过语义向量相似度匹配字段
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class VectorMatchRecaller extends AbstractFieldRecaller {

    private final FieldVectorMatchService fieldVectorMatchService;

    @Override
    public RecallType getRecallType() {
        return RecallType.VECTOR_MATCH;
    }
    
    /**
     * 判断字段是否为字符型
     * 基于FilterInfo中的字段类型判断
     */
    private boolean isCharacterField(MatchContext context) {
        if (context.getFilterInfo() == null || 
            StringUtils.isBlank(context.getFilterInfo().getFieldType())) {
            // 如果没有提供字段类型，保守地假设不是字符型
            return false;
        }
        
        String fieldType = context.getFilterInfo().getFieldType();
        // 使用DataTypeEnum判断是否为字符型（不是数值型且不是日期型）
        return DataTypeEnum.isString(fieldType);
    }

    @Override
    public List<FieldMatchResult> recall(MatchContext context, RecallScope scope) {
        if (!isApplicable(context) || !hasCandidates(context, scope)) {
            return Collections.emptyList();
        }

        String fieldName = context.getFieldName();
        List<String> candidateIds = getCandidateIds(context, scope);

        if (CollectionUtils.isEmpty(candidateIds)) {
            log.info("没有候选字段ID可供匹配: scope={}", scope);
            return Collections.emptyList();
        }

        // 判断是否为字符型字段
        boolean isCharacter = isCharacterField(context);
        if (isCharacter) {
            log.info("检测到字符型字段: {}, 向量召回时将只从维度类知识检索", fieldName);
        }

        // 使用语义匹配服务进行向量相似度查询
        Pair<String, KnowledgeEmbedding> matchResult;
        try {
            // 对于字符型字段，调用指定方法避免检索指标类知识
            if (isCharacter) {
                matchResult = fieldVectorMatchService.matchFieldIdOnlyDimension(
                        fieldName,
                        candidateIds,
                        context.getUserIdentity()
                );
            } else {
                // 非字符型字段，使用常规匹配方法
                matchResult = fieldVectorMatchService.matchFieldId(
                        fieldName,
                        candidateIds,
                        context.getUserIdentity()
                );
            }
        } catch (Exception e) {
            log.error("调用向量匹配服务异常: {}", e.getMessage(), e);
            return Collections.emptyList();
        }

        if (matchResult == null) {
            log.info("向量匹配范围内没有相似字段: {}", scope);
            return Collections.emptyList();
        }

        // 获取匹配结果
        String matchedFieldId = matchResult.first;
        if (StringUtils.isBlank(matchedFieldId)) {
            log.info("向量匹配未找到匹配字段: fieldName={}, scope={}", fieldName, scope);
            return Collections.emptyList();
        }

        KnowledgeEmbedding knowledgeEmbedding = matchResult.second;
        // 创建匹配结果
        FieldMatchResult result = createMatchResult(
                matchedFieldId,
                0.85f, // 默认相似度，实际应从服务获取
                getRecallType(),
                scope,
                fieldName,
                1,  // 默认使用频率
                knowledgeEmbedding.getVectorScore()
        );

        log.info("向量匹配成功: 范围：{}, {} -> {}", scope, fieldName, matchResult);
        return Collections.singletonList(result);
    }
} 