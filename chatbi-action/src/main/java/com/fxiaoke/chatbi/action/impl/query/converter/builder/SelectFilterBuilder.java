package com.fxiaoke.chatbi.action.impl.query.converter.builder;

import com.alibaba.fastjson.JSON;
import com.facishare.bi.common.entities.EnumItem;
import com.facishare.bi.common.entities.stat.Filter;
import com.facishare.bi.metadata.context.dto.dw.BiMtDimension;
import com.fxiaoke.chatbi.action.impl.query.converter.AbstractFilterBuilder;
import com.fxiaoke.chatbi.action.impl.query.converter.ConverterContext;
import com.fxiaoke.chatbi.action.impl.query.converter.UiTypeService;
import com.fxiaoke.chatbi.common.model.dto.FilterConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 选择类型筛选器构建器
 * 专门处理单选、多选等选择类型的过滤条件
 */
@Slf4j
@Service
public class SelectFilterBuilder extends AbstractFilterBuilder {

  @Autowired
  private UiTypeService uiTypeService;

  @Override
  public Filter doProcess(FilterConfig filterConfig, ConverterContext context) {
    // 标准化布尔值
    List<String> values = context.getFieldValues(filterConfig.getFieldId(), filterConfig.getValue1());

    BiMtDimension biMtDimension = context.getDimensionMap().get(filterConfig.getFieldId());

    Filter filter = dim2Filter(biMtDimension, filterConfig, values);

    uiTypeService.fillUiType(filter, context.getUserIdentity());

    return filter;
  }

  private Filter dim2Filter(BiMtDimension biMtDimension, FilterConfig filterConfig, List<String> values) {
    String type = biMtDimension.getDimensionType();
    String operator = filterConfig.getOperator();

    // 使用父类方法创建基础Filter对象
    Filter filter = createBaseFilter(biMtDimension);

    // 设置选择类型特有的值格式
    filter.setValue1(JSON.toJSONString(values.stream().map(value -> {
      EnumItem enumItem = new EnumItem();
      enumItem.setEnumId(value);
      enumItem.setDisplayName(value);
      enumItem.setNodeName(value);
      enumItem.setOptionCode(value);
      return enumItem;
    }).collect(Collectors.toList())));

    // 使用父类方法设置操作符
    setOperator(filter, type, operator);

    return filter;
  }
}