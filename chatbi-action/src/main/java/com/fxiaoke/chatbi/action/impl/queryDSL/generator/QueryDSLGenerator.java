package com.fxiaoke.chatbi.action.impl.queryDSL.generator;

import com.fxiaoke.chatbi.common.model.action.input.QueryDSLInput;
import com.fxiaoke.chatbi.common.model.action.output.QueryDSLOutput;
import com.fxiaoke.chatbi.common.model.action.ActionContext;

/**
 * QueryDSL生成器接口
 * 负责生成查询DSL，支持多种生成策略
 */
public interface QueryDSLGenerator {
    /**
     * 生成查询DSL
     *
     * @param input   输入参数
     * @param context 执行上下文
     * @return 查询DSL输出
     */
    QueryDSLOutput generate(QueryDSLInput input, ActionContext context);
} 