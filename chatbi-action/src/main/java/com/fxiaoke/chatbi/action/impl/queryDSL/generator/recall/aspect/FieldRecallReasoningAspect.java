package com.fxiaoke.chatbi.action.impl.queryDSL.generator.recall.aspect;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.chatbi.action.impl.queryDSL.generator.FilterProcessorContext;
import com.fxiaoke.chatbi.action.impl.queryDSL.generator.recall.FieldMatchResult;
import com.fxiaoke.chatbi.action.impl.queryDSL.generator.recall.MatchContext;
import com.fxiaoke.chatbi.action.impl.queryDSL.generator.recall.RecallScope;
import com.fxiaoke.chatbi.action.impl.queryDSL.generator.recall.RecallType;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.action.ActionType;
import com.fxiaoke.chatbi.common.model.reasoning.ReasoningCollector;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 字段ID多路召回信息收集切面
 * 收集字段ID召回过程中的关键信息
 */
@Slf4j
@Aspect
@Component
public class FieldRecallReasoningAspect {

    /**
     * 拦截FieldIdMultiRecallProcessor.process方法
     * 收集字段ID处理过程中的整体信息
     */
    @Around("execution(* com.fxiaoke.chatbi.action.impl.queryDSL.generator.recall.FieldIdMultiRecallProcessor.process(..)) && args(context)")
    public Object collectProcessInfo(ProceedingJoinPoint joinPoint, FilterProcessorContext context) throws Throwable {
        ReasoningCollector collector = context.getReasoningCollector();
        if (collector == null) {
            return joinPoint.proceed();
        }
        
        long startTime = System.currentTimeMillis();
        try {
            Object result = joinPoint.proceed();
            
            // 简化信息收集，只记录匹配结果数量
            Map<String, Object> processInfo = new HashMap<>();
            processInfo.put("schemaId", context.getSchemaId());
            processInfo.put("matchCount", context.getFieldIdMapping() != null ? context.getFieldIdMapping().size() : 0);
            processInfo.put("timeUsed", System.currentTimeMillis() - startTime);
            
            collector.addReasoningData(ActionType.QUERY_DSL, JSON.toJSONString(processInfo));
            return result;
        } catch (Exception e) {
            Map<String, Object> errorInfo = new HashMap<>();
            errorInfo.put("error", e.getMessage());
            collector.addReasoningData(ActionType.QUERY_DSL, JSON.toJSONString(errorInfo));
            throw e;
        }
    }
    
    /**
     * 拦截带ReasoningCollector参数的MultiChannelMatch方法
     * 收集字段ID匹配详细信息
     */
    @Around("execution(* com.fxiaoke.chatbi.action.impl.queryDSL.generator.recall.FieldIdMultiRecallProcessor.multiChannelMatch(..)) && args(filters, schemaId, userIdentity, viewFieldIds, viewFilterIds, schemaFieldIds, actionDateFieldId, reasoningCollector)")
    public Object collectMatchInfo(ProceedingJoinPoint joinPoint,
                                  Object filters,
                                  String schemaId,
                                  UserIdentity userIdentity,
                                  List<String> viewFieldIds, 
                                  List<String> viewFilterIds, 
                                  List<String> schemaFieldIds, 
                                  String actionDateFieldId,
                                  ReasoningCollector reasoningCollector) throws Throwable {
        if (reasoningCollector == null) {
            return joinPoint.proceed();
        }
        
        try {
            return joinPoint.proceed();
        } catch (Exception e) {
            Map<String, Object> errorInfo = new HashMap<>();
            errorInfo.put("error", e.getMessage());
            reasoningCollector.addReasoningData(ActionType.QUERY_DSL, JSON.toJSONString(errorInfo));
            throw e;
        }
    }
    
    /**
     * 拦截不带ReasoningCollector参数的FieldIdMultiRecallProcessor.multiChannelMatch方法
     */
    @Around("execution(* com.fxiaoke.chatbi.action.impl.queryDSL.generator.recall.FieldIdMultiRecallProcessor.multiChannelMatch(..)) && args(filters, schemaId, userIdentity, viewFieldIds, viewFilterIds, schemaFieldIds, actionDateFieldId)")
    public Object collectMatchInfoWithoutCollector(ProceedingJoinPoint joinPoint,
                                     Object filters,
                                     String schemaId,
                                     UserIdentity userIdentity,
                                     List<String> viewFieldIds, 
                                     List<String> viewFilterIds, 
                                     List<String> schemaFieldIds, 
                                     String actionDateFieldId) throws Throwable {
        // 无推理收集器参数，直接执行原方法
        return joinPoint.proceed();
    }
    
    /**
     * 拦截getBestMatchFieldId方法，收集最佳匹配信息
     */
    @Around("execution(* com.fxiaoke.chatbi.action.impl.queryDSL.generator.recall.MultiChannelFieldRecaller.getBestMatchFieldId(..)) && args(context)")
    public Object collectBestMatchInfo(ProceedingJoinPoint joinPoint, MatchContext context) throws Throwable {
        ReasoningCollector collector = context.getReasoningCollector();
        if (collector == null) {
            return joinPoint.proceed();
        }
        
        try {
            String fieldId = (String) joinPoint.proceed();
            
            // 极简匹配信息，只记录是否匹配成功
            if (fieldId != null) {
                Map<String, Object> matchInfo = new HashMap<>();
                matchInfo.put("fieldName", context.getFieldName());
                matchInfo.put("fieldId", fieldId);
                
                // 如果有匹配结果且详情可用，仅添加匹配类型
                if (context.getResultsByType() != null && !context.getResultsByType().isEmpty()) {
                    FieldMatchResult matchResult = context.getResultsByType().values().stream()
                            .flatMap(List::stream)
                            .filter(result -> fieldId.equals(result.getFieldId()))
                            .findFirst()
                            .orElse(null);
                    
                    if (matchResult != null) {
                        matchInfo.put("type", matchResult.getRecallType().name());
                        matchInfo.put("scope", matchResult.getRecallScope().name());
                        // 保留评分信息以展示排名依据
                        matchInfo.put("confidence", matchResult.getConfidence());
                        matchInfo.put("totalScore", matchResult.getTotalScore());
                    }
                }
                
                collector.addReasoningData(ActionType.QUERY_DSL, JSON.toJSONString(matchInfo));
            }
            
            return fieldId;
        } catch (Exception e) {
            Map<String, Object> errorInfo = new HashMap<>();
            errorInfo.put("error", e.getMessage());
            collector.addReasoningData(ActionType.QUERY_DSL, JSON.toJSONString(errorInfo));
            throw e;
        }
    }
} 