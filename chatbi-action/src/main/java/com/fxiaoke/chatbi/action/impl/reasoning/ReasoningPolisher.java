package com.fxiaoke.chatbi.action.impl.reasoning;

import com.fxiaoke.chatbi.common.model.action.input.ReasoningPolishingInput;
import com.fxiaoke.chatbi.common.model.action.output.ReasoningPolishingOutput;
import com.fxiaoke.chatbi.common.model.action.ActionContext;
import com.fxiaoke.chatbi.common.exception.ActionException;
import com.fxiaoke.chatbi.common.exception.ChatbiErrorCodeEnum;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import java.io.IOException;
import java.io.InputStreamReader;
import java.io.StringReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 推理润色器
 * 负责对推理过程进行润色和优化
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ReasoningPolisher {

  private final Configuration freemarkerConfig;
  private static final String DEFAULT_TEMPLATE_PATH = "prompts/reasoning_polish.ftl";

  /**
   * 执行推理润色
   *
   * @param input   输入参数
   * @param context 执行上下文
   * @return 润色结果
   * @throws ActionException 执行异常
   */
  public ReasoningPolishingOutput polish(ReasoningPolishingInput input, ActionContext context) throws ActionException {
    String originalReasoning = input.getOriginalReasoning();
    int originalLength = originalReasoning.length();

    try {
      // 准备模板参数
      Map<String, Object> params = new HashMap<>();
      params.put("originalReasoning", originalReasoning);
      params.put("reasoningData", context.getReasoningCollector().getReasoningData());

      // 使用Freemarker模板处理
      String templateContent = input.getTemplateContent();
      String polishedReasoning;
      
      if (StringUtils.isBlank(templateContent)) {
        // 如果没有提供模板内容，从资源文件加载默认模板
        polishedReasoning = processTemplateFromResource(DEFAULT_TEMPLATE_PATH, params);
      } else {
        // 使用提供的模板内容
        polishedReasoning = processTemplate(templateContent, params);
      }

      if (StringUtils.isBlank(polishedReasoning)) {
        log.warn("模板处理后的润色结果为空");
        throw new ActionException(ChatbiErrorCodeEnum.SYSTEM_ERROR, "推理润色结果异常");
      }

      int polishedLength = polishedReasoning.length();
      log.info("推理润色完成, 原始长度: {}, 润色后长度: {}", originalLength, polishedLength);

      return ReasoningPolishingOutput.builder()
                                     .polishedReasoning(polishedReasoning)
                                     .originalLength(originalLength)
                                     .polishedLength(polishedLength)
                                     .build();
    } catch (ActionException e) {
      throw e;
    } catch (Exception e) {
      log.error("推理润色过程发生异常", e);
      throw new ActionException(ChatbiErrorCodeEnum.SYSTEM_ERROR, "推理润色执行异常", e);
    }
  }
  
  /**
   * 处理Freemarker模板字符串
   */
  private String processTemplate(String templateContent, Map<String, Object> params) throws IOException, TemplateException {
    Template template = new Template("reasoningTemplate", new StringReader(templateContent), freemarkerConfig);
    return FreeMarkerTemplateUtils.processTemplateIntoString(template, params);
  }
  
  /**
   * 从资源文件加载并处理Freemarker模板
   */
  private String processTemplateFromResource(String templatePath, Map<String, Object> params) throws IOException, TemplateException {
    Resource resource = new ClassPathResource(templatePath);
    if (!resource.exists()) {
      log.warn("模板资源文件不存在: {}", templatePath);
      return getSimpleDefaultTemplate(params);
    }
    
    try (InputStreamReader reader = new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8)) {
      Template template = new Template("reasoningTemplate", reader, freemarkerConfig);
      return FreeMarkerTemplateUtils.processTemplateIntoString(template, params);
    }
  }
  
  /**
   * 获取简单的默认模板处理结果
   * 仅在无法加载默认模板资源时使用
   */
  private String getSimpleDefaultTemplate(Map<String, Object> params) {
    String originalReasoning = (String) params.get("originalReasoning");
    return originalReasoning;  // 简单返回原始内容
  }
} 