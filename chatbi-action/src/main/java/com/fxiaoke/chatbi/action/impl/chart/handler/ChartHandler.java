package com.fxiaoke.chatbi.action.impl.chart.handler;

import com.fxiaoke.chatbi.common.model.action.input.InsightInput;
import com.fxiaoke.chatbi.common.model.action.output.DataQueryOutput;
import com.fxiaoke.chatbi.common.model.action.output.InsightOutput;
import com.fxiaoke.chatbi.common.model.action.ActionContext;
import com.fxiaoke.chatbi.common.model.dto.ChartQueryDSL;

import java.util.Map;

/**
 * 图表处理器接口
 * 用于处理不同类型图表的查询和洞察
 */
public interface ChartHandler {
    /**
     * 获取此处理器支持的图表ID列表
     * 
     * @return 支持的图表ID列表
     */
    String[] getSupportedViewIds();

    /**
     * 处理数据查询
     * 
     * @param queryArg 查询参数
     * @param headers  HTTP头信息
     * @param context  执行上下文
     * @return 查询输出结果
     */
    DataQueryOutput handleQuery(ChartQueryDSL queryArg, Map<String, String> headers, ActionContext context);

    /**
     * 处理数据洞察
     * 
     * @param input   洞察输入
     * @param context 执行上下文
     * @return 洞察输出结果
     */
    InsightOutput handleInsight(InsightInput input, ActionContext context);
}