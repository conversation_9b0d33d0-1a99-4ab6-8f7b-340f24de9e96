package com.fxiaoke.chatbi.action.impl.queryDSL.generator.recall;

import com.fxiaoke.chatbi.common.utils.AsyncTaskUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 多路召回协调器
 * 负责协调不同召回方式和范围的召回过程，支持并行执行
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MultiChannelFieldRecaller {
    // 所有可用的召回器
    private final List<FieldRecaller> fieldRecallers;
    
    @Resource(name = "monitorAsyncTaskExecutor")
    private TaskExecutor monitorAsyncTaskExecutor;
    
    @Value("${chatbi.field.recall.timeout:3000}")
    private long timeoutMillis;
    
    /**
     * 初始化
     */
    @PostConstruct
    public void init() {
        if (CollectionUtils.isEmpty(fieldRecallers)) {
            log.warn("字段召回器列表为空");
            return;
        }
        
        log.info("字段多路召回初始化完成，召回器数量: {}", fieldRecallers.size());
        
        // 输出每个召回器的类型信息
        for (FieldRecaller recaller : fieldRecallers) {
            log.info("已注册字段召回器: {}, 类型: {}", 
                    recaller.getClass().getSimpleName(), 
                    recaller.getRecallType());
        }
    }
    
    /**
     * 执行多路召回
     * 整合不同范围和不同召回方式的结果
     * @param context 匹配上下文
     * @return 匹配结果列表
     */
    public List<FieldMatchResult> multiChannelRecall(MatchContext context) {
        if (StringUtils.isBlank(context.getFieldName())) {
            log.warn("字段名为空，无法进行多路召回");
            return Collections.emptyList();
        }
        
        // 获取可用的召回范围
        List<RecallScope> availableScopes = getAvailableScopes(context);
        if (availableScopes.isEmpty()) {
            log.warn("无可用的召回范围，请确保至少提供一种候选字段列表");
            return Collections.emptyList();
        }
        
        long startTime = System.currentTimeMillis();
        log.info("开始执行字段并行多路召回, 字段名: {}, 可用召回范围: {}, 可用召回器: {}", 
                context.getFieldName(), availableScopes.size(), fieldRecallers.size());
        
        // 存储所有召回结果
        Map<RecallType, List<FieldMatchResult>> resultsByType = new HashMap<>();
        
        // 创建召回任务列表
        List<CompletableFuture<RecallResult>> futures = new ArrayList<>();
        
        // 对每种召回方式，在每个可用的范围内创建召回任务
        for (FieldRecaller recaller : fieldRecallers) {
            if (!recaller.isApplicable(context)) {
                continue;
            }
            
            // 为每个范围创建一个异步任务
            for (RecallScope scope : availableScopes) {
                futures.add(createRecallTask(recaller, context, scope));
            }
        }
        
        // 等待所有任务完成或超时
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0]));
        
        try {
            // 设置超时时间，避免单个渠道阻塞整体流程
            allFutures.get(timeoutMillis, TimeUnit.MILLISECONDS);
        } catch (TimeoutException e) {
            log.warn("字段多路召回部分渠道超时，将继续处理已完成的渠道结果");
        } catch (Exception e) {
            log.error("字段多路召回等待任务完成时发生异常", e);
        }

        // 收集所有已完成任务的结果
        int totalResults = 0;
        
        for (CompletableFuture<RecallResult> future : futures) {
            if (future.isDone() && !future.isCompletedExceptionally()) {
                try {
                    RecallResult result = future.get();
                    if (CollectionUtils.isNotEmpty(result.getResults())) {
                        // 合并相同类型的结果
                        resultsByType.computeIfAbsent(
                                result.getRecallType(), 
                                k -> new ArrayList<>()
                        ).addAll(result.getResults());
                        
                        totalResults += result.getResults().size();
                        
                        log.info("{}在{}范围召回完成，获得{}个结果",
                                result.getRecallType().getDescription(),
                                result.getRecallScope().getDescription(),
                                result.getResults().size());
                    }
                } catch (Exception e) {
                    log.error("获取召回结果时发生异常", e);
                }
            }
        }
        
        // 将结果存入上下文，便于进一步分析
        context.setResultsByType(resultsByType);
        
        // 对所有结果进行重排序
        List<FieldMatchResult> combinedResults = resultsByType.values().stream()
                .flatMap(List::stream)
                .sorted() // 使用FieldMatchResult的compareTo方法排序
                .collect(Collectors.toList());
        
        long endTime = System.currentTimeMillis();
        if (combinedResults.isEmpty()) {
            log.warn("未找到匹配的字段，字段名: {}, 耗时: {}ms", 
                    context.getFieldName(), (endTime - startTime));
            return Collections.emptyList();
        }
        
        log.info("字段多路召回完成，共获得{}个结果，耗时: {}ms", 
                combinedResults.size(), (endTime - startTime));
        return combinedResults;
    }
    
    /**
     * 创建单个召回任务
     * @param recaller 召回器
     * @param context 匹配上下文
     * @param scope 召回范围
     * @return 异步任务
     */
    private CompletableFuture<RecallResult> createRecallTask(
            FieldRecaller recaller, MatchContext context, RecallScope scope) {
        
        return CompletableFuture.supplyAsync(AsyncTaskUtils.wrapSupplier(() -> {
            try {
                List<FieldMatchResult> results = recaller.recall(context, scope);
                return new RecallResult(recaller.getRecallType(), scope, results);
            } catch (Exception e) {
                log.error("[{}][{}] 执行召回时发生异常", 
                        recaller.getRecallType().getDescription(), 
                        scope.getDescription(), e);
                return new RecallResult(recaller.getRecallType(), scope, Collections.emptyList());
            }
        }), monitorAsyncTaskExecutor);
    }
    
    /**
     * 召回结果包装类
     */
    private static class RecallResult {
        private final RecallType recallType;
        private final RecallScope recallScope;
        private final List<FieldMatchResult> results;
        
        public RecallResult(RecallType recallType, RecallScope recallScope, List<FieldMatchResult> results) {
            this.recallType = recallType;
            this.recallScope = recallScope;
            this.results = results != null ? results : Collections.emptyList();
        }
        
        public RecallType getRecallType() {
            return recallType;
        }
        
        public RecallScope getRecallScope() {
            return recallScope;
        }
        
        public List<FieldMatchResult> getResults() {
            return results;
        }
    }
    
    /**
     * 获取可用的召回范围
     */
    private List<RecallScope> getAvailableScopes(MatchContext context) {
        List<RecallScope> availableScopes = new ArrayList<>();
        
        if (CollectionUtils.isNotEmpty(context.getViewFieldIds())) {
            availableScopes.add(RecallScope.VIEW_FIELD);
        }
        
        if (CollectionUtils.isNotEmpty(context.getViewFilterIds())) {
            availableScopes.add(RecallScope.VIEW_FILTER);
        }
        
        if (CollectionUtils.isNotEmpty(context.getSchemaFieldIds())) {
            availableScopes.add(RecallScope.SCHEMA_FIELD);
        }
        
        return availableScopes;
    }
    
    /**
     * 获取最佳匹配结果
     * 返回排序后的前N个结果
     * @param context 匹配上下文
     * @param limit 结果数量限制
     * @return 最佳匹配结果
     */
    public List<FieldMatchResult> getBestMatches(MatchContext context, int limit) {
        List<FieldMatchResult> allResults = multiChannelRecall(context);
        
        if (CollectionUtils.isEmpty(allResults)) {
            return Collections.emptyList();
        }
        
        return allResults.stream()
                .limit(limit)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取最佳匹配的字段ID
     * 如果存在匹配结果，返回排序后的第一个结果的字段ID
     * @param context 匹配上下文
     * @return 最佳匹配的字段ID，如果没有匹配则返回null
     */
    public String getBestMatchFieldId(MatchContext context) {
        List<FieldMatchResult> bestMatches = getBestMatches(context, 1);
        
        if (CollectionUtils.isEmpty(bestMatches)) {
            return null;
        }
        
        FieldMatchResult bestMatch = bestMatches.get(0);
        log.info("最佳匹配: {} -> {} (总分:{}, 置信度:{}, 召回类型:{}, 召回范围:{})",
                context.getFieldName(),
                bestMatch.getFieldId(),
                bestMatch.getTotalScore(),
                bestMatch.getConfidence(),
                bestMatch.getRecallType().getDescription(),
                bestMatch.getRecallScope().getDescription());
        
        return bestMatch.getFieldId();
    }
} 