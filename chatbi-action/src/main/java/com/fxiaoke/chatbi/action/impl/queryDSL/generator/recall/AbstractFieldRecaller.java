package com.fxiaoke.chatbi.action.impl.queryDSL.generator.recall;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 字段召回器抽象基类
 * 提供通用实现和辅助方法
 */
@Slf4j
public abstract class AbstractFieldRecaller implements FieldRecaller {
    /**
     * 获取指定范围的候选字段ID列表
     */
    protected List<String> getCandidateIds(MatchContext context, RecallScope scope) {
        switch (scope) {
            case VIEW_FIELD:
                return context.getViewFieldIds();
            case VIEW_FILTER:
                return context.getViewFilterIds();
            case SCHEMA_FIELD:
                return context.getSchemaFieldIds();
            default:
                return Collections.emptyList();
        }
    }

    protected Map<String, String> getCandidateNam2IdeMap(MatchContext context, RecallScope scope) {
        switch (scope) {
            case VIEW_FIELD:
                return context.getViewFieldName2IdMap();
            case VIEW_FILTER:
                return context.getViewFilterName2IdMap();
            case SCHEMA_FIELD:
                return context.getSchemaFieldName2IdMap();
            default:
                return Collections.emptyMap();
        }
    }
    
    /**
     * 创建匹配结果
     */
    protected FieldMatchResult createMatchResult(String fieldId, float confidence, 
                                               RecallType type, RecallScope scope, 
                                               String originalFieldName, int usageFrequency,
                                                 double vectorScore) {
        FieldMatchResult result = FieldMatchResult.builder()
                .fieldId(fieldId)
                .confidence(confidence)
                .recallType(type)
                .recallScope(scope)
                .originalFieldName(originalFieldName)
                .usageFrequency(usageFrequency)
                .vectorScore(vectorScore)
                .build();
        
        // 计算总分
        result.calculateTotalScore();
        return result;
    }
    
    /**
     * 默认的适用性检查
     * 检查上下文中是否包含必要的字段
     */
    @Override
    public boolean isApplicable(MatchContext context) {
        // 检查待匹配的字段名是否存在
        if (StringUtils.isBlank(context.getFieldName())) {
            return false;
        }
        
        // 特定类型的召回器可能需要额外检查
        return true;
    }
    
    /**
     * 检查指定范围是否有候选字段
     */
    protected boolean hasCandidates(MatchContext context, RecallScope scope) {
        List<String> candidateIds = getCandidateIds(context, scope);
        return CollectionUtils.isNotEmpty(candidateIds);
    }
    
    /**
     * 获取所有可用的召回范围
     * 根据上下文中是否有对应的候选字段列表来确定
     */
    protected List<RecallScope> getAvailableScopes(MatchContext context) {
        List<RecallScope> availableScopes = new ArrayList<>();
        
        if (CollectionUtils.isNotEmpty(context.getViewFieldIds())) {
            availableScopes.add(RecallScope.VIEW_FIELD);
        }
        
        if (CollectionUtils.isNotEmpty(context.getViewFilterIds())) {
            availableScopes.add(RecallScope.VIEW_FILTER);
        }
        
        if (CollectionUtils.isNotEmpty(context.getSchemaFieldIds())) {
            availableScopes.add(RecallScope.SCHEMA_FIELD);
        }
        
        return availableScopes;
    }
} 