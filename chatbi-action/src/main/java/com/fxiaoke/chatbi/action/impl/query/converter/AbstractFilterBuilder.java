package com.fxiaoke.chatbi.action.impl.query.converter;

import com.facishare.bi.common.entities.filter.FilterOperatorMapByTypeEnum;
import com.facishare.bi.common.entities.stat.Filter;
import com.facishare.bi.metadata.context.convert.dto.BIType;
import com.facishare.bi.metadata.context.convert.util.BIDataNormInfo;
import com.facishare.bi.metadata.context.dto.dw.BiMtDimension;
import com.facishare.bi.metadata.context.dto.ods.FieldTypeMetadata;
import com.fxiaoke.chatbi.common.enums.AIFilterOperatorMapByTypeEnum;
import com.fxiaoke.chatbi.common.model.dto.FilterConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;

/**
 * 过滤器构建器抽象类
 * 负责处理不同类型的过滤条件
 */
@Slf4j
public abstract class AbstractFilterBuilder {

  // 常用分隔符正则表达式
  protected static final Pattern DELIMITER_PATTERN = Pattern.compile("[,;，；、\\s]+");

  private static final Map<String, AbstractFilterBuilder> BUILDER_MAP = new ConcurrentHashMap<>();

  /**
   * 注册字段类型与对应的处理器
   */
  public static void register(String type, AbstractFilterBuilder builder) {
    BUILDER_MAP.put(type, builder);
  }

  /**
   * 获取指定类型的过滤器构建器
   */
  public static AbstractFilterBuilder getBuilder(String type) {
    return BUILDER_MAP.get(type);
  }

  /**
   * 检查指定字段类型是否已注册
   */
  public static boolean isTypeRegistered(String type) {
    return BUILDER_MAP.containsKey(type);
  }


  /**
   * 处理过滤条件，构建Filter对象
   * 子类必须实现此方法
   *
   * @param filterConfig 过滤配置
   * @param context      转换上下文
   * @return 过滤器对象
   */
  public abstract Filter doProcess(FilterConfig filterConfig, ConverterContext context);

  /**
   * 通用的处理过滤条件的模板方法
   * 子类可以重写此方法以提供自定义逻辑
   *
   * @param filterConfig 过滤配置
   * @param context 转换上下文
   * @return 过滤器对象
   */
  protected Filter processFilter(FilterConfig filterConfig, ConverterContext context) {
    if (filterConfig == null || StringUtils.isBlank(filterConfig.getFieldId())) {
      return null;
    }

    BiMtDimension biMtDimension = context.getDimensionMap().get(filterConfig.getFieldId());
    if (biMtDimension == null) {
      log.warn("未找到维度信息: {}", filterConfig.getFieldId());
      return null;
    }

    return buildFilter(biMtDimension, filterConfig);
  }

  /**
   * 构建基本的过滤器对象
   * 子类应该重写此方法以提供特定类型的值设置逻辑
   *
   * @param biMtDimension 维度信息
   * @param filterConfig 过滤配置
   * @return 过滤器对象
   */
  protected Filter buildFilter(BiMtDimension biMtDimension, FilterConfig filterConfig) {
    // 创建基础Filter对象
    Filter filter = createBaseFilter(biMtDimension);

    // 设置值
    setFilterValues(filter, filterConfig);

    // 设置操作符
    setOperator(filter, biMtDimension.getDimensionType(), filterConfig.getOperator());

    return filter;
  }

  /**
   * 检查值是否包含多个值
   */
  protected boolean isMultiValue(String value) {
    if (StringUtils.isBlank(value)) {
      return false;
    }
    return DELIMITER_PATTERN.matcher(value).find();
  }

  /**
   * 基础的Filter构建方法
   */
  protected Filter process(FilterConfig filterConfig, ConverterContext context) {
    if (filterConfig == null || StringUtils.isBlank(filterConfig.getFieldId())) {
      return null;
    }
    return doProcess(filterConfig, context);
  }

  /**
   * 设置过滤器的值
   * 默认实现设置简单的值1和值2
   * 子类可以重写此方法以提供自定义逻辑
   *
   * @param filter 过滤器对象
   * @param filterConfig 过滤配置
   */
  protected void setFilterValues(Filter filter, FilterConfig filterConfig) {
    filter.setValue1(filterConfig.getValue1());
    filter.setValue2(filterConfig.getValue2());
  }

  /**
   * 创建基础Filter对象，设置通用属性
   *
   * @param biMtDimension 维度信息
   * @return 基础Filter对象
   */
  protected Filter createBaseFilter(BiMtDimension biMtDimension) {
    String fieldId = biMtDimension.getDimensionId();
    String type = biMtDimension.getDimensionType();

    // 创建基础Filter对象
    Filter filter = new Filter();
    filter.setFieldID(fieldId);
    filter.setDbFieldName(biMtDimension.getDimensionField());
    filter.setObjectDescribeApiName(biMtDimension.getDescribeApiName());
    filter.setCrmObjName(biMtDimension.getDescribeApiName());
    filter.setType(type);
    filter.setFieldName(biMtDimension.getDimensionName());

    // 设置字段类型信息
    setFieldTypeInfo(filter, biMtDimension);

    return filter;
  }

  /**
   * 设置字段类型信息
   *
   * @param filter 过滤器对象
   * @param biMtDimension 维度信息
   */
  protected void setFieldTypeInfo(Filter filter, BiMtDimension biMtDimension) {
    // 设置字段类型
    FieldTypeMetadata fieldTypeMetadata = FieldTypeMetadata.of(
        biMtDimension.getDescribeApiName(),
        biMtDimension.getDimensionField(),
        biMtDimension.getDimensionType(),
        biMtDimension.getCustomType());
    filter.setFieldType(fieldTypeMetadata.getBaseFieldType().getFieldType());

    // 设置子字段类型
    BIType biType = BIDataNormInfo.getBIType(biMtDimension.getDimensionType());
    biType = Objects.isNull(biType) ? BIDataNormInfo.getBIType(null) : biType;
    String subFieldType = biType.getSub_field_type();
    filter.setSubFieldType(subFieldType);
  }

  /**
   * 设置操作符
   *
   * @param filter 过滤器对象
   * @param type 字段类型
   * @param operator 操作符
   */
  protected void setOperator(Filter filter, String type, String operator) {
    try {
      int operatorId = AIFilterOperatorMapByTypeEnum.getBiOperatorByPaas(type, operator);
      filter.setOperator(operatorId);
    } catch (Exception e) {
      log.warn("设置操作符失败，使用默认值0: {}", e.getMessage());
      filter.setOperator(0);
    }
  }
}
