package com.fxiaoke.chatbi.action.core;

import com.fxiaoke.chatbi.common.exception.ActionException;
import com.fxiaoke.chatbi.common.exception.ChatbiErrorCodeEnum;
import com.fxiaoke.chatbi.common.model.action.ActionInput;
import com.fxiaoke.chatbi.common.model.action.ActionOutput;
import com.fxiaoke.chatbi.common.model.action.ActionType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Action工厂类
 * 负责创建和管理Action实例
 */
@Slf4j
@Component
public class ActionFactory {
    private final ApplicationContext applicationContext;
    private final Map<String, Action<?, ?>> actionCache;
    
    public ActionFactory(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
        this.actionCache = new ConcurrentHashMap<>();
    }
    
    /**
     * 获取指定类型的Action实例
     *
     * @param type Action类型
     * @param <I>  输入参数类型
     * @param <O>  输出结果类型
     * @return Action实例
     * @throws ActionException 如果找不到对应的Action
     */
    @SuppressWarnings("unchecked")
    public <I extends ActionInput, O extends ActionOutput> Action<I, O> getAction(ActionType type) throws ActionException {
        if (type == null) {
            throw new ActionException(ChatbiErrorCodeEnum.PARAM_ERROR, "Action类型不能为空");
        }
        
        return (Action<I, O>) actionCache.computeIfAbsent(
            type.getCode(),
            key -> findActionBean(type)
        );
    }
    
    /**
     * 查找Action Bean实例
     */
    private Action<?, ?> findActionBean(ActionType type) {
        return applicationContext.getBeansOfType(Action.class).values().stream()
            .filter(action -> type.equals(action.getType()))
            .findFirst()
            .orElseThrow(() -> new ActionException(
                ChatbiErrorCodeEnum.ACTION_NOT_FOUND,
                String.format("未找到类型为[%s]的Action实现", type)
            ));
    }
}