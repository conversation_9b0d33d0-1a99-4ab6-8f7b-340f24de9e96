package com.fxiaoke.chatbi.action.impl.query.converter;

import com.facishare.bi.metadata.context.dto.dw.BiMtDimension;
import com.facishare.bi.metadata.context.dto.dw.BiMtMeasure;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 转换器上下文
 * 包含转换过程中需要的上下文信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class ConverterContext {
  /**
   * 用户身份信息
   */
  private UserIdentity userIdentity;

  /**
   * 维度映射，key为维度ID
   */
  private Map<String, BiMtDimension> dimensionMap;

  /**
   * 指标映射，key为指标ID
   */
  private Map<String, BiMtMeasure> measureMap;

  /**
   * 显示值到字段值的映射
   * key: fieldId
   * value: Map<displayValue, List<fieldValue>>
   * 例如：
   * "gender" -> {
   *   "男性" -> ["male", "M", "1"],
   *   "女性" -> ["female", "F", "2"]
   * }
   */
  private Map<String, Map<String, List<String>>> displayValueFieldMap;

  private boolean isMultiGoal;

  /**
   * 从上下文中获取字段类型
   */
  public String getFieldType(String fieldId) {
    try {
      if (dimensionMap.containsKey(fieldId)) {
        // 尝试获取维度类型
        try {
          return dimensionMap.get(fieldId).getDimensionType();
        } catch (Exception e) {
          try {
            return dimensionMap.get(fieldId).getCustomType();
          } catch (Exception e2) {
            return "text"; // 默认为文本类型
          }
        }
      } else if (measureMap.containsKey(fieldId)) {
        return "number"; // 指标默认为数值类型
      }
    } catch (Exception e) {
      log.warn("获取字段类型时发生异常: {}", e.getMessage());
      return "text"; // 异常情况下返回默认类型
    }

    return "text"; // 无法确定类型时返回默认类型
  }

  /**
   * 获取显示值对应的字段值列表
   *
   * @param fieldId 字段ID
   * @param displayValue 显示值
   * @return 字段值列表，如果没有映射则返回包含原值的列表
   */
  public List<String> getFieldValues(String fieldId, String displayValue) {
    if (StringUtils.isBlank(fieldId) || StringUtils.isBlank(displayValue)) {
      return Collections.singletonList(displayValue);
    }

    try {
      Map<String, List<String>> valueMap = displayValueFieldMap.get(fieldId);
      if (valueMap == null) {
        log.info("没有查到匹配的筛选器值");
        return Collections.singletonList(displayValue);
      }

      if (!displayValue.contains(",")) {
        return valueMap.getOrDefault(displayValue, Collections.emptyList());
      }

      return Arrays.stream(displayValue.split(",")).map(value -> {
                List<String> valueList = valueMap.get(value);
                if (CollectionUtils.isEmpty(valueList)) {
                  log.info("当前value值:{}没有值匹配", value);
                  return Collections.singletonList(displayValue);
                }
                return valueList;
              }).flatMap(Collection::stream)
              .distinct()
              .collect(Collectors.toList());
    } catch (Exception e) {
      log.warn("获取字段值时发生异常: fieldId={}, displayValue={}, error={}", 
        fieldId, displayValue, e.getMessage());
    }

    return Collections.singletonList(displayValue);
  }

  /**
   * 获取多个显示值对应的所有字段值列表
   *
   * @param fieldId 字段ID
   * @param displayValues 显示值列表
   * @return 字段值列表
   */
  public List<String> getAllFieldValues(String fieldId, List<String> displayValues) {
    if (StringUtils.isBlank(fieldId) || CollectionUtils.isEmpty(displayValues)) {
      return displayValues;
    }

    try {
      Map<String, List<String>> valueMap = displayValueFieldMap.get(fieldId);
      if (valueMap != null) {
        return displayValues.stream()
          .map(display -> valueMap.getOrDefault(display, Collections.singletonList(display)))
          .flatMap(List::stream)
          .collect(Collectors.toList());
      }
    } catch (Exception e) {
      log.warn("获取字段值列表时发生异常: fieldId={}, displayValues={}, error={}", 
        fieldId, displayValues, e.getMessage());
    }

    return displayValues;
  }

  /**
   * 添加显示值到字段值的映射
   *
   * @param fieldId 字段ID
   * @param displayMap 显示值到字段值的映射
   */
  public void addDisplayValueFieldMap(String fieldId, Map<String, List<String>> displayMap) {
    if (StringUtils.isNotBlank(fieldId) && displayMap != null) {
      displayValueFieldMap.put(fieldId, displayMap);
    }
  }

}