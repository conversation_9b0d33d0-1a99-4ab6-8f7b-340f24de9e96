package com.fxiaoke.chatbi.action.impl.query.converter;

import com.alibaba.fastjson.JSON;
import com.facishare.bi.common.entities.QueryChartDataArg;
import com.facishare.bi.common.entities.StatFieldInfoDto;
import com.facishare.bi.metadata.context.dto.ads.StatView;
import com.facishare.bi.metadata.context.dto.dw.BiMtDimension;
import com.facishare.bi.metadata.context.dto.dw.BiMtMeasure;
import com.facishare.bi.metadata.context.dto.dw.Schema;
import com.facishare.bi.metadata.context.dto.dw.arg.QuerySchemasArg;
import com.facishare.bi.metadata.context.service.ads.IChartService;
import com.facishare.bi.metadata.context.service.dw.ISchemaQueryService;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.dto.ChartQueryDSL;
import com.fxiaoke.chatbi.common.model.dto.FilterConfig;
import com.fxiaoke.chatbi.common.model.dto.ObjectRelationByObjNameResult;
import com.fxiaoke.chatbi.common.utils.UserInfoConvertUtil;
import com.fxiaoke.chatbi.integration.client.fsbi.FSBIClient;
import com.fxiaoke.chatbi.integration.model.ch.DimensionValueFull;
import com.fxiaoke.chatbi.integration.repository.ch.DimensionValueRepository;
import com.fxiaoke.chatbi.integration.utils.HttpHeaderBuilder;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * ConverterContext构建工厂
 * 职责：创建及初始化ConverterContext对象
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ContextFactory {

  public static final String GOAL_RULE_FIELD_ID = "BI_f1763fde9d8c8be29674150b86ce5e9f";

  // 注入依赖的服务
  private final FSBIClient fsbiClient;
  private final ISchemaQueryService schemaQueryService;
  private final DimensionValueRepository dimensionValueRepository;
  private final IChartService chartService;

  /**
   * 为数据查询创建转换器上下文
   */
  public ConverterContext createConverterContext(QueryChartDataArg arg,
                                                 UserIdentity userIdentity,
                                                 ChartQueryDSL simpleArg,
                                                 boolean isMultiGoalTarget) {

    log.info("开始构建ConverterContext, arg:{}, userIdentity:{}", arg.getId(), userIdentity.getUserId());
    Map<String, BiMtDimension> dimensionMap;
    Map<String, BiMtMeasure> measureMap;
    if (isMultiGoalTarget) {
      ObjectRelationByObjNameResult result = fetchObjectRelations(arg, userIdentity);
      dimensionMap = buildMultiGoalDimensionMap(result);
      measureMap = buildMultiGoalMeasureMap(result);
    } else {
      List<Schema> schemas = fetchSchemas(arg, userIdentity);
      dimensionMap = constructDimensionMap(schemas);
      measureMap = constructMeasureMap(schemas);
    }

    Map<String, Map<String, List<String>>> displayValueFieldMap = constructDisplayValueMap(simpleArg.getFilters(), userIdentity);


    ConverterContext context = ConverterContext.builder()
                                               .userIdentity(userIdentity)
                                               .dimensionMap(dimensionMap)
                                               .measureMap(measureMap)
                                               .displayValueFieldMap(displayValueFieldMap)
                                               .isMultiGoal(isMultiGoalTarget)
                                               .build();
    log.info("ConverterContext构建完成, dimensionMap.size:{}, measureMap.size:{}, displayValueFieldMap.size:{}", context.getDimensionMap()
                                                                                                                        .size(), context.getMeasureMap()
                                                                                                                                        .size(), context.getDisplayValueFieldMap()
                                                                                                                                                        .size());

    return context;
  }

  public boolean isIsMultiGoalTarget(QueryChartDataArg arg, UserIdentity userIdentity) {
    List<StatView> views = chartService.getViewListByViewIds(Lists.newArrayList(arg.getId()), UserInfoConvertUtil.createUserInfo(userIdentity));
    StatView view = views.get(0);
    return view.getViewFilters().stream().anyMatch(filter -> GOAL_RULE_FIELD_ID.equals(filter.getFieldId()));
  }

  /**
   * 构建多目标场景维度映射
   */
  private Map<String, BiMtDimension> buildMultiGoalDimensionMap(ObjectRelationByObjNameResult result) {
    return Optional.ofNullable(result.getDimensionFields())
                   .filter(CollectionUtils::isNotEmpty)
                   .map(dimensions -> dimensions.stream()
                                                .map(this::convertToBiMtDimension)
                                                .collect(Collectors.toMap(BiMtDimension::getDimensionId, Function.identity(), (v1, v2) -> v2)))
                   .orElse(Collections.emptyMap());
  }

  /**
   * 构建多目标场景度量映射
   */
  private Map<String, BiMtMeasure> buildMultiGoalMeasureMap(ObjectRelationByObjNameResult result) {
    return Optional.ofNullable(result.getMeasureFields())
                   .filter(CollectionUtils::isNotEmpty)
                   .map(measures -> measures.stream()
                                            .flatMap(measureField -> measureField.getFields().stream())
                                            .map(this::convertToBiMtMeasure)
                                            .collect(Collectors.toMap(BiMtMeasure::getMeasureId, Function.identity(), (v1, v2) -> v2)))
                   .orElse(Collections.emptyMap());
  }

  // 提取对象关系查询逻辑
  private ObjectRelationByObjNameResult fetchObjectRelations(QueryChartDataArg arg, UserIdentity userIdentity) {
    try {
      return fsbiClient.getObjRelationByObjNameResult(arg, HttpHeaderBuilder.constructHttpHeader(userIdentity));
    } catch (Exception e) {
      log.error("获取对象关系失败, arg:{}", arg.getId(), e);
      return null;
    }
  }

  // 提取Schema查询逻辑
  private List<Schema> fetchSchemas(QueryChartDataArg arg, UserIdentity userIdentity) {
    try {
      QuerySchemasArg schemasArg = new QuerySchemasArg();
      schemasArg.setIncludeMeasures(true);
      schemasArg.setIncludeDimensions(true);
      schemasArg.setSchemaIds(Collections.singletonList(arg.getSchemaId()));
      return schemaQueryService.querySchemas(schemasArg, UserInfoConvertUtil.createUserInfo(userIdentity));
    } catch (Exception e) {
      log.error("获取Schema失败, schemaId:{}", arg.getSchemaId(), e);
      return Collections.emptyList();
    }
  }

  private Map<String, Map<String, List<String>>> constructDisplayValueMap(List<FilterConfig> filters,
                                                                          UserIdentity userIdentity) {
    // 1. 参数检查
    if (CollectionUtils.isEmpty(filters)) {
      log.info("无过滤条件，返回空映射");
      return Collections.emptyMap();
    }
    long startTime = System.currentTimeMillis();

    // 2. 提取并去重每个维度的显示值
    Map<String, Set<String>> dimensionDisplayValues = filters.stream()
                                                             // 过滤掉没有 fieldId 的
                                                             .filter(f -> StringUtils.isNotBlank(f.getFieldId()))
                                                             // 拆分 value1（逗号）+ value2，映射成 (fieldId, 显示值) 对
                                                             .flatMap(f -> {
                                                               Stream<String> v1 = Stream.empty();
                                                               if (StringUtils.isNotBlank(f.getValue1())) {
                                                                 v1 = f.getValue1().contains(",") ?
                                                                   Arrays.stream(f.getValue1().split(",")) :
                                                                   Stream.of(f.getValue1());
                                                               }
                                                               Stream<String> v2 = StringUtils.isNotBlank(f.getValue2()) ?
                                                                 Stream.of(f.getValue2()) :
                                                                 Stream.empty();
                                                               return Stream.concat(v1, v2)
                                                                            .map(String::trim)
                                                                            .filter(StringUtils::isNotBlank)
                                                                            .map(val -> new AbstractMap.SimpleEntry<>(f.getFieldId(), val));
                                                             })
                                                             // 按 fieldId 分组并去重
                                                             .collect(Collectors.groupingBy(Map.Entry::getKey, Collectors.mapping(Map.Entry::getValue, Collectors.toSet())));

    if (dimensionDisplayValues.isEmpty()) {
      log.info("无有效维度显示值，返回空映射");
      return Collections.emptyMap();
    }

    // 3. 扁平出所有待查询的 displayValue
    List<String> allDisplayValues = dimensionDisplayValues.values()
                                                          .stream()
                                                          .flatMap(Set::stream)
                                                          .distinct()
                                                          .collect(Collectors.toList());
    log.info("去重后待查询显示值总数：{}", allDisplayValues.size());
    if (allDisplayValues.isEmpty()) {
      return Collections.emptyMap();
    }

    // 4. 一次性批量查询所有可能的 DimensionValueFull
    long queryStart = System.currentTimeMillis();

    log.info("需要查询的所有维度值映射，dimensionDisplayValues:{}", JSON.toJSONString(dimensionDisplayValues));

    List<DimensionValueFull> allDimensionValueList = dimensionDisplayValues.entrySet().parallelStream()
            .map(entry -> dimensionValueRepository.vagueMatchByDisplayValues(
                    entry.getKey(), Lists.newArrayList(entry.getValue()), userIdentity.getTenantId(), 100))
            .flatMap(List::stream)
            .collect(Collectors.toList());

    log.info("allDimensionValueList:{}", JSON.toJSONString(allDimensionValueList));

    log.info("查询到 {} 条维度值，耗时 {} ms", CollectionUtils.size(allDimensionValueList),
      System.currentTimeMillis() - queryStart);
    if (CollectionUtils.isEmpty(allDimensionValueList)) {
      return Collections.emptyMap();
    }

    // 5. 按 dimensionId 分组查询结果
    Map<String, List<DimensionValueFull>> groupedByDimension = allDimensionValueList.stream()
                                                                                 .collect(Collectors.groupingBy(DimensionValueFull::getDimensionId));

    // 6. 构造最终返回结果
    Map<String, Map<String, List<String>>> result = new HashMap<>();
    dimensionDisplayValues.forEach((dimensionId, displaySet) -> {
      List<DimensionValueFull> candidates = groupedByDimension.getOrDefault(dimensionId, Collections.emptyList());
      if (candidates.isEmpty()) {
        log.warn("维度 {} 无匹配值，跳过，目标显示值={}", dimensionId, displaySet);
        return;
      }
      Map<String, List<String>> valueMap = displaySet.stream()
                                                     .collect(Collectors.toMap(dv -> dv, dv -> candidates.stream()
                                                                                                         .filter(cv -> cv.getDisplayValue()
                                                                                                                         .contains(dv))
                                                                                                         .map(DimensionValueFull::getValue)
                                                                                                         .collect(Collectors.toList())));
      result.put(dimensionId, valueMap);
    });

    log.info("filter value值，最终匹配到的显示值的结果集：{}", JSON.toJSONString(result));

    log.info("构建完成：维度数={}, 映射维度数={}, 总耗时={} ms", dimensionDisplayValues.size(), result.size(),
      System.currentTimeMillis() - startTime);
    return result;
  }

  private Map<String, BiMtMeasure> constructMeasureMap(List<Schema> schemaList) {
    return Optional.ofNullable(schemaList)
                   .filter(schemas -> !schemas.isEmpty())
                   .flatMap(schemas -> schemas.stream().findFirst())
                   .map(Schema::getMeasures)
                   .filter(measures -> !measures.isEmpty())
                   .map(measures -> measures.stream()
                                            .collect(Collectors.toMap(BiMtMeasure::getMeasureId, Function.identity(), (v1, v2) -> v2)))
                   .orElse(Collections.emptyMap());
  }

  private Map<String, BiMtDimension> constructDimensionMap(List<Schema> schemaList) {
    return Optional.ofNullable(schemaList)
                   .filter(schemas -> !schemas.isEmpty())
                   .flatMap(schemas -> schemas.stream().findFirst())
                   .map(Schema::getDimensions)
                   .filter(dimensions -> !dimensions.isEmpty())
                   .map(dimensions -> dimensions.stream()
                                                .collect(Collectors.toMap(BiMtDimension::getDimensionId, Function.identity(), (v1, v2) -> v2)))
                   .orElse(Collections.emptyMap());
  }

  private BiMtDimension convertToBiMtDimension(StatFieldInfoDto dto) {
    BiMtDimension dimension = new BiMtDimension();
    dimension.setDimensionType(dto.getType());
    dimension.setDimensionName(dto.getFieldName());
    dimension.setDimensionField(dto.getDbFieldName());
    dimension.setDescribeApiName(dto.getObjectDescribeApiName());
    dimension.setFieldId(dto.getUdfFieldId());
    dimension.setDimensionId(dto.getFieldID());
    return dimension;
  }

  private BiMtMeasure convertToBiMtMeasure(StatFieldInfoDto dto) {
    BiMtMeasure measure = new BiMtMeasure();
    measure.setName(dto.getFieldName());
    measure.setFieldId(dto.getFieldID());
    measure.setCrmObjName(dto.getCrmObjName());
    measure.setCrmFieldName(dto.getDbFieldName());
    return measure;
  }
}
