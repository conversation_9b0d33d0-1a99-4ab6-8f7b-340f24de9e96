package com.fxiaoke.chatbi.action.impl.queryDSL.generator.recall;

import java.util.List;

/**
 * 字段召回器接口
 * 定义字段ID召回的基本方法
 */
public interface FieldRecaller {
    /**
     * 获取召回类型
     * @return 召回类型枚举
     */
    RecallType getRecallType();
    
    /**
     * 执行字段召回
     * @param context 匹配上下文
     * @param scope 召回范围
     * @return 匹配结果列表
     */
    List<FieldMatchResult> recall(MatchContext context, RecallScope scope);
    
    /**
     * 检查该召回器是否适用于当前上下文
     * @param context 匹配上下文
     * @return 是否适用
     */
    boolean isApplicable(MatchContext context);
} 