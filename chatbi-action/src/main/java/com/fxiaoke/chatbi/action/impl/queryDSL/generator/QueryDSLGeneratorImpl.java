package com.fxiaoke.chatbi.action.impl.queryDSL.generator;

import com.facishare.bi.metadata.context.dto.ads.StatView;
import com.facishare.bi.metadata.context.service.ads.IChartService;
import com.facishare.bi.metadata.context.service.dw.ISchemaQueryService;
import com.fxiaoke.chatbi.action.impl.queryDSL.generator.recall.FieldRecallerFactory;
import com.fxiaoke.chatbi.common.exception.ActionException;
import com.fxiaoke.chatbi.common.exception.ChatbiErrorCodeEnum;
import com.fxiaoke.chatbi.common.model.action.ActionContext;
import com.fxiaoke.chatbi.common.model.action.input.QueryDSLInput;
import com.fxiaoke.chatbi.common.model.action.output.QueryDSLOutput;
import com.fxiaoke.chatbi.common.model.dto.ChartQueryDSL;
import com.fxiaoke.chatbi.common.model.intent.ExtractedInfo;
import com.fxiaoke.chatbi.common.utils.UserInfoConvertUtil;
import com.fxiaoke.chatbi.knowledge.embedding.service.DictionaryVectorMatchService;
import com.fxiaoke.chatbi.knowledge.embedding.service.FieldVectorMatchService;
import com.fxiaoke.chatbi.knowledge.retrieval.service.FilterValueFieldMatcher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * QueryDSL生成器实现
 * 负责将用户意图转换为图表查询DSL
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QueryDSLGeneratorImpl implements QueryDSLGenerator {

    private final DictionaryVectorMatchService dictionaryVectorMatchService;
    private final IChartService chartService;
    private final ISchemaQueryService schemaQueryService;
    private final FieldRecallerFactory fieldRecallerFactory;
    private final FilterProcessorManager filterProcessorManager;

    @Override
    public QueryDSLOutput generate(QueryDSLInput input, ActionContext context) {
        log.info("开始生成QueryDSL...");
        
        // 1. 验证并获取视图
        StatView statView = getAndValidateView(input, context);
        
        // 2. 创建基础DSL
        ChartQueryDSL chartQueryDSL = createBaseDSL(input, statView);
        
        // 3. 处理过滤条件
        processFilters(chartQueryDSL, input, statView, context);
        
        log.info("QueryDSL生成完成: viewId={}, schemaId={}, 过滤条件数量={}", 
                chartQueryDSL.getViewId(), chartQueryDSL.getSchemaId(), 
                chartQueryDSL.getFilters() != null ? chartQueryDSL.getFilters().size() : 0);
        
        return QueryDSLOutput.builder().chartQueryDSL(chartQueryDSL).build();
    }
    
    /**
     * 获取并验证视图
     */
    private StatView getAndValidateView(QueryDSLInput input, ActionContext context) {
        // 验证视图ID
        if (input.getKnowledgeScope() == null || CollectionUtils.isEmpty(input.getKnowledgeScope().getViewIds())) {
            log.error("未提供有效的视图ID");
            throw new ActionException(ChatbiErrorCodeEnum.PLANNING_ERROR, "未提供有效的视图ID");
        }
        
        String viewId = input.getKnowledgeScope().getViewIds().iterator().next();
        
        // 查询视图信息
        List<StatView> viewList = chartService.getViewListByViewIds(
                input.getKnowledgeScope().getViewIds(), 
                UserInfoConvertUtil.createUserInfo(context.getUserIdentity()));
                
        if (CollectionUtils.isEmpty(viewList)) {
            log.error("视图ID不存在: {}", viewId);
            throw new ActionException(ChatbiErrorCodeEnum.PLANNING_ERROR, "视图ID不存在");
        }
        
        log.info("成功获取视图: viewId={}, schemaId={}", viewId, viewList.get(0).getSchemaId());
        return viewList.get(0);
    }

    /**
     * 创建基础DSL对象
     */
    private ChartQueryDSL createBaseDSL(QueryDSLInput input, StatView statView) {
        String viewId = input.getKnowledgeScope().getViewIds().iterator().next();
        
        ChartQueryDSL dsl = new ChartQueryDSL();
        dsl.setViewId(viewId);
        dsl.setSchemaId(statView.getSchemaId());
        dsl.setChartName(statView.getViewName());
        dsl.setChartType(statView.getChartType());
        
        // 设置分析类型
        ExtractedInfo info = input.getUserIntent() != null ? input.getUserIntent().getExtractedInfo() : null;
        if (info != null && StringUtils.isNotBlank(info.getAnalysisType())) {
            dsl.setAnalysisType(info.getAnalysisType());
            log.debug("设置分析类型: {}", info.getAnalysisType());
        }
        
        return dsl;
    }
    
    /**
     * 处理过滤条件
     */
    private void processFilters(ChartQueryDSL dsl, QueryDSLInput input, StatView statView, ActionContext context) {
        // 使用注入的FilterProcessorManager处理过滤条件，同时传递推理收集器
        filterProcessorManager.processFilters(dsl, input, statView, context.getUserIdentity(), context.getReasoningCollector());
    }
}

