package com.fxiaoke.chatbi.action.impl.query.converter.builder;

import com.facishare.bi.common.entities.filter.FilterOperatorMapByTypeEnum;
import com.fxiaoke.chatbi.action.impl.query.converter.AbstractFilterBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 筛选器构建器注册类
 * 直接使用FilterOperatorMapByTypeEnum提供的信息进行类型与构建器映射
 */
@Slf4j
@Component
public class FilterBuilderRegistrar implements InitializingBean {
    
    private final TextFilterBuilder textFilterBuilder;
    private final NumberFilterBuilder numberFilterBuilder;
    private final DateFilterBuilder dateFilterBuilder;
    private final BooleanFilterBuilder booleanFilterBuilder;
    private final SelectFilterBuilder selectFilterBuilder;
    private final RefManyFilterBuilder refManyFilterBuilder;
    private final CircleFilterBuilder circleFilterBuilder;

    // 预定义类型与构建器的映射关系
    private final Map<String, AbstractFilterBuilder> typeBuilderMap;

    // 预定义前缀与构建器的映射关系
    private final Map<String, AbstractFilterBuilder> prefixBuilderMap;
    
    public FilterBuilderRegistrar(
            TextFilterBuilder textFilterBuilder,
            NumberFilterBuilder numberFilterBuilder,
            DateFilterBuilder dateFilterBuilder,
            BooleanFilterBuilder booleanFilterBuilder,
            SelectFilterBuilder selectFilterBuilder,
            RefManyFilterBuilder refManyFilterBuilder,
            CircleFilterBuilder circleFilterBuilder) {
        this.textFilterBuilder = textFilterBuilder;
        this.numberFilterBuilder = numberFilterBuilder;
        this.dateFilterBuilder = dateFilterBuilder;
        this.booleanFilterBuilder = booleanFilterBuilder;
        this.selectFilterBuilder = selectFilterBuilder;
        this.refManyFilterBuilder = refManyFilterBuilder;
        this.circleFilterBuilder = circleFilterBuilder;
        
        // 初始化类型映射
        this.typeBuilderMap = new HashMap<>();
        typeBuilderMap.put("text", textFilterBuilder);
        typeBuilderMap.put("number", numberFilterBuilder);
        typeBuilderMap.put("date", dateFilterBuilder);
        typeBuilderMap.put("datetime", dateFilterBuilder);
        typeBuilderMap.put("time", dateFilterBuilder);
        typeBuilderMap.put("boolean", booleanFilterBuilder);
        typeBuilderMap.put("Boolean", booleanFilterBuilder);
        typeBuilderMap.put("select", selectFilterBuilder);
        typeBuilderMap.put("select_one", selectFilterBuilder);
        typeBuilderMap.put("select_many", selectFilterBuilder);

        // 初始化前缀映射
        this.prefixBuilderMap = new HashMap<>();
        prefixBuilderMap.put("TEXT_", textFilterBuilder);
        prefixBuilderMap.put("NUMBER_", numberFilterBuilder);
        prefixBuilderMap.put("DATE_", dateFilterBuilder);
        prefixBuilderMap.put("TIME_", dateFilterBuilder);
        prefixBuilderMap.put("BOOLEAN_", booleanFilterBuilder);
        prefixBuilderMap.put("SELECT_", selectFilterBuilder);
        prefixBuilderMap.put("REF_MANY_", refManyFilterBuilder);
        prefixBuilderMap.put("CIRCLE_", circleFilterBuilder);
    }
    
    /**
     * 当所有Bean都初始化完成后自动执行注册
     */
    @Override
    public void afterPropertiesSet() {
        log.info("开始注册所有筛选器构建器...");
        
        // 注册预定义的类型
        typeBuilderMap.forEach((type, builder) -> {
            if (!AbstractFilterBuilder.isTypeRegistered(type)) {
                AbstractFilterBuilder.register(type, builder);
            }
        });

        // 注册枚举中定义的类型
        for (FilterOperatorMapByTypeEnum fe : FilterOperatorMapByTypeEnum.values()) {
            String name = fe.name();
            List<String> types = fe.getTypes();
            
            if (types == null || types.isEmpty()) {
                continue;
            }

            // 根据前缀获取对应的构建器
            AbstractFilterBuilder builder = prefixBuilderMap.entrySet().stream()
                .filter(entry -> name.startsWith(entry.getKey()))
                .map(Map.Entry::getValue)
                .findFirst()
                .orElse(null);
            
            // 注册所有支持的类型
            if (builder != null) {
                types.forEach(type -> {
                    if (!AbstractFilterBuilder.isTypeRegistered(type)) {
                        AbstractFilterBuilder.register(type, builder);
                    }
                });
            }
        }
        
        log.info("筛选器构建器注册完成");
    }
} 