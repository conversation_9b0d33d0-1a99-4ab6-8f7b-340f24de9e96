package com.fxiaoke.chatbi.action.impl.query.converter;

import com.facishare.bi.common.entities.stat.FilterList;
import com.fxiaoke.chatbi.common.model.dto.FilterRequest;

import java.util.List;

/**
 * 过滤器转换器
 */
public interface FilterConverter {
    /**
     * 将FilterRequest转换为FilterList
     *
     * @param filterRequest 过滤请求
     * @param context 转换上下文
     * @return 过滤列表
     */
    List<FilterList> convert(FilterRequest filterRequest, ConverterContext context);
}