package com.fxiaoke.chatbi.action.impl.query.converter;

import com.facishare.bi.common.entities.stat.Filter;
import com.facishare.bi.common.entities.stat.FilterList;
import com.fxiaoke.chatbi.action.impl.query.converter.builder.TextFilterBuilder;
import com.fxiaoke.chatbi.common.model.dto.FilterConfig;
import com.fxiaoke.chatbi.common.model.dto.FilterRequest;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class FilterConverterImpl implements FilterConverter {
  private final TextFilterBuilder textFilterBuilder;

  @Override
  public List<FilterList> convert(FilterRequest filterRequest, ConverterContext context) {
    List<FilterConfig> filters = filterRequest.getFilters();
    if (filters == null || filters.isEmpty()) {
      return new ArrayList<>();
    }

    List<Filter> filterList = filters.stream()
                                     .map(filterConfig -> buildFilter(filterConfig, context))
                                     .filter(Objects::nonNull)
                                     .collect(Collectors.toList());

    if (CollectionUtils.isEmpty(filterList)) {
      return new ArrayList<>();
    }

    FilterList filter = new FilterList();
    filter.setFilters(filterList);
    return Lists.newArrayList(filter);
  }

  /**
   * 直接从FilterConfig构建Filter对象
   */
  private Filter buildFilter(FilterConfig filterConfig, ConverterContext context) {
    try {
      String fieldId = filterConfig.getFieldId();
      if (StringUtils.isBlank(fieldId)) {
        log.warn("字段ID为空，无法构建过滤器");
        return null;
      }

      // 从ConverterContext中获取字段信息
      String type = context.getFieldType(fieldId);
      if (StringUtils.isBlank(type)) {
        // 如果无法确定字段类型，使用默认类型
        log.warn("未找到字段类型信息, 使用默认类型'text', fieldId={}", fieldId);
        type = "text";
      }

      // 获取对应类型的过滤器构建器
      AbstractFilterBuilder builder = AbstractFilterBuilder.getBuilder(type);
      if (builder == null) {
        // 如果没有特定类型的构建器，直接使用文本类型的构建器
        log.warn("未找到适用于字段类型 {} 的过滤器构建器，使用文本类型构建器", type);
        builder = textFilterBuilder;
      }

      // 构建并返回Filter对象
      return builder.process(filterConfig, context);
    } catch (Exception e) {
      log.error("构建过滤器失败, 过滤配置: {}", filterConfig, e);
      return null;
    }
  }


}
