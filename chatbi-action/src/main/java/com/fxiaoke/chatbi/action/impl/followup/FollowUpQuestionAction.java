package com.fxiaoke.chatbi.action.impl.followup;

import com.fxiaoke.chatbi.action.core.AbstractAction;
import com.fxiaoke.chatbi.common.model.action.input.FollowUpQuestionInput;
import com.fxiaoke.chatbi.common.model.action.output.FollowUpQuestionOutput;
import com.fxiaoke.chatbi.common.model.action.ActionType;
import com.fxiaoke.chatbi.action.impl.followup.generator.FollowUpQuestionGenerator;
import com.fxiaoke.chatbi.common.model.action.ActionContext;
import com.fxiaoke.chatbi.common.exception.ActionException;
import org.springframework.stereotype.Component;

/**
 * 通用推荐Action
 * 负责根据不同场景和上下文生成各类推荐
 */
@Component
public class FollowUpQuestionAction extends AbstractAction<FollowUpQuestionInput, FollowUpQuestionOutput> {

  private final FollowUpQuestionGenerator followUpQuestionGenerator;

  public FollowUpQuestionAction(FollowUpQuestionGenerator followUpQuestionGenerator) {
    this.followUpQuestionGenerator = followUpQuestionGenerator;
  }

  @Override
  public ActionType getType() {
    return ActionType.FOLLOW_UP_QUESTION;
  }

  @Override
  protected FollowUpQuestionOutput doExecute(FollowUpQuestionInput input, ActionContext context) throws ActionException {
    return followUpQuestionGenerator.getFollowUpQuestions(input, context);
  }


}