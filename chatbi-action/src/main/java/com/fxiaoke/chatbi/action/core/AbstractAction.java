package com.fxiaoke.chatbi.action.core;

import com.fxiaoke.chatbi.common.model.action.ActionContext;
import com.fxiaoke.chatbi.common.exception.ActionException;
import com.fxiaoke.chatbi.common.exception.ChatbiErrorCodeEnum;
import com.fxiaoke.chatbi.common.model.action.ActionInput;
import com.fxiaoke.chatbi.common.model.action.ActionOutput;
import com.fxiaoke.chatbi.common.model.action.ActionType;
import com.fxiaoke.chatbi.common.model.prompts.PromptTemplateType;
import com.fxiaoke.chatbi.common.model.reasoning.ReasoningData;
import com.fxiaoke.chatbi.integration.utils.MarkdownUtil;
import com.fxiaoke.chatbi.prompts.PromptTemplateService;
import com.fxiaoke.common.MapUtils;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.StringReader;
import java.io.StringWriter;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * Action抽象基类
 * 提供通用的执行流程和异常处理
 *
 * @param <I> 输入参数类型
 * @param <O> 输出结果类型
 */
@Slf4j
public abstract class AbstractAction<I extends ActionInput, O extends ActionOutput> implements Action<I, O> {

    @Autowired
    protected PromptTemplateService promptTemplateService;

    @Autowired
    protected Configuration freemarkerConfig;

    @Override
    public final ActionResult<O> execute(I input, ActionContext context) throws ActionException {
        String actionName = getClass().getSimpleName();
        long startTime = System.currentTimeMillis();

        try {
            // 基础检查
            validate(input);
            checkTimeout(context);

            // 执行业务逻辑
            O output = doExecute(input, context);

            // 验证输出
            if (output != null && !output.isValid()) {
                throw new ActionException(ChatbiErrorCodeEnum.INVALID_OUTPUT,
                        String.format("Invalid output for action %s: %s", actionName, output.getOutputType()));
            }

            // 收集推理数据
            collectReasoningIfNeeded(input, output, context);

            // 记录执行信息
            logSuccess(actionName, startTime);

            // 创建结果
            ActionResult<O> result = ActionResult.success(output);

            return result;

        } catch (Exception e) {
            logError(actionName, e);
            throw wrapException(e);
        }
    }

    /**
     * 执行具体业务逻辑
     *
     * @param input   输入参数
     * @param context 执行上下文
     * @return 执行结果
     * @throws ActionException 执行异常
     */
    protected abstract O doExecute(I input, ActionContext context) throws ActionException;

    /**
     * 收集推理数据
     * 使用模板引擎处理推理数据
     *
     * @param input   输入参数
     * @param output  输出结果
     * @param context 执行上下文
     * @return 推理数据映射
     */
    protected Map<ActionType, String> collectReasoning(I input, O output, ActionContext context) {
        try {
            // 检查输出是否是推理数据且需要收集
            if (!(output instanceof ReasoningData)) {
                return Collections.emptyMap();
            }

            ReasoningData reasoningData = (ReasoningData) output;
            if (!reasoningData.shouldCollectReasoning()) {
                return Collections.emptyMap();
            }

            // 获取描述数据和模板类型
            Map<String, Object> description = reasoningData.getDescription();

            if (MapUtils.isNullOrEmpty(description)) {
                return Collections.emptyMap();
            }

            PromptTemplateType templateType;

            try {
                // 获取模板类型
                templateType = reasoningData.getReasoningTemplateType();
                if (templateType == null) {
                    templateType = PromptTemplateType.REASONING_CONVERT_INTENT;
                    log.warn("ReasoningData返回的模板类型为null，使用默认类型: {}", templateType);
                }
            } catch (Exception e) {
                templateType = PromptTemplateType.REASONING_CONVERT_INTENT;
                log.warn("获取模板类型时发生异常，使用默认类型: {}", templateType, e);
            }

            // 获取模板并处理
            PromptTemplateType promptsType = PromptTemplateType.valueOf(templateType.name());

            String templateContent = promptTemplateService.getTemplate(promptsType);
            Template template = new Template("dynamicTemplate", new StringReader(templateContent), freemarkerConfig);

            // 处理模板
            StringWriter writer = new StringWriter();
            template.process(description, writer);
//            String prompt = MarkdownUtil.markdownToHtml(writer.toString());
            String prompt = writer.toString();
            return Collections.singletonMap(getType(), prompt);
        } catch (Exception e) {
            log.warn("actionType:{}, 处理推理数据时发生异常: {}", getType(), e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    /**
     * 检查超时
     */
    private void checkTimeout(ActionContext context) throws ActionException {
        if (context.isTimeout()) {
            throw new ActionException(ChatbiErrorCodeEnum.TIMEOUT_ERROR);
        }
    }

    /**
     * 收集推理数据（如果需要）
     */
    private void collectReasoningIfNeeded(I input, O output, ActionContext context) {
        Optional.ofNullable(context.getReasoningCollector())
                .ifPresent(collector -> {
                    try {
                        Optional.ofNullable(collectReasoning(input, output, context))
                                .ifPresent(reasoning -> reasoning.forEach(collector::addReasoningData));
                    } catch (Exception e) {
                        log.warn("推理数据收集失败: {}", e.getMessage());
                    }
                });
    }

    /**
     * 记录成功日志
     */
    private void logSuccess(String actionName, long startTime) {
        log.info("[{}] 执行成功, 耗时={}ms", actionName, System.currentTimeMillis() - startTime);
    }

    /**
     * 记录错误日志
     */
    private void logError(String actionName, Exception e) {
        log.error("[{}] 执行失败: {}", actionName, e.getMessage(), e);
    }

    /**
     * 包装异常
     */
    private ActionException wrapException(Exception e) {
        return e instanceof ActionException ? (ActionException) e
                : new ActionException(ChatbiErrorCodeEnum.SYSTEM_ERROR, e.getMessage());
    }
}