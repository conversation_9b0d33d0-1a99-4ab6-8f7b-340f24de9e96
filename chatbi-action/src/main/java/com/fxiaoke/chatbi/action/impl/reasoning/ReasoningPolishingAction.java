package com.fxiaoke.chatbi.action.impl.reasoning;

import com.fxiaoke.chatbi.action.core.AbstractAction;
import com.fxiaoke.chatbi.common.model.action.input.ReasoningPolishingInput;
import com.fxiaoke.chatbi.common.model.action.output.ReasoningPolishingOutput;
import com.fxiaoke.chatbi.common.model.action.ActionContext;
import com.fxiaoke.chatbi.common.exception.ActionException;
import com.fxiaoke.chatbi.common.exception.ChatbiErrorCodeEnum;
import com.fxiaoke.chatbi.common.model.action.ActionType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 推理润色动作
 * 负责对推理过程进行润色和优化,使其更加流畅自然
 * 使用Freemarker模板直接处理，不依赖LLM
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ReasoningPolishingAction extends AbstractAction<ReasoningPolishingInput, ReasoningPolishingOutput> {

  private final ReasoningPolisher reasoningPolisher;

  @Override
  public ActionType getType() {
    return ActionType.REASONING_POLISHING;
  }

  @Override
  public void validate(ReasoningPolishingInput input) throws ActionException {
    super.validate(input);
    if (StringUtils.isBlank(input.getOriginalReasoning())) {
      throw new ActionException(ChatbiErrorCodeEnum.PARAM_ERROR, "原始推理内容不能为空");
    }
    // 模板内容可为空，会使用默认模板
  }

  @Override
  protected ReasoningPolishingOutput doExecute(ReasoningPolishingInput input, ActionContext context) {
    log.info("开始执行推理润色, 原始内容长度: {}, 使用模板: {}", 
            input.getOriginalReasoning().length(),
            StringUtils.isBlank(input.getTemplateContent()) ? "默认模板" : "自定义模板");
    try {
      ReasoningPolishingOutput output = reasoningPolisher.polish(input, context);
      log.info("推理润色完成, 结果长度: {}",
        output.getPolishedReasoning() != null ? output.getPolishedReasoning().length() : 0);
      return output;
    } catch (Exception e) {
      log.error("推理润色执行异常", e);
      throw e;
    }
  }
}