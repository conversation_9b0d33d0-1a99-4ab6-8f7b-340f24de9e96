package com.fxiaoke.chatbi.action.aspect;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.chatbi.action.core.AbstractAction;
import com.fxiaoke.chatbi.common.model.action.ActionContext;
import com.fxiaoke.chatbi.common.model.action.ActionInput;
import com.fxiaoke.chatbi.common.model.action.ActionOutput;
import com.fxiaoke.chatbi.common.model.action.ActionType;
import com.fxiaoke.chatbi.common.model.action.input.DataQueryInput;
import com.fxiaoke.chatbi.common.model.action.input.FollowUpQuestionInput;
import com.fxiaoke.chatbi.common.model.action.input.InsightInput;
import com.fxiaoke.chatbi.common.model.action.input.IntentInput;
import com.fxiaoke.chatbi.common.model.action.input.QueryDSLInput;
import com.fxiaoke.chatbi.common.model.action.input.QueryPreProcessInput;
import com.fxiaoke.chatbi.common.model.action.input.ReasoningPolishingInput;
import com.fxiaoke.chatbi.common.model.action.output.DataQueryOutput;
import com.fxiaoke.chatbi.common.model.action.output.FollowUpQuestionOutput;
import com.fxiaoke.chatbi.common.model.action.output.InsightOutput;
import com.fxiaoke.chatbi.common.model.action.output.IntentOutput;
import com.fxiaoke.chatbi.common.model.action.output.QueryDSLOutput;
import com.fxiaoke.chatbi.common.model.action.output.QueryPreProcessOutput;
import com.fxiaoke.chatbi.common.model.action.output.ReasoningPolishingOutput;
import com.fxiaoke.chatbi.common.model.dto.SimpleChartResult;
import com.fxiaoke.chatbi.common.model.intent.KnowledgeScope;
import com.fxiaoke.chatbi.common.model.prompts.PromptTemplateType;
import com.fxiaoke.chatbi.common.model.reasoning.ReasoningCollector;
import com.fxiaoke.chatbi.common.model.reasoning.ReasoningData;
import com.fxiaoke.chatbi.integration.utils.MarkdownUtil;
import com.fxiaoke.chatbi.prompts.PromptTemplateService;
import com.google.common.collect.Maps;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.StringReader;
import java.io.StringWriter;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * Action日志收集切面
 * 拦截所有Action的执行，收集执行日志信息
 * 根据不同Action类型收集特定信息
 */
@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class ActionLogCollectorAspect {

    @Autowired
    protected PromptTemplateService promptTemplateService;

    @Autowired
    protected Configuration freemarkerConfig;

    @Around("execution(* com.fxiaoke.chatbi.action.core.AbstractAction+.doExecute(..))")
    public Object collectActionLogs(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取目标Action实例
        AbstractAction<?, ?> action = (AbstractAction<?, ?>) joinPoint.getTarget();
        ActionType actionType = action.getType();
        // 获取方法参数
        Object[] args = joinPoint.getArgs();
        ActionInput input = (ActionInput) args[0];
        ActionContext context = (ActionContext) args[1];

        // 获取ReasoningCollector
        ReasoningCollector collector = context.getReasoningCollector();
        if (collector == null) {
            return joinPoint.proceed(); // 如果没有收集器，直接执行原方法
        }

        // 记录开始执行的日志
        long startTime = System.currentTimeMillis();

        try {
            // 执行原方法 - doExecute返回的是ActionOutput
            ActionOutput output = (ActionOutput) joinPoint.proceed();

            // 记录执行成功的日志
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            // 构建基础日志信息
            Map<String, Object> logInfo = buildBaseLogInfo(actionType, duration,
                    "SUCCESS");
            if (actionType == ActionType.KNOWLEDGE_RETRIEVAL || actionType == ActionType.DATA_QUERY) {
                return output;
            }
            // 根据不同Action类型收集特定信息
            collectActionSpecificInfoForOutput(actionType, input, output, logInfo);

            Map<ActionType, String> actionTypeStringMap = collectActionLog(actionType, output, context, logInfo);
            actionTypeStringMap.forEach(collector::addActionLog);
            // 添加到推理收集器
//            collector.addActionLog(actionType, JSON.toJSONString(logInfo));

            return output;
        } catch (Exception e) {
            // 记录执行失败的日志
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            Map<String, Object> errorInfo = buildBaseLogInfo(actionType, duration,
                    "ERROR");
            errorInfo.put("errorType", e.getClass().getSimpleName());
            errorInfo.put("errorMessage", e.getMessage());
            // 添加到推理收集器
            collector.addActionLog(actionType, JSON.toJSONString(errorInfo));
            return joinPoint.proceed();
        }
    }

    /**
     * 构建基础日志信息
     */
    private Map<String, Object> buildBaseLogInfo(ActionType actionType,
                                                 long duration, String status) {
        Map<String, Object> logInfo = new HashMap<>();
        logInfo.put("actionType", actionType.name());
        logInfo.put("duration", duration);
        logInfo.put("status", status);
        return logInfo;
    }

    /**
     * 根据不同Action类型收集特定信息 - 直接处理ActionOutput
     * 在每个case分支内创建inputInfo和outputInfo
     */
    private void collectActionSpecificInfoForOutput(ActionType actionType, ActionInput input, ActionOutput output,
                                                    Map<String, Object> logInfo) {

        if (output != null) {
            // 创建输入和输出信息容器
            Map<String, Object> inputInfo = new HashMap<>();
            Map<String, Object> outputInfo = new HashMap<>();
            Map<String, Object> actionLog = new HashMap<>();

            // 根据不同Action类型收集特定信息
            switch (actionType) {
                case QUERY_PREPROCESSING:
                    if (input instanceof QueryPreProcessInput) {
                        QueryPreProcessInput queryPreProcessInput = (QueryPreProcessInput) input;
                        inputInfo.put("originalQuery", queryPreProcessInput.getOriginalQuery());
                    }
                    if (output instanceof QueryPreProcessOutput) {
                        QueryPreProcessOutput queryPreProcessOutput = (QueryPreProcessOutput) output;
                        actionLog = queryPreProcessOutput.getActionLog();
                    }
                    break;
                case INTENT_RECOGNITION:
                    if (input instanceof IntentInput) {
                        IntentInput intentInput = (IntentInput) input;
                        inputInfo.put("query", intentInput.getQuery());
                    }
                    if (output instanceof IntentOutput) {

                        IntentOutput intentOutput = (IntentOutput) output;
                        actionLog = intentOutput.getActionLog();
//                        if (intentOutput.getIntent() != null && intentOutput.getIntent().getExtractedInfo() != null) {
//                            outputInfo.put("intentType", intentOutput.getIntent().getIntentType());
//                            outputInfo.put("analysisType",
//                                    intentOutput.getIntent().getExtractedInfo().getAnalysisType());
//                            outputInfo.put("dimensions", intentOutput.getIntent().getExtractedInfo().getDimensions());
//                            outputInfo.put("measures", intentOutput.getIntent().getExtractedInfo().getMeasures());
//                            outputInfo.put("filterCount",
//                                    intentOutput.getIntent().getExtractedInfo().getFilters() != null
//                                            ? intentOutput.getIntent().getExtractedInfo().getFilters().size()
//                                            : 0);
//                        }
                    }
                    break;

                case KNOWLEDGE_RETRIEVAL:
                    // 知识检索特定信息
                    if (output instanceof KnowledgeScope) {
                        KnowledgeScope knowledgeScope = (KnowledgeScope) output;
                        outputInfo.put("viewCount",
                                knowledgeScope.getViewIds() != null ? knowledgeScope.getViewIds().size() : 0);
                        outputInfo.put("chartInfoMap", knowledgeScope.getChartInfoMap());
                    }
                    break;

                case QUERY_DSL:
                    if (input instanceof QueryDSLInput) {
                        QueryDSLInput queryDSLInput = (QueryDSLInput) input;
                        if (queryDSLInput.getUserIntent() != null) {
                            inputInfo.put("intentType", queryDSLInput.getUserIntent().getIntentType());
                        }
                        if (queryDSLInput.getKnowledgeScope() != null) {
                            inputInfo.put("viewCount",
                                    queryDSLInput.getKnowledgeScope().getViewIds() != null
                                            ? queryDSLInput.getKnowledgeScope().getViewIds().size()
                                            : 0);
                        }
                    }
                    if (output instanceof QueryDSLOutput) {
                        QueryDSLOutput queryDSLOutput = (QueryDSLOutput) output;
                        actionLog = queryDSLOutput.getActionLog();
//                        if (queryDSLOutput.getChartQueryDSL() != null) {
//                            outputInfo.put("viewId", queryDSLOutput.getChartQueryDSL().getViewId());
//                            outputInfo.put("schemaId", queryDSLOutput.getChartQueryDSL().getSchemaId());
//                            outputInfo.put("chartType", queryDSLOutput.getChartQueryDSL().getChartType());
//                            outputInfo.put("filterCount",
//                                    queryDSLOutput.getChartQueryDSL().getFilters() != null
//                                            ? queryDSLOutput.getChartQueryDSL().getFilters().size()
//                                            : 0);
//                        }
                    }
                    break;

                case DATA_QUERY:
                    if (input instanceof DataQueryInput) {
                        DataQueryInput dataQueryInput = (DataQueryInput) input;
                        if (dataQueryInput.getQueryArg() != null) {
                            inputInfo.put("viewId", dataQueryInput.getQueryArg().getViewId());
                            inputInfo.put("schemaId", dataQueryInput.getQueryArg().getSchemaId());
                            inputInfo.put("chartType", dataQueryInput.getQueryArg().getChartType());
                        }
                    }
                    if (output instanceof DataQueryOutput) {
                        DataQueryOutput dataQueryOutput = (DataQueryOutput) output;
                        if (dataQueryOutput.getResultData() != null) {
                            outputInfo.put("hasQueryData", true);
                            // 使用ChartResultData接口中的方法获取数据集大小
                            outputInfo.put("dataSetSize", dataQueryOutput.getResultData().getDataSetSize());
                            outputInfo.put("chartTitle", dataQueryOutput.getTitle());
                            outputInfo.put("chartType", dataQueryOutput.getResultData().getChartType());
                        }
                    }
                    break;

                case DATA_INSIGHT:
                    if (input instanceof InsightInput) {
                        InsightInput insightInput = (InsightInput) input;
                        if (insightInput.getQueryOutput() != null
                                && insightInput.getQueryOutput().getResultData() != null) {
                            inputInfo.put("chartTitle", insightInput.getQueryOutput().getTitle());
                            inputInfo.put("chartType", insightInput.getQueryOutput().getResultData().getChartType());
                            inputInfo.put("datasetSize",
                                    insightInput.getQueryOutput().getResultData().getDataSetSize());
                        }
                    }
                    if (output instanceof InsightOutput) {
                        InsightOutput insightOutput = (InsightOutput) output;
                        outputInfo.put("quickInsightLength",
                                insightOutput.getQuickInsight() != null ? insightOutput.getQuickInsight().length() : 0);
                        outputInfo.put("fullInsightLength",
                                insightOutput.getFullInsight() != null ? insightOutput.getFullInsight().length() : 0);
                    }
                    break;

                case FOLLOW_UP_QUESTION:
                    if (input instanceof FollowUpQuestionInput) {
                        FollowUpQuestionInput followUpInput = (FollowUpQuestionInput) input;
                        inputInfo.put("instructions", followUpInput.getInstructions());
                        inputInfo.put("limit", followUpInput.getLimit());
                        if (followUpInput.getQueryResult() != null) {
                            inputInfo.put("chartType", ((SimpleChartResult)followUpInput.getQueryResult()).getChartType());
                        }
                    }
                    if (output instanceof FollowUpQuestionOutput) {
                        FollowUpQuestionOutput followUpOutput = (FollowUpQuestionOutput) output;
                        outputInfo.put("questionCount",
                                followUpOutput.getFollowUpQuestions() != null
                                        ? followUpOutput.getFollowUpQuestions().size()
                                        : 0);
                    }
                    break;

                case REASONING_POLISHING:
                    if (input instanceof ReasoningPolishingInput) {
                        ReasoningPolishingInput polishingInput = (ReasoningPolishingInput) input;
                        inputInfo.put("originalReasoningLength",
                                polishingInput.getOriginalReasoning() != null
                                        ? polishingInput.getOriginalReasoning().length()
                                        : 0);
                        inputInfo.put("hasCustomTemplate", polishingInput.getTemplateContent() != null &&
                                !polishingInput.getTemplateContent().isEmpty());
                    }
                    if (output instanceof ReasoningPolishingOutput) {
                        ReasoningPolishingOutput polishingOutput = (ReasoningPolishingOutput) output;
                        actionLog = polishingOutput.getActionLog();
                        outputInfo.put("polishedReasoningLength",
                                polishingOutput.getPolishedReasoning() != null
                                        ? polishingOutput.getPolishedReasoning().length()
                                        : 0);
                        outputInfo.put("originalLength", polishingOutput.getOriginalLength());
                        outputInfo.put("polishedLength", polishingOutput.getPolishedLength());
                        outputInfo.put("compressionRatio", polishingOutput.getOriginalLength() > 0
                                ? (double) polishingOutput.getPolishedLength() / polishingOutput.getOriginalLength()
                                : 1.0);
                    }
                    break;

                case ATTRIBUTION:
                    // 归因分析特定信息
                    // 由于没有找到具体的输入输出类，使用通用处理
                    inputInfo.put("actionType", "ATTRIBUTION");
                    outputInfo.put("actionType", "ATTRIBUTION");
                    break;

                case RESPONSE_GENERATION:
                    // 对话生成特定信息
                    // 由于没有找到具体的输入输出类，使用通用处理
                    inputInfo.put("actionType", "RESPONSE_GENERATION");
                    outputInfo.put("actionType", "RESPONSE_GENERATION");
                    break;
            }
//            logInfo.put("inputInfo", inputInfo);
//            logInfo.put("outputInfo", outputInfo);
            logInfo.putAll(actionLog);

        }
    }


    /**
     * 收集日志数据
     * 使用模板引擎处理日志数据
     *
     * @param output  输出结果
     * @param context 执行上下文
     * @return 推理数据映射
     */
    private Map<ActionType, String> collectActionLog(ActionType actionType, ActionOutput output, ActionContext context, Map<String, Object> actionLogInfo) {
        try {
            if(MapUtils.isEmpty(actionLogInfo)) {
                return Maps.newHashMap();
            }

            if (!(output instanceof ReasoningData)) {
                return Maps.newHashMap();
            }

            ReasoningData reasoningData = (ReasoningData) output;
            // 获取描述数据和模板类型
//            Map<String, Object> actionLog = reasoningData.getActionLog();
//
//            if (MapUtils.isNullOrEmpty(description)) {
//                return Collections.emptyMap();
//            }
            PromptTemplateType templateType;

            try {
                // 获取模板类型
                templateType = reasoningData.getActionLogTemplateType();
                if (templateType == null) {
                    templateType = PromptTemplateType.REASONING_CONVERT_INTENT;
                    log.warn("ReasoningData返回的日志模板类型为null，使用默认类型: {}", templateType);
                }
            } catch (Exception e) {
                templateType = PromptTemplateType.REASONING_CONVERT_INTENT;
                log.warn("获取模板类型时发生异常，使用默认类型: {}", templateType, e);
            }

            // 获取模板并处理
            PromptTemplateType promptsType =
                    PromptTemplateType.valueOf(templateType.name());

            String templateContent = promptTemplateService.getTemplate(promptsType);
            Template template = new Template("dynamicTemplate", new StringReader(templateContent), freemarkerConfig);

            // 处理模板
            StringWriter writer = new StringWriter();
            template.process(actionLogInfo, writer);
            return Collections.singletonMap(actionType, writer.toString());
        } catch (Exception e) {
            log.warn("actionType:{}, 处理推理数据时发生异常: {}",actionType, e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

}