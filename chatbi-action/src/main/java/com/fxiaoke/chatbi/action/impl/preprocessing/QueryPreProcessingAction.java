package com.fxiaoke.chatbi.action.impl.preprocessing;

import com.fxiaoke.chatbi.action.core.AbstractAction;
import com.fxiaoke.chatbi.common.model.action.input.QueryPreProcessInput;
import com.fxiaoke.chatbi.common.model.action.output.QueryPreProcessOutput;
import com.fxiaoke.chatbi.common.model.action.ActionContext;
import com.fxiaoke.chatbi.common.exception.ActionException;
import com.fxiaoke.chatbi.common.model.action.ActionType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 查询预处理Action
 * 负责预处理用户查询，包括术语替换、流程指引等
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class QueryPreProcessingAction extends AbstractAction<QueryPreProcessInput, QueryPreProcessOutput> {

  private final QueryPreProcessor queryPreProcessor;

  @Override
  public ActionType getType() {
    return ActionType.QUERY_PREPROCESSING;
  }

  @Override
  protected QueryPreProcessOutput doExecute(QueryPreProcessInput input, ActionContext context) throws ActionException {
    QueryPreProcessOutput output = queryPreProcessor.process(input.getOriginalQuery(), context.getUserIdentity());

    log.info("查询预处理: {} -> {}", input.getOriginalQuery(), output.getProcessedQuery());
    return output;
  }
}