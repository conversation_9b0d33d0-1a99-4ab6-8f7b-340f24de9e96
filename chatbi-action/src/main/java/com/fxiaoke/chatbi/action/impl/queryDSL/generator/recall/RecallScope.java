package com.fxiaoke.chatbi.action.impl.queryDSL.generator.recall;

/**
 * 召回范围枚举
 * 表示字段ID匹配的查询范围
 */
public enum RecallScope {
    // 视图字段范围 - 从视图的维度和指标字段中查找
    VIEW_FIELD(1.0f, "视图字段"),
    
    // 视图过滤器范围 - 从视图的过滤器字段中查找
    VIEW_FILTER(0.9f, "视图过滤器"),
    
    // 主题字段范围 - 从整个主题的所有字段中查找
    SCHEMA_FIELD(0.75f, "主题字段");
    
    // 默认权重 - 不同范围的权重，范围越小权重越低
    private final float defaultWeight;
    // 描述
    private final String description;
    
    RecallScope(float defaultWeight, String description) {
        this.defaultWeight = defaultWeight;
        this.description = description;
    }
    
    public float getDefaultWeight() {
        return defaultWeight;
    }
    
    public String getDescription() {
        return description;
    }
} 