package com.fxiaoke.chatbi.action.impl.chart.handler.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.facishare.bi.common.entities.*;
import com.fxiaoke.chatbi.action.impl.chart.handler.ChartHandler;
import com.fxiaoke.chatbi.action.impl.query.converter.ContextFactory;
import com.fxiaoke.chatbi.action.impl.query.converter.ConverterContext;
import com.fxiaoke.chatbi.action.impl.query.converter.FilterConverter;
import com.fxiaoke.chatbi.common.exception.ActionException;
import com.fxiaoke.chatbi.common.exception.ChatbiErrorCodeEnum;
import com.fxiaoke.chatbi.common.model.action.input.InsightInput;
import com.fxiaoke.chatbi.common.model.action.output.DataQueryOutput;
import com.fxiaoke.chatbi.common.model.action.output.InsightOutput;
import com.fxiaoke.chatbi.common.model.action.ActionContext;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.dto.*;
import com.fxiaoke.chatbi.integration.client.fsbi.FSBIClient;
import com.fxiaoke.chatbi.integration.client.llm.LlmClient;
import com.fxiaoke.chatbi.integration.utils.HttpHeaderBuilder;
import com.fxiaoke.chatbi.integration.utils.LLMResponseParser;
import com.fxiaoke.chatbi.prompts.PromptTemplateService;
import com.fxiaoke.chatbi.common.model.prompts.PromptTemplateType;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.fxiaoke.chatbi.integration.client.llm.LlmClient.chartType2LayoutMap;
import static com.fxiaoke.chatbi.knowledge.dictionary.EasyStatChartTypeEnum.GAUGE;

/**
 * 默认图表处理器
 * 用于处理标准图表类型的查询和洞察
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DefaultChartHandler implements ChartHandler {

    private final FSBIClient fsbiClient;
    private final FilterConverter filterConverter;
    private final ContextFactory contextFactory;
    private final LlmClient llmClient;
    private final PromptTemplateService promptTemplateService;

    @Override
    public String[] getSupportedViewIds() {
        // 默认处理器不直接支持特定ID
        return new String[0];
    }

    @Override
    public DataQueryOutput handleQuery(ChartQueryDSL queryArg, Map<String, String> headers, ActionContext context) {
        UserIdentity userIdentity = context.getUserIdentity();

        try {
            // 获取图表配置
            StatChartConfResult chartConfig = fetchChartConfig(queryArg, headers);

            // 如果图表配置有提示信息但不是严重错误，构建包含提示的结果并返回
            if (Strings.isNotEmpty(chartConfig.getTips())) {
                log.warn("图表配置包含提示信息: {}", chartConfig.getTips());

                // 创建标准图表结果
                StandardChartResult resultData = StandardChartResult.builder()
                        .chartConfig(chartConfig)
                        .build();

                return DataQueryOutput.builder()
                        .queryArg(queryArg)
                        .title(chartConfig.getViewName())
                        .resultData(resultData)
                        .build();
            }

            // 执行数据查询
            QueryChartDataResult queryData = executeChartDataQuery(queryArg, chartConfig, userIdentity, headers);

            // 创建简化图表结果
            SimpleChartResult simpleResult = SimpleChartResult.fromFullResult(queryData, chartConfig);

            // 创建标准图表结果
            StandardChartResult resultData = StandardChartResult.builder()
                    .queryData(queryData)
                    .chartConfig(chartConfig)
                    .simpleChartResult(simpleResult)
                    .build();

            // 构建输出
            return DataQueryOutput.builder()
                    .queryArg(queryArg)
                    .title(chartConfig.getViewName())
                    .resultData(resultData)
                    .build();

        } catch (Exception e) {
            log.error("执行标准图表查询失败", e);
            throw new ActionException(ChatbiErrorCodeEnum.DATA_QUERY_ERROR, "执行标准图表查询失败: " + e.getMessage());
        }
    }

    @Override
    public InsightOutput handleInsight(InsightInput input, ActionContext context) {
        // 获取查询结果
        DataQueryOutput queryOutput = input.getQueryOutput();

        if (queryOutput == null || queryOutput.getResultData() == null) {
            return InsightOutput.builder()
                    .quickInsight("无法生成洞察，缺少图表数据")
                    .fullInsight("无法生成洞察，缺少图表数据")
                    .build();
        }

        // 准备参数
        Map<String, Object> params = new HashMap<>();
        params.put("chartData", queryOutput.getJsonForLLM());
        params.put("chartType", queryOutput.getResultData().getChartType());

        // 获取提示词模板并调用LLM
        String template = promptTemplateService.getTemplate(PromptTemplateType.CHART_INSIGHT);
        String llmResponse = llmClient.chatWithTemplate(template, params, context);

        // 解析结果
        InsightOutput content = LLMResponseParser.parseJson(llmResponse, InsightOutput.class);

        // 如果无法解析为JSON，则使用整个响应作为完整洞察，并提取前200字符作为快速洞察
        if (content == null) {
            String quickInsight = llmResponse.length() > 200 ? llmResponse.substring(0, 200) + "..." : llmResponse;
            return InsightOutput.builder().quickInsight(quickInsight).fullInsight(llmResponse).build();
        }

        return content;
    }

    /**
     * 获取图表配置
     */
    public StatChartConfResult fetchChartConfig(ChartQueryDSL queryArg,
                                                Map<String, String> headers) throws ActionException {
        try {
            ChartConfigArg chartConfArg = convertToChartConfArg(queryArg);
            BaseResult baseResult = fsbiClient.queryChartConfig(chartConfArg, headers);
            StatChartConfResult chartConfResult = baseResult.getResult().toJavaObject(StatChartConfResult.class);

            // layout 替换
            String chartType = chartConfResult.getChartType();
            StatLayoutInfo layout = chartConfResult.getLayout();
            mergeConfig(layout, chartType);
            chartConfResult.setUdfStatAsyncQuery(0);

            log.info("根据VIEWID({})反查chartConfig结果: {}", queryArg.getViewId(), JSON.toJSONString(baseResult));
            return chartConfResult;
        } catch (Exception e) {
            log.error("获取图表配置失败", e);
            throw new ActionException(ChatbiErrorCodeEnum.DATA_QUERY_ERROR, "获取图表配置失败: " + e.getMessage());
        }
    }

    /**
     * 执行图表数据查询
     */
    private QueryChartDataResult executeChartDataQuery(ChartQueryDSL queryArg,
            StatChartConfResult chartConfig,
            UserIdentity userIdentity,
            Map<String, String> headers) throws ActionException {
        try {
            ViewDataQueryArg chartDataArg = convertToChartDataArg(queryArg, chartConfig, userIdentity);
            log.info("chartDataArg:{}", JSON.toJSONString(chartDataArg));
            BaseResult baseResult = fsbiClient.queryChartData(chartDataArg, headers);
            QueryChartDataResult queryData = baseResult.getResult().toJavaObject(QueryChartDataResult.class);
            log.debug("图表数据查询完成，viewId={}", queryArg.getViewId());
            return queryData;
        } catch (Exception e) {
            log.error("执行图表数据查询失败", e);
            throw new ActionException(ChatbiErrorCodeEnum.DATA_QUERY_ERROR, "执行图表数据查询失败: " + e.getMessage());
        }
    }

    /**
     * 合并配置
     */
    private void mergeConfig(Object target, String chartType) {
        Map<String, Object> config = chartType2LayoutMap.get(chartType);
        if (config == null)
            return;

        BeanWrapper wrapper = new BeanWrapperImpl(target);
        config.forEach((key, value) -> {
            if (wrapper.isWritableProperty(key)) {
                try {
                    wrapper.setPropertyValue(key, convertValue(value));
                } catch (Exception e) {
                    throw new RuntimeException("属性设置失败: " + key, e);
                }
            }
        });
    }

    // 类型转换方法
    private Object convertValue(Object value) {
        if (value instanceof Integer) {
            return value; // 基础类型直接返回
        }
        if (value instanceof String) {
            String strVal = (String) value;
            // 添加自定义类型转换逻辑
            if (strVal.equalsIgnoreCase("true"))
                return true;
            if (strVal.equalsIgnoreCase("false"))
                return false;
        }
        return value;
    }

    /**
     * 转换为ChartConfArg
     */
    private ChartConfigArg convertToChartConfArg(ChartQueryDSL simpleArg) {
        ChartConfigArg arg = new ChartConfigArg();
        arg.setId(simpleArg.getViewId());
        arg.setIsView(1);
        arg.setInnerFrom("AI");
        return arg;
    }

    private void constructGaugeQueryArg(QueryChartDataArg queryArg, StatChartConfResult statChartConfResult) {
        if (Objects.isNull(statChartConfResult)) {
            return;
        }
        try {
            StatLayoutInfo layout = statChartConfResult.getLayout();
            String gaugeMax = layout.getGaugeMax();
            String gaugeMin = layout.getGaugeMin();
            List<MeasureFieldDto> gaugeMeasureFields = new ArrayList<>();

            if (StringUtils.isNotBlank(gaugeMax)) {
                JSONObject jsonObject = com.alibaba.fastjson.JSON.parseObject(gaugeMax);
                String valueStr = jsonObject.getString("value");
                if (StringUtils.isNotBlank(valueStr) && !Objects.equals("0", valueStr)) {
                    gaugeMeasureFields.add(com.alibaba.fastjson.JSON.parseObject(valueStr, MeasureFieldDto.class));
                }
            }

            if (StringUtils.isNotBlank(gaugeMin)) {
                JSONObject jsonObject = com.alibaba.fastjson.JSON.parseObject(gaugeMin);
                String valueStr = jsonObject.getString("value");
                if (StringUtils.isNotBlank(valueStr) && !Objects.equals("0", valueStr)) {
                    gaugeMeasureFields.add(com.alibaba.fastjson.JSON.parseObject(valueStr, MeasureFieldDto.class));
                }
            }

            if (CollectionUtils.isNotEmpty(statChartConfResult.getMeasureFields())) {
                gaugeMeasureFields.addAll(statChartConfResult.getMeasureFields());
            }
            queryArg.setMeasureFields(gaugeMeasureFields);
        } catch (Exception e) {
            log.error("构造仪表盘arg报错, queryArg:{}, layout:{}", queryArg, statChartConfResult.getLayout(), e);
        }
    }

    /**
     * 转换为ChartDataArg
     */
    private ViewDataQueryArg convertToChartDataArg(ChartQueryDSL simpleArg,
            StatChartConfResult chartConfig,
            UserIdentity userIdentity) {
        ViewDataQueryArg arg = new ViewDataQueryArg();
        arg.setId(simpleArg.getViewId());
        arg.setSchemaId(simpleArg.getSchemaId());
        arg.setIsView(1);
        arg.setInnerFrom("AI");

        // 设置图表配置相关参数
        updateChartConfigParams(arg, chartConfig);

        // 处理仪表盘特殊配置
        if (Objects.equals(GAUGE.getKey(), arg.getChartType())) {
            constructGaugeQueryArg(arg, chartConfig);
        }

        // 处理筛选条件
        applyFilters(arg, simpleArg, userIdentity);

        return arg;
    }

    /**
     * 更新图表配置参数
     */
    private void updateChartConfigParams(QueryChartDataArg arg, StatChartConfResult chartConfig) {
        if (chartConfig != null) {
            arg.setSchemaId(chartConfig.getSchemaId());
            arg.setIsShowDimension(chartConfig.getIsShowDimension());
            arg.setChartType(chartConfig.getChartType());
        }
    }

    /**
     * 应用筛选条件
     */
    public void applyFilters(QueryChartDataArg arg, ChartQueryDSL simpleArg, UserIdentity userIdentity) {
        if (skipConstructFilterByViewId(arg, userIdentity))  {
            return;
        }

        // 判断是否是新目标
        boolean isMultiGoalTarget = contextFactory.isIsMultiGoalTarget(arg, userIdentity);

        // 构造普通筛选器
        applyNormalFilters(arg, simpleArg, userIdentity, isMultiGoalTarget);

        // 构造目标规则筛选器
        applyMultiGoalRuleFilter(arg, userIdentity, isMultiGoalTarget);

    }

    private boolean skipConstructFilterByViewId(QueryChartDataArg arg, UserIdentity userIdentity) {
        String templateContent = promptTemplateService.getTemplate(PromptTemplateType.OTHER);
        if (StringUtils.isBlank(templateContent)) {
            return false;
        }

        Map<String, Map<String, List<String>>> map = promptTemplateService.convertToComplexMap(templateContent);

        if (MapUtils.isEmpty(map)) {
            return false;
        }

        Map<String, List<String>> ei2ViewIds = map.get("skipConstructFilterByViewId");

        if (MapUtils.isEmpty(ei2ViewIds)) {
            return false;
        }

        List<String> viewIds = ei2ViewIds.get(userIdentity.getTenantId());

        if (CollectionUtils.isEmpty(viewIds)) {
            return false;
        }

        return viewIds.contains(arg.getId());
    }

    private void applyNormalFilters(QueryChartDataArg arg, ChartQueryDSL simpleArg, UserIdentity userIdentity,
            boolean isMultiGoalTarget) {
        if (userIdentity == null || CollectionUtils.isEmpty(simpleArg.getFilters())) {
            return;
        }

        try {
            log.info("开始构造标准筛选器，arg:{}, simpleArg:{}, isMultiGoalTarget:{}", arg, simpleArg, isMultiGoalTarget);
            ConverterContext context = contextFactory.createConverterContext(arg, userIdentity, simpleArg,
                    isMultiGoalTarget);
            List<com.facishare.bi.common.entities.stat.FilterList> filterLists = filterConverter.convert(
                    FilterRequest.builder().filters(simpleArg.getFilters()).build(),
                    context);

            if (CollectionUtils.isNotEmpty(filterLists)) {
                arg.setFilterLists(filterLists);
            }

            log.info("标准筛选器构造完成，filterLists:{}", JSON.toJSONString(filterLists));
        } catch (Exception e) {
            log.error("筛选器构造失败，simpleArg:{}", simpleArg, e);
            // 不抛出异常，继续执行
        }
    }

    /**
     * 追加目标规则筛选
     */
    private void applyMultiGoalRuleFilter(QueryChartDataArg arg, UserIdentity userIdentity, boolean isMultiGoalTarget) {
        if (!isMultiGoalTarget) {
            return;
        }

        try {
            log.info("开始构造目标筛选器，arg:{}, isMultiGoalTarget:{}", arg, isMultiGoalTarget);
            // 获取原生筛选器
            StatFilterArg statFilterArg = new StatFilterArg();
            statFilterArg.setId(arg.getId());
            statFilterArg.setIsView(1);
            StatFilterResult statFilterResult = fsbiClient.queryFiltersResult(statFilterArg,
                    HttpHeaderBuilder.constructHttpHeader(userIdentity));

            if (statFilterResult == null) {
                log.error("rpc 获取筛选器失败, statFilterArg:{}, userIdentity:{}", JSON.toJSONString(statFilterArg),
                        userIdentity);
                return;
            }

            // 找到目标规则筛选器
            List<com.facishare.bi.common.entities.stat.FilterList> filterListList = statFilterResult.getFilterLists();
            com.facishare.bi.common.entities.stat.Filter filter = filterListList.stream()
                    .flatMap(filterList -> filterList.getFilters().stream())
                    .filter(f -> Objects.equals(f.getFieldID(), ContextFactory.GOAL_RULE_FIELD_ID))
                    .findFirst()
                    .orElse(null);

            if (filter == null) {
                return;
            }

            // 追加目标规则筛选
            List<com.facishare.bi.common.entities.stat.FilterList> filterLists = arg.getFilterLists();
            if (CollectionUtils.isEmpty(filterLists)) {
                // 如果 filterLists 为空，创建新的 FilterList 并添加 filter
                com.facishare.bi.common.entities.stat.FilterList newFilterList = new com.facishare.bi.common.entities.stat.FilterList();
                newFilterList.setFilters(Lists.newArrayList(filter));
                arg.setFilterLists(Lists.newArrayList(newFilterList));
                return;
            }

            // 向现有的 FilterList 中添加 filter
            filterLists.forEach(filterList -> {
                List<com.facishare.bi.common.entities.stat.Filter> filters = filterList.getFilters();
                if (CollectionUtils.isNotEmpty(filters)) {
                    filters.add(filter);
                }
            });
            log.info("目标筛选器构造完成，filterLists:{}", JSON.toJSONString(filterLists));

        } catch (Exception e) {
            log.error("构造目标规则失败", e);
        }

    }
}