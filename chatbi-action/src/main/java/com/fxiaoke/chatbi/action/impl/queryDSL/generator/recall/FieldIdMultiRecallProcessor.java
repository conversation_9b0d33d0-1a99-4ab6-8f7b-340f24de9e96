package com.fxiaoke.chatbi.action.impl.queryDSL.generator.recall;

import com.facishare.bi.metadata.context.dto.ads.StatViewField;
import com.facishare.bi.metadata.context.dto.ads.StatViewFilter;
import com.facishare.bi.metadata.context.dto.dw.BiMtDimension;
import com.facishare.bi.metadata.context.dto.dw.Schema;
import com.facishare.bi.metadata.context.dto.dw.arg.QuerySchemasArg;
import com.facishare.bi.metadata.context.service.dw.ISchemaQueryService;
import com.fxiaoke.chatbi.action.impl.queryDSL.generator.FilterProcessor;
import com.fxiaoke.chatbi.action.impl.queryDSL.generator.FilterProcessorContext;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.intent.FilterInfo;
import com.fxiaoke.chatbi.common.utils.UserInfoConvertUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 字段ID多路召回处理器
 * 基于多路召回系统，处理过滤条件中的字段ID匹配
 * 替代原有的FieldIdProcessor
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FieldIdMultiRecallProcessor implements FilterProcessor {

    private final FieldRecallerFactory fieldRecallerFactory;
    private final ISchemaQueryService schemaQueryService;

    @Override
    public void process(FilterProcessorContext context) {
        List<FilterInfo> filters = context.getFilters();
        if (CollectionUtils.isEmpty(filters)) {
            log.info("过滤条件为空，跳过字段ID处理");
            return;
        }

        // 有效过滤条件筛选 - 只保留有字段名的过滤条件
        List<FilterInfo> validFilters = filters.stream()
                .filter(f -> StringUtils.isNotBlank(f.getField()))
                .collect(Collectors.toList());

        if (validFilters.isEmpty()) {
            log.info("无有效过滤条件，跳过字段ID处理");
            return;
        }

        // 准备召回范围数据
        List<String> viewFieldIds = getViewFieldIds(context);
        List<String> viewFilterIds = getViewFilterIds(context);
        SchemaFieldIdsResult schemaFieldIdsResult = getSchemaFieldIds(context);
        List<String> schemaFieldIds = schemaFieldIdsResult.getAllFieldIds();
        String actionDateFieldId = schemaFieldIdsResult.getSpecialFieldIds().get("action_date");
        Map<String, String> viewFieldName2IdMap = getViewFieldName2IdMap(context);
        Map<String, String> viewFilterName2Ids = getViewFilterName2Ids(context);
        Map<String, String> name2FieldIdMap = schemaFieldIdsResult.getName2FieldIdMap();

        log.info("viewFieldIds:{}", viewFieldIds);
        log.info("viewFilterIds:{}", viewFilterIds);
        log.info("viewFieldName2IdMap:{}", viewFieldName2IdMap);
        log.info("viewFilterName2Ids:{}", viewFilterName2Ids);
        log.info("name2FieldIdMap:{}", name2FieldIdMap);


        // 执行多路召回
        Map<String, String> fieldIdMapping = multiChannelMatch(
                validFilters,
                context.getSchemaId(),
                context.getUserIdentity(),
                viewFieldIds,
                viewFilterIds,
                schemaFieldIds, 
                actionDateFieldId,
                context.getReasoningCollector(),
                viewFieldName2IdMap,
                viewFilterName2Ids,
                name2FieldIdMap
        );

        // 设置结果到上下文
        context.setFieldIdMapping(fieldIdMapping);
        log.info("字段ID多路召回匹配完成, 匹配数量: {}/{}",
                fieldIdMapping.size(), validFilters.size());

        // 记录未匹配的字段
        if (fieldIdMapping.size() < validFilters.size()) {
            List<String> unmatchedFields = validFilters.stream()
                    .map(FilterInfo::getField)
                    .filter(field -> !fieldIdMapping.containsKey(field))
                    .collect(Collectors.toList());
            log.warn("以下字段未能匹配: {}", unmatchedFields);
        }
    }

    /**
     * 执行多路召回匹配
     */
    private Map<String, String> multiChannelMatch(
            List<FilterInfo> filters,
            String schemaId,
            UserIdentity userIdentity,
            List<String> viewFieldIds,
            List<String> viewFilterIds,
            List<String> schemaFieldIds, 
            String actionDateFieldId,
            com.fxiaoke.chatbi.common.model.reasoning.ReasoningCollector reasoningCollector,
            Map<String, String> viewFieldName2IdMap,
            Map<String, String> viewFilterName2IdMap,
            Map<String, String> name2FieldIdMap) {

        Map<String, String> fieldIdMapping = new HashMap<>();
        MultiChannelFieldRecaller recaller = fieldRecallerFactory.getMultiChannelRecaller();

        for (FilterInfo filter : filters) {
            // 创建匹配上下文
            MatchContext context = MatchContextBuilder.builder()
                    .fieldName(filter.getField())
                    .fieldValues(filter.getValues())
                    .schemaId(schemaId)
                    .userIdentity(userIdentity)
                    .filterInfo(filter)
                    .viewFieldIds(viewFieldIds)
                    .viewFilterIds(viewFilterIds)
                    .schemaFieldIds(schemaFieldIds)
                    .actionDateFieldId(actionDateFieldId)
                    .reasoningCollector(reasoningCollector)
                    .viewFieldName2IdMap(viewFieldName2IdMap)
                    .viewFilterName2IdMap(viewFilterName2IdMap)
                    .schemaFieldName2IdMap(name2FieldIdMap)
                    .build();

            // 获取最佳匹配
            String fieldId = recaller.getBestMatchFieldId(context);

            // 添加到映射结果
            if (StringUtils.isNotBlank(fieldId)) {
                fieldIdMapping.put(filter.getField(), fieldId);
            }
        }

        return fieldIdMapping;
    }

    /**
     * 获取视图字段ID列表
     */
    private List<String> getViewFieldIds(FilterProcessorContext context) {
        if (context.getStatView() == null ||
                CollectionUtils.isEmpty(context.getStatView().getViewFields())) {
            return new ArrayList<>();
        }

        return context.getStatView().getViewFields().stream()
                .map(StatViewField::getFieldId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    /**
     * 获取视图过滤器ID列表
     */
    private List<String> getViewFilterIds(FilterProcessorContext context) {
        if (context.getStatView() == null ||
                CollectionUtils.isEmpty(context.getStatView().getViewFilters())) {
            return new ArrayList<>();
        }

        return context.getStatView().getViewFilters().stream()
                .map(viewFilter -> viewFilter.getFieldId())
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    /**
     * 获取主题字段ID列表和特殊字段ID
     * 通过SchemaService查询所有主题字段
     */
    private SchemaFieldIdsResult getSchemaFieldIds(FilterProcessorContext context) {
        String schemaId = context.getSchemaId();
        if (StringUtils.isBlank(schemaId)) {
            log.info("无主题ID，无法获取主题字段");
            return new SchemaFieldIdsResult(Collections.emptyList(), Collections.emptyMap(), Collections.emptyMap());
        }
        try {
            QuerySchemasArg schemasArg = new QuerySchemasArg();
            schemasArg.setIncludeMeasures(true);
            schemasArg.setIncludeDimensions(true);
            schemasArg.setSchemaIds(Collections.singletonList(schemaId));

            List<Schema> schemas = schemaQueryService.querySchemas(
                    schemasArg,
                    UserInfoConvertUtil.createUserInfo(context.getUserIdentity())
            );

            if (CollectionUtils.isEmpty(schemas)) {
                log.warn("未找到主题: {}", schemaId);
                return new SchemaFieldIdsResult(Collections.emptyList(), Collections.emptyMap(), Collections.emptyMap());
            }

            Schema schema = schemas.get(0);
            List<String> allFieldIds = new ArrayList<>();
            Map<String, String> specialFieldIds = new HashMap<>();
            Map<String, String> name2FieldIdMap = new HashMap<>();


            // 提取维度字段ID
            if (CollectionUtils.isNotEmpty(schema.getDimensions())) {
                for (BiMtDimension dim : schema.getDimensions()) {
                    String fieldId = dim.getDimensionId();
                    String dimensionField = dim.getDimensionField();
                    String dimensionName = dim.getDimensionName();
                    if (StringUtils.isNotBlank(fieldId)) {
                        allFieldIds.add(fieldId);
                        name2FieldIdMap.put(dimensionName, fieldId);
                        if ("action_date".equalsIgnoreCase(dimensionField) || "日期".equalsIgnoreCase(dim.getDimensionName())) {
                            specialFieldIds.put("action_date", fieldId);
                        }
                    }
                }
            }
            log.info("从主题{}中获取到{}个字段ID, action_date字段ID: {}", schemaId, allFieldIds.size(), specialFieldIds.get("action_date"));
            return new SchemaFieldIdsResult(allFieldIds, specialFieldIds, name2FieldIdMap);
        } catch (Exception e) {
            log.error("获取主题字段失败: {}", schemaId, e);
            List<String> fallbackIds = new ArrayList<>();
            fallbackIds.addAll(getViewFieldIds(context));
            fallbackIds.addAll(getViewFilterIds(context));
            return new SchemaFieldIdsResult(fallbackIds, Collections.emptyMap(), Collections.emptyMap());
        }
    }

    /**
     * 获取视图字段ID列表
     */
    private Map<String, String> getViewFieldName2IdMap(FilterProcessorContext context) {
        if (context.getStatView() == null ||
                CollectionUtils.isEmpty(context.getStatView().getViewFields())) {
            return Collections.emptyMap();
        }

        return context.getStatView().getViewFields().stream()
                .collect(Collectors.toMap(StatViewField::getFieldName, StatViewField::getFieldId, (v1, v2) -> v2));
    }

    /**
     * 获取视图过滤器ID列表
     */
    private Map<String, String> getViewFilterName2Ids(FilterProcessorContext context) {
        if (context.getStatView() == null ||
                CollectionUtils.isEmpty(context.getStatView().getViewFilters())) {
            return Collections.emptyMap();
        }

        return context.getStatView().getViewFilters().stream()
                .collect(Collectors.toMap(StatViewFilter::getFieldName, StatViewFilter::getFieldId, (v1, v2) -> v2));
    }


} 