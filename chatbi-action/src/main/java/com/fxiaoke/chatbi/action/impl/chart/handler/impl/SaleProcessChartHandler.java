package com.fxiaoke.chatbi.action.impl.chart.handler.impl;

import com.facishare.bi.api.UdfObjFieldService;
import com.facishare.bi.api.dto.UdfObjFieldInfo;
import com.facishare.bi.common.entities.QueryChartDataArg;
import com.facishare.bi.common.entities.QueryReportDataArg;
import com.facishare.bi.common.entities.StatChartConfResult;
import com.facishare.bi.common.entities.stat.FilterList;
import com.fxiaoke.chatbi.action.impl.chart.handler.ChartHandler;
import com.fxiaoke.chatbi.common.mapstruct.FilterEntityMapper;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.action.input.InsightInput;
import com.fxiaoke.chatbi.common.model.action.output.DataQueryOutput;
import com.fxiaoke.chatbi.common.model.action.output.InsightOutput;
import com.fxiaoke.chatbi.common.model.action.ActionContext;
import com.fxiaoke.chatbi.common.model.dto.ChartQueryDSL;
import com.fxiaoke.chatbi.common.model.dto.SaleProcessAnalysisResult;
import com.fxiaoke.chatbi.common.model.dto.SpecialChartResult;
import com.fxiaoke.chatbi.common.model.prompts.PromptTemplateType;
import com.fxiaoke.chatbi.integration.client.fsbi.FSBIUDFClient;
import com.fxiaoke.chatbi.integration.client.llm.LlmClient;
import com.fxiaoke.chatbi.integration.utils.LLMResponseParser;
import com.fxiaoke.chatbi.prompts.PromptTemplateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 销售流程分析图表处理器
 * 专门处理销售流程相关的特殊图表
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SaleProcessChartHandler implements ChartHandler {

    private final FSBIUDFClient fsbiudfClient;
    private final LlmClient llmClient;
    private final PromptTemplateService promptTemplateService;
    private final DefaultChartHandler defaultChartHandler;
    private final UdfObjFieldService udfObjFieldService;

    private final String viewName = "销售阶段转化分析";

    /**
     * 支持的图表ID列表
     */
    private static final String[] SUPPORTED_VIEW_IDS = {
            "BI_5dce336dfe4f310001b4ac22",
            "BI_595e213437aa1badec9778e6"
    };

    @Override
    public String[] getSupportedViewIds() {
        return SUPPORTED_VIEW_IDS;
    }

    @Override
    public DataQueryOutput handleQuery(ChartQueryDSL queryArg, Map<String, String> headers, ActionContext context) {
        log.info("使用销售流程分析图表处理器处理查询, viewId: {}", queryArg.getViewId());
        StatChartConfResult chartConfig = defaultChartHandler.fetchChartConfig(queryArg, headers);
        chartConfig.setChartType("oppo2");
        chartConfig.setUdfStatAsyncQuery(0);
        chartConfig.setViewName(viewName);

        // 查询销售流程数据
        QueryReportDataArg udfQueryReportDataArg = new QueryReportDataArg();
        String viewId = StringUtils.isNotBlank(chartConfig.getSwitchReportId()) ? chartConfig.getSwitchReportId() : "BI_5dce336dfe4f310001b4ac22";
        udfQueryReportDataArg.setViewId(viewId);
        udfQueryReportDataArg.setIsView(1);

        //构造筛选器
        QueryChartDataArg queryChartDataArg = new QueryChartDataArg();
        queryChartDataArg.setId(queryArg.getViewId());

        constructUdfFilters(queryChartDataArg, queryArg, context.getUserIdentity(), udfQueryReportDataArg);

        log.info("特殊报表arg：{}", udfQueryReportDataArg);
        SaleProcessAnalysisResult saleProcessAnalysisResult = fsbiudfClient.queryReportData(udfQueryReportDataArg, headers);

        // 创建标准图表结果
        SpecialChartResult resultData = SpecialChartResult.builder()
                .chartConfig(chartConfig)
                .saleProcessAnalysisResult(saleProcessAnalysisResult)
                .build();

        // 构建输出
        return DataQueryOutput.builder()
                .queryArg(queryArg)
                .title(viewName)
                .resultData(resultData)
                .build();
    }

    private void constructUdfFilters(QueryChartDataArg queryChartDataArg, ChartQueryDSL queryArg, UserIdentity userIdentity, QueryReportDataArg udfQueryReportDataArg) {

        defaultChartHandler.applyFilters(queryChartDataArg, queryArg, userIdentity);

        List<FilterList> filterLists = queryChartDataArg.getFilterLists();

        if (CollectionUtils.isEmpty(filterLists)) {
            return;
        }

        log.info("特殊报表筛选器转换前-stat-filterList：{}", filterLists);

        List<String> udfFieldIds = filterLists.stream().flatMap(filterList -> filterList.getFilters().stream())
                .map(com.facishare.bi.common.entities.stat.Filter::getUdfFieldId).toList();


        //查询udf描述
        List<UdfObjFieldInfo> udfObjFieldInfos = udfObjFieldService.getDisplayFieldByIdsIgnoreIsShow(udfFieldIds, Integer.parseInt(userIdentity.getTenantId()));
        if (CollectionUtils.isEmpty(udfObjFieldInfos)) {
            return;
        }

        // statFilter -> rptFilter
        List<com.facishare.bi.common.entities.FilterList> filterListList = FilterEntityMapper.INSTANCE.toFilterList(filterLists);


        Map<String, UdfObjFieldInfo> fieldId2UdfFieldMap = udfObjFieldInfos.stream().collect(Collectors.toMap(UdfObjFieldInfo::getFieldId, Function.identity(), (v1, v2) -> v2));

        for (com.facishare.bi.common.entities.FilterList filterList : filterListList) {
            List<com.facishare.bi.common.entities.Filter> filters = filterList.getFilters();
            if (CollectionUtils.isEmpty(filters)) {
                continue;
            }
            for (com.facishare.bi.common.entities.Filter filter : filters) {
                String fieldId = filter.getFieldId();
                UdfObjFieldInfo udfObjFieldInfo = fieldId2UdfFieldMap.get(fieldId);
                if (udfObjFieldInfo == null) {
                    continue;
                }
                udfObjFieldInfo2Filter(filter, udfObjFieldInfo);
            }
        }
        log.info("特殊报表筛选转换后-rpt-filterList：{}", filterListList);

        udfQueryReportDataArg.setFilterLists(filterListList);
    }

    @Override
    public InsightOutput handleInsight(InsightInput input, ActionContext context) {
        // 获取查询结果
        DataQueryOutput queryOutput = input.getQueryOutput();

        if (queryOutput == null || queryOutput.getResultData() == null) {
            return InsightOutput.builder()
                    .quickInsight("无法生成洞察，缺少销售流程数据")
                    .fullInsight("无法生成洞察，缺少销售流程数据")
                    .build();
        }

        // 准备参数
        Map<String, Object> params = new HashMap<>();
        params.put("chartData", queryOutput.getJsonForLLM());
        params.put("chartType", "oppo2"); // 明确指定图表类型

        // 获取提示词模板并调用LLM
        String template = promptTemplateService.getTemplate(PromptTemplateType.CHART_INSIGHT);
        String llmResponse = llmClient.chatWithTemplate(template, params, context);

        // 解析结果
        InsightOutput content = LLMResponseParser.parseJson(llmResponse, InsightOutput.class);

        // 如果无法解析为JSON，则使用整个响应作为完整洞察，并提取前200字符作为快速洞察
        if (content == null) {
            String quickInsight = llmResponse.length() > 200 ? llmResponse.substring(0, 200) + "..." : llmResponse;
            return InsightOutput.builder().quickInsight(quickInsight).fullInsight(llmResponse).build();
        }

        return content;
    }

    private void udfObjFieldInfo2Filter(com.facishare.bi.common.entities.Filter filter, UdfObjFieldInfo udfObjFieldInfo) {
        filter.setDbObjName(udfObjFieldInfo.getDbObjName());
        filter.setDbFieldName(udfObjFieldInfo.getDbFieldName());
        filter.setCrmFieldName(udfObjFieldInfo.getCrmFieldName());
        filter.setCrmObjName(udfObjFieldInfo.getCrmObjName());
        filter.setFieldId(udfObjFieldInfo.getFieldId());
        filter.setRefObjName(udfObjFieldInfo.getRefObjName());
        filter.setFieldName(udfObjFieldInfo.getFieldName());
        filter.setFieldType(udfObjFieldInfo.getFieldType());
        filter.setSubFieldType(udfObjFieldInfo.getSubFieldType());
        filter.setIsPre(udfObjFieldInfo.getIsPre());
        //objName暂时没传 也能正常使用
        filter.setObjName(udfObjFieldInfo.getObjName());
        filter.setFieldLocation(udfObjFieldInfo.getFieldLocation());

    }
}