package com.fxiaoke.chatbi.action.impl.queryDSL.generator;

import com.facishare.bi.metadata.context.dto.ads.StatView;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.intent.ExtractedInfo;
import com.fxiaoke.chatbi.common.model.intent.FilterInfo;
import com.fxiaoke.chatbi.common.model.reasoning.ReasoningCollector;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * 过滤处理器上下文
 * 包含所有处理器共享的状态和数据
 */
@Slf4j
@RequiredArgsConstructor
@Data
public class FilterProcessorContext {
    private final ExtractedInfo info;
    private final List<FilterInfo> filters;
    private final StatView statView;
    private final UserIdentity userIdentity;
    
    // 推理信息收集器，可以为null
    private ReasoningCollector reasoningCollector;

    // 处理结果
    private Map<String, String> operatorMapping;
    private Map<String, String> fieldIdMapping;
    private String dateRangeMapping;

    public String getSchemaId() {
        return statView.getSchemaId();
    }
}
