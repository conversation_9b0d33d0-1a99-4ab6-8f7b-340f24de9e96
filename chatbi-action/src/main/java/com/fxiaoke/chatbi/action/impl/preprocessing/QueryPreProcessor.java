package com.fxiaoke.chatbi.action.impl.preprocessing;

import com.fxiaoke.chatbi.common.model.action.output.QueryPreProcessOutput;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.integration.model.pg.EnterpriseKnowledge;
import com.fxiaoke.chatbi.knowledge.service.EnterpriseKnowledgeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 问题预处理器 - 基于企业知识库的术语标准化
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class QueryPreProcessor {

  private final EnterpriseKnowledgeService enterpriseKnowledgeService;

  /**
   * 对问题中的词语进行标准化处理
   * 1. 将sourceTerm替换为targetTerm
   * 2. 将synonymTerm中的词替换为targetTerm
   *
   * @param query        原始查询
   * @param userIdentity 用户身份
   * @return 处理结果，包含处理后的查询和替换映射
   */
  public QueryPreProcessOutput process(String query, UserIdentity userIdentity) {
    if (StringUtils.isBlank(query)) {
      return QueryPreProcessOutput.builder()
                                  .originalQuery(query)
                                  .processedQuery(query)
                                  .termReplacements(new HashMap<>())
                                  .build();
    }
    String tenantId = userIdentity.getTenantId();

    // 获取该租户的所有知识条目
    List<EnterpriseKnowledge> knowledgeList = enterpriseKnowledgeService.listAllByTenant(tenantId);

    // 用于收集术语替换映射
    Map<String, String> replacements = new HashMap<>();

    // 对每个知识条目进行处理
    String processedQuery = query;
    for (EnterpriseKnowledge knowledge : knowledgeList) {
      // 1. 处理sourceTerm到targetTerm的替换
      if (StringUtils.isNotBlank(knowledge.getSourceTerm()) && StringUtils.isNotBlank(knowledge.getTargetTerm())) {
        if (query.contains(knowledge.getSourceTerm())) {
          processedQuery = processedQuery.replace(knowledge.getSourceTerm(), knowledge.getTargetTerm());
          replacements.put(knowledge.getSourceTerm(), knowledge.getTargetTerm());
        }
      }

      // 2. 处理synonymTerm到targetTerm的替换
      if (StringUtils.isNotBlank(knowledge.getSynonymTerm()) && StringUtils.isNotBlank(knowledge.getTargetTerm())) {
        // 同义词可能有多个，用逗号分隔
        String[] synonyms = knowledge.getSynonymTerm().split(",");
        for (String synonym : synonyms) {
          if (StringUtils.isNotBlank(synonym) && query.contains(synonym.trim())) {
            processedQuery = processedQuery.replace(synonym.trim(), knowledge.getTargetTerm());
            replacements.put(synonym.trim(), knowledge.getTargetTerm());
          }
        }
      }
    }

    // 如果有替换发生，记录日志
    if (!processedQuery.equals(query)) {
      log.info("术语标准化处理: {} -> {}", query, processedQuery);
    }

    return QueryPreProcessOutput.builder()
                                .originalQuery(query)
                                .processedQuery(processedQuery)
                                .termReplacements(replacements)
                                .build();
  }
}