package com.fxiaoke.chatbi.action.impl.insight;

import com.fxiaoke.chatbi.action.core.AbstractAction;
import com.fxiaoke.chatbi.action.impl.insight.generator.DataInsightGenerator;
import com.fxiaoke.chatbi.common.model.action.input.InsightInput;
import com.fxiaoke.chatbi.common.model.action.output.InsightOutput;
import com.fxiaoke.chatbi.common.model.action.ActionContext;
import com.fxiaoke.chatbi.common.exception.ActionException;
import com.fxiaoke.chatbi.common.model.action.ActionType;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 数据洞察Action
 * 负责从查询结果中提取洞察
 */
@Component
@RequiredArgsConstructor
public class DataInsightAction extends AbstractAction<InsightInput, InsightOutput> {

  private final DataInsightGenerator dataInsightGenerator;

  @Override
  public ActionType getType() {
    return ActionType.DATA_INSIGHT;
  }

  @Override
  protected InsightOutput doExecute(InsightInput input, ActionContext context) throws ActionException {
    // 直接执行洞察生成，让异常自然向上传播
    return dataInsightGenerator.generate(input, context);
  }


}