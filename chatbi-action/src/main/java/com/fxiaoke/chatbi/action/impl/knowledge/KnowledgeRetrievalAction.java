package com.fxiaoke.chatbi.action.impl.knowledge;

import com.fxiaoke.chatbi.action.core.AbstractAction;
import com.fxiaoke.chatbi.common.model.action.ActionContext;
import com.fxiaoke.chatbi.common.model.action.ActionInput;
import com.fxiaoke.chatbi.common.model.action.ActionType;
import com.fxiaoke.chatbi.common.model.intent.KnowledgeScope;
import com.fxiaoke.chatbi.common.model.intent.UserIntent;
import com.fxiaoke.chatbi.knowledge.service.KnowledgeRetrievalService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;

/**
 * 知识检索动作
 * 负责根据用户意图检索相关知识
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class KnowledgeRetrievalAction extends AbstractAction<KnowledgeRetrievalAction.Input, KnowledgeScope> {

    private final KnowledgeRetrievalService knowledgeRetrievalService;

    @Override
    public ActionType getType() {
        return ActionType.KNOWLEDGE_RETRIEVAL;
    }

    @Override
    protected KnowledgeScope doExecute(Input input, ActionContext context) {
        long startTime = System.currentTimeMillis();
        log.info("开始执行知识检索动作: userId={}", context.getUserIdentity().getUserId());

        try {
            // 执行知识检索
            KnowledgeScope knowledgeScope = knowledgeRetrievalService.retrieveKnowledge(
                    input.getUserIntent(),
                    context.getUserIdentity(),
                    context.getReasoningCollector()
            );

            // 处理空结果情况
            if (isEmptyResult(knowledgeScope)) {
                log.warn("未找到匹配的知识: userId={}, query={}",
                        context.getUserIdentity().getUserId(),
                        input.getUserIntent().getInstructions());
                return createEmptyKnowledgeScope();
            }

            // 记录执行完成日志
            log.info("知识检索动作执行完成: userId={}, 耗时={}ms, 匹配图表数={}",
                    context.getUserIdentity().getUserId(),
                    System.currentTimeMillis() - startTime,
                    CollectionUtils.size(knowledgeScope.getViewIds()));

            return knowledgeScope;

        } catch (Exception e) {
            log.error("知识检索动作执行异常: userId={}", context.getUserIdentity().getUserId(), e);
            throw e;
        }
    }

    /**
     * 检查知识范围是否为空
     */
    private boolean isEmptyResult(KnowledgeScope scope) {
        return scope == null ||
                (CollectionUtils.isEmpty(scope.getViewIds()));
    }

    /**
     * 创建空的知识范围对象
     */
    private KnowledgeScope createEmptyKnowledgeScope() {
        KnowledgeScope emptyScope = new KnowledgeScope();
        emptyScope.setViewIds(Collections.emptyList());
        return emptyScope;
    }

    /**
     * 输入参数
     */
    @Data
    public static class Input extends ActionInput {
        /**
         * 用户意图
         */
        private UserIntent userIntent;
    }
}