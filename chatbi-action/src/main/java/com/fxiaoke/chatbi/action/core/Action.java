package com.fxiaoke.chatbi.action.core;

import com.fxiaoke.chatbi.common.exception.ActionException;
import com.fxiaoke.chatbi.common.model.action.ActionContext;
import com.fxiaoke.chatbi.common.model.action.ActionInput;
import com.fxiaoke.chatbi.common.model.action.ActionOutput;
import com.fxiaoke.chatbi.common.model.action.ActionType;
import com.fxiaoke.chatbi.common.exception.ChatbiErrorCodeEnum;

/**
 * Action顶层接口
 * 定义动作执行的核心契约
 *
 * @param <I> 输入参数类型
 * @param <O> 输出结果类型
 */
public interface Action<I extends ActionInput, O extends ActionOutput> {

    /**
     * 获取动作类型
     */
    ActionType getType();

    /**
     * 执行动作
     *
     * @param input   输入参数
     * @param context 执行上下文
     * @return 执行结果
     * @throws ActionException 执行异常
     */
    ActionResult<O> execute(I input, ActionContext context) throws ActionException;

    /**
     * 验证输入
     * 子类可以覆盖此方法实现具体的验证逻辑
     *
     * @param input 输入参数
     * @throws ActionException 验证失败时抛出
     */
    default void validate(I input) throws ActionException {
        if (input == null) {
            throw new ActionException(ChatbiErrorCodeEnum.PARAM_ERROR, "输入参数不能为空");
        }
    }
} 