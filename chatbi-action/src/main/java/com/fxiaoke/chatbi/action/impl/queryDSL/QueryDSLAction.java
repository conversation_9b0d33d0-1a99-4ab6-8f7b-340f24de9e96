package com.fxiaoke.chatbi.action.impl.queryDSL;

import com.fxiaoke.chatbi.action.core.AbstractAction;
import com.fxiaoke.chatbi.common.model.action.input.QueryDSLInput;
import com.fxiaoke.chatbi.common.model.action.output.QueryDSLOutput;
import com.fxiaoke.chatbi.common.model.action.ActionType;
import com.fxiaoke.chatbi.action.impl.queryDSL.generator.QueryDSLGenerator;
import com.fxiaoke.chatbi.common.model.action.ActionContext;
import com.fxiaoke.chatbi.common.exception.ActionException;
import com.fxiaoke.chatbi.common.exception.ChatbiErrorCodeEnum;
import com.fxiaoke.chatbi.common.model.intent.KnowledgeScope;
import com.fxiaoke.chatbi.common.model.intent.UserIntent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 查询计划Action
 * 根据用户意图和知识检索结果，规划查询操作
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class QueryDSLAction extends AbstractAction<QueryDSLInput, QueryDSLOutput> {

    private final QueryDSLGenerator queryDSLGenerator;

    @Override
    public ActionType getType() {
        return ActionType.QUERY_DSL;
    }

    @Override
    protected QueryDSLOutput doExecute(QueryDSLInput input, ActionContext context) throws ActionException {
        // 直接执行业务逻辑，让异常自然向上传播
        UserIntent userIntent = input.getUserIntent();
        KnowledgeScope knowledgeScope = input.getKnowledgeScope();

        // 1. 验证核心输入数据
        if (userIntent == null) {
            throw new ActionException(ChatbiErrorCodeEnum.PARAM_ERROR, "用户意图不能为空");
        }

        if (knowledgeScope == null ||
                CollectionUtils.isEmpty(knowledgeScope.getViewIds())) {
            log.warn("知识范围为空，可能影响查询计划质量");
        }

        // 2. 生成查询计划
        log.info("开始生成查询计划，意图类型：{}", userIntent.getIntentType());
        QueryDSLOutput planningOutput = queryDSLGenerator.generate(input, context);

        return planningOutput;
    }
}