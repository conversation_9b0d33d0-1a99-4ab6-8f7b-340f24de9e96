package com.fxiaoke.chatbi.action.impl.queryDSL.generator;

import com.fxiaoke.chatbi.common.model.intent.ExtractedInfo;
import com.fxiaoke.chatbi.knowledge.embedding.service.DictionaryVectorMatchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 日期范围处理器
 * 负责处理过滤条件中的日期范围匹配
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DateRangeProcessor implements FilterProcessor {
    private final DictionaryVectorMatchService dictionaryVectorMatchService;

    @Override
    public void process(FilterProcessorContext context) {
        ExtractedInfo info = context.getInfo();
        if (info != null && StringUtils.isNotBlank(info.getTimeRange())) {
            String dateRangeMapping = dictionaryVectorMatchService.matchDateRange(
                    info.getTimeRange(), context.getUserIdentity());
            context.setDateRangeMapping(dateRangeMapping);
            log.info("日期范围匹配完成: {} -> {}", info.getTimeRange(), dateRangeMapping);
        }
    }
}
