package com.fxiaoke.chatbi.action.impl.query.converter.builder;

import com.alibaba.fastjson.JSON;
import com.facishare.bi.common.entities.stat.Filter;
import com.facishare.bi.common.model.EmployeeAndCircle;
import com.facishare.bi.metadata.context.dto.dw.BiMtDimension;
import com.fxiaoke.chatbi.action.impl.query.converter.AbstractFilterBuilder;
import com.fxiaoke.chatbi.action.impl.query.converter.ConverterContext;
import com.fxiaoke.chatbi.action.impl.query.converter.UiTypeService;
import com.fxiaoke.chatbi.common.model.dto.FilterConfig;
import com.fxiaoke.chatbi.integration.dao.ch.StatFieldMapper;
import com.fxiaoke.chatbi.integration.model.ch.StatField;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 人员部门筛选器构建器
 * 专门处理人员和部门类型的过滤条件
 */
@Slf4j
@Service
public class CircleFilterBuilder extends AbstractFilterBuilder {

  @Autowired
  private UiTypeService uiTypeService;

  @Autowired
  private StatFieldMapper statFieldMapper;


  @Override
  public Filter doProcess(FilterConfig filterConfig, ConverterContext context) {
    List<String> values = context.getFieldValues(filterConfig.getFieldId(), filterConfig.getValue1());
    BiMtDimension biMtDimension = context.getDimensionMap().get(filterConfig.getFieldId());

    if (biMtDimension == null) {
      log.warn("未找到维度信息: {}", filterConfig.getFieldId());
      return null;
    }

    // 创建基础Filter对象
    Filter filter = createBaseFilter(biMtDimension);

    List<StatField> statFieldByFieldId = statFieldMapper.setTenantId(context.getUserIdentity().getTenantId()).getStatFieldByFieldId(context.getUserIdentity().getTenantId(), filter.getFieldID());

    if (CollectionUtils.isNotEmpty(statFieldByFieldId)) {
      StatField statField = statFieldByFieldId.get(0);
      log.info("人员筛选器反查udf_field_id信息，statField:{}", JSON.toJSONString(statField));
      filter.setUdfFieldId(statField.getUdfFieldId());
    }

    // 设置人员部门类型特有的值格式
    setCircleValues(filter, values);

    // 设置操作符
    setOperator(filter, biMtDimension.getDimensionType(), filterConfig.getOperator());

    uiTypeService.fillUiType(filter, context.getUserIdentity());

    return filter;
  }

  /**
   * 设置人员部门类型的值
   *
   * @param filter 过滤器对象
   * @param values 值列表
   */
  private void setCircleValues(Filter filter, List<String> values) {
    filter.setValue1(JSON.toJSONString(values.stream()
        .map(value -> EmployeeAndCircle.builder().type("p").id(value).build())
        .collect(Collectors.toList())));
  }

}