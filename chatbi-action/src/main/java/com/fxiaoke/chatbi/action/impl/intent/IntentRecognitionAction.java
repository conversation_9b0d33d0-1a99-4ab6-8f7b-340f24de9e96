package com.fxiaoke.chatbi.action.impl.intent;

import com.fxiaoke.chatbi.action.core.AbstractAction;
import com.fxiaoke.chatbi.common.model.action.input.IntentInput;
import com.fxiaoke.chatbi.common.model.action.output.IntentOutput;
import com.fxiaoke.chatbi.common.model.action.ActionContext;
import com.fxiaoke.chatbi.common.exception.ActionException;
import com.fxiaoke.chatbi.common.model.action.ActionType;
import com.fxiaoke.chatbi.common.model.intent.UserIntent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 意图识别Action
 * 负责识别用户查询的意图类型，如分析意图、澄清意图等
 */
@Component
@RequiredArgsConstructor
public class IntentRecognitionAction extends AbstractAction<IntentInput, IntentOutput> {

    private final IntentRecognizer intentRecognizer;

    @Override
    public ActionType getType() {
        return ActionType.INTENT_RECOGNITION;
    }

    @Override
    protected IntentOutput doExecute(IntentInput input, ActionContext context) throws ActionException {
        UserIntent intent = intentRecognizer.recognize(input.getQuery(), input.getContext());
        return IntentOutput.builder().intent(intent).build();
    }
}