package com.fxiaoke.chatbi.action.impl.queryDSL.generator.recall;

import lombok.Builder;
import lombok.Data;

/**
 * 字段匹配结果
 * 包含匹配的字段ID、置信度和其他匹配信息
 */
@Data
@Builder
public class FieldMatchResult implements Comparable<FieldMatchResult> {
    // 匹配到的字段ID
    private String fieldId;
    
    // 匹配置信度 (0.0-1.0)
    private float confidence;
    
    // 匹配类型
    private RecallType recallType;
    
    // 匹配范围
    private RecallScope recallScope;
    
    // 用于排序的总分
    private float totalScore;
    
    // 额外信息 - 如匹配到的原始字段名等
    private String originalFieldName;
    
    // 字段使用频率 - 用于排序时加权
    private int usageFrequency;

    //向量匹配的相似度
    private double vectorScore;

    /**
     * 计算召回项的总分
     * 基于召回类型权重、召回范围权重、置信度和使用频率的加权计算
     *
     * @return 计算后的总分
     */
    public float calculateTotalScore() {
        // 获取基础权重
        float typeWeight = recallType.getDefaultWeight();
        float scopeWeight = recallScope.getDefaultWeight();

        // 计算基础分数
        float baseScore = typeWeight * scopeWeight * confidence;

        // 使用频率加权因子，最大0.2
        float frequencyBonus = Math.min(0.2f, usageFrequency / 100.0f);

        // 添加使用频率奖励
        float scoreWithFrequency = baseScore + frequencyBonus;

        // 对向量匹配类型应用相似度调整
        return applySimilarityAdjustment(recallType, vectorScore, scoreWithFrequency);
    }

    /**
     * 应用相似度调整因子
     * 针对向量匹配类型，根据相似度调整分数
     *
     * @param matchType 召回类型
     * @param similarity 相似度值 (0.0-1.0)
     * @param baseScore 基础分数
     * @return 调整后的分数
     */
    private float applySimilarityAdjustment(RecallType matchType, double similarity, float baseScore) {
        // 只对向量匹配类型应用相似度调整
        if (matchType != RecallType.VECTOR_MATCH) {
            return baseScore; // 非向量匹配直接返回基础分数
        }

        // 根据相似度分段确定调整系数
        float similarityAdjustment;
        if (similarity >= 0.95f) {
            similarityAdjustment = 1.5f;      // 极高相似度
        } else if (similarity >= 0.9f) {
            similarityAdjustment = 1.3f;      // 高相似度
        } else if (similarity >= 0.85f) {
            similarityAdjustment = 1.1f;      // 较高相似度
        } else if (similarity >= 0.8f) {
            similarityAdjustment = 1.0f;      // 中等相似度
        } else if (similarity >= 0.7f) {
            similarityAdjustment = 0.9f;      // 较低相似度
        } else {
            similarityAdjustment = 0.8f;      // 低相似度
        }

        // 应用相似度调整
        float adjustedScore = baseScore * similarityAdjustment;

        // 值匹配的基础上限（假设值匹配权重为0.8，范围权重和置信度均为1.0）
        float valueMatchUpperBound = 0.8f;

        // 防止普通向量匹配超过值匹配优先级（除非相似度极高）
        if (similarity < 0.98f && adjustedScore > valueMatchUpperBound) {
            adjustedScore = valueMatchUpperBound * 0.95f;  // 略低于值匹配上限
        }

        return adjustedScore;
    }
    @Override
    public int compareTo(FieldMatchResult other) {
        // 总分降序排序
        return Float.compare(other.getTotalScore(), this.getTotalScore());
    }
} 