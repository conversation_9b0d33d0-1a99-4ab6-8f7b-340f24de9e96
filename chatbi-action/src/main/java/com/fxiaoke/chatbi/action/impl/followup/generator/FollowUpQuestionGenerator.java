package com.fxiaoke.chatbi.action.impl.followup.generator;

import com.alibaba.fastjson2.JSON;
import com.fxiaoke.chatbi.common.model.action.ActionContext;
import com.fxiaoke.chatbi.common.model.action.input.FollowUpQuestionInput;
import com.fxiaoke.chatbi.common.model.action.output.FollowUpQuestionOutput;
import com.fxiaoke.chatbi.integration.client.llm.LlmClient;
import com.fxiaoke.chatbi.prompts.PromptTemplateService;
import com.fxiaoke.chatbi.common.model.prompts.PromptTemplateType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@Service
@RequiredArgsConstructor
public class FollowUpQuestionGenerator {
  private final LlmClient llmClient;
  private final PromptTemplateService promptTemplateService;

  /**
   * 综合推荐
   */
  public FollowUpQuestionOutput getFollowUpQuestions(FollowUpQuestionInput input, ActionContext context) {
    // 1. 用fastjson将input的参数转为Map<String,Object>
    Map<String, Object> tempMap = JSON.parseObject(JSON.toJSONString(input), Map.class);
    Map<String, Object> variables = tempMap.entrySet().stream()
            .collect(Collectors.toMap(
                    Map.Entry::getKey,
                    e -> JSON.toJSONString(e.getValue())
            ));

    // 2. 从PromptTemplateService中获取模板
    String template = promptTemplateService.getTemplate(PromptTemplateType.FOLLOW_UP_QUESTION);

    // 3. 调用LLmClient方法，直接获取Output对象
    FollowUpQuestionOutput output = llmClient.chatWithTemplate(template, variables, context, FollowUpQuestionOutput.class);

    return output;
  }
}