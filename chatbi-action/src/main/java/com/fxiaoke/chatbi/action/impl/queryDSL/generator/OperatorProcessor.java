package com.fxiaoke.chatbi.action.impl.queryDSL.generator;

import com.fxiaoke.chatbi.common.model.intent.FilterInfo;
import com.fxiaoke.chatbi.knowledge.embedding.service.DictionaryVectorMatchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 操作符处理器
 * 负责处理过滤条件中的操作符匹配
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperatorProcessor implements FilterProcessor {
    private final DictionaryVectorMatchService dictionaryVectorMatchService;

    @Override
    public void process(FilterProcessorContext context) {
        List<FilterInfo> filters = context.getFilters();
        List<String> ops = filters.stream()
                .map(FilterInfo::getOperator)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        if (!ops.isEmpty()) {
            Map<String, String> operatorMapping = dictionaryVectorMatchService.matchOperators(ops, context.getUserIdentity());
            context.setOperatorMapping(operatorMapping);
            log.info("操作符匹配完成, 匹配数量: {}/{}", operatorMapping.size(), ops.size());
        }
    }
}
