package com.fxiaoke.chatbi.action.impl.intent;

import com.fxiaoke.chatbi.common.model.action.ActionContext;
import com.fxiaoke.chatbi.common.model.intent.IntentType;
import com.fxiaoke.chatbi.common.model.intent.UserIntent;
import com.fxiaoke.chatbi.common.model.memory.ConversationContext;
import com.fxiaoke.chatbi.integration.client.llm.LlmClient;
import com.fxiaoke.chatbi.prompts.PromptTemplateService;
import com.fxiaoke.chatbi.common.model.prompts.PromptTemplateType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 基于LLM的意图识别器实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LlmIntentRecognizer implements IntentRecognizer {

  private final LlmClient llmClient;
  private final PromptTemplateService promptTemplateService;

  @Override
  public UserIntent recognize(String query, ConversationContext context) {
    // 创建意图对象
    UserIntent intent = new UserIntent();
    intent.setIntentId(UUID.randomUUID().toString());
    intent.setInstructions(query);

    try {
      // 准备提示词模板参数
      Map<String, Object> params = new HashMap<>();
      params.put("instructions", query);
      params.put("history", context.getHistory());
      params.put("workingMemory", context.getWorkingMemory());
      params.put("previousIntent", context.getLastIntent());


      // 获取提示词模板
      String template = promptTemplateService.getTemplate(PromptTemplateType.INTENT_RECOGNITION);

      // 创建ActionContext
      ActionContext actionContext = new ActionContext(context.getSessionId());
      actionContext.setUserIdentity(context.getUserIdentity());

      // 使用LLM分析意图
      log.info("识别用户意图: {}", query);
      UserIntent llmIntent = llmClient.chatWithTemplate(template, params, actionContext, UserIntent.class);

      // 复制必要的字段
      intent.setIntentType(llmIntent.getIntentType());
      intent.setConfidence(llmIntent.getConfidence());
      intent.setNeedsClarification(llmIntent.isNeedsClarification());
      intent.setClarificationQuestion(llmIntent.getClarificationQuestion());
      intent.setExtractedInfo(llmIntent.getExtractedInfo());

      // 处理连续性意图
      if (context.getLastIntent() != null && !context.getLastIntent().isEmpty()) {
        intent.setContinuation(true);
      }

      // 检查是否需要澄清
      checkClarificationNeeded(intent);

      log.info("意图识别结果: type={}, confidence={}, needsClarification={}", intent.getIntentType(), intent.getConfidence(), intent.isNeedsClarification());
      return intent;

    } catch (Exception e) {
      log.error("意图识别失败: {}", query, e);
      return createClarificationIntent(query, "抱歉，我在理解您的意图时遇到了问题，您能重新描述一下吗？");
    }
  }

  private void checkClarificationNeeded(UserIntent intent) {
    // 如果置信度过低，设置需要澄清
    if (intent.getConfidence() < 0.6) {
      intent.setNeedsClarification(true);
      if (intent.getClarificationQuestion() == null) {
        intent.setClarificationQuestion("您能更详细地描述一下您的分析需求吗？");
      }
    }
  }

  private UserIntent createClarificationIntent(String query, String question) {
    UserIntent intent = new UserIntent();
    intent.setIntentId(UUID.randomUUID().toString());
    intent.setInstructions(query);
    intent.setIntentType(IntentType.CLARIFICATION);
    intent.setNeedsClarification(true);
    intent.setClarificationQuestion(question);
    intent.setConfidence(0.3);
    return intent;
  }
} 