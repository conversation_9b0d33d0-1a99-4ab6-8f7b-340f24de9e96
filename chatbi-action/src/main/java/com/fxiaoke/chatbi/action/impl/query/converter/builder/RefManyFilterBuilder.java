package com.fxiaoke.chatbi.action.impl.query.converter.builder;

import com.alibaba.fastjson.JSON;
import com.facishare.bi.common.entities.EnumItem;
import com.facishare.bi.common.entities.stat.Filter;
import com.facishare.bi.metadata.context.dto.dw.BiMtDimension;
import com.fxiaoke.chatbi.action.impl.query.converter.AbstractFilterBuilder;
import com.fxiaoke.chatbi.action.impl.query.converter.ConverterContext;
import com.fxiaoke.chatbi.action.impl.query.converter.UiTypeService;
import com.fxiaoke.chatbi.common.model.dto.FilterConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 多引用筛选器构建器
 * 专门处理多对象引用类型的过滤条件
 */
@Slf4j
@Service
public class RefManyFilterBuilder extends AbstractFilterBuilder {

  @Autowired
  private UiTypeService uiTypeService;

  @Override
  public Filter doProcess(FilterConfig filterConfig, ConverterContext context) {

    List<String> values = context.getFieldValues(filterConfig.getFieldId(), filterConfig.getValue1());
    BiMtDimension biMtDimension = context.getDimensionMap().get(filterConfig.getFieldId());

    if (biMtDimension == null) {
      log.warn("未找到维度信息: {}", filterConfig.getFieldId());
      return null;
    }

    // 创建基础Filter对象
    Filter filter = createBaseFilter(biMtDimension);

    // 设置多引用类型特有的值格式
    setEnumValues(filter, values);

    // 设置操作符
    setOperator(filter, biMtDimension.getDimensionType(), filterConfig.getOperator());

    uiTypeService.fillUiType(filter, context.getUserIdentity());

    return filter;
  }

  /**
   * 设置多引用类型的枚举值
   *
   * @param filter 过滤器对象
   * @param values 值列表
   */
  private void setEnumValues(Filter filter, List<String> values) {
    filter.setValue1(JSON.toJSONString(values.stream().map(value -> {
      EnumItem enumItem = new EnumItem();
      enumItem.setEnumId(value);
      enumItem.setDisplayName(value);
      enumItem.setNodeName(value);
      return enumItem;
    }).collect(Collectors.toList())));
  }
}