package com.fxiaoke.chatbi.action.impl.queryDSL.generator.recall;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 字段召回器工厂
 * 管理所有的字段召回器实例
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FieldRecallerFactory {

    private final ApplicationContext applicationContext;
    private final MultiChannelFieldRecaller multiChannelRecaller;
    private List<FieldRecaller> fieldRecallers;
    
    /**
     * 初始化所有字段召回器实例
     */
    @PostConstruct
    public void init() {
        // 获取所有实现了FieldRecaller接口的Bean
        Map<String, FieldRecaller> recallerMap = applicationContext.getBeansOfType(FieldRecaller.class);
        
        if (recallerMap.isEmpty()) {
            log.warn("未找到任何字段召回器实现类");
            fieldRecallers = new ArrayList<>();
            return;
        }
        
        fieldRecallers = new ArrayList<>(recallerMap.values());
        
        // 过滤掉无效的召回器
        fieldRecallers.removeIf(recaller -> {
            if (recaller == null) {
                log.warn("发现空的字段召回器实例");
                return true;
            }
            if (recaller.getRecallType() == null) {
                log.warn("字段召回器的RecallType为空: {}", recaller.getClass().getName());
                return true;
            }
            return false;
        });
        
        // 按照召回类型的权重降序排序
        fieldRecallers.sort((r1, r2) -> 
                Float.compare(r2.getRecallType().getDefaultWeight(), 
                        r1.getRecallType().getDefaultWeight()));
        
        // 输出初始化信息
        log.info("字段召回器工厂初始化完成，已注册{}个召回器:", fieldRecallers.size());
        for (FieldRecaller recaller : fieldRecallers) {
            log.info("- {}: {}", recaller.getRecallType(), recaller.getClass().getSimpleName());
        }
    }
    
    /**
     * 获取多路召回协调器
     */
    public MultiChannelFieldRecaller getMultiChannelRecaller() {
        return multiChannelRecaller;
    }
    
    /**
     * 获取所有字段召回器
     */
    public List<FieldRecaller> getAllRecallers() {
        if (CollectionUtils.isEmpty(fieldRecallers)) {
            return new ArrayList<>();
        }
        return new ArrayList<>(fieldRecallers);
    }
    
    /**
     * 获取指定类型的字段召回器
     */
    public FieldRecaller getRecaller(RecallType recallType) {
        if (recallType == null || CollectionUtils.isEmpty(fieldRecallers)) {
            return null;
        }
        
        return fieldRecallers.stream()
                .filter(recaller -> recaller.getRecallType() == recallType)
                .findFirst()
                .orElse(null);
    }
}