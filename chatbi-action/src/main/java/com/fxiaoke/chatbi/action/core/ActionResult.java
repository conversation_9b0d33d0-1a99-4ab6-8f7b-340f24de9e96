package com.fxiaoke.chatbi.action.core;

import lombok.Builder;
import lombok.Data;

/**
 * Action执行结果
 *
 * @param <T> 结果数据类型
 */
@Data
@Builder
public class ActionResult<T> {

  /**
   * 执行是否成功
   */
  private boolean success;

  /**
   * 结果数据
   */
  private T data;

  /**
   * 错误信息
   */
  private String errorMsg;

  /**
   * 创建成功结果
   */
  public static <T> ActionResult<T> success(T data) {
    return ActionResult.<T>builder().success(true).data(data).build();
  }

  /**
   * 创建失败结果
   */
  public static <T> ActionResult<T> fail(String errorMsg) {
    return ActionResult.<T>builder().success(false).errorMsg(errorMsg).build();
  }

  /**
   * 创建空结果
   * 用于异步执行时的占位，表示结果尚未就绪
   */
  public static <T> ActionResult<T> empty() {
    return ActionResult.<T>builder()
        .success(true)  // 设为true因为这是正常的中间状态
        .data(null)     // 数据为空
        .errorMsg(null) // 无错误信息
        .build();
  }
} 