package com.fxiaoke.chatbi.action.impl.query.converter;

import com.alibaba.fastjson.JSONObject;
import com.facishare.bi.common.entities.FieldTypeInfo;
import com.facishare.bi.common.entities.UI;
import com.facishare.bi.common.entities.stat.Filter;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.integration.client.fsbi.FSBiUiClient;
import com.fxiaoke.chatbi.integration.client.fsbi.FieldUIType;
import com.fxiaoke.chatbi.integration.utils.HttpHeaderBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

@Service
public class UiTypeService {

    @Autowired
    private FSBiUiClient fsBiUiClient;

    public FieldTypeInfo getUiType(UserIdentity userIdentity, String dimensionId) {

        Map<String, String> headers = HttpHeaderBuilder.constructHttpHeader(userIdentity);

        JSONObject arg = new JSONObject();

        arg.put("dimensionId", dimensionId);

        FieldUIType.SingleFieldResult uiType = fsBiUiClient.getUIType(arg, headers);

        if (Objects.isNull(uiType) || Objects.isNull(uiType.getUi())) {
            return new FieldTypeInfo();
        }

        UI ui = uiType.getUi();

        FieldTypeInfo fieldTypeInfo = new FieldTypeInfo();
        fieldTypeInfo.setType(ui.getType());
        fieldTypeInfo.setData(ui.getData());
        return fieldTypeInfo;
    }

   public void fillUiType(Filter filter, UserIdentity userIdentity) {

       FieldTypeInfo uiType = getUiType(userIdentity, filter.getFieldID());

       filter.setUi(uiType);
   }
}
