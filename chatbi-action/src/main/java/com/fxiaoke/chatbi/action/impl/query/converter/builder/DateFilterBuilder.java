package com.fxiaoke.chatbi.action.impl.query.converter.builder;

import com.facishare.bi.common.entities.stat.Filter;
import com.facishare.bi.metadata.context.dto.dw.BiMtDimension;
import com.fxiaoke.chatbi.action.impl.query.converter.AbstractFilterBuilder;
import com.fxiaoke.chatbi.action.impl.query.converter.ConverterContext;
import com.fxiaoke.chatbi.common.model.dto.FilterConfig;
import com.fxiaoke.chatbi.knowledge.dictionary.DateRangeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

/**
 * 日期筛选器构建器
 * 专门处理日期类型的过滤条件
 */
@Slf4j
@Service
public class DateFilterBuilder extends AbstractFilterBuilder {
  @Override
  public Filter doProcess(FilterConfig filterConfig, ConverterContext context) {
    return processFilter(filterConfig, context);
  }

  @Override
  protected Filter buildFilter(BiMtDimension biMtDimension, FilterConfig filterConfig) {
    // 创建基础Filter对象
    Filter filter = createBaseFilter(biMtDimension);

    String dateRange = filterConfig.getDateRange();
    String type = biMtDimension.getDimensionType();
    String operator = filterConfig.getOperator();

    // 处理日期范围
    if (Strings.isNotEmpty(dateRange)) {
      filter.setDateRangeID(DateRangeEnum.getIdByCode(dateRange));
      filter.setOperator(23);
    } else {
      filter.setDateRangeID(null);
      // 设置值
      setFilterValues(filter, filterConfig);
      // 设置操作符
      setOperator(filter, type, operator);
    }

    return filter;
  }
}
