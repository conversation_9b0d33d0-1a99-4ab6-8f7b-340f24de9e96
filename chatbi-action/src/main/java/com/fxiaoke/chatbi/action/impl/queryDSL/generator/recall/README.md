# 字段ID多路召回系统

本系统实现了字段ID的多路召回能力，基于两个维度九种组合(召回范围、召回方式)设计，支持并行执行。

## 核心设计

### 两个维度

1. **召回范围**：定义在`RecallScope`枚举中
   - `VIEW_FIELD`: 视图字段范围 - 从视图的维度和指标字段中查找
   - `VIEW_FILTER`: 视图过滤器范围 - 从视图的过滤器字段中查找
   - `SCHEMA_FIELD`: 主题字段范围 - 从整个主题的所有字段中查找

2. **召回方式**：定义在`RecallType`枚举中
   - `EXACT_MATCH`: 精确匹配 - 通过字段名精确匹配
   - `VALUE_MATCH`: 值匹配 - 通过字段值匹配对应的字段
   - `VECTOR_MATCH`: 向量匹配 - 通过语义向量相似度匹配

### 九种组合

上述两个维度的组合，产生9种不同的召回渠道：

1. 视图字段 + 精确匹配
2. 视图字段 + 值匹配
3. 视图字段 + 向量匹配
4. 视图过滤器 + 精确匹配
5. 视图过滤器 + 值匹配
6. 视图过滤器 + 向量匹配
7. 主题字段 + 精确匹配
8. 主题字段 + 值匹配
9. 主题字段 + 向量匹配

## 核心组件

1. **FieldRecaller**：字段召回器接口，定义召回方法
   - `ExactMatchRecaller`: 精确匹配实现
   - `ValueMatchRecaller`: 值匹配实现
   - `VectorMatchRecaller`: 向量匹配实现

2. **MatchContext**：匹配上下文，包含匹配过程中的输入和中间状态
   - 包含字段名、字段值、候选范围等信息

3. **FieldMatchResult**：匹配结果，包含匹配的字段ID、置信度等信息
   - 实现了Comparable接口，用于排序

4. **MultiChannelFieldRecaller**：多路召回协调器，整合不同召回渠道的结果
   - 支持并行执行各个召回任务，提高性能
   - 使用CompletableFuture实现异步处理
   - 支持超时控制，避免单个渠道阻塞整体流程

## 并行多路召回

系统使用Java的CompletableFuture机制实现并行召回，主要特点：

1. **线程池自适应**：线程池大小默认为召回器数量+1，也可通过配置文件调整
2. **任务隔离**：每个召回方式+召回范围组合作为独立任务执行
3. **超时控制**：统一设置超时时间，超时后仍处理已完成的任务结果
4. **任务监控**：使用AsyncTaskUtils包装任务，便于性能监控和异常处理
5. **结果合并**：同类型召回结果优雅合并，避免竞态条件

### 配置参数

```properties
# 多路召回超时时间（毫秒）
chatbi.field.recall.timeout=1000
# 多路召回线程池大小（0表示自动设置为召回器数量+1）
chatbi.field.recall.thread-pool-size=0
```

## 使用方法

### 基本用法

```java
// 创建MatchContext
MatchContext context = MatchContextBuilder.builder()
    .fieldName("销售额")                      // 设置字段名
    .fieldValue("1000000")                  // 设置字段值（可选）
    .schemaId("schema123")                 // 设置主题ID
    .userIdentity(userIdentity)            // 设置用户身份
    .viewFieldIds(viewFieldIds)            // 设置视图字段ID列表
    .viewFilterIds(viewFilterIds)          // 设置视图过滤器ID列表
    .schemaFieldIds(schemaFieldIds)        // 设置主题字段ID列表
    .build();

// 获取多路召回器
MultiChannelFieldRecaller recaller = fieldRecallerFactory.getMultiChannelRecaller();

// 获取最佳匹配的字段ID
String fieldId = recaller.getBestMatchFieldId(context);

// 获取前N个最佳匹配结果
List<FieldMatchResult> bestMatches = recaller.getBestMatches(context, 3);

// 获取所有匹配结果
List<FieldMatchResult> allMatches = recaller.multiChannelRecall(context);
```

### 基于过滤条件的用法

```java
// 从过滤条件创建上下文
FilterInfo filterInfo = new FilterInfo();
filterInfo.setField("销售额");
filterInfo.setValue("1000000");

MatchContext context = MatchContextBuilder.fromFilter(filterInfo, schemaId, userIdentity)
    .viewFieldIds(viewFieldIds)
    .viewFilterIds(viewFilterIds)
    .schemaFieldIds(schemaFieldIds)
    .build();

// 获取最佳匹配
String fieldId = recaller.getBestMatchFieldId(context);
```

### 处理多个过滤条件

```java
// 创建处理器实例
FieldIdMultiRecallProcessor processor = new FieldIdMultiRecallProcessor(fieldRecallerFactory);

// 处理过滤条件
processor.process(filterProcessorContext);

// 获取结果映射
Map<String, String> fieldIdMapping = filterProcessorContext.getFieldIdMapping();
```

## 排序机制

匹配结果会基于多种因子进行排序：

1. **召回类型权重**：精确匹配 > 值匹配 > 向量匹配
2. **召回范围权重**：候选范围越小权重越高
3. **匹配置信度**：不同召回方式的匹配分数
4. **使用频率**：字段使用频率作为附加因子
5. **总分公式**：`类型权重 * 范围权重 * 置信度 + 使用频率因子`

## 扩展性设计

1. **新增召回器**：实现`FieldRecaller`接口，添加新的召回方式
2. **新增召回范围**：在`RecallScope`枚举中添加新的范围定义
3. **调整排序因子**：修改`FieldMatchResult.calculateTotalScore()`方法
4. **自定义并行策略**：修改线程池配置或超时时间

## 性能优化

1. **并行召回**：所有9种组合同时并行执行，提高响应速度
2. **超时控制**：设置合理的超时时间，避免某个慢查询拖慢整体
3. **缓存机制**：常用字段匹配结果可以缓存
4. **候选范围预筛选**：通过预筛选减少候选字段集合
5. **提前终止**：对于一些场景，可以在获取到高质量结果后提前返回 