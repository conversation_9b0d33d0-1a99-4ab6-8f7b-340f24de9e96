package com.fxiaoke.chatbi.action.impl.insight.generator;

import com.fxiaoke.chatbi.action.impl.chart.handler.ChartHandler;
import com.fxiaoke.chatbi.action.impl.chart.handler.ChartHandlerRegistry;
import com.fxiaoke.chatbi.action.impl.chart.handler.impl.DefaultChartHandler;
import com.fxiaoke.chatbi.common.model.action.input.InsightInput;
import com.fxiaoke.chatbi.common.model.action.output.InsightOutput;
import com.fxiaoke.chatbi.common.model.action.ActionContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 数据洞察生成器
 * 负责生成图表数据洞察内容
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataInsightGenerator {

    private final ChartHandlerRegistry handlerRegistry;
    private final DefaultChartHandler defaultChartHandler;

    /**
     * 生成数据洞察
     *
     * @param input   输入参数
     * @param context 执行上下文
     * @return 数据洞察结果
     */
    public InsightOutput generate(InsightInput input, ActionContext context) {
        // 查找对应的图表处理器
        String viewId = null;

        // 从查询结果中获取图表ID
        if (input.getQueryOutput() != null && input.getQueryOutput().getQueryArg() != null) {
            viewId = input.getQueryOutput().getQueryArg().getViewId();
        }

        // 如果找到特定处理器，使用它处理洞察
        if (viewId != null) {
            ChartHandler handler = handlerRegistry.getHandler(viewId);
            if (handler != null) {
                log.info("找到图表处理器，使用专用处理器处理洞察: viewId={}", viewId);
                return handler.handleInsight(input, context);
            }
        }

        // 否则使用默认处理器
        log.info("未找到专用图表处理器，使用默认处理器处理洞察");
        return defaultChartHandler.handleInsight(input, context);
    }
}
