package com.fxiaoke.chatbi.action.impl.queryDSL.generator.recall;

import com.fxiaoke.common.MapUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import com.fxiaoke.chatbi.common.model.intent.FilterInfo;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 精确匹配召回器
 * 通过字段名精确匹配字段ID
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ExactMatchRecaller extends AbstractFieldRecaller {

    @Override
    public RecallType getRecallType() {
        return RecallType.EXACT_MATCH;
    }
    
    @Override
    public List<FieldMatchResult> recall(MatchContext context, RecallScope scope) {
        // 仅在主题字段范围下做特殊处理
        if (scope == RecallScope.SCHEMA_FIELD) {
            FilterInfo filterInfo = context.getFilterInfo();
            String actionDateFieldId = context.getActionDateFieldId();
            if (filterInfo != null
                    && "DATE".equalsIgnoreCase(filterInfo.getFieldType())
                    && Boolean.TRUE.equals(filterInfo.getUserSpecified())) {
                if (actionDateFieldId != null) {
                    FieldMatchResult result = createMatchResult(
                            actionDateFieldId, 1.0f, getRecallType(), scope, "action_date", 1, 0);
                    log.info("主题字段范围下，日期类型且未指定字段，直接返回action_date字段ID: {}", actionDateFieldId);
                    return Collections.singletonList(result);
                } else {
                    log.warn("主题字段范围下，日期类型且未指定字段，但action_date字段ID为null，无法返回结果");
                    return Collections.emptyList();
                }
            }
        }
        if (!isApplicable(context) || !hasCandidates(context, scope)) {
            return Collections.emptyList();
        }
        
        String fieldName = context.getFieldName();
        Map<String, String> candidateNam2IdeMap = getCandidateNam2IdeMap(context, scope);

        if (MapUtils.isNullOrEmpty(candidateNam2IdeMap)) {
            return Collections.emptyList();
        }

        // 精确匹配：只有完全相等时才视为匹配（忽略大小写）
        String fieldId = candidateNam2IdeMap.get(fieldName);

        if (StringUtils.isEmpty(fieldId)) {
            return Collections.emptyList();
        }
        
        FieldMatchResult result = createMatchResult(
                fieldId, 1.0f, getRecallType(), scope, fieldName, 1, 0);
        
        log.info("精确匹配成功:  范围：{}, {} -> {}", scope, fieldName, fieldId);
        return Collections.singletonList(result);
    }
} 