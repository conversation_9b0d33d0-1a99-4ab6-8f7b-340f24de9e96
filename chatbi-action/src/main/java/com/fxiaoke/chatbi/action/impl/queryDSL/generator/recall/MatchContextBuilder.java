package com.fxiaoke.chatbi.action.impl.queryDSL.generator.recall;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.intent.FilterInfo;
import com.fxiaoke.chatbi.common.model.reasoning.ReasoningCollector;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * 匹配上下文构建器
 */
@Slf4j
@RequiredArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class MatchContextBuilder {
    private final MatchContext.MatchContextBuilder builder;
    
    /**
     * 创建新的构建器实例
     */
    public static MatchContextBuilder builder() {
        return new MatchContextBuilder(MatchContext.builder());
    }

    /**
     * 设置字段名
     */
    public MatchContextBuilder fieldName(String fieldName) {
        builder.fieldName(fieldName);
        return this;
    }
    
    /**
     * 设置字段值列表
     */
    public MatchContextBuilder fieldValues(List<String> fieldValues) {
        builder.fieldValues(fieldValues);
        return this;
    }
    
    /**
     * 设置主题ID
     */
    public MatchContextBuilder schemaId(String schemaId) {
        builder.schemaId(schemaId);
        return this;
    }
    
    /**
     * 设置用户身份
     */
    public MatchContextBuilder userIdentity(UserIdentity userIdentity) {
        builder.userIdentity(userIdentity);
        return this;
    }
    
    /**
     * 设置过滤条件信息
     */
    public MatchContextBuilder filterInfo(FilterInfo filterInfo) {
        builder.filterInfo(filterInfo);
        return this;
    }
    
    /**
     * 设置视图字段ID列表
     */
    public MatchContextBuilder viewFieldIds(List<String> viewFieldIds) {
        builder.viewFieldIds(viewFieldIds);
        return this;
    }
    
    /**
     * 设置视图过滤器ID列表
     */
    public MatchContextBuilder viewFilterIds(List<String> viewFilterIds) {
        builder.viewFilterIds(viewFilterIds);
        return this;
    }
    
    /**
     * 设置主题字段ID列表
     */
    public MatchContextBuilder schemaFieldIds(List<String> schemaFieldIds) {
        builder.schemaFieldIds(schemaFieldIds);
        return this;
    }

    /**
     * 设置日期字段ID列表
     */
    public MatchContextBuilder actionDateFieldId(String actionDateFieldId) {
        builder.actionDateFieldId(actionDateFieldId);
        return this;
    }
    
    /**
     * 设置推理收集器
     */
    public MatchContextBuilder reasoningCollector(ReasoningCollector reasoningCollector) {
        builder.reasoningCollector(reasoningCollector);
        return this;
    }

    /**
     * 设置视图字段ID列表
     */
    public MatchContextBuilder viewFieldName2IdMap(Map<String, String> viewFieldName2IdMap) {
        builder.viewFieldName2IdMap(viewFieldName2IdMap);
        return this;
    }

    /**
     * 设置视图过滤器ID列表
     */
    public MatchContextBuilder viewFilterName2IdMap(Map<String, String> viewFilterName2IdMap) {
        builder.viewFilterName2IdMap(viewFilterName2IdMap);
        return this;
    }

    /**
     * 设置主题字段ID列表
     */
    public MatchContextBuilder schemaFieldName2IdMap(Map<String, String> schemaFieldName2IdMap) {
        builder.schemaFieldName2IdMap(schemaFieldName2IdMap);
        return this;
    }
    /**
     * 构建最终的匹配上下文
     */
    public MatchContext build() {
        return builder.build();
    }
} 