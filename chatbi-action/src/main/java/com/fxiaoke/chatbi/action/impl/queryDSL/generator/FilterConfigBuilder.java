package com.fxiaoke.chatbi.action.impl.queryDSL.generator;

import com.fxiaoke.chatbi.common.model.dto.FilterConfig;
import com.fxiaoke.chatbi.common.model.intent.ExtractedInfo;
import com.fxiaoke.chatbi.common.model.intent.FilterInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 过滤配置构建器
 * 负责将处理后的过滤条件转换为DSL过滤配置
 */
@Slf4j
@RequiredArgsConstructor
class FilterConfigBuilder {
    private final FilterProcessorContext context;

    /**
     * 构建过滤配置列表
     * 
     * @return 过滤配置列表
     */
    public List<FilterConfig> buildFilterConfigs() {
        // 提取上下文中的处理结果
        Map<String, String> fieldIdMapping = context.getFieldIdMapping();
        Map<String, String> operatorMapping = context.getOperatorMapping();
        String dateRangeMapping = context.getDateRangeMapping();
        List<FilterInfo> filters = context.getFilters();
        ExtractedInfo info = context.getInfo();
        
        // 验证必要数据是否存在
        if (fieldIdMapping == null || CollectionUtils.isEmpty(filters)) {
            log.warn("缺少必要的过滤条件数据，无法构建过滤配置");
            return Collections.emptyList();
        }
        
        // 转换过滤条件为过滤配置
        return filters.stream()
                .filter(filter -> StringUtils.isNotBlank(filter.getField()))
                .map(filter -> buildSingleFilterConfig(
                        filter, 
                        fieldIdMapping, 
                        operatorMapping, 
                        dateRangeMapping, 
                        info))
                .filter(config -> config != null && StringUtils.isNotBlank(config.getFieldId()))
                .collect(Collectors.toList());
    }
    
    /**
     * 构建单个过滤配置
     */
    private FilterConfig buildSingleFilterConfig(
            FilterInfo filter, 
            Map<String, String> fieldIdMapping,
            Map<String, String> operatorMapping,
            String dateRangeMapping,
            ExtractedInfo info) {
        
        // 获取匹配的字段ID
        String fieldId = fieldIdMapping.get(filter.getField());
        if (StringUtils.isBlank(fieldId)) {
            log.warn("字段未匹配到ID: {}", filter.getField());
            return null;
        }
        
        // 初始化过滤配置
        FilterConfig config = FilterConfig.builder()
                .fieldId(fieldId)
                .fieldName(filter.getField())
                .operator(operatorMapping != null ?
                        operatorMapping.getOrDefault(filter.getOperator(), filter.getOperator()) :
                        filter.getOperator())
                .build();
        
        // 设置过滤值
        if (CollectionUtils.isNotEmpty(filter.getValues())) {
            config.setValue1(filter.getValues().get(0));
            if (filter.getValues().size() > 1) {
                config.setValue2(filter.getValues().get(1));
            }
        }
        
        // 日期类型特殊处理
        if ("DATE".equalsIgnoreCase(filter.getFieldType())) {
            config.setDateRange(StringUtils.isNotBlank(dateRangeMapping) ?
                    dateRangeMapping :
                    (info != null ? info.getTimeRange() : null));
        }
        
        log.debug("构建过滤配置: {} {} [{}, {}] 日期范围={}",
                fieldId, config.getOperator(), config.getValue1(),
                config.getValue2(), config.getDateRange());
        
        return config;
    }
}
