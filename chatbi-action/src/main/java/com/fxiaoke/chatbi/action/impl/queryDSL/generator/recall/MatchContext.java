package com.fxiaoke.chatbi.action.impl.queryDSL.generator.recall;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.intent.FilterInfo;
import com.fxiaoke.chatbi.common.model.reasoning.ReasoningCollector;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 字段匹配上下文
 * 包含匹配过程中所需的全部上下文信息
 */
@Data
@Builder
public class MatchContext {
    
    /**
     * 要匹配的字段名
     */
    private String fieldName;
    
    /**
     * 字段对应的值列表
     */
    private List<String> fieldValues;
    
    /**
     * 主题ID
     */
    private String schemaId;
    
    /**
     * 用户身份
     */
    private UserIdentity userIdentity;
    
    /**
     * 原始过滤条件信息
     */
    private FilterInfo filterInfo;
    
    /**
     * 视图字段ID列表
     */
    private List<String> viewFieldIds;
    
    /**
     * 视图过滤器ID列表
     */
    private List<String> viewFilterIds;
    
    /**
     * 主题字段ID列表
     */
    private List<String> schemaFieldIds;
    
    /**
     * 特殊字段ID：时间字段
     */
    private String actionDateFieldId;
    
    /**
     * 按召回类型分组的结果
     * 用于收集各种召回方式的匹配结果
     * key: 召回类型, value: 该类型下的匹配结果列表
     */
    private Map<RecallType, List<FieldMatchResult>> resultsByType;
    
    /**
     * 推理收集器
     * 用于收集匹配过程中的推理信息
     */
    private ReasoningCollector reasoningCollector;

    /**
     * 视图字段Name->ID列表
     */
    private Map<String, String> viewFieldName2IdMap;

    /**
     * 视图过滤器Name->ID列表
     */
    private Map<String, String> viewFilterName2IdMap;

    /**
     * 主题字段Name->ID列表
     */
    private Map<String, String> schemaFieldName2IdMap;
} 