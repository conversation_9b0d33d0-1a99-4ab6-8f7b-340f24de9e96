package com.fxiaoke.chatbi.action.impl.queryDSL.generator;

import com.facishare.bi.metadata.context.dto.ads.StatView;
import com.fxiaoke.chatbi.action.impl.queryDSL.generator.recall.FieldIdMultiRecallProcessor;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.action.input.QueryDSLInput;
import com.fxiaoke.chatbi.common.model.dto.ChartQueryDSL;
import com.fxiaoke.chatbi.common.model.dto.FilterConfig;
import com.fxiaoke.chatbi.common.model.intent.ExtractedInfo;
import com.fxiaoke.chatbi.common.model.intent.FilterInfo;
import com.fxiaoke.chatbi.common.model.reasoning.ReasoningCollector;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 过滤处理器管理类
 * 使用责任链模式处理过滤条件
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FilterProcessorManager {

    // 注入所有处理器
    private final OperatorProcessor operatorProcessor;
    private final FieldIdMultiRecallProcessor fieldIdMultiRecallProcessor;
    private final DateRangeProcessor dateRangeProcessor;

    /**
     * 处理过滤条件
     * 依次应用所有过滤器处理器
     */
    public void processFilters(ChartQueryDSL dsl, QueryDSLInput input, StatView statView, UserIdentity userIdentity) {
        processFilters(dsl, input, statView, userIdentity, null);
    }
    
    /**
     * 处理过滤条件
     * 依次应用所有过滤器处理器，并收集处理过程中的推理信息
     * 
     * @param dsl 查询DSL
     * @param input 输入参数
     * @param statView 统计视图
     * @param userIdentity 用户身份
     * @param reasoningCollector 推理收集器
     */
    public void processFilters(ChartQueryDSL dsl, QueryDSLInput input, StatView statView, 
                              UserIdentity userIdentity, ReasoningCollector reasoningCollector) {
        // 从意图中获取过滤条件
        ExtractedInfo extractedInfo = input.getUserIntent() != null 
                ? input.getUserIntent().getExtractedInfo() : null;
                
        List<FilterInfo> filters = extractedInfo != null 
                ? extractedInfo.getFilters() : new ArrayList<>();
                
        if (CollectionUtils.isEmpty(filters)) {
            log.info("无过滤条件需要处理");
            return;
        }
        
        log.info("开始处理{}个过滤条件", filters.size());
        
        // 创建过滤器处理上下文
        FilterProcessorContext context = new FilterProcessorContext(
                extractedInfo, filters, statView, userIdentity);
        
        // 设置推理收集器
        context.setReasoningCollector(reasoningCollector);
                
        // 执行所有处理器
        executeProcessors(context);
        
        // 将处理结果转换为过滤配置并添加到DSL
        List<FilterConfig> filterConfigs = new FilterConfigBuilder(context).buildFilterConfigs();
        if (CollectionUtils.isNotEmpty(filterConfigs)) {
            dsl.setFilters(filterConfigs);
            log.info("过滤条件处理完成，生成了{}个过滤配置", filterConfigs.size());
        }
    }
    
    /**
     * 执行所有处理器
     */
    private void executeProcessors(FilterProcessorContext context) {
        // 创建处理器列表
        List<FilterProcessor> processors = new ArrayList<>();
        processors.add(operatorProcessor);
        processors.add(fieldIdMultiRecallProcessor);
        processors.add(dateRangeProcessor);
        
        // 顺序执行所有处理器
        processors.stream()
            .filter(Objects::nonNull)
            .forEach(processor -> {
                try {
                    log.debug("执行处理器: {}", processor.getClass().getSimpleName());
                    processor.process(context);
                } catch (Exception e) {
                    log.error("处理器执行失败: {}", processor.getClass().getSimpleName(), e);
                }
            });
    }
}
