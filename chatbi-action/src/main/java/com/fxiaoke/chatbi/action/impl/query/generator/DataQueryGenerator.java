package com.fxiaoke.chatbi.action.impl.query.generator;

import com.alibaba.fastjson2.JSON;
import com.fxiaoke.chatbi.action.impl.chart.handler.ChartHandler;
import com.fxiaoke.chatbi.action.impl.chart.handler.ChartHandlerRegistry;
import com.fxiaoke.chatbi.action.impl.chart.handler.impl.DefaultChartHandler;
import com.fxiaoke.chatbi.common.model.action.input.DataQueryInput;
import com.fxiaoke.chatbi.common.model.action.output.DataQueryOutput;
import com.fxiaoke.chatbi.common.model.action.ActionContext;
import com.fxiaoke.chatbi.common.exception.ActionException;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.dto.ChartQueryDSL;
import com.fxiaoke.chatbi.integration.utils.HttpHeaderBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 数据查询生成器
 * 根据图表匹配结果执行数据查询
 * 使用图表处理器模式处理不同类型的图表
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataQueryGenerator {

    private final ChartHandlerRegistry handlerRegistry;
    private final DefaultChartHandler defaultChartHandler;

    /**
     * 执行数据查询
     *
     * @param input   输入参数
     * @param context 执行上下文
     * @return 查询结果
     * @throws ActionException 执行异常
     */
    public DataQueryOutput generate(DataQueryInput input, ActionContext context) throws ActionException {
        // 记录入参并获取用户身份
        ChartQueryDSL queryArg = input.getQueryArg();
        UserIdentity userIdentity = context.getUserIdentity();
        log.info("开始数据查询，LLM匹配DSL结果: {}", JSON.toJSONString(queryArg));

        // 构建HTTP头信息
        Map<String, String> headers = HttpHeaderBuilder.constructHttpHeader(userIdentity);

        // 查找对应的图表处理器
        ChartHandler handler = handlerRegistry.getHandler(queryArg.getViewId());

        // 如果找到特定处理器，使用它处理查询
        if (handler != null) {
            log.info("找到图表处理器，使用专用处理器处理查询: viewId={}", queryArg.getViewId());
            return handler.handleQuery(queryArg, headers, context);
        }

        // 否则使用默认处理器
        log.info("未找到专用图表处理器，使用默认处理器处理查询: viewId={}", queryArg.getViewId());
        return defaultChartHandler.handleQuery(queryArg, headers, context);
    }
}
