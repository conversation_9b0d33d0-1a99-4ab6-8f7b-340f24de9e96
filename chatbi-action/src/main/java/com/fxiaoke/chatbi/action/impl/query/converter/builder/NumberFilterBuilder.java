package com.fxiaoke.chatbi.action.impl.query.converter.builder;

import com.facishare.bi.common.entities.stat.Filter;
import com.fxiaoke.chatbi.action.impl.query.converter.AbstractFilterBuilder;
import com.fxiaoke.chatbi.action.impl.query.converter.ConverterContext;
import com.fxiaoke.chatbi.common.model.dto.FilterConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 数值筛选器构建器
 * 专门处理数值类型的过滤条件
 */
@Slf4j
@Service
public class NumberFilterBuilder extends AbstractFilterBuilder {
  @Override
  public Filter doProcess(FilterConfig filterConfig, ConverterContext context) {
    return processFilter(filterConfig, context);
  }
}