package com.fxiaoke.chatbi.action.impl.queryDSL.generator.recall;

import com.fxiaoke.chatbi.common.model.enums.DataTypeEnum;
import com.fxiaoke.chatbi.knowledge.retrieval.service.FilterValueFieldMatcher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 值匹配召回器
 * 通过字段值查找包含该值的字段
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ValueMatchRecaller extends AbstractFieldRecaller {
    
    private final FilterValueFieldMatcher filterValueFieldMatcher;
    
    @Override
    public RecallType getRecallType() {
        return RecallType.VALUE_MATCH;
    }
    
    @Override
    public boolean isApplicable(MatchContext context) {
        // 值匹配需要有字段值才能进行
        if (!super.isApplicable(context) || 
            CollectionUtils.isEmpty(context.getFieldValues())) {
            return false;
        }
        
        // 如果FilterInfo提供了字段类型，使用DataTypeEnum枚举进行判断
        if (context.getFilterInfo() != null && 
            StringUtils.isNotBlank(context.getFilterInfo().getFieldType())) {
            
            String fieldType = context.getFilterInfo().getFieldType();
            
            // 对于日期类字段，跳过基于值的召回
            if (DataTypeEnum.isDate(fieldType)) {
                log.info("字段类型为DATE，跳过基于值的召回: {}", context.getFieldName());
                return false;
            }
            
            // 对于数值类字段，跳过基于值的召回
            if (DataTypeEnum.isNumber(fieldType)) {
                log.info("字段类型为NUMBER，跳过基于值的召回: {}", context.getFieldName());
                return false;
            }
        }
        
        return true;
    }
    
    @Override
    public List<FieldMatchResult> recall(MatchContext context, RecallScope scope) {
        if (!isApplicable(context) || !hasCandidates(context, scope)) {
            return Collections.emptyList();
        }
        
        String fieldName = context.getFieldName();
        List<String> fieldValues = context.getFieldValues();
        List<String> candidateIds = getCandidateIds(context, scope);
        
        if (CollectionUtils.isEmpty(candidateIds)) {
            log.info("没有候选字段ID可供匹配: scope={}", scope);
            return Collections.emptyList();
        }
        
        // 过滤空值
        List<String> validFieldValues = fieldValues.stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
                
        if (validFieldValues.isEmpty()) {
            log.info("没有有效的字段值用于匹配: scope={}", scope);
            return Collections.emptyList();
        }
        
        // 使用批量值匹配方法处理所有字段值
        Map<String, Float> matchResults;
        try {
            matchResults = filterValueFieldMatcher.matchFieldsByFilterValues(
                    validFieldValues,
                    candidateIds,
                    context.getUserIdentity());
        } catch (Exception e) {
            log.error("调用值匹配服务异常: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
        
        if (matchResults.isEmpty()) {
            log.info("值匹配范围内没有包含字段值的字段: 范围={}, 值列表大小={}", scope, validFieldValues.size());
            return Collections.emptyList();
        }
        
        // 构建结果列表
        List<FieldMatchResult> results = new ArrayList<>();
        
        // 遍历匹配结果
        for (Map.Entry<String, Float> entry : matchResults.entrySet()) {
            String fieldId = entry.getKey();
            float confidence = entry.getValue();
            
            // 创建匹配结果
            FieldMatchResult result = createMatchResult(
                    fieldId,
                    confidence,
                    getRecallType(),
                    scope,
                    fieldName,
                    1, // 默认使用频率为1,
                    0
            );
            results.add(result);
            log.debug("值匹配成功: {} -> {} (置信度:{})", fieldName, fieldId, confidence);
        }
        
        if (!results.isEmpty()) {
            log.info("值匹配成功: 范围：{}, 共找到{}个匹配字段", scope, results.size());
        }
        
        return results;
    }
} 