package com.fxiaoke.chatbi.action.impl.queryDSL.generator.recall;

/**
 * 召回类型枚举
 * 表示不同的字段ID匹配方式
 */
public enum RecallType {
    // 精确匹配 - 通过字段名精确匹配
    EXACT_MATCH(1.0f, "精确匹配"),
    
    // 值匹配 - 通过字段值匹配对应的字段
    VALUE_MATCH(0.8f, "值匹配"),
    
    // 向量匹配 - 通过语义向量相似度匹配
    VECTOR_MATCH(0.6f, "向量匹配");
    
    // 默认权重 - 用于不同召回类型的结果排序
    private final float defaultWeight;
    // 描述
    private final String description;
    
    RecallType(float defaultWeight, String description) {
        this.defaultWeight = defaultWeight;
        this.description = description;
    }
    
    public float getDefaultWeight() {
        return defaultWeight;
    }
    
    public String getDescription() {
        return description;
    }
} 