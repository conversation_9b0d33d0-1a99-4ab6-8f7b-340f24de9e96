package com.fxiaoke.chatbi.action.impl.query;

import com.fxiaoke.chatbi.action.core.AbstractAction;
import com.fxiaoke.chatbi.common.model.action.input.DataQueryInput;
import com.fxiaoke.chatbi.common.model.action.output.DataQueryOutput;
import com.fxiaoke.chatbi.common.model.action.ActionType;
import com.fxiaoke.chatbi.action.impl.query.generator.DataQueryGenerator;
import com.fxiaoke.chatbi.common.model.action.ActionContext;
import com.fxiaoke.chatbi.common.exception.ActionException;
import com.fxiaoke.chatbi.common.model.reasoning.ReasoningData;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;

/**
 * 数据查询Action
 * 根据图表匹配的结果执行查询获取图表数据
 */
@Component
@RequiredArgsConstructor
public class DataQueryAction extends AbstractAction<DataQueryInput, DataQueryOutput> {

  private final DataQueryGenerator dataQueryGenerator;

  @Override
  public ActionType getType() {
    return ActionType.DATA_QUERY;
  }

  @Override
  protected DataQueryOutput doExecute(DataQueryInput input, ActionContext context) throws ActionException {
    // 直接调用dataQueryGenerator进行数据查询
    return dataQueryGenerator.generate(input, context);
  }

}