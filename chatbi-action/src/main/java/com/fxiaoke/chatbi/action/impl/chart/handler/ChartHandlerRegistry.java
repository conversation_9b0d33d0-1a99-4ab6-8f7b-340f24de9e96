package com.fxiaoke.chatbi.action.impl.chart.handler;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 图表处理器注册中心
 * 负责注册和管理所有图表处理器
 */
@Component
public class ChartHandlerRegistry {
    private final Map<String, ChartHandler> handlerMap = new HashMap<>();

    @Autowired
    private List<ChartHandler> handlers;

    /**
     * 初始化注册所有处理器
     */
    @PostConstruct
    public void init() {
        for (ChartHandler handler : handlers) {
            for (String viewId : handler.getSupportedViewIds()) {
                handlerMap.put(viewId, handler);
            }
        }
    }

    /**
     * 获取指定图表ID对应的处理器
     * 
     * @param viewId 图表ID
     * @return 对应的处理器，如果不存在则返回null
     */
    public ChartHandler getHandler(String viewId) {
        return handlerMap.get(viewId);
    }

}