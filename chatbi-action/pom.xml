<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fxiaoke</groupId>
        <artifactId>fs-bi-agent</artifactId>
        <version>9.5.0-SNAPSHOT</version>
    </parent>

    <artifactId>chatbi-action</artifactId>
    <packaging>jar</packaging>
    <name>chatbi-action</name>
    <description>ChatBI 执行系统模块，执行计划和处理结果</description>

    <dependencies>
        <!-- 模块依赖 -->
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>chatbi-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>chatbi-prompts</artifactId>
        </dependency>
        
        <!-- Spring依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        
        <!-- BI相关依赖 -->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-bi-common-entities</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-bi-metadata-context</artifactId>
        </dependency>
        
        <!-- 数据库相关 -->
        <dependency>
            <groupId>com.clickhouse</groupId>
            <artifactId>clickhouse-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.clickhouse</groupId>
            <artifactId>clickhouse-data</artifactId>
        </dependency>
        <dependency>
            <groupId>com.clickhouse</groupId>
            <artifactId>clickhouse-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>jdbc-support</artifactId>
        </dependency>
        
        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
      <dependency>
        <groupId>com.fxiaoke</groupId>
        <artifactId>chatbi-integration</artifactId>
      </dependency>
      <dependency>
        <groupId>com.fxiaoke</groupId>
        <artifactId>chatbi-knowledge</artifactId>
      </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project> 