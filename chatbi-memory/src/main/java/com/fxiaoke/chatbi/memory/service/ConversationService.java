package com.fxiaoke.chatbi.memory.service;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.intent.UserIntent;
import com.fxiaoke.chatbi.common.model.memory.ConversationContext;

import java.util.List;
import java.util.Map;

/**
 * 会话服务接口
 * 定义会话管理的核心方法，包括对话上下文管理和工作记忆更新
 */
public interface ConversationService {
    
    /**
     * 创建新的会话上下文
     *
     * @param userIdentity 用户身份信息
     * @param sessionId
     * @return 新的会话上下文
     */
    ConversationContext createContext(UserIdentity userIdentity, String sessionId);
    
    /**
     * 获取会话上下文
     * 如果不存在则创建新的
     *
     * @param sessionId 会话ID
     * @param userIdentity 用户身份信息
     * @return 会话上下文
     */
    ConversationContext getOrCreateContext(String sessionId, UserIdentity userIdentity);
    
    /**
     * 更新会话上下文
     *
     * @param context 会话上下文
     */
    void updateContext(ConversationContext context);
    
    /**
     * 获取用户的所有会话
     *
     * @param userIdentity 用户身份信息
     * @return 用户的所有会话上下文
     */
    List<ConversationContext> findByUser(UserIdentity userIdentity);
    
    /**
     * 根据会话ID获取会话上下文
     *
     * @param sessionId 会话ID
     * @return 会话上下文，如果不存在则返回null
     */
    ConversationContext findById(String sessionId);
    
    /**
     * 根据计划ID查找对应的会话上下文
     *
     * @param planId 计划ID
     * @return 会话上下文，如果不存在则返回null
     */
    ConversationContext findByPlanId(String planId);
    
    /**
     * 更新会话上下文的意图信息
     * 综合更新最后意图、计划ID和意图相关的工作记忆
     *
     * @param context 会话上下文
     * @param intent 用户意图
     * @param planId 计划ID
     */
    void updateIntentInfo(ConversationContext context, UserIntent intent, String planId);
    
    /**
     * 更新会话的工作记忆
     * 从用户意图中提取关键信息并添加到工作记忆
     *
     * @param context 会话上下文
     * @param intent 用户意图
     */
    void updateWorkingMemoryFromIntent(ConversationContext context, UserIntent intent);
    
    /**
     * 更新会话的工作记忆
     * 添加图表相关信息到工作记忆
     *
     * @param context 会话上下文
     * @param chartName 图表名称
     * @param chartType 图表类型
     */
    void updateWorkingMemoryFromChartResult(ConversationContext context, String chartName, String chartType);
    
    /**
     * 通过会话ID，从查询结果更新会话工作记忆
     *
     * @param sessionId 会话ID
     * @param chartName 图表名称
     * @param chartType 图表类型
     * @return 更新后的会话上下文，如果会话不存在则返回null
     */
    ConversationContext updateSessionWithChartResult(String sessionId, String chartName, String chartType);
    
    /**
     * 更新工作记忆
     * 直接添加键值对到会话的工作记忆
     *
     * @param context 会话上下文
     * @param key 键
     * @param value 值
     */
    void addToWorkingMemory(ConversationContext context, String key, Object value);
    
    /**
     * 批量更新工作记忆
     * 
     * @param context 会话上下文
     * @param memoryEntries 要添加的记忆条目
     */
    void addToWorkingMemory(ConversationContext context, Map<String, Object> memoryEntries);
}