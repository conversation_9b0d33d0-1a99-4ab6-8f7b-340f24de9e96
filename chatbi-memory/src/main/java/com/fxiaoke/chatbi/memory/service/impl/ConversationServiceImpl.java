package com.fxiaoke.chatbi.memory.service.impl;

import com.alibaba.fastjson2.JSON;
import com.fxiaoke.chatbi.common.cache.ChatRedisService;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.intent.UserIntent;
import com.fxiaoke.chatbi.common.model.memory.ConversationContext;
import com.fxiaoke.chatbi.memory.service.ConversationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 会话服务实现类
 * 使用Redis作为存储，管理会话上下文
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ConversationServiceImpl implements ConversationService {

    private final ChatRedisService chatRedisService;

    /**
     * Redis键前缀 - 会话上下文
     */
    private static final String CONTEXT_KEY_PREFIX = "chatbi:context:";
    
    /**
     * Redis键前缀 - 用户会话列表
     */
    private static final String USER_SESSIONS_KEY_PREFIX = "chatbi:user:sessions:";
    
    /**
     * Redis键前缀 - 计划ID到会话ID的映射
     */
    private static final String PLAN_TO_SESSION_KEY_PREFIX = "chatbi:plan:session:";
    
    /**
     * 会话过期时间（秒）- 7天
     */
    private static final int CONTEXT_EXPIRE_SECONDS = 7 * 24 * 3600;

    @Override
    public ConversationContext createContext(UserIdentity userIdentity, String sessionId) {
        ConversationContext context = new ConversationContext();
        context.setSessionId(sessionId);
        context.setUserIdentity(userIdentity);
        
        // 保存会话上下文
        saveContext(context);
        
        // 添加到用户的会话列表
        addToUserSessions(userIdentity, context.getSessionId());
        
        log.info("创建新的会话上下文: sessionId={}, userId={}", context.getSessionId(), userIdentity.getUserId());
        return context;
    }

    @Override
    public ConversationContext getOrCreateContext(String sessionId, UserIdentity userIdentity) {
        ConversationContext context = findById(sessionId);
        if (context != null) {
            return context;
        }
        return createContext(userIdentity, sessionId);
    }

    @Override
    public void updateContext(ConversationContext context) {
        context.setUpdateTime(System.currentTimeMillis());
        saveContext(context);
        log.debug("更新会话上下文: sessionId={}", context.getSessionId());
    }

    @Override
    public List<ConversationContext> findByUser(UserIdentity userIdentity) {
        List<ConversationContext> contexts = new ArrayList<>();
        List<String> sessionIds = getUserSessions(userIdentity);
        
        for (String sessionId : sessionIds) {
            ConversationContext context = findById(sessionId);
            if (context != null) {
                contexts.add(context);
            }
        }
        
        log.debug("获取用户会话列表: userId={}, count={}", userIdentity.getUserId(), contexts.size());
        return contexts;
    }

    @Override
    public ConversationContext findById(String sessionId) {
        String json = chatRedisService.get(CONTEXT_KEY_PREFIX + sessionId);
        if (json == null) {
            return null;
        }
        return JSON.parseObject(json, ConversationContext.class);
    }
    
    @Override
    public ConversationContext findByPlanId(String planId) {
        if (planId == null) {
            return null;
        }
        
        // 从Redis获取计划ID对应的会话ID
        String sessionId = chatRedisService.get(PLAN_TO_SESSION_KEY_PREFIX + planId);
        if (sessionId == null) {
            log.warn("找不到计划对应的会话: planId={}", planId);
            return null;
        }
        
        // 获取会话上下文
        return findById(sessionId);
    }
    
    @Override
    public void updateIntentInfo(ConversationContext context, UserIntent intent, String planId) {
        if (context == null || intent == null) {
            return;
        }
        
        // 1. 更新基本意图信息
        context.setLastIntent(intent.getInstructions());
        context.setLastPlanId(planId);
        
        // 2. 更新工作记忆
        updateWorkingMemoryFromIntent(context, intent);
        
        // 3. 保存计划ID到会话ID的映射
        if (planId != null) {
            savePlanToSessionMapping(planId, context.getSessionId());
        }
        
        log.info("更新会话意图信息: sessionId={}, intentType={}, planId={}", 
                context.getSessionId(), intent.getIntentType(), planId);
    }
    
    @Override
    public void updateWorkingMemoryFromIntent(ConversationContext context, UserIntent intent) {
        if (context == null || intent == null) {
            return;
        }
        
        Map<String, Object> workingMemory = new HashMap<>();
        
        // 从意图中提取关键信息
        if (intent.getExtractedInfo() != null) {
            if (StringUtils.isNotBlank(intent.getExtractedInfo().getAnalysisType())) {
                workingMemory.put("分析类型", intent.getExtractedInfo().getAnalysisType());
            }
            if (CollectionUtils.isNotEmpty(intent.getExtractedInfo().getMeasures())) {
                workingMemory.put("指标", String.join(", ", intent.getExtractedInfo().getMeasures()));
            }
            if (CollectionUtils.isNotEmpty(intent.getExtractedInfo().getDimensions())) {
                workingMemory.put("维度", String.join(", ", intent.getExtractedInfo().getDimensions()));
            }
            if (StringUtils.isNotBlank(intent.getExtractedInfo().getTimeRange())) {
                workingMemory.put("时间范围", intent.getExtractedInfo().getTimeRange());
            }
        }
        
        // 合并已有记忆
        addToWorkingMemory(context, workingMemory);
    }
    
    @Override
    public void updateWorkingMemoryFromChartResult(ConversationContext context, String chartName, String chartType) {
        if (context == null) {
            return;
        }
        
        Map<String, Object> workingMemory = new HashMap<>();
        if (StringUtils.isNotBlank(chartName)) {
            workingMemory.put("图表", chartName);
        }
        if (StringUtils.isNotBlank(chartType)) {
            workingMemory.put("图表类型", chartType);
        }
        
        // 合并已有记忆
        addToWorkingMemory(context, workingMemory);
    }
    
    @Override
    public ConversationContext updateSessionWithChartResult(String sessionId, String chartName, String chartType) {
        // 1. 获取会话
        ConversationContext context = findById(sessionId);
        if (context == null) {
            log.warn("更新会话工作记忆失败: 找不到会话, sessionId={}", sessionId);
            return null;
        }
        
        // 2. 更新工作记忆
        updateWorkingMemoryFromChartResult(context, chartName, chartType);
        
        log.info("更新会话图表结果: sessionId={}, chartName={}, chartType={}", 
                sessionId, chartName, chartType);
        
        return context;
    }
    
    @Override
    public void addToWorkingMemory(ConversationContext context, String key, Object value) {
        if (context == null || key == null) {
            return;
        }
        
        Map<String, Object> memoryEntry = new HashMap<>();
        memoryEntry.put(key, value);
        addToWorkingMemory(context, memoryEntry);
    }
    
    @Override
    public void addToWorkingMemory(ConversationContext context, Map<String, Object> memoryEntries) {
        if (context == null || memoryEntries == null || memoryEntries.isEmpty()) {
            return;
        }
        
        Map<String, Object> existingMemory = context.getWorkingMemory();
        if (existingMemory == null) {
            existingMemory = new HashMap<>();
        }
        
        // 创建新的合并后的记忆Map
        Map<String, Object> mergedMemory = new HashMap<>(existingMemory);
        // 新信息优先级更高，覆盖旧记忆
        mergedMemory.putAll(memoryEntries);
        
        // 更新上下文
        context.setWorkingMemory(mergedMemory);
        updateContext(context);
        
        log.debug("更新工作记忆: sessionId={}, mergedKeys={}", context.getSessionId(), memoryEntries.keySet());
    }

    /**
     * 保存会话上下文到Redis
     */
    private void saveContext(ConversationContext context) {
        String key = CONTEXT_KEY_PREFIX + context.getSessionId();
        chatRedisService.set(key, JSON.toJSONString(context), CONTEXT_EXPIRE_SECONDS);
    }
    
    /**
     * 保存计划ID到会话ID的映射
     */
    private void savePlanToSessionMapping(String planId, String sessionId) {
        if (planId == null || sessionId == null) {
            return;
        }
        
        String key = PLAN_TO_SESSION_KEY_PREFIX + planId;
        chatRedisService.set(key, sessionId, CONTEXT_EXPIRE_SECONDS);
    }
    
    /**
     * 获取用户的会话ID列表
     */
    private List<String> getUserSessions(UserIdentity userIdentity) {
        String key = USER_SESSIONS_KEY_PREFIX + userIdentity.getUserId();
        String json = chatRedisService.get(key);
        if (json == null) {
            return new ArrayList<>();
        }
        return JSON.parseArray(json, String.class);
    }
    
    /**
     * 添加会话ID到用户的会话列表
     */
    private void addToUserSessions(UserIdentity userIdentity, String sessionId) {
        String key = USER_SESSIONS_KEY_PREFIX + userIdentity.getUserId();
        List<String> sessions = getUserSessions(userIdentity);
        if (!sessions.contains(sessionId)) {
            sessions.add(sessionId);
            chatRedisService.set(key, JSON.toJSONString(sessions), CONTEXT_EXPIRE_SECONDS);
        }
    }
}