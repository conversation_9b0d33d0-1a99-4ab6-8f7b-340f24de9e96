/**
 * ChatBI提示词模板管理器
 * 用于查看和更新各类提示词模板
 */
document.addEventListener('DOMContentLoaded', function() {
    // 常量定义
    const API_BASE_URL = '/api/v1/prompts';

    // DOM元素
    const templateTabsContainer = document.getElementById('template-tabs-container');
    const templateEditor = document.getElementById('template-editor');
    const templateTitle = document.getElementById('current-template-title');
    const templateDescription = document.getElementById('template-description');
    const templateDemo = document.getElementById('template-demo');
    const templateVars = document.getElementById('template-variables');
    const lastModified = document.getElementById('last-modified');
    const saveButtons = document.querySelectorAll('.save-template');
    const refreshButton = document.querySelector('.refresh-template');
    const confirmSaveButton = document.getElementById('confirm-save');

    // 当前选中的模板类型
    let currentTemplate = 'INTENT_RECOGNITION';
    
    // 初始化CodeMirror编辑器
    const editor = CodeMirror.fromTextArea(templateEditor, {
        mode: 'markdown',
        theme: 'dracula',
        lineNumbers: true,
        lineWrapping: true,
        tabSize: 2,
        placeholder: '请等待模板加载...'
    });

    // 加载模板类型列表
    loadTemplateTypes();

    // 绑定事件监听器
    
    // 保存按钮
    saveButtons.forEach(button => {
        button.addEventListener('click', function() {
            // 显示确认对话框
            const confirmModal = new bootstrap.Modal(document.getElementById('confirm-modal'));
            confirmModal.show();
        });
    });
    
    // 确认保存
    confirmSaveButton.addEventListener('click', function() {
        saveTemplate(currentTemplate, editor.getValue());
        bootstrap.Modal.getInstance(document.getElementById('confirm-modal')).hide();
    });
    
    // 刷新按钮
    refreshButton.addEventListener('click', function() {
        if (editor.isClean() === false) {
            if (confirm('当前有未保存的更改，确定要重新加载吗？')) {
                loadTemplate(currentTemplate);
            }
        } else {
            loadTemplate(currentTemplate);
        }
    });

    /**
     * 加载模板类型列表
     */
    function loadTemplateTypes() {
        fetch(`${API_BASE_URL}/types`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误 ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.code === 200 && data.data) {
                    // 添加新的模板类型标签页
                    data.data.forEach((type, index) => {
                        const tabItem = document.createElement('li');
                        tabItem.className = 'nav-item';
                        tabItem.role = 'presentation';
                        
                        const tabLink = document.createElement('a');
                        tabLink.className = `nav-link ${type.name === currentTemplate ? 'active' : ''}`;
                        tabLink.id = `tab-${type.name}`;
                        tabLink.dataset.template = type.name;
                        tabLink.href = '#';
                        tabLink.role = 'tab';
                        tabLink.setAttribute('aria-selected', type.name === currentTemplate ? 'true' : 'false');
                        
                        // 标签页内容
                        tabLink.innerHTML = `
                            <span>${type.description}</span>
                            <span class="badge bg-primary ms-2 badge-template-name">${type.templateName}</span>
                        `;
                        
                        // 添加点击事件
                        tabLink.addEventListener('click', function(event) {
                            event.preventDefault();
                            
                            // 检查是否有未保存的更改
                            if (editor.isClean() === false) {
                                if (confirm('当前模板有未保存的更改，确定要切换吗？')) {
                                    switchTemplate(this);
                                }
                            } else {
                                switchTemplate(this);
                            }
                        });
                        
                        tabItem.appendChild(tabLink);
                        templateTabsContainer.appendChild(tabItem);
                    });
                    
                    // 加载当前选中的模板
                    loadTemplate(currentTemplate);
                } else {
                    throw new Error(data.message || '加载模板类型失败');
                }
            })
            .catch(error => {
                console.error('加载模板类型失败:', error);
                showAlert('加载模板类型失败: ' + error.message, 'danger');
            });
    }

    /**
     * 切换模板
     */
    function switchTemplate(tabElement) {
        // 移除所有标签页的active类
        document.querySelectorAll('#template-tabs-container .nav-link').forEach(tab => {
            tab.classList.remove('active');
            tab.setAttribute('aria-selected', 'false');
        });
        
        // 添加新的active类
        tabElement.classList.add('active');
        tabElement.setAttribute('aria-selected', 'true');
        
        // 获取模板类型
        currentTemplate = tabElement.dataset.template;
        
        // 更新UI
        templateTitle.textContent = tabElement.querySelector('span').textContent;
        
        // 加载新模板
        loadTemplate(currentTemplate);
    }
    
    /**
     * 加载指定类型的模板
     */
    function loadTemplate(templateType) {
        fetch(`${API_BASE_URL}/${templateType.toLowerCase()}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误 ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.code === 200 && data.data) {
                    editor.setValue(data.data);
                    editor.markClean(); // 标记为已保存状态
                    loadTemplateMetadata(templateType);
                    loadTemplateVariables(templateType);
                    lastModified.textContent = `最后更新: ${formatDate(new Date())}`;
                } else if (data.code === 200 && data.data === '') {
                    editor.setValue('');
                    editor.markClean(); // 标记为已保存状态
                    lastModified.textContent = `最后更新: ${formatDate(new Date())}`;
                } else {
                    throw new Error(data.message || '加载模板失败');
                }
            })
            .catch(error => {
                console.error('加载模板失败:', error);
                editor.setValue('');
                showAlert('加载模板失败: ' + error.message, 'danger');
            });
    }

    /**
     * 加载模板元数据
     */
    function loadTemplateMetadata(templateType) {
        fetch(`${API_BASE_URL}/metadata/${templateType.toLowerCase()}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误 ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.code === 200 && data.data) {
                    templateDescription.textContent = data.data.description || '';
                    
                    // 更新示例
                    const demoContent = data.data.demo || '';
                    const demoJsonContentElement = document.getElementById('demo-json-content');
                    if (demoJsonContentElement) {
                        demoJsonContentElement.textContent = demoContent;
                        if (demoContent) {
                            document.getElementById('template-demo').style.display = 'block';
                        } else {
                            document.getElementById('template-demo').style.display = 'none';
                        }
                    }
                }
            })
            .catch(error => {
                console.error('加载模板元数据失败:', error);
                templateDescription.textContent = '';
            });
    }

    /**
     * 加载模板变量
     */
    function loadTemplateVariables(templateType) {
        fetch(`${API_BASE_URL}/variables/${templateType.toLowerCase()}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误 ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.code === 200 && data.data && data.data.length > 0) {
                    // 更新模板变量UI
                    renderTemplateVariables(data.data);
                } else {
                    templateVars.innerHTML = '';
                }
            })
            .catch(error => {
                console.error('加载模板变量失败:', error);
                templateVars.innerHTML = '';
            });
    }

    /**
     * 渲染模板变量
     */
    function renderTemplateVariables(variables) {
        templateVars.innerHTML = '<p class="text-muted small mb-3">这些变量在模板中被引用，通过调用API时传入相应值</p>';
        
        const table = document.createElement('table');
        table.className = 'table table-sm table-bordered var-table';
        
        // 创建表头
        const thead = document.createElement('thead');
        thead.innerHTML = `
            <tr>
                <th style="width: 20%">变量名</th>
                <th style="width: 15%">类型</th>
                <th style="width: 65%">描述</th>
            </tr>
        `;
        table.appendChild(thead);
        
        // 创建表体
        const tbody = document.createElement('tbody');
        variables.forEach(variable => {
            const row = document.createElement('tr');
            
            // 检查是否有示例值
            const hasExample = variable.example && variable.example.trim() !== '';
            
            row.innerHTML = `
                <td>
                    <code>${variable.name}</code>
                    ${variable.required === '必填' ? '<span class="badge bg-danger var-type float-end">必填</span>' : ''}
                </td>
                <td><span class="badge bg-secondary var-type">${variable.type}</span></td>
                <td>
                    <div class="fw-semibold mb-1">${variable.displayName}</div>
                    <div class="small text-muted mb-2">${variable.description}</div>
                    ${hasExample ? 
                        `<div class="example-value">
                            <span class="badge bg-info var-type mb-1">示例</span>
                            <pre class="example-pre mt-1"><code>${escapeHtml(variable.example)}</code></pre>
                        </div>` : 
                        ''}
                </td>
            `;
            tbody.appendChild(row);
        });
        table.appendChild(tbody);
        
        templateVars.appendChild(table);
    }

    /**
     * 转义HTML特殊字符
     */
    function escapeHtml(text) {
        return text
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }
    
    /**
     * 保存模板
     */
    function saveTemplate(templateType, content) {
        const payload = {
            templateType: templateType,
            content: content
        };
        
        fetch(`${API_BASE_URL}/refresh`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(payload)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP错误 ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.code === 200) {
                editor.markClean(); // 标记为已保存状态
                lastModified.textContent = `最后更新: ${formatDate(new Date())}`;
                showAlert('模板已成功保存', 'success');
            } else {
                throw new Error(data.message || '保存模板失败');
            }
        })
        .catch(error => {
            console.error('保存模板失败:', error);
            showAlert('保存模板失败: ' + error.message, 'danger');
        });
    }

    /**
     * 显示通知
     */
    function showAlert(message, type) {
        const alertContainer = document.getElementById('alert-container');
        const alertId = 'alert-' + Date.now();
        
        const alertHtml = `
            <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show alert-floating" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        
        alertContainer.insertAdjacentHTML('beforeend', alertHtml);
        
        // 5秒后自动关闭
        setTimeout(() => {
            const alert = document.getElementById(alertId);
            if (alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, 5000);
    }

    /**
     * 格式化日期
     */
    function formatDate(date) {
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }
}); 