/**
 * * ChatBI向量数据管理
 *  * 用于查看和更新各类向量数据
 */

// 假设你有一个 API 可以获取图表数据
async function fetchChartNames() {
    try {
        const tenantId = localStorage.getItem('param-tenantId') || '82313';
        const response = await fetch(`/api/embedding/chart-embeddings/${tenantId}`); // 替换为实际的 API 路径
        if (!response.ok) {
            throw new Error('网络响应错误');
        }
        const apiResult = await response.json(); // 假设返回的是 ApiResult 对象

        console.log('API 返回的数据:', apiResult); // 添加调试信息

        if (apiResult.code !== 200) { // 假设 200 是成功的状态码
            throw new Error(`错误代码: ${apiResult.code}, 消息: ${apiResult.message}`);
        }

        const chartEmbeddings = apiResult.data; // 提取 List<ChartEmbeddingResponse>

        if (!Array.isArray(chartEmbeddings)) {
            throw new TypeError('返回的数据不是数组');
        }

        populateChartNames(chartEmbeddings);
    } catch (error) {
        console.error('获取图表名称失败:', error);
    }
}

function populateChartNames(chartEmbeddings) {
    const chartNameList = document.getElementById('chart-name-list');
    chartNameList.innerHTML = ''; // 清空现有列表

    chartEmbeddings.forEach(embedding => {
        const listItem = document.createElement('li');
        listItem.textContent = embedding.viewName; // 设置图表名称为 viewName
        listItem.viewId = embedding.viewId; // 存储 viewId
        listItem.onclick = () => {
            // 移除其他图表的 selected 类
            const selectedCharts = document.querySelectorAll('#chart-name-list li.selected');
            selectedCharts.forEach(chart => chart.classList.remove('selected'));

            // 添加 selected 类到当前点击的图表
            listItem.classList.add('selected');

            // 展示图表详细信息
            displayChartDetails(embedding);
        };

        chartNameList.appendChild(listItem); // 添加到列表中
    });
}

function displayChartDetails(embedding) {
    const chartDetailsArea = document.getElementById('chart-details');
    chartDetailsArea.innerHTML = ''; // 清空现有内容

    const title = document.createElement('h3');
    title.textContent = `图表: ${embedding.viewName}`; // 图表标题
    chartDetailsArea.appendChild(title);

    const featuresTitle = document.createElement('h4');
    featuresTitle.textContent = '特征:';
    chartDetailsArea.appendChild(featuresTitle);

    const featuresList = document.createElement('ul');
    embedding.features.forEach(feature => {
        const featureItem = document.createElement('li');
        featureItem.textContent = feature; // 添加特征
        featuresList.appendChild(featureItem);
    });
    chartDetailsArea.appendChild(featuresList);
}

// 新增的生成特征功能
async function generateFeatures(viewId) {
    const chartDetailsArea = document.getElementById('chart-details');

    // 清空下方的特征无序列表
    const existingFeaturesList = chartDetailsArea.querySelector('ul');
    if (existingFeaturesList) {
        existingFeaturesList.innerHTML = ''; // 清空现有特征列表
    }

    // 显示加载指示器
    const loadingIndicator = document.createElement('div');
    loadingIndicator.textContent = '加载中...'; // 可以替换为更复杂的加载动画
    chartDetailsArea.appendChild(loadingIndicator);

    try {
        const tenantId = localStorage.getItem('param-tenantId') || '82313';
        const response = await fetch(`/api/embedding/generate-features/${viewId}?tenantId=${tenantId}`, {
            method: 'GET',
            headers: getRequestHeaders()
        });

        if (!response.ok) {
            throw new Error('网络响应错误');
        }

        const apiResult = await response.json();
        if (apiResult.code !== 200) {
            throw new Error(`错误代码: ${apiResult.code}, 消息: ${apiResult.message}`);
        }

        const features = apiResult.data; // 直接获取 List<String>
        displayGeneratedFeatures(features);
    } catch (error) {
        console.error('生成特征失败:', error);
        alert('生成特征失败，请重试。'); // 提示用户错误信息
    } finally {
        // 移除加载指示器
        if (loadingIndicator.parentNode) {
            chartDetailsArea.removeChild(loadingIndicator);
        }
    }
}

function displayGeneratedFeatures(features) {
    const chartDetailsArea = document.getElementById('chart-details');
    const featuresList = document.createElement('ul');
    features.forEach(feature => {
        const featureItem = document.createElement('li');
        featureItem.textContent = feature; // 添加生成的特征
        featuresList.appendChild(featureItem);
    });
    chartDetailsArea.appendChild(featuresList);
}

// 绑定生成按钮的点击事件
document.getElementById('generate-features-btn').addEventListener('click', () => {
    const selectedChart = document.querySelector('#chart-name-list li.selected'); // 假设选中的图表有一个 'selected' 类
    if (selectedChart) {
        const viewId = selectedChart.viewId; // 假设在 li 中存储了 viewId
        generateFeatures(viewId);
    } else {
        alert('请先选择一个图表。');
    }
});

// 绑定保存特征按钮的点击事件
document.getElementById('save-features-btn').addEventListener('click', () => {
    const selectedChart = document.querySelector('#chart-name-list li.selected'); // 获取选中的图表
    if (selectedChart) {
        const viewId = selectedChart.viewId; // 获取选中的图表的 viewId
        const featuresList = document.querySelector('#chart-details ul'); // 获取特征列表
        const features = Array.from(featuresList.children).map(item => item.textContent); // 获取特征文本内容

        saveFeatures(viewId, features); // 调用保存特征的函数
    } else {
        alert('请先选择一个图表。'); // 提示用户选择图表
    }
});

// 保存特征的函数
async function saveFeatures(viewId, features) {
    try {
        const tenantId = localStorage.getItem('param-tenantId') || '82313';
        const response = await fetch(`/api/embedding/save-features`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...getRequestHeaders()
            },
            body: JSON.stringify({
                viewId: viewId,
                features: features // 传递特征文本内容
            })
        });

        if (!response.ok) {
            throw new Error('网络响应错误');
        }

        const apiResult = await response.json();
        if (apiResult.code !== 200) {
            throw new Error(`错误代码: ${apiResult.code}, 消息: ${apiResult.message}`);
        }

        alert('特征保存成功！'); // 提示用户保存成功
    } catch (error) {
        console.error('保存特征失败:', error);
        alert('保存特征失败，请重试。'); // 提示用户错误信息
    }
}

// 获取请求头
function getRequestHeaders() {
    const tenantId = localStorage.getItem('param-tenantId') || '82313';
    const userId = localStorage.getItem('param-userId') || '1067';
    const locale = localStorage.getItem('param-locale') || 'zh_CN';
    const channel = localStorage.getItem('param-channel') || 'web';

    return {
        'X-Tenant-Id': tenantId,
        'X-Admin-Target-Tenant-Id': tenantId,
        'X-Admin-Id': userId,
        'X-User-Id': userId,
        'X-Locale': locale,
        'X-Channel': channel,
        'Content-Type': 'application/json'
    };
}

// 绑定查询按钮的点击事件
document.getElementById('query-tenant-btn').addEventListener('click', () => {
    const tenantIdInput = document.getElementById('tenant-id-input');
    const tenantId = tenantIdInput.value.trim();

    if (tenantId) {
        localStorage.setItem('param-tenantId', tenantId);
        fetchChartNames();
    } else {
        alert('请输入有效的租户ID');
    }
});

document.addEventListener('DOMContentLoaded', () => {
    const queryButton = document.getElementById('query-tenant-btn');
    if (queryButton) {
        queryButton.click();
    }
});
