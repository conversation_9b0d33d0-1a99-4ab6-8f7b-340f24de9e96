<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>向量库管理</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3bc47d;
            --bg-color: #f8f9fa;
            --text-color: #333;
            --sidebar-bg: #f0f2f5;
            --border-radius: 10px;
            --shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            color: var(--text-color);
            background-color: #f4f6f9;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            background: linear-gradient(135deg, #f4f6f9, #4895ef);
            color: white;
            padding: 20px;
            border-radius: var(--border-radius);
            margin-bottom: 25px;
            box-shadow: var(--shadow);
            text-align: center;
        }

        header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
            letter-spacing: 1px;
        }

        .main-content {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 25px;
        }

        .sidebar {
            background-color: var(--sidebar-bg);
            border-radius: var(--border-radius);
            padding: 20px;
            box-shadow: var(--shadow);
            height: fit-content;
        }

        .search-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px 20px 20px;
        }

        .sidebar h3 {
            font-size: 18px;
            margin-top: 0;
            padding-bottom: 10px;
            border-bottom: 2px solid #e1e5e9;
            color: var(--primary-color);
        }

        .tenant-input-container {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        #tenant-id-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            font-size: 14px;
        }

        #query-tenant-btn {
            padding: 8px 15px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
        }

        #query-tenant-btn:hover {
            background-color: #3051d3;
            transform: translateY(-2px);
        }

        #chart-name-list {
            list-style-type: none;
            padding: 0;
        }

        #chart-name-list li {
            padding: 10px 15px;
            margin-bottom: 8px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            cursor: pointer;
            transition: var(--transition);
        }

        #chart-name-list li:hover {
            background-color: #e8f0fe;
            transform: translateY(-2px);
        }

        .button-container {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }

        #generate-features-btn,
        #save-features-btn {
            height: 44px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            padding: 10px 20px;
            cursor: pointer;
            font-size: 15px;
            font-weight: 500;
            width: auto;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition);
            box-shadow: 0 4px 8px rgba(67, 97, 238, 0.3);
        }

        #save-features-btn {
            background-color: var(--secondary-color);
            box-shadow: 0 4px 8px rgba(59, 196, 125, 0.3);
        }

        #save-features-btn:hover {
            background-color: #32a86c;
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(59, 196, 125, 0.4);
        }

        #generate-features-btn:hover {
            background-color: #3051d3;
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(67, 97, 238, 0.4);
        }

        .content-area {
            position: relative;
            padding: 25px;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }

        .content-area h3 {
            font-size: 20px;
            margin-top: 0;
            color: var(--primary-color);
            border-bottom: 2px solid #e1e5e9;
            padding-bottom: 12px;
            margin-bottom: 20px;
        }

        #features-container ul {
            padding-left: 20px;
        }

        #features-container li {
            margin-bottom: 10px;
            padding: 8px 12px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border-left: 3px solid var(--primary-color);
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .button-container {
                flex-direction: column;
            }

            #generate-features-btn,
            #save-features-btn {
                width: 100%;
            }
        }
    </style>
</head>

<body>
<div class="container">
    <header>
        <h1>向量库管理</h1>
    </header>

    <div class="search-container">
        <div class="tenant-input-container">
            <input type="text" id="tenant-id-input" value="-1" placeholder="请输入租户ID">
            <button id="query-tenant-btn">查询</button>
        </div>
    </div>

    <div class="main-content">
        <div class="sidebar">
            <h3>图表名称列表</h3>
            <ul id="chart-name-list">
                <!-- 动态填充图表名称 -->
            </ul>
        </div>
        <div>
            <div class="button-container">
                <button id="generate-features-btn">生成特征</button>
                <button id="save-features-btn">保存特征</button>
            </div>
            <div class="content-area" id="chart-details">
                <h3>特征:</h3>
                <div id="features-container">
                    <ul>
                        <!-- 生成的特征将显示在这里 -->
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="js/embedding-manage.js"></script>
</body>

</html>