/* 提示词管理器样式 */
body {
    background-color: #f8f9fa;
}

.container {
    max-width: 1400px;
}

.main-content {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
}

.page-header {
    border-bottom: 1px solid #eee;
    padding: 15px 20px;
}

.page-header h2 {
    margin-bottom: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
}

.layout-container {
    display: flex;
    height: calc(100vh - 180px);
    min-height: 600px;
}

.template-editor-container {
    flex: 3;
    border-right: 1px solid #eee;
    display: flex;
    flex-direction: column;
}

.variables-container {
    flex: 1.5;
    overflow-y: auto;
    padding: 15px;
}

.template-tabs-wrapper {
    padding: 15px 15px 0;
    border-bottom: 1px solid #eee;
    background-color: #f8f9fa;
}

.template-info {
    padding: 15px;
    border-bottom: 1px solid #eee;
}

.template-info h3 {
    margin-bottom: 5px;
    font-size: 1.2rem;
    font-weight: 600;
}

.actions-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border-bottom: 1px solid #eee;
    background-color: #fafafa;
}

.actions-bar .last-update {
    font-size: 0.8rem;
    color: #6c757d;
}

.actions-bar .btn-group {
    margin-left: auto;
}

.CodeMirror {
    height: 100% !important;
    font-family: 'JetBrains Mono', monospace;
    font-size: 14px;
}

.template-demo {
    background-color: #f8f9fa;
    border-left: 4px solid #6c757d;
    padding: 10px 15px;
    margin: 10px 0;
    overflow-x: auto;
}

.demo-json {
    font-family: 'JetBrains Mono', monospace;
    font-size: 0.85rem;
    white-space: pre-wrap;
    color: #555;
}

.template-variables-section {
    margin-top: 15px;
}

.template-variables-section h5 {
    font-size: 1rem;
    font-weight: 600;
    color: #444;
    margin-bottom: 15px;
    border-bottom: 2px solid #eee;
    padding-bottom: 8px;
}

.var-table {
    font-size: 0.85rem;
}

.var-table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.03);
}

.var-type {
    font-size: 0.75rem;
}

.badge-template-name {
    font-size: 0.7rem;
    font-weight: normal;
}

.action-footer {
    border-top: 1px solid #eee;
    padding: 15px;
    background-color: #f8f9fa;
}

/* 弹出通知 */
.alert-floating {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
    min-width: 300px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 992px) {
    .layout-container {
        flex-direction: column;
        height: auto;
    }
    
    .template-editor-container,
    .variables-container {
        flex: none;
        width: 100%;
        border-right: none;
    }
    
    .variables-container {
        border-top: 1px solid #eee;
    }
    
    .CodeMirror {
        height: 400px !important;
    }
}

/* 自定义标签页样式 */
.nav-tabs {
    border-bottom: 1px solid #dee2e6;
}

.nav-tabs .nav-link {
    margin-bottom: -1px;
    border: 1px solid transparent;
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
}

.nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6;
}

.nav-tabs .nav-link.active {
    color: #495057;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
    font-weight: 500;
}

/* 滚动条美化 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
} 