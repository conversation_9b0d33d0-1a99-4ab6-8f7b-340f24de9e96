<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatBI - 提示词模板管理</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/codemirror@5.65.13/lib/codemirror.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/codemirror@5.65.13/theme/dracula.css">
    <style>
        body {
            padding-top: 20px;
            background-color: #f8f9fa;
        }
        .container-fluid {
            max-width: 1500px;
            padding: 0 30px;
        }
        .CodeMirror {
            height: 550px;
            border: 1px solid #ced4da;
            border-radius: 0.25rem;
        }
        .card {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .template-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .alert-floating {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
        }
        .nav-tabs .nav-link {
            color: #495057;
            border-radius: 0;
            font-weight: 500;
            padding: 0.7rem 1.2rem;
        }
        .nav-tabs .nav-link.active {
            background-color: #f8f9fa;
            border-bottom-color: #f8f9fa;
            border-top: 2px solid #0d6efd;
        }
        .nav-tabs .nav-link:hover:not(.active) {
            border-color: transparent;
            background-color: rgba(13, 110, 253, 0.05);
        }
        .var-table {
            font-size: 0.85rem;
        }
        .var-table code {
            color: #6f42c1;
            font-size: 0.8rem;
        }
        .var-table th {
            background-color: #f1f1f1;
        }
        .var-type {
            font-size: 0.75rem;
            padding: 0.2rem 0.4rem;
        }
        .sidebar {
            height: calc(100vh - 180px);
            overflow-y: auto;
            position: sticky;
            top: 20px;
        }
        .editor-container {
            position: relative;
        }
        .footer-actions {
            position: sticky;
            bottom: 0;
            background-color: #fff;
            border-top: 1px solid #dee2e6;
            padding: 10px 0;
            margin-top: 10px;
            z-index: 100;
        }
        .template-title {
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .badge-template-name {
            font-weight: normal;
            font-size: 0.8em;
        }
        /* 示例值样式 */
        .example-value {
            background-color: #f8f9fa;
            border-radius: 4px;
            margin-top: 5px;
        }
        .example-pre {
            margin: 0;
            padding: 8px;
            font-size: 0.75rem;
            background-color: #f1f3f5;
            border-radius: 3px;
            max-height: 120px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .example-pre code {
            color: #212529;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="card mb-3">
            <div class="card-header bg-primary text-white py-3">
                <div class="d-flex justify-content-between align-items-center">
                    <h1 class="h3 mb-0">ChatBI 提示词模板管理</h1>
                    <div>
                        <button class="btn btn-light btn-sm refresh-template">
                            <i class="bi bi-arrow-clockwise"></i> 重新加载
                        </button>
                        <button class="btn btn-success btn-sm save-template ms-2">
                            <i class="bi bi-save"></i> 保存修改
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body pb-0">
                <p class="text-muted">本页面用于管理和更新ChatBI系统中使用的各类提示词模板。提示词是AI模型生成内容的关键指令，良好的提示词可以显著提升生成质量。</p>
                
                <!-- 横向模板类型标签页 -->
                <ul class="nav nav-tabs" id="template-tabs-container" role="tablist">
                </ul>
            </div>
        </div>

        <div class="row">
            <!-- 左侧编辑器面板 -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header template-header bg-light">
                        <div class="template-title">
                            <h5 class="mb-0" id="current-template-title"></h5>
                            <div class="text-muted small mt-1" id="template-description"></div>
                        </div>
                        
                        <div class="row align-items-center mb-3">
                            <div class="col text-end">
                                <span class="text-muted small" id="last-modified">最后更新: --</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-body editor-container pt-0">
                        <textarea id="template-editor"></textarea>
                    </div>
                </div>
                
                <!-- 示例面板 -->
                <div class="card mt-3" id="template-demo" style="display: none;">
                    <div class="card-header">
                        <button class="btn btn-link p-0 text-decoration-none" data-bs-toggle="collapse" data-bs-target="#demo-content">
                            <i class="bi bi-chevron-down"></i> 示例内容
                        </button>
                    </div>
                    <div class="collapse" id="demo-content">
                        <div class="card-body">
                            <pre id="demo-json-content" class="mb-0"></pre>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 右侧模板变量面板 -->
            <div class="col-md-4">
                <div class="card sidebar">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="bi bi-braces"></i> 模板变量</h5>
                    </div>
                    <div class="card-body" id="template-variables">
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 底部操作栏 -->
        <div class="footer-actions">
            <div class="d-flex justify-content-end">
                <button class="btn btn-secondary me-2" onclick="window.location.reload()">
                    <i class="bi bi-arrow-counterclockwise"></i> 重置
                </button>
                <button class="btn btn-primary save-template">
                    <i class="bi bi-save"></i> 保存模板
                </button>
            </div>
        </div>
    </div>

    <!-- 通知弹窗 -->
    <div id="alert-container"></div>

    <!-- 确认对话框 -->
    <div class="modal fade" id="confirm-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认操作</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    确定要保存此模板的修改吗？此操作会影响系统的AI生成结果。
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirm-save">确认保存</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/codemirror@5.65.13/lib/codemirror.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/codemirror@5.65.13/mode/markdown/markdown.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/codemirror@5.65.13/addon/display/placeholder.js"></script>
    <script src="js/prompt-manager.js"></script>
</body>
</html>