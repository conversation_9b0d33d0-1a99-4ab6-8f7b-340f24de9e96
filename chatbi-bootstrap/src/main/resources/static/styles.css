/* 基础风格重置 - 纷享销客企业风格 */
:root { /* 定义CSS变量，全局可用 */
    --primary-color: #F4B63E;      /* 主色调：蜜蜂黄，定义品牌特色 */
    --primary-light: #FFF8E6;      /* 主色调浅色，用于背景和高亮 */
    --primary-dark: #E09C1F;       /* 主色调深色，用于悬停状态 */
    --text-primary: #333333;       /* 主要文本颜色，深灰色 */
    --text-secondary: #666666;     /* 次要文本颜色，中灰色 */
    --text-light: #999999;         /* 轻文本颜色，浅灰色 */
    --border-color: #e8e8e8;       /* 边框颜色，浅黄色 */
    --bg-light: #FFFCF5;           /* 背景浅色，米黄色 */
    --success-color: #52c41a;      /* 成功状态颜色，绿色 */
    --warning-color: #faad14;      /* 警告状态颜色，橙色 */
    --danger-color: #ff4d4f;       /* 危险状态颜色，红色 */
    --secondary-color: #52c41a;
    --text-color: #333;
    --light-text: #666;
    --lighter-text: #999;
    --background-color: #f5f5f5;
    --white: #fff;
    --shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    --hover-color: #FFF8E6;
    --active-color: #E09C1F;
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

* { /* 通用选择器，应用于所有元素 */
    box-sizing: border-box; /* 盒模型计算方式，宽高包含padding和border */
    margin: 0; /* 外边距清零 */
    padding: 0; /* 内边距清零 */
    font-family: var(--font-family); /* 字体系列，优先使用中文字体 */
}

body { /* 页面主体样式 */
    background-color: var(--background-color); /* 背景色，使用CSS变量 */
    color: var(--text-color); /* 文本颜色 */
    line-height: 1.6;
}

.container { /* 主容器样式 */
    max-width: 1200px; /* 最大宽度，防止在大屏上过宽 */
    margin: 0 auto; /* 水平居中 */
    padding: 20px; /* 内边距 */
    height: 100vh;
    display: flex;
    flex-direction: column;
}

header { /* 页面头部样式 */
    text-align: center;
    margin-bottom: 20px;
    padding: 20px;
    color: #333;
    position: relative;
    background-color: var(--white);
    border-radius: 8px;
    box-shadow: var(--shadow);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-content {
    flex: 1;
    text-align: center;
}

header h1 {
    color: var(--primary-color);
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
}

header h1 i {
    margin-right: 10px;
    color: var(--warning-color);
}

header p {
    color: var(--light-text);
    font-size: 14px;
}

/* 添加管理工具样式 */
.admin-tools {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 100;
}

.admin-link {
    display: inline-flex;
    align-items: center;
    padding: 10px 15px;
    background-color: var(--primary-color);
    color: #fff;
    border-radius: 6px;
    text-decoration: none;
    font-size: 15px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 2px solid var(--primary-color);
}

.admin-link i {
    margin-right: 8px;
    font-size: 16px;
}

.admin-link:hover {
    background-color: #fff;
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.main-content {
    display: flex;
    flex: 1;
    gap: 20px;
    height: calc(100vh - 120px);
}

.sidebar {
    width: 250px;
    background-color: var(--white);
    border-radius: 8px;
    box-shadow: var(--shadow);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.history-header {
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.history-header h3 {
    font-size: 16px;
    color: var(--text-color);
}

#new-chat {
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.3s;
}

#new-chat:hover {
    background-color: var(--active-color);
}

.history-list {
    flex: 1;
    overflow-y: auto;
}

.history-item {
    padding: 12px 15px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: background-color 0.3s;
    display: flex;
    flex-direction: column;
}

.history-item:hover {
    background-color: var(--hover-color);
}

.history-item.active {
    background-color: var(--hover-color);
    border-left: 3px solid var(--primary-color);
}

.history-item span {
    font-size: 14px;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.history-item small {
    font-size: 12px;
    color: var(--lighter-text);
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: var(--white);
    border-radius: 8px;
    box-shadow: var(--shadow);
    overflow: hidden;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

.message {
    margin-bottom: 20px;
    max-width: 85%;
    padding: 12px 16px;
    border-radius: 8px;
    position: relative;
}

.user-message {
    background-color: var(--primary-color);
    color: var(--white);
    margin-left: auto;
    border-top-right-radius: 0;
}

.bot-message {
    background-color: #f8f9fa;
    color: var(--text-color);
    margin-right: auto;
    border-top-left-radius: 0;
    border: 1px solid #e8e8e8;
}

.bot-message p, .bot-message ul, .bot-message ol {
    margin-bottom: 10px;
}

.bot-message p:last-child, .bot-message ul:last-child, .bot-message ol:last-child {
    margin-bottom: 0;
}

.bot-message ul, .bot-message ol {
    padding-left: 20px;
}

.bot-message li {
    margin-bottom: 5px;
}

.bot-message li:last-child {
    margin-bottom: 0;
}

.bot-message strong {
    font-weight: 600;
    color: var(--primary-color);
}

.chart-container {
    position: relative;
    margin: 20px 0;
    background-color: #ffffff;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s, box-shadow 0.3s;
    border: 1px solid #e8e8e8;
}

.chart-container:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12);
    border-color: #F4B63E;
}

.chart-container h4 {
    margin-bottom: 15px;
    color: #333;
    font-size: 15px;
    font-weight: 600;
    text-align: center;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 10px;
    position: relative;
}

.chart-container h4::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 3px;
}

.chart-container canvas {
    width: 100%;
    height: 300px !important;
    display: block;
}

.input-area {
    padding: 15px;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 10px;
}

#user-input {
    flex: 1;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    resize: none;
    height: 60px;
    font-family: var(--font-family);
    font-size: 14px;
    transition: border-color 0.3s;
}

#user-input:focus {
    outline: none;
    border-color: var(--primary-color);
}

#send-button {
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 4px;
    padding: 0 20px;
    cursor: pointer;
    transition: background-color 0.3s;
    font-size: 14px;
}

#send-button:hover {
    background-color: var(--active-color);
}

.thinking-dots {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 24px;
}

.thinking-dots span {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--light-text);
    margin: 0 3px;
    animation: thinking 1.4s infinite ease-in-out both;
}

.thinking-dots span:nth-child(1) {
    animation-delay: -0.32s;
}

.thinking-dots span:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes thinking {
    0%, 80%, 100% { 
        transform: scale(0);
    } 
    40% { 
        transform: scale(1);
    }
}

/* 用户画像面板 */
.user-profile-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--shadow);
    transition: all 0.3s;
    z-index: 1000;
}

.user-profile-toggle:hover {
    background-color: var(--active-color);
    transform: scale(1.05);
}

.user-profile-toggle.active {
    background-color: var(--danger-color);
}

.user-profile-panel {
    position: fixed;
    top: 0;
    right: -350px;
    width: 350px;
    height: 100vh;
    background-color: var(--white);
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
    padding: 20px;
    overflow-y: auto;
    transition: right 0.3s;
    z-index: 999;
}

.user-profile-panel.active {
    right: 0;
}

.user-profile-panel h3 {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-color);
}

.profile-section {
    margin-bottom: 25px;
}

.profile-section h4 {
    margin-bottom: 10px;
    color: var(--light-text);
    font-size: 14px;
}

#interest-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.interest-tag {
    background-color: var(--hover-color);
    color: var(--primary-color);
    padding: 4px 10px;
    border-radius: 16px;
    font-size: 12px;
}

.preference-item {
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
}

.preference-item .label {
    color: var(--light-text);
    font-size: 13px;
}

.preference-item .value {
    color: var(--text-color);
    font-size: 13px;
    font-weight: 500;
}

/* 反馈组件 */
.feedback-container {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

.feedback-question {
    margin-bottom: 10px;
    font-size: 14px;
    color: var(--light-text);
}

.rating-stars {
    display: flex;
    margin-bottom: 15px;
}

.star {
    font-size: 24px;
    color: var(--border-color);
    cursor: pointer;
    transition: color 0.2s;
    margin-right: 5px;
}

.star:hover, .star.active {
    color: var(--warning-color);
}

.feedback-comment {
    width: 100%;
    height: 80px;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    margin-bottom: 15px;
    font-family: var(--font-family);
    font-size: 14px;
    resize: none;
}

.feedback-comment:focus {
    outline: none;
    border-color: var(--primary-color);
}

.feedback-submit {
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
    transition: background-color 0.3s;
    font-size: 14px;
}

.feedback-submit:hover:not(:disabled) {
    background-color: var(--active-color);
}

.feedback-submit:disabled {
    background-color: var(--border-color);
    cursor: not-allowed;
}

.feedback-success {
    display: none;
    margin-top: 10px;
    padding: 8px;
    background-color: rgba(82, 196, 26, 0.1);
    border: 1px solid var(--success-color);
    border-radius: 4px;
    color: var(--success-color);
    font-size: 13px;
    text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        height: auto;
        max-height: 200px;
    }
    
    .user-profile-panel {
        width: 100%;
        right: -100%;
    }
    
    header {
        flex-direction: column;
        padding: 15px;
    }
    
    .header-content {
        margin-bottom: 15px;
    }
    
    .admin-tools {
        position: relative;
        top: 0;
        right: 0;
        margin-top: 10px;
    }
}

/* 增强图表样式 */
.chart-container {
    position: relative;
    margin: 20px 0;
    background-color: var(--white);
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s, box-shadow 0.3s;
}

.chart-container:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12);
}

.chart-container h4 {
    margin-bottom: 15px;
    color: var(--text-color);
    font-size: 15px;
    font-weight: 600;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
    position: relative;
}

.chart-container h4::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 3px;
}

.chart-container canvas {
    width: 100%;
    height: 320px;
}

/* 增强列表样式 */
.bot-message ul, .bot-message ol {
    padding-left: 25px;
    margin: 15px 0;
}

.bot-message li {
    margin-bottom: 8px;
    position: relative;
}

.bot-message ul li::before {
    content: '•';
    color: var(--primary-color);
    font-weight: bold;
    position: absolute;
    left: -15px;
}

.bot-message ol {
    counter-reset: item;
}

.bot-message ol li {
    counter-increment: item;
}

.bot-message ol li::before {
    content: counter(item) ".";
    color: var(--primary-color);
    font-weight: bold;
    position: absolute;
    left: -20px;
}

/* 强调重点内容 */
.bot-message strong {
    color: var(--primary-color);
    font-weight: 600;
}

/* 改进嵌套列表样式 */
.bot-message ul ul, 
.bot-message ol ol,
.bot-message ul ol,
.bot-message ol ul {
    margin: 8px 0 8px 5px;
}

/* 图表ID标识样式 */
.chart-container h4 span.chart-id {
    font-size: 12px;
    color: var(--lighter-text);
    font-weight: normal;
    margin-left: 5px;
}

/* 添加图表ID样式 */
.chart-container h4 {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.chart-container h4::after {
    content: attr(data-id);
    font-size: 12px;
    color: var(--lighter-text);
    font-weight: normal;
    background-color: var(--background-color);
    padding: 2px 8px;
    border-radius: 4px;
    margin-top: 5px;
}

/* 调整现有样式 */
.bot-introduction {
    background-color: #ffffff;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    border: none;
    box-shadow: none;
}

.bot-introduction h3 {
    margin-top: 0;
    color: #333;
    margin-bottom: 15px;
    font-size: 18px;
}

.bot-introduction p {
    margin-bottom: 15px;
    color: #666;
    font-size: 14px;
}

.bot-introduction ul {
    margin-bottom: 0;
    list-style: none;
    padding-left: 0;
}

.bot-introduction li {
    margin-bottom: 8px;
    color: #4A6CF7;
    cursor: pointer;
    position: relative;
    padding-left: 20px;
}

.bot-introduction li::before {
    content: '•';
    color: #4A6CF7;
    position: absolute;
    left: 5px;
    font-size: 18px;
    line-height: 1;
}

.bot-introduction li:hover {
    text-decoration: underline;
}

/* 引导页图表推荐样式 */
.recommended-charts {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin: 20px 0;
}

.chart-recommendation {
    flex: 1;
    min-width: 200px;
    background-color: #ffffff;
    border-radius: 4px;
    padding: 15px;
    box-shadow: none;
    transition: all 0.3s ease;
    border: 1px solid #F4B63E;
}

.chart-recommendation:hover {
    transform: none;
    box-shadow: none;
}

.chart-recommendation h4 {
    margin-top: 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 10px;
}

.chart-recommendation p {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
    line-height: 1.4;
}

.chart-preview {
    height: 100px;
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 0;
    margin-top: 10px;
    border: 1px solid #eee;
}

.chart-preview-border {
    border: 1px solid #e0e0e0;
    background-color: #f8f9fa;
    min-height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

/* 销售漏斗图表预览 */
#funnel-preview::before {
    content: '';
    position: absolute;
    width: 80%;
    height: 80%;
    background-image: 
        linear-gradient(to right, #f4b63e 1px, transparent 1px),
        linear-gradient(to bottom, #f4b63e 1px, transparent 1px);
    background-size: 20px 20px;
    opacity: 0.3;
}

#funnel-preview::after {
    content: '';
    position: absolute;
    width: 80%;
    height: 70%;
    background-image: 
        linear-gradient(to right, transparent 0%, #f4b63e 0%, #f4b63e 10%, transparent 10%),
        linear-gradient(to right, transparent 20%, #f4b63e 20%, #f4b63e 30%, transparent 30%),
        linear-gradient(to right, transparent 40%, #f4b63e 40%, #f4b63e 50%, transparent 50%),
        linear-gradient(to right, transparent 60%, #f4b63e 60%, #f4b63e 70%, transparent 70%),
        linear-gradient(to right, transparent 80%, #f4b63e 80%, #f4b63e 90%, transparent 90%);
    background-size: 100% 20%;
    background-repeat: no-repeat;
    background-position: 
        center 10%,
        center 30%,
        center 50%,
        center 70%,
        center 90%;
    opacity: 0.2;
}

/* 线索转商机分析图表预览 */
#lead-conversion-preview::before {
    content: '';
    position: absolute;
    width: 80%;
    height: 80%;
    background-image: 
        linear-gradient(to right, #f4b63e 1px, transparent 1px),
        linear-gradient(to bottom, #f4b63e 1px, transparent 1px);
    background-size: 20px 20px;
    opacity: 0.3;
}

#lead-conversion-preview::after {
    content: '';
    position: absolute;
    width: 80%;
    height: 70%;
    background-image: 
        linear-gradient(to bottom, transparent 0%, #4A6CF7 0%, #4A6CF7 15%, transparent 15%),
        linear-gradient(to bottom, transparent 25%, #4A6CF7 25%, #4A6CF7 35%, transparent 35%),
        linear-gradient(to bottom, transparent 45%, #4A6CF7 45%, #4A6CF7 65%, transparent 65%),
        linear-gradient(to bottom, transparent 75%, #4A6CF7 75%, #4A6CF7 85%, transparent 85%);
    background-size: 20% 100%;
    background-repeat: no-repeat;
    background-position: 
        10% center,
        30% center,
        50% center,
        70% center;
    opacity: 0.3;
}

/* 从线索到成交转化分析图表预览 */
#full-funnel-preview::before {
    content: '';
    position: absolute;
    width: 80%;
    height: 80%;
    background-image: 
        linear-gradient(to right, #f4b63e 1px, transparent 1px),
        linear-gradient(to bottom, #f4b63e 1px, transparent 1px);
    background-size: 20px 20px;
    opacity: 0.3;
}

#full-funnel-preview::after {
    content: '';
    position: absolute;
    width: 80%;
    height: 60%;
    background-image: 
        linear-gradient(to bottom, #f4b63e 0%, #f4b63e 100%),
        linear-gradient(to bottom, #f4b63e 0%, #f4b63e 100%),
        linear-gradient(to bottom, #f4b63e 0%, #f4b63e 100%),
        linear-gradient(to bottom, #f4b63e 0%, #f4b63e 100%);
    background-size: 15% 100%, 15% 70%, 15% 40%, 15% 20%;
    background-repeat: no-repeat;
    background-position: 
        20% center,
        40% center,
        60% center,
        80% center;
    opacity: 0.3;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .recommended-charts {
        flex-direction: column;
    }
    
    .chart-recommendation {
        width: 100%;
    }
}

/* 知识文档链接区域样式 */
.docs-section {
    margin-top: 20px;
    padding: 15px;
    border-top: 1px solid #e0e0e0;
}

.docs-section h3 {
    font-size: 16px;
    margin-bottom: 10px;
    color: #333;
}

.doc-link {
    padding: 8px 0;
}

.doc-link a {
    color: #1a73e8;
    text-decoration: none;
    font-size: 14px;
    display: block;
    padding: 5px 0;
}

.doc-link a:hover {
    text-decoration: underline;
    color: #0d47a1;
}

/* 固定位置的提示词管理按钮 */
.prompt-manage-btn {
    position: fixed;
    bottom: 20px;
    left: 20px;
    z-index: 1001;
}

.prompt-manage-btn a {
    display: flex;
    align-items: center;
    background-color: var(--primary-color);
    color: white;
    padding: 10px 15px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
    border: 2px solid var(--primary-color);
    transition: all 0.3s ease;
}

.prompt-manage-btn a:hover {
    background-color: white;
    color: var(--primary-color);
}

.prompt-manage-btn i {
    margin-right: 8px;
    font-size: 16px;
}

/* 提示词管理链接样式 */
.prompt-manage-link {
    display: block;
    margin-top: 10px;
    padding: 8px 12px;
    background-color: var(--primary-color);
    color: white !important;
    border-radius: 4px;
    text-align: center;
    font-weight: 500;
    transition: all 0.3s ease;
}

.prompt-manage-link:hover {
    background-color: var(--active-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.prompt-manage-link i {
    margin-right: 5px;
}

/* 管理工具区域样式 */
.admin-section {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 3px solid var(--primary-color);
    display: block !important; /* 确保始终显示 */
    visibility: visible !important; /* 保证可见性 */
}

.admin-section h3 {
    margin-top: 0;
    color: #495057;
    font-size: 16px;
    margin-bottom: 12px;
}

.admin-link-container {
    text-align: center;
}

.admin-link-button {
    display: inline-block;
    width: 90%;
    padding: 12px 15px;
    background-color: #f07b3c !important;
    color: white !important;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 700;
    font-size: 16px;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.admin-link-button:hover {
    background-color: #e05a1c !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.25);
}

.admin-link-button i {
    margin-right: 8px;
    font-size: 16px;
}

/* 重要管理区域 - 更加明显的样式 */
.admin-important {
    background-color: #ffe8cc !important;
    border: 2px solid var(--primary-color) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
    margin: 25px 0 !important;
    padding: 15px !important;
}

/* 管理工具和文档区域共享样式 */
.section-container {
    margin-top: 20px;
    padding: 15px;
    background-color: #ffffff;
    border-radius: 8px;
    border-left: 3px solid #3b82f6;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-container h3 {
    margin-top: 0;
    margin-bottom: 12px;
    color: #4b5563;
    font-size: 16px;
    font-weight: 600;
}

.tool-link {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background-color: #f0f9ff;
    color: #3b82f6;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
}

.tool-link:hover {
    background-color: #e0f2fe;
    color: #2563eb;
    transform: translateY(-1px);
}

.tool-link i {
    margin-right: 8px;
    font-size: 14px;
}

/* 蜜蜂主题样式 */
.bee-section {
    margin-top: 15px;
    padding: 10px 15px;
    background-color: transparent;
    border-radius: 0;
    border-left: none;
    box-shadow: none;
}

.bee-section h3 {
    margin-top: 0;
    margin-bottom: 12px;
    color: #666;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
}

.bee-section h3 i {
    color: #4285f4;
    margin-right: 8px;
    font-size: 16px;
}

.bee-link {
    display: flex;
    align-items: center;
    padding: 8px 0;
    background-color: transparent;
    color: #4285f4 !important;
    border-radius: 0;
    text-decoration: none;
    font-weight: normal;
    transition: all 0.2s ease;
}

.bee-link:hover {
    background-color: transparent;
    transform: none;
    box-shadow: none;
    text-decoration: underline;
}

.bee-link i {
    margin-right: 8px;
    color: #4285f4;
    font-size: 14px;
} 