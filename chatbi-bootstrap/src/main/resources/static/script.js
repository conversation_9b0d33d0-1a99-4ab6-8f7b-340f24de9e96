document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM加载完成，初始化应用...');
    
    const chatMessages = document.getElementById('chat-messages');
    const userInput = document.getElementById('user-input');
    const sendButton = document.getElementById('send-button');
    const newChatButton = document.getElementById('new-chat');
    const profileToggle = document.getElementById('profile-toggle');
    const profilePanel = document.getElementById('user-profile-panel');
    const demoScenario = document.getElementById('demo-scenario');
    const demoTemplate = document.getElementById('demo-conversation-template');
    const ratingStars = document.getElementById('rating-stars');
    const feedbackComment = document.getElementById('feedback-comment');
    const feedbackSubmit = document.getElementById('feedback-submit');
    const feedbackSuccess = document.getElementById('feedback-success');
    const adminSection = document.querySelector('.admin-section');
    
    // 当前会话ID
    let sessionId = generateSessionId();
    // 历史消息数组
    let messageHistory = [];
    
    // API基础路径
    const API_BASE_URL = '/api';
    
    // 获取URL参数
    const urlParams = new URLSearchParams(window.location.search);
    const tenantId = urlParams.get('tenantId') || 'demo_tenant';
    const userId = urlParams.get('userId') || 'demo_user';
    
    // 确保管理工具区域显示 - 延迟执行以确保DOM完全加载
    setTimeout(() => {
        if (adminSection) {
            console.log('确保管理工具区域显示...');
            adminSection.style.display = 'block';
            adminSection.style.visibility = 'visible';
            adminSection.style.opacity = '1';
        } else {
            console.warn('未找到管理工具区域元素！');
        }
    }, 500);
    
    // 切换用户画像面板
    profileToggle.addEventListener('click', function() {
        profilePanel.classList.toggle('active');
        profileToggle.classList.toggle('active');
    });
    
    // 发送按钮事件
    sendButton.addEventListener('click', function() {
        sendMessage();
    });
    
    // 按Enter键发送消息
    userInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });
    
    // 新对话按钮事件
    newChatButton.addEventListener('click', function() {
        chatMessages.innerHTML = '';
        messageHistory = [];
        sessionId = generateSessionId();
        // 显示欢迎消息
        showWelcomeMessage();
        
        // 更新历史项目的活跃状态
        updateHistoryActiveState(null);
    });
    
    // 典型对话场景点击事件
    demoScenario.addEventListener('click', function() {
        loadDemoConversation();
        updateHistoryActiveState(demoScenario);
    });
    
    // 初始化用户画像面板
    setupUserProfilePanel();
    
    // 初始化反馈组件
    setupFeedbackComponent();
    
    // 初始化分析评价
    initAnalysisRatings();
    
    // 显示欢迎消息或加载典型对话示例
    console.log('准备加载典型对话示例...');
    showWelcomeMessage();
    
    // 添加窗口大小变化时重绘图表的函数
    window.addEventListener('resize', function() {
        // 获取所有图表实例并重绘
        const chartElements = document.querySelectorAll('.chart-container div, .chart-preview');
        chartElements.forEach(element => {
            const chart = echarts.getInstanceByDom(element);
            if (chart) {
                chart.resize();
            }
        });
    });
    
    // 更新历史项目的活跃状态
    function updateHistoryActiveState(activeItem) {
        const historyItems = document.querySelectorAll('.history-item');
        historyItems.forEach(item => {
            item.classList.remove('active');
        });
        
        if (activeItem) {
            activeItem.classList.add('active');
        } else {
            // 如果没有指定活跃项，默认选中第一个
            const firstItem = document.querySelector('.history-item');
            if (firstItem) {
                firstItem.classList.add('active');
            }
        }
    }
    
    // 加载典型对话场景
    function loadDemoConversation() {
        // 确保demoTemplate已经获取到
        const demoTemplate = document.getElementById('demo-conversation-template');
        if (!demoTemplate) {
            console.error('找不到对话模板元素');
            return;
        }
        
        console.log('加载典型对话示例...');
        console.log('模板内容:', demoTemplate.innerHTML.substring(0, 100) + '...');
        
        // 清空当前聊天内容
        chatMessages.innerHTML = '';
        messageHistory = [];
        
        // 复制模板内容到聊天区域
        chatMessages.innerHTML = demoTemplate.innerHTML;
        
        // 渲染所有图表，增加延迟确保DOM已完全加载
        setTimeout(() => {
            try {
                console.log('开始渲染预设图表...');
                renderPredefinedCharts();
                console.log('预设图表渲染完成');
            } catch (error) {
                console.error('渲染预设图表出错:', error);
            }
        }, 500);
        
        // 滚动到顶部，而不是底部
        chatMessages.scrollTop = 0;
    }
    
    // 渲染预设的图表
    function renderPredefinedCharts() {
        try {
            // 引导页 - 推荐图表预览
            renderPreviewCharts();
            
            // 销售漏斗图表
            const salesFunnelChart = document.getElementById('sales-funnel-chart');
            if (salesFunnelChart) {
                // 初始化ECharts实例
                const salesFunnelChartInstance = echarts.init(salesFunnelChart);
                
                // 设置图表配置项
                salesFunnelChartInstance.setOption({
                    title: {
                        text: '销售漏斗（商机金额）',
                        subtext: 'BI_5dcb747488da4e0001c2a50a',
                        left: 'center'
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>{b} : {c}万元 ({d}%)'
                    },
                    series: [
                        {
                            name: '销售漏斗',
                            type: 'funnel',
                            left: '10%',
                            top: 60,
                            bottom: 60,
                            width: '80%',
                            min: 0,
                            max: 1200,
                            minSize: '0%',
                            maxSize: '100%',
                            sort: 'descending',
                            gap: 2,
                            label: {
                                show: true,
                                position: 'inside'
                            },
                            labelLine: {
                                length: 10,
                                lineStyle: {
                                    width: 1,
                                    type: 'solid'
                                }
                            },
                            itemStyle: {
                                borderColor: '#fff',
                                borderWidth: 1
                            },
                            emphasis: {
                                label: {
                                    fontSize: 14
                                }
                            },
                            data: [
                                { value: 320, name: '初步接触' },
                                { value: 210, name: '需求确认' },
                                { value: 150, name: '方案制定' },
                                { value: 90, name: '报价谈判' },
                                { value: 65, name: '合同签订' }
                            ]
                        }
                    ]
                });
                
                // 保存实例以便后续引用
                window.salesFunnelChartInstance = salesFunnelChartInstance;
            }
            
            // 线索转商机分析图表
            const leadConversionChart = document.getElementById('lead-conversion-chart');
            if (leadConversionChart) {
                // 初始化ECharts实例
                const leadConversionChartInstance = echarts.init(leadConversionChart);
                
                // 设置图表配置项
                leadConversionChartInstance.setOption({
                    title: {
                        text: '线索转商机分析',
                        subtext: 'BI_5dd638ddb60ace0001917be7',
                        left: 'center'
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        }
                    },
                    legend: {
                        data: ['线索数', '转化商机数'],
                        top: 'bottom'
                    },
                    xAxis: {
                        type: 'category',
                        data: ['网站', '社交媒体', '电话营销', '合作伙伴', '展会']
                    },
                    yAxis: {
                        type: 'value'
                    },
                    series: [
                        {
                            name: '线索数',
                            type: 'bar',
                            data: [350, 420, 180, 250, 300],
                            itemStyle: {
                                color: '#4A6CF7'
                            }
                        },
                        {
                            name: '转化商机数',
                            type: 'bar',
                            data: [120, 80, 90, 150, 200],
                            itemStyle: {
                                color: '#F4B63E'
                            }
                        }
                    ]
                });
                
                // 保存实例以便后续引用
                window.leadConversionChartInstance = leadConversionChartInstance;
            }
            
            // 线索转商机排行top10图表
            const teamPerformanceChart = document.getElementById('team-performance-chart');
            if (teamPerformanceChart) {
                // 初始化ECharts实例
                const teamPerformanceChartInstance = echarts.init(teamPerformanceChart);
                
                // 设置图表配置项
                teamPerformanceChartInstance.setOption({
                    title: {
                        text: '线索转商机排行top10',
                        subtext: 'BI_5dd63a80b60ace0001917c45',
                        left: 'center'
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        },
                        formatter: '{b}: {c}%'
                    },
                    xAxis: {
                        type: 'category',
                        data: ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十'],
                        axisLabel: {
                            interval: 0,
                            rotate: 30
                        }
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: {
                            formatter: '{value}%'
                        }
                    },
                    series: [
                        {
                            type: 'bar',
                            data: [65, 58, 52, 48, 42, 35, 28, 22],
                            itemStyle: {
                                color: '#F4B63E'
                            },
                            label: {
                                show: true,
                                position: 'top',
                                formatter: '{c}%'
                            }
                        }
                    ]
                });
                
                // 保存实例以便后续引用
                window.teamPerformanceChartInstance = teamPerformanceChartInstance;
            }
            
            // 从线索到成交转化分析图表
            const fullFunnelChart = document.getElementById('full-funnel-chart');
            if (fullFunnelChart) {
                // 初始化ECharts实例
                const fullFunnelChartInstance = echarts.init(fullFunnelChart);
                
                // 设置图表配置项
                fullFunnelChartInstance.setOption({
                    title: {
                        text: '从线索到成交转化分析',
                        subtext: 'BI_5eb5450ae6835e00010a894b',
                        left: 'center'
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>{b} : {c} ({d}%)'
                    },
                    series: [
                        {
                            name: '转化漏斗',
                            type: 'funnel',
                            left: '10%',
                            top: 60,
                            bottom: 60,
                            width: '80%',
                            min: 0,
                            max: 1000,
                            minSize: '0%',
                            maxSize: '100%',
                            sort: 'descending',
                            gap: 2,
                            label: {
                                show: true,
                                position: 'inside',
                                formatter: '{b}: {c} ({d}%)'
                            },
                            labelLine: {
                                length: 10,
                                lineStyle: {
                                    width: 1,
                                    type: 'solid'
                                }
                            },
                            itemStyle: {
                                borderColor: '#fff',
                                borderWidth: 1
                            },
                            emphasis: {
                                label: {
                                    fontSize: 14
                                }
                            },
                            data: [
                                { value: 1000, name: '线索' },
                                { value: 600, name: '商机' },
                                { value: 350, name: '方案' },
                                { value: 200, name: '谈判' },
                                { value: 120, name: '成交' }
                            ]
                        }
                    ]
                });
                
                // 保存实例以便后续引用
                window.fullFunnelChartInstance = fullFunnelChartInstance;
            }
            
            // 输单原因分析图表
            const lossReasonChart = document.getElementById('loss-reason-chart');
            if (lossReasonChart) {
                // 初始化ECharts实例
                const lossReasonChartInstance = echarts.init(lossReasonChart);
                
                // 设置图表配置项
                lossReasonChartInstance.setOption({
                    title: {
                        text: '输单原因分析',
                        subtext: 'BI_5ddf9649fadde20001b08a82',
                        left: 'center'
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>{b} : {c} ({d}%)'
                    },
                    legend: {
                        orient: 'vertical',
                        left: 'left',
                        top: 'center'
                    },
                    series: [
                        {
                            name: '输单原因',
                            type: 'pie',
                            radius: '50%',
                            center: ['60%', '50%'],
                            data: [
                                { value: 35, name: '价格因素' },
                                { value: 25, name: '竞品优势' },
                                { value: 15, name: '需求变更' },
                                { value: 10, name: '预算削减' },
                                { value: 10, name: '决策延迟' },
                                { value: 5, name: '其他' }
                            ],
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }
                    ]
                });
                
                // 保存实例以便后续引用
                window.lossReasonChartInstance = lossReasonChartInstance;
            }
        } catch (error) {
            console.error('渲染图表出错:', error);
        }
    }
    
    // 渲染引导页的推荐图表预览
    function renderPreviewCharts() {
        try {
            // 销售漏斗预览图
            const funnelPreview = document.getElementById('funnel-preview');
            if (funnelPreview) {
                // 初始化ECharts实例
                const funnelPreviewInstance = echarts.init(funnelPreview);
                
                // 设置图表配置项
                funnelPreviewInstance.setOption({
                    series: [
                        {
                            type: 'funnel',
                            left: '10%',
                            top: 0,
                            bottom: 0,
                            width: '80%',
                            min: 0,
                            max: 100,
                            minSize: '0%',
                            maxSize: '100%',
                            sort: 'descending',
                            gap: 2,
                            label: {
                                show: false
                            },
                            itemStyle: {
                                color: '#F4B63E',
                                opacity: 0.6
                            },
                            data: [
                                { value: 100, name: '线索' },
                                { value: 80, name: '商机' },
                                { value: 60, name: '方案' },
                                { value: 40, name: '谈判' },
                                { value: 20, name: '成交' }
                            ]
                        }
                    ]
                });
                
                // 保存实例以便后续引用
                window.funnelPreviewInstance = funnelPreviewInstance;
            }
            
            // 线索转商机分析预览图
            const leadConversionPreview = document.getElementById('lead-conversion-preview');
            if (leadConversionPreview) {
                // 初始化ECharts实例
                const leadConversionPreviewInstance = echarts.init(leadConversionPreview);
                
                // 设置图表配置项
                leadConversionPreviewInstance.setOption({
                    xAxis: {
                        type: 'category',
                        data: ['网站', '社交', '电话', '合作', '展会'],
                        show: false
                    },
                    yAxis: {
                        type: 'value',
                        show: false
                    },
                    grid: {
                        left: 0,
                        right: 0,
                        top: 0,
                        bottom: 0
                    },
                    series: [
                        {
                            type: 'bar',
                            data: [35, 42, 18, 25, 30],
                            itemStyle: {
                                color: '#4A6CF7',
                                opacity: 0.6
                            }
                        }
                    ]
                });
                
                // 保存实例以便后续引用
                window.leadConversionPreviewInstance = leadConversionPreviewInstance;
            }
            
            // 从线索到成交转化分析预览图
            const fullFunnelPreview = document.getElementById('full-funnel-preview');
            if (fullFunnelPreview) {
                // 初始化ECharts实例
                const fullFunnelPreviewInstance = echarts.init(fullFunnelPreview);
                
                // 设置图表配置项
                fullFunnelPreviewInstance.setOption({
                    series: [
                        {
                            type: 'funnel',
                            left: '10%',
                            top: 0,
                            bottom: 0,
                            width: '80%',
                            min: 0,
                            max: 100,
                            minSize: '0%',
                            maxSize: '100%',
                            sort: 'descending',
                            gap: 2,
                            label: {
                                show: false
                            },
                            itemStyle: {
                                color: '#4A6CF7',
                                opacity: 0.6
                            },
                            data: [
                                { value: 100, name: '线索' },
                                { value: 60, name: '商机' },
                                { value: 35, name: '方案' },
                                { value: 20, name: '谈判' },
                                { value: 12, name: '成交' }
                            ]
                        }
                    ]
                });
                
                // 保存实例以便后续引用
                window.fullFunnelPreviewInstance = fullFunnelPreviewInstance;
            }
        } catch (error) {
            console.error('渲染预览图表出错:', error);
        }
    }
    
    // 显示欢迎消息
    function showWelcomeMessage() {
        console.log('执行showWelcomeMessage函数...');
        
        // 直接加载典型对话示例，而不是显示欢迎消息
        loadDemoConversation();
        
        // 更新历史项的活跃状态
        updateHistoryActiveState(document.getElementById('demo-scenario'));
        
        console.log('showWelcomeMessage函数执行完毕');
    }
    
    // 发送消息函数
    function sendMessage() {
        const message = userInput.value.trim();
        if (!message) return;
        
        // 添加用户消息到对话界面
        addUserMessage(message);
        userInput.value = '';
        
        // 显示加载状态
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'message bot-message loading';
        loadingIndicator.innerHTML = '正在思考中<span class="loading-dots"></span>';
        chatMessages.appendChild(loadingIndicator);
        
        // 更新消息历史
        messageHistory.push({
            senderType: 'USER',
            content: message
        });
        
        // 调用后端API
        callChatAPI(message);
    }
    
    // 调用聊天API
    async function callChatAPI(message) {
        try {
            // 移除加载状态指示器
            const loadingElement = document.querySelector('.loading');
            if (loadingElement) {
                loadingElement.remove();
            }
            
            // 模拟API响应延迟
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 使用模拟的静态响应代替实际API调用
            const data = getSimulatedResponse(message);
            
            // 添加机器人回复
            addBotMessage(data.message || data.content);
            
            // 更新消息历史
            messageHistory.push({
                senderType: 'ASSISTANT',
                content: data.message || data.content
            });
            
            // 渲染图表（如果有）
            if (data.chartData) {
                renderApiChart(data.chartData);
            }
            
            // 显示推荐问题（如果有）
            if (data.suggestedQuestions && data.suggestedQuestions.length > 0) {
                showSuggestedQuestions(data.suggestedQuestions);
            }
            
        } catch (error) {
            console.error('处理出错:', error);
            
            // 移除加载状态
            const loadingElement = document.querySelector('.loading');
            if (loadingElement) {
                loadingElement.remove();
            }
            
            // 显示错误消息
            addBotMessage(`<p class="error-message">抱歉，处理您的请求时出现了问题。请稍后再试。</p>`);
        }
    }
    
    // 获取模拟的静态响应
    function getSimulatedResponse(message) {
        // 将消息转换为小写并去除空格，用于简单的关键词匹配
        const normalizedMessage = message.toLowerCase().trim();
        
        // 销售业绩分析响应
        if (normalizedMessage.includes('销售业绩') || normalizedMessage.includes('业绩分析') || normalizedMessage.includes('第二季度')) {
            return {
                message: `
                    <p>我为您生成了第二季度销售业绩概览图表。与第一季度相比，整体销售额下降了15%，特别是5月份表现最差。</p>
                    <div class="chart-container">
                        <h4>商机趋势 (BI_5ddf9655fadde20001b08aa6)</h4>
                        <canvas id="sales-trend-chart"></canvas>
                    </div>
                    <p>主要观察点：</p>
                    <ul>
                        <li>总销售额：¥3.75M，比目标差20%</li>
                        <li>新签约客户：32家，同比下降18%</li>
                        <li>平均交易金额：¥117K，比Q1下降5%</li>
                    </ul>
                    <p>您希望我深入分析哪个方面？比如团队表现、产品线分析或客户类型转化？</p>
                `,
                chartData: {
                    title: '第二季度销售业绩趋势',
                    chartType: 'line',
                    data: {
                        labels: ['4月', '5月', '6月'],
                        datasets: [{
                            label: '实际销售额(万元)',
                            data: [135, 110, 130],
                            borderColor: '#F4B63E',
                            backgroundColor: 'rgba(244, 182, 62, 0.1)',
                            fill: true
                        }, {
                            label: '目标销售额(万元)',
                            data: [150, 155, 160],
                            borderColor: '#999',
                            borderDash: [5, 5],
                            fill: false
                        }]
                    }
                },
                suggestedQuestions: [
                    '请分析下各销售代表的表现情况',
                    '哪些产品线销售下滑最严重？',
                    '客户转化率与上季度相比如何变化？'
                ]
            };
        }
        
        // 销售代表表现分析
        else if (normalizedMessage.includes('销售代表') || normalizedMessage.includes('团队表现') || normalizedMessage.includes('个人业绩')) {
            return {
                message: `
                    <p>已为您生成销售团队个人业绩分析图表，横向比较了各成员的表现：</p>
                    <div class="chart-container">
                        <h4>赢单商机金额排行榜top10 (BI_5dd286aaccbfc00001bf3a58)</h4>
                        <canvas id="sales-rep-chart"></canvas>
                    </div>
                    <p>关键发现：</p>
                    <ul>
                        <li>团队表现分化明显，非普遍性下滑</li>
                        <li>林文、王强和周梅的业绩达成率不到50%</li>
                        <li>李华和陈静保持了较高水平，达成率超过90%</li>
                        <li>新入职的4名销售（&lt;6个月经验）业绩普遍较低</li>
                    </ul>
                    <p>是否需要进一步分析业绩差异背后的原因？例如销售活动数量、线索质量或转化漏斗？</p>
                `,
                chartData: {
                    title: '销售代表业绩对比',
                    chartType: 'bar',
                    data: {
                        labels: ['李华', '陈静', '张明', '刘芳', '王强', '周梅', '林文'],
                        datasets: [{
                            label: '实际销售额(万元)',
                            data: [85, 78, 65, 52, 38, 35, 22],
                            backgroundColor: '#F4B63E'
                        }, {
                            label: '目标销售额(万元)',
                            data: [90, 85, 80, 75, 80, 75, 70],
                            backgroundColor: 'rgba(153, 153, 153, 0.5)'
                        }]
                    }
                },
                suggestedQuestions: [
                    '请分析各销售代表的转化漏斗',
                    '低绩效销售的主要问题是什么？',
                    '高绩效销售有哪些成功经验可以分享？'
                ]
            };
        }
        
        // 转化漏斗分析
        else if (normalizedMessage.includes('转化漏斗') || normalizedMessage.includes('转化率') || normalizedMessage.includes('漏斗分析')) {
            return {
                message: `
                    <p>已生成Q2销售漏斗对比分析，比较了绩效前三名与后三名销售代表的转化情况：</p>
                    <div class="chart-container">
                        <h4>从线索到成交转化分析 (BI_5eb5450ae6835e00010a894b)</h4>
                        <canvas id="conversion-funnel-chart"></canvas>
                    </div>
                    <p>显著差异：</p>
                    <ul>
                        <li>高绩效组初始线索量仅比低绩效组多15%，但最终成交金额高2.8倍</li>
                        <li>关键差异点在演示到方案阶段，高绩效组转化率为62%，低绩效组仅为28%</li>
                        <li>高绩效组平均交易周期为32天，低绩效组为47天</li>
                        <li>高绩效组的客单价高出35%，说明更善于大客户开发</li>
                    </ul>
                    <p>根据分析，问题主要不是线索获取，而是中后期转化能力。您想了解更多关于具体转化障碍的分析，还是想看看高绩效组采用了哪些成功策略？</p>
                `,
                chartData: {
                    title: '销售漏斗转化率对比',
                    chartType: 'bar',
                    data: {
                        labels: ['线索→初步接触', '初步接触→需求确认', '需求确认→演示', '演示→方案', '方案→谈判', '谈判→成交'],
                        datasets: [{
                            label: '高绩效组转化率(%)',
                            data: [85, 72, 68, 62, 58, 52],
                            backgroundColor: '#F4B63E'
                        }, {
                            label: '低绩效组转化率(%)',
                            data: [80, 65, 45, 28, 22, 18],
                            backgroundColor: 'rgba(153, 153, 153, 0.5)'
                        }]
                    }
                },
                suggestedQuestions: [
                    '低绩效组在演示到方案阶段遇到的主要障碍是什么？',
                    '高绩效组采用了哪些成功策略？',
                    '如何提高整体团队的转化率？'
                ]
            };
        }
        
        // 默认响应
        else {
            return {
                message: `
                    <p>我理解您想了解"${message}"。请问您具体需要分析哪些方面的数据？</p>
                    <p>我可以帮助您分析以下几个方面：</p>
                    <ul>
                        <li>销售业绩分析</li>
                        <li>客户流失预警</li>
                        <li>营销活动效果</li>
                        <li>销售漏斗转化率</li>
                        <li>客户细分分析</li>
                    </ul>
                    <p>您可以尝试提问："帮我分析一下我们部门第二季度的销售业绩情况"</p>
                `,
                suggestedQuestions: [
                    '帮我分析一下我们部门第二季度的销售业绩情况',
                    '客户流失预警分析',
                    '销售漏斗转化率分析'
                ]
            };
        }
    }
    
    // 渲染从API返回的图表数据
    function renderApiChart(chartData) {
        // 创建图表容器
        const chartContainer = document.createElement('div');
        chartContainer.className = 'chart-container';
        
        // 生成唯一ID
        const chartId = 'chart-' + Date.now();
        
        chartContainer.innerHTML = `
            <h4>${chartData.title || '数据分析'}</h4>
            <div id="${chartId}" style="height: 300px;"></div>
        `;
        
        // 将图表容器添加到最后一条机器人消息中
        const lastBotMessage = document.querySelector('.bot-message:last-child');
        if (lastBotMessage) {
            lastBotMessage.appendChild(chartContainer);
        } else {
            chatMessages.appendChild(chartContainer);
        }
        
        // 确保图表容器已添加到DOM
        setTimeout(() => {
            // 初始化ECharts实例
            const chart = echarts.init(document.getElementById(chartId));
            
            // 根据图表类型设置不同的配置
            let option = {};
            
            switch(chartData.chartType || 'bar') {
                case 'funnel':
                    option = {
                        tooltip: {
                            trigger: 'item',
                            formatter: '{a} <br/>{b} : {c} ({d}%)'
                        },
                        series: [
                            {
                                name: chartData.seriesName || '数据系列',
                                type: 'funnel',
                                left: '10%',
                                top: 30,
                                bottom: 30,
                                width: '80%',
                                min: 0,
                                max: Math.max(...chartData.data.map(item => item.value || 0)),
                                minSize: '0%',
                                maxSize: '100%',
                                sort: 'descending',
                                gap: 2,
                                label: {
                                    show: true,
                                    position: 'inside'
                                },
                                data: chartData.data || []
                            }
                        ]
                    };
                    break;
                    
                case 'pie':
                    option = {
                        tooltip: {
                            trigger: 'item',
                            formatter: '{a} <br/>{b} : {c} ({d}%)'
                        },
                        legend: {
                            orient: 'vertical',
                            left: 'left',
                            top: 'center'
                        },
                        series: [
                            {
                                name: chartData.seriesName || '数据系列',
                                type: 'pie',
                                radius: '50%',
                                center: ['60%', '50%'],
                                data: chartData.data || [],
                                emphasis: {
                                    itemStyle: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    }
                                }
                            }
                        ]
                    };
                    break;
                    
                case 'bar':
                default:
                    option = {
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow'
                            }
                        },
                        xAxis: {
                            type: 'category',
                            data: chartData.xAxis || [],
                            axisLabel: {
                                interval: 0,
                                rotate: chartData.xAxis && chartData.xAxis.length > 5 ? 30 : 0
                            }
                        },
                        yAxis: {
                            type: 'value'
                        },
                        series: chartData.series || [
                            {
                                type: 'bar',
                                data: chartData.data || [],
                                itemStyle: {
                                    color: '#F4B63E'
                                }
                            }
                        ]
                    };
                    break;
            }
            
            // 设置图表选项
            chart.setOption(option);
            
            // 添加窗口大小变化时的重绘
            window.addEventListener('resize', function() {
                chart.resize();
            });
        }, 0);
        
        return chartContainer;
    }
    
    // 显示推荐问题
    function showSuggestedQuestions(questions) {
        const suggestedContainer = document.createElement('div');
        suggestedContainer.className = 'suggested-questions';
        suggestedContainer.innerHTML = '<h4>推荐问题：</h4>';
        
        const questionsList = document.createElement('div');
        questionsList.className = 'questions-list';
        
        questions.forEach(question => {
            const questionItem = document.createElement('div');
            questionItem.className = 'question-item';
            questionItem.textContent = question;
            questionItem.addEventListener('click', () => {
                userInput.value = question;
                sendMessage();
            });
            questionsList.appendChild(questionItem);
        });
        
        suggestedContainer.appendChild(questionsList);
        
        // 将推荐问题添加到最后一条机器人消息中
        const lastBotMessage = document.querySelector('.bot-message:last-child');
        if (lastBotMessage) {
            lastBotMessage.appendChild(suggestedContainer);
        }
    }
    
    // 添加用户消息到聊天界面
    function addUserMessage(message) {
        const messageElement = document.createElement('div');
        messageElement.className = 'message user-message';
        messageElement.innerHTML = `<p>${message}</p>`;
        chatMessages.appendChild(messageElement);
        
        // 添加新消息时滚动到底部
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
    
    // 添加机器人消息到聊天界面
    function addBotMessage(message) {
        const messageElement = document.createElement('div');
        messageElement.className = 'message bot-message';
        messageElement.innerHTML = message;
        chatMessages.appendChild(messageElement);
        
        // 添加新消息时滚动到底部
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
    
    // 生成会话ID
    function generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    // 设置用户画像面板
    function setupUserProfilePanel() {
        console.log('初始化用户画像面板...');
        // 这里可以添加用户画像面板的初始化逻辑
        // 由于我们已经在DOMContentLoaded事件中设置了profileToggle的点击事件
        // 这里不需要额外的逻辑
    }
    
    // 设置反馈组件
    function setupFeedbackComponent() {
        const stars = document.querySelectorAll('.star');
        
        stars.forEach(star => {
            star.addEventListener('click', function() {
                const value = this.getAttribute('data-value');
                
                // 重置所有星星
                stars.forEach(s => s.classList.remove('active'));
                
                // 设置选中的星星
                for (let i = 0; i < value; i++) {
                    stars[i].classList.add('active');
                }
                
                // 启用提交按钮
                feedbackSubmit.disabled = false;
            });
        });
        
        // 提交反馈事件
        feedbackSubmit.addEventListener('click', function() {
            // 这里可以添加将反馈发送到服务器的逻辑
            console.log('发送反馈:', {
                rating: stars.filter(s => s.classList.contains('active')).length,
                comment: feedbackComment.value,
                sessionId: sessionId
            });
            
            // 显示成功消息
            feedbackSuccess.style.display = 'block';
            setTimeout(() => {
                feedbackSuccess.style.display = 'none';
                // 重置表单
                stars.forEach(s => s.classList.remove('active'));
                feedbackComment.value = '';
                feedbackSubmit.disabled = true;
            }, 3000);
        });
    }
    
    // 初始化分析评价
    function initAnalysisRatings() {
        const analysisTypes = [
            { type: '销售业绩分析', rating: 4.8 },
            { type: '客户流失预警', rating: 4.5 },
            { type: '营销活动分析', rating: 4.2 }
        ];
        
        const ratingsContainer = document.getElementById('analysis-ratings');
        
        analysisTypes.forEach(item => {
            const ratingElement = document.createElement('div');
            ratingElement.className = 'preference-item';
            
            const starsHtml = generateStars(item.rating);
            
            ratingElement.innerHTML = `
                <span class="label">${item.type}:</span>
                <span class="rating-stars-display">${starsHtml}</span>
                <span class="rating-value">${item.rating.toFixed(1)}</span>
            `;
            
            ratingsContainer.appendChild(ratingElement);
        });
    }
    
    // 生成星星HTML
    function generateStars(rating) {
        const fullStars = Math.floor(rating);
        const halfStar = rating % 1 >= 0.5;
        const emptyStars = 5 - fullStars - (halfStar ? 1 : 0);
        
        let html = '';
        
        // 全星
        for (let i = 0; i < fullStars; i++) {
            html += '<span class="star-display full">★</span>';
        }
        
        // 半星
        if (halfStar) {
            html += '<span class="star-display half">★</span>';
        }
        
        // 空星
        for (let i = 0; i < emptyStars; i++) {
            html += '<span class="star-display empty">☆</span>';
        }
        
        return html;
    }
}); 