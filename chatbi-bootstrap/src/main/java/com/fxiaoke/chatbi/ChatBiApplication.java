// 声明包名，指定该类所在的包路径
package com.fxiaoke.chatbi;

// 导入Spring Boot应用程序类
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * ChatBI应用程序入口
 */
// 标记这是一个Spring Boot应用程序，启用自动配置和组件扫描
@SpringBootApplication
@ComponentScan(basePackages = {"com.fxiaoke.chatbi.memory", "com.fxiaoke.chatbi.knowledge", "com.fxiaoke.chatbi.planning", "com.fxiaoke.chatbi.action", "com.fxiaoke.chatbi.service", "com.fxiaoke.chatbi.common", "com.fxiaoke.chatbi.controller", "com.fxiaoke.chatbi.integration", "com.fxiaoke.chatbi"})
public class ChatBiApplication extends SpringBootServletInitializer {

  // 应用程序的主方法，程序执行的入口点
  public static void main(String[] args) {
    // 启动Spring Boot应用程序，将当前类作为配置源
    SpringApplication.run(ChatBiApplication.class, args);
  }

  /**
   * 配置CORS，允许前端跨域访问
   */
  // 定义一个Bean，用于配置跨域资源共享(CORS)
  @Bean
  public WebMvcConfigurer corsConfigurer() {
    // 返回WebMvcConfigurer接口的匿名实现类
    return new WebMvcConfigurer() {
      // 重写addCorsMappings方法，配置CORS策略
      @Override
      public void addCorsMappings(CorsRegistry registry) {
        // 对所有/api/**路径的请求应用CORS配置
        registry.addMapping("/api/**")
                // 允许所有来源的请求
                .allowedOrigins("*")
                // 允许GET、POST、PUT、DELETE和OPTIONS请求方法
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                // 允许所有请求头
                .allowedHeaders("*");
      }
    };
  }
}