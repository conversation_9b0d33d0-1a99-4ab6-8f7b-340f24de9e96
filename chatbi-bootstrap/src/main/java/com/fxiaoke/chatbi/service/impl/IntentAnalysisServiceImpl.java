package com.fxiaoke.chatbi.service.impl;

import com.fxiaoke.chatbi.action.core.Action;
import com.fxiaoke.chatbi.action.core.ActionFactory;
import com.fxiaoke.chatbi.action.core.ActionResult;
import com.fxiaoke.chatbi.action.impl.knowledge.KnowledgeRetrievalAction;
import com.fxiaoke.chatbi.common.exception.ChatbiErrorCodeEnum;
import com.fxiaoke.chatbi.common.exception.IntentAnalysisException;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.action.ActionContext;
import com.fxiaoke.chatbi.common.model.action.ActionInput;
import com.fxiaoke.chatbi.common.model.action.ActionType;
import com.fxiaoke.chatbi.common.model.action.input.IntentInput;
import com.fxiaoke.chatbi.common.model.action.input.QueryPreProcessInput;
import com.fxiaoke.chatbi.common.model.action.output.IntentOutput;
import com.fxiaoke.chatbi.common.model.action.output.QueryPreProcessOutput;
import com.fxiaoke.chatbi.common.model.intent.IntentAnalysisResult;
import com.fxiaoke.chatbi.common.model.intent.KnowledgeScope;
import com.fxiaoke.chatbi.common.model.intent.UserIntent;
import com.fxiaoke.chatbi.common.model.memory.ConversationContext;
import com.fxiaoke.chatbi.common.model.reasoning.ReasoningCollector;
import com.fxiaoke.chatbi.common.sse.event.factory.SseEventFactory;
import com.fxiaoke.chatbi.common.sse.publisher.SseEventPublisher;
import com.fxiaoke.chatbi.service.IntentAnalysisService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 意图分析服务实现类
 * 负责查询预处理、意图识别、知识检索和DSL生成
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IntentAnalysisServiceImpl implements IntentAnalysisService {

  private final ActionFactory actionFactory;
  private final SseEventPublisher eventPublisher;
  private final SseEventFactory sseEventFactory;



  @Override
  public IntentAnalysisResult analyzeIntent(String query,
                                            ConversationContext sessionContext,
                                            UserIdentity userIdentity,
                                            String llmModel,
                                            ReasoningCollector reasoningCollector) {
    log.info("开始意图分析, 会话ID: {}, 查询: {}", sessionContext.getSessionId(), query);
    
    try {
      // 1. 问题预处理
      String processedQuery = preprocessQuery(query, userIdentity, llmModel, reasoningCollector, sessionContext.getSessionId());
      if (processedQuery == null) {
        log.warn("查询预处理失败, 原始查询: {}", query);
        throw new IntentAnalysisException(ChatbiErrorCodeEnum.PREPROCESSING_FAILED, "查询预处理失败");
      }
      log.info("查询预处理完成: {}", processedQuery);

      // 2. 意图识别
      UserIntent intent = recognizeIntent(processedQuery, sessionContext, userIdentity, llmModel, reasoningCollector);
      if (intent == null) {
        log.warn("意图识别失败, 处理后查询: {}", processedQuery);
        throw new IntentAnalysisException(ChatbiErrorCodeEnum.INTENT_RECOGNITION_FAILED, "意图识别失败");
      }
      log.info("意图识别完成, 类型: {}", intent.getIntentType());

      // 3. 知识检索
      KnowledgeScope knowledgeScope = retrieveKnowledge(intent, userIdentity, llmModel, reasoningCollector, sessionContext.getSessionId());
      if (knowledgeScope == null) {
        log.warn("知识检索失败, 意图类型: {}", intent.getIntentType());
        throw new IntentAnalysisException(ChatbiErrorCodeEnum.KNOWLEDGE_RETRIEVAL_FAILED, "知识检索失败");
      }
      log.info("知识检索完成");

      // 4. 返回分析结果
      return IntentAnalysisResult.success(processedQuery, intent, knowledgeScope);
    } catch (IntentAnalysisException e) {
      // 直接抛出已包装的异常
      throw e;
    } catch (Exception e) {
      log.error("意图分析过程中发生异常", e);
      throw new IntentAnalysisException(ChatbiErrorCodeEnum.INTENT_ANALYSIS_FAILED, "意图分析异常: " + e.getMessage());
    }
  }

  /**
   * 执行查询预处理
   * 对原始查询进行术语替换和标准化
   */
  private String preprocessQuery(String originalQuery, UserIdentity userIdentity, String llmModel, ReasoningCollector reasoningCollector, String sessionId) {
    ActionContext preprocessContext = new ActionContext(sessionId);
    preprocessContext.setUserIdentity(userIdentity);
    preprocessContext.setLlmModel(llmModel);

    QueryPreProcessInput preprocessInput = new QueryPreProcessInput();
    preprocessInput.setOriginalQuery(originalQuery);
    preprocessContext.setReasoningCollector(reasoningCollector);

    try {
      Action<ActionInput, QueryPreProcessOutput> preprocessAction = actionFactory.getAction(ActionType.QUERY_PREPROCESSING);
      ActionResult<QueryPreProcessOutput> preprocessResult = preprocessAction.execute(preprocessInput, preprocessContext);

      if (!preprocessResult.isSuccess()) {
        log.warn("查询预处理失败: {}", preprocessResult.getErrorMsg());
        return null;
      }

      return preprocessResult.getData().getProcessedQuery();
    } catch (Exception e) {
      log.error("查询预处理异常", e);
      return null;
    }
  }

  /**
   * 执行意图识别
   * 识别用户查询的意图类型和关键信息
   */
  private UserIntent recognizeIntent(String processedQuery,
                                     ConversationContext context,
                                     UserIdentity userIdentity,
                                     String llmModel,
                                     ReasoningCollector reasoningCollector) {
    try {
      ActionContext intentContext = new ActionContext(context.getSessionId());
      intentContext.setUserIdentity(userIdentity);
      intentContext.setLlmModel(llmModel);
      intentContext.setReasoningCollector(reasoningCollector);

      IntentInput intentInput = new IntentInput();
      intentInput.setQuery(processedQuery);
      intentInput.setContext(context);


      Action<ActionInput, IntentOutput> intentAction = actionFactory.getAction(ActionType.INTENT_RECOGNITION);
      ActionResult<IntentOutput> intentResult = intentAction.execute(intentInput, intentContext);

      if (!intentResult.isSuccess()) {
        log.warn("意图识别失败: {}", intentResult.getErrorMsg());
        return null;
      }
      // 获取推理内容
      String reasoning = reasoningCollector.getReasoningsMap().get(ActionType.INTENT_RECOGNITION.name());

      eventPublisher.publish(context.getSessionId(),
              sseEventFactory.createThinkingEvent(reasoning + "\n", true));

      return intentResult.getData().getIntent();
    } catch (Exception e) {
      log.error("意图识别异常", e);
      return null;
    }
  }

  /**
   * 执行知识检索
   * 根据用户意图检索相关知识
   */
  private KnowledgeScope retrieveKnowledge(UserIntent intent,
                                           UserIdentity userIdentity,
                                           String llmModel,
                                           ReasoningCollector reasoningCollector,
                                           String sessionId) {
    try {
      ActionContext knowledgeContext = new ActionContext(sessionId);
      knowledgeContext.setUserIdentity(userIdentity);
      knowledgeContext.setLlmModel(llmModel);
      knowledgeContext.setReasoningCollector(reasoningCollector);
      KnowledgeRetrievalAction.Input input = new KnowledgeRetrievalAction.Input();
      input.setUserIntent(intent);

      Action<ActionInput, KnowledgeScope> knowledgeAction = actionFactory.getAction(ActionType.KNOWLEDGE_RETRIEVAL);
      ActionResult<KnowledgeScope> knowledgeOutput = knowledgeAction.execute(input, knowledgeContext);
      // 获取推理内容
      String reasoning = reasoningCollector.getReasoningsMap().get(ActionType.KNOWLEDGE_RETRIEVAL.name());

      eventPublisher.publish(sessionId,
              sseEventFactory.createThinkingEvent(reasoning + "\n", true));
      return knowledgeOutput.getData();
    } catch (Exception e) {
      log.error("知识检索异常", e);
      return null;
    }
  }
}