package com.fxiaoke.chatbi.service.impl;

import com.fxiaoke.chatbi.common.config.RedisConfig;
import com.fxiaoke.chatbi.common.exception.ChatbiErrorCodeEnum;
import com.fxiaoke.chatbi.common.exception.IntentAnalysisException;
import com.fxiaoke.chatbi.common.model.LoadingStatus;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.intent.IntentAnalysisResult;
import com.fxiaoke.chatbi.common.model.memory.ConversationContext;
import com.fxiaoke.chatbi.common.model.reasoning.ReasoningCollector;
import com.fxiaoke.chatbi.common.model.request.ChatBiRequest;
import com.fxiaoke.chatbi.common.model.response.*;
import com.fxiaoke.chatbi.common.sse.event.factory.SseEventFactory;
import com.fxiaoke.chatbi.common.sse.publisher.SseEventPublisher;
import com.fxiaoke.chatbi.memory.service.ConversationService;
import com.fxiaoke.chatbi.planning.context.Plan;
import com.fxiaoke.chatbi.planning.context.PlanContext;
import com.fxiaoke.chatbi.planning.flow.analysis.AsyncDataManager;
import com.fxiaoke.chatbi.planning.service.PlanningService;
import com.fxiaoke.chatbi.service.ChatBiService;
import com.fxiaoke.chatbi.service.IntentAnalysisService;
import com.fxiaoke.chatbi.service.QueryLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.UUID;

/**
 * ChatBI服务实现类
 * 作为系统主入口，协调意图分析、计划创建和执行流程
 * 集中管理会话上下文
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChatBiServiceImpl implements ChatBiService {

    private final PlanningService planningService;
    private final IntentAnalysisService intentAnalysisService;
    private final ConversationService conversationService;
    private final AsyncDataManager asyncDataManager;
    private final SseEventPublisher eventPublisher;
    private final SseEventFactory eventFactory;
    private final QueryLogService queryLogService;

    @Override
    public ReasoningResponse chat(ChatBiRequest request, UserIdentity userIdentity) {
        String requestId = UUID.randomUUID().toString();
        String sessionId = request.getSessionId();
        String instructions = request.getInstructions();

        log.info("处理BI查询指令, 请求ID: {}, 用户: {}, 指令: {}", requestId, userIdentity.getUserId(), instructions);

        try {
            long startTime = System.currentTimeMillis();
            // 1. 获取或创建会话上下文
            ConversationContext conversationContext = conversationService.getOrCreateContext(sessionId, userIdentity);

            ReasoningCollector reasoningCollector = new ReasoningCollector();

            // 2. 意图分析阶段
            IntentAnalysisResult analysisResult = intentAnalysisService.analyzeIntent(instructions, conversationContext,
                    userIdentity, request.getLlmModel(), reasoningCollector);

            if (!analysisResult.isSuccess()) {
                log.warn("意图分析失败: {}", analysisResult.getErrorMessage());
                throw new IntentAnalysisException(ChatbiErrorCodeEnum.INTENT_ANALYSIS_FAILED,
                        analysisResult.getErrorMessage());
            }

            // 3. 计划创建和执行
            PlanContext planContext = planningService.initPlanContext(analysisResult, conversationContext,
                    request.getLlmModel(), requestId, reasoningCollector);
            Plan plan = planningService.createPlan(planContext);
            ReasoningResponse reasoningResponse = planningService.executePlan(planContext, plan);

            // 4. 更新会话意图信息
            conversationService.updateIntentInfo(conversationContext, analysisResult.getIntent(), requestId);

            // 5.保存查询
            queryLogService.recordQueryLogAsync(planContext, reasoningCollector, startTime);
            return reasoningResponse;

        } catch (IntentAnalysisException e) {
            throw e; // 已包装的异常直接抛出
        } catch (Exception e) {
            log.error("处理BI查询异常", e);
            throw new IntentAnalysisException(ChatbiErrorCodeEnum.SYSTEM_ERROR, "处理BI查询失败: " + e.getMessage());
        }
    }

    @Override
    public void chatWithSse(ChatBiRequest request, UserIdentity userIdentity, String sessionId) {
        String requestId = UUID.randomUUID().toString();
        String instructions = request.getInstructions();

        log.info("通过SSE处理BI查询指令, 请求ID: {}, 会话ID: {}, 用户: {}, 指令: {}",
                requestId, sessionId, userIdentity, instructions);

        try {
            // 1. 获取或创建会话上下文
            ConversationContext conversationContext = conversationService.getOrCreateContext(sessionId, userIdentity);

            ReasoningCollector reasoningCollector = new ReasoningCollector();

            // 2. 意图分析阶段
            IntentAnalysisResult analysisResult = intentAnalysisService.analyzeIntent(
                    instructions, conversationContext, userIdentity, request.getLlmModel(), reasoningCollector);

            if (!analysisResult.isSuccess()) {
                log.warn("意图分析失败: {}", analysisResult.getErrorMessage());
                // 发送错误事件
                eventPublisher.publish(sessionId,
                        eventFactory.createDataEvent(
                                ChartDataResponse.builder()
                                        .loadingStatus(LoadingStatus.ERROR)
                                        .build(),
                                false));
                eventPublisher.complete(sessionId);
                throw new IntentAnalysisException(ChatbiErrorCodeEnum.INTENT_ANALYSIS_FAILED,
                        analysisResult.getErrorMessage());
            }


            // 4. 计划创建和执行
            PlanContext planContext = planningService.initPlanContext(
                    analysisResult, conversationContext, request.getLlmModel(), requestId, reasoningCollector);
            planContext.setSessionId(sessionId); // 设置会话ID用于SSE事件发布

            Plan plan = planningService.createPlan(planContext);
            planningService.executePlan(planContext, plan);

            // 5. 更新会话意图信息
            conversationService.updateIntentInfo(conversationContext, analysisResult.getIntent(), requestId);

            // 注意：事件完成逻辑由各Action异步处理
            // 例如在AsyncDataManager中调用eventPublisher.complete(sessionId)

        } catch (IntentAnalysisException e) {
            // 异常已经在前面处理
            throw e;
        } catch (Exception e) {
            log.error("通过SSE处理BI查询异常", e);
            // 发送错误事件
            try {
                eventPublisher.publish(sessionId,
                        eventFactory.createDataEvent(
                                ChartDataResponse.builder()
                                        .loadingStatus(LoadingStatus.ERROR)
                                        .build(),
                                false));
                eventPublisher.complete(sessionId);
            } catch (Exception ex) {
                log.error("发送错误事件失败", ex);
            }
            throw new IntentAnalysisException(ChatbiErrorCodeEnum.SYSTEM_ERROR, "处理BI查询失败: " + e.getMessage());
        }
    }

    @Override
    public ChartDataResponse loadChartData(String requestId, UserIdentity userIdentity) {
        ChartDataResponse response = asyncDataManager.getResult(requestId, RedisConfig.CHATBI_DATA_KEY_PREFIX,
                ChartDataResponse.class);
        if (Objects.nonNull(response)) {
            ActionLogResponse actionLogResponse = asyncDataManager.getResult(requestId,
                    RedisConfig.CHATBI_ACRTION_LOG_KEY_PREFIX, ActionLogResponse.class);
            response.setActionLog(actionLogResponse != null ? actionLogResponse.getActionLog() : null);
        }
        return response != null ? response : createChartDataLoadingResponse(requestId);
    }

    @Override
    public InsightResponse loadInsight(String requestId, UserIdentity userIdentity) {
        InsightResponse response = asyncDataManager.getResult(requestId, RedisConfig.CHATBI_INSIGHT_KEY_PREFIX,
                InsightResponse.class);
        return response != null ? response : createInsightLoadingResponse(requestId);
    }

    @Override
    public FollowUpResponse loadFollowUpQuestions(String requestId, UserIdentity userIdentity) {
        FollowUpResponse response = asyncDataManager.getResult(requestId, RedisConfig.CHATBI_FOLLOWUP_KEY_PREFIX,
                FollowUpResponse.class);
        return response != null ? response : createFollowUpLoadingResponse(requestId);
    }

    /**
     * 创建图表数据加载中的响应
     */
    private ChartDataResponse createChartDataLoadingResponse(String requestId) {
        return ChartDataResponse.builder().requestId(requestId).loadingStatus(LoadingStatus.LOADING).build();
    }

    /**
     * 创建解读数据加载中的响应
     */
    private InsightResponse createInsightLoadingResponse(String requestId) {
        return InsightResponse.builder().requestId(requestId).loadingStatus(LoadingStatus.LOADING).build();
    }

    /**
     * 创建追问建议加载中的响应
     */
    private FollowUpResponse createFollowUpLoadingResponse(String requestId) {
        return FollowUpResponse.builder().requestId(requestId).loadingStatus(LoadingStatus.LOADING).build();
    }
}
