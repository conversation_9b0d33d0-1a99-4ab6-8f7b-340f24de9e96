package com.fxiaoke.chatbi.service;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.intent.IntentAnalysisResult;
import com.fxiaoke.chatbi.common.model.memory.ConversationContext;
import com.fxiaoke.chatbi.common.model.reasoning.ReasoningCollector;

/**
 * 意图分析服务
 * 负责查询预处理、意图识别和知识范围检索
 */
public interface IntentAnalysisService {
    
    /**
     * 分析用户意图
     *
     * @param query              原始查询文本
     * @param sessionContext     会话上下文
     * @param userIdentity       用户身份
     * @param llmModel           LLM模型名称
     * @param reasoningCollector
     * @return 意图分析结果
     */
    IntentAnalysisResult analyzeIntent(String query,
                                       ConversationContext sessionContext,
                                       UserIdentity userIdentity,
                                       String llmModel,
                                       ReasoningCollector reasoningCollector);

}