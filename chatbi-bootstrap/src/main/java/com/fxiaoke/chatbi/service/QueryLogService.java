package com.fxiaoke.chatbi.service;

import com.fxiaoke.chatbi.common.model.reasoning.ReasoningCollector;
import com.fxiaoke.chatbi.planning.context.PlanContext;

/**
 * 负责记录查询链路日志
 */
public interface QueryLogService {

    void recordQueryLog(PlanContext planContext, ReasoningCollector reasoningCollector, long startTime);

    void recordQueryLogAsync(PlanContext planContext, ReasoningCollector reasoningCollector, long startTime);
}
