package com.fxiaoke.chatbi.service.impl;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.chatbi.common.config.RedisConfig;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.intent.UserIntent;
import com.fxiaoke.chatbi.common.model.memory.ConversationContext;
import com.fxiaoke.chatbi.common.model.reasoning.ReasoningCollector;
import com.fxiaoke.chatbi.common.model.response.ChartDataResponse;
import com.fxiaoke.chatbi.common.model.response.FollowUpResponse;
import com.fxiaoke.chatbi.common.model.response.InsightResponse;
import com.fxiaoke.chatbi.common.model.response.ReasoningResponse;
import com.fxiaoke.chatbi.integration.dto.ResponseComponent;
import com.fxiaoke.chatbi.integration.model.ch.accuracy.QueryLog;
import com.fxiaoke.chatbi.integration.repository.ch.accuracy.QueryLogRepository;
import com.fxiaoke.chatbi.integration.utils.MarkdownUtil;
import com.fxiaoke.chatbi.planning.context.PlanContext;
import com.fxiaoke.chatbi.planning.flow.analysis.AsyncDataManager;
import com.fxiaoke.chatbi.service.QueryLogService;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 查询链路日志实现类
 * 主要负责记录查询链路日志 填充测试集数据
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class QueryLogServiceImpl implements QueryLogService {

    private final QueryLogRepository queryLogRepository;
    private final AsyncDataManager asyncDataManager;


    @Override
    public void recordQueryLog(PlanContext planContext, ReasoningCollector reasoningCollector, long startTime) {
        ConversationContext conversationContext = planContext.getConversationContext();
        UserIdentity userIdentity = conversationContext.getUserIdentity();

        log.info("开始记录查询链路日志 会话ID: {}, 企业:{}, 用户:{}", conversationContext.getSessionId(), userIdentity.getTenantId(),
                userIdentity.getUserId());
        QueryLog queryLog = buildQueryLog(planContext, reasoningCollector, startTime);
        queryLogRepository.save(queryLog);
    }

    @Async
    @Override
    public void recordQueryLogAsync(PlanContext planContext, ReasoningCollector reasoningCollector, long startTime) {
        try {
            doRecordQueryLog(planContext, reasoningCollector, startTime);
        } catch (Exception e) {
            log.error("异步记录查询日志失败", e);
        }
    }

    private void doRecordQueryLog(PlanContext planContext, ReasoningCollector reasoningCollector, long startTime) {
        ConversationContext conversationContext = planContext.getConversationContext();
        UserIdentity userIdentity = conversationContext.getUserIdentity();
        UserIntent userIntent = planContext.getUserIntent();
        long endTime = System.currentTimeMillis();
        QueryLog queryLog = QueryLog.builder()
                .id(IdGenerator.get())
                .traceId(Objects.nonNull(TraceContext.get().getTraceId()) ? TraceContext.get().getTraceId() : "trace_test_001")
                .sessionId(conversationContext.getSessionId())
                .tenantId(userIdentity.getTenantId())
                .userId(userIdentity.getUserId())
                .tags(getTags())
                .query(userIntent.getInstructions())
                .response(getResponse(reasoningCollector))
                .responseComponents(getResponseComponents(planContext, reasoningCollector))
                .responseTime(endTime - startTime)
                .isContextEnabled(1)
                .source("WEB")
                .feedback(0)
                .feedbackComment("")
                .isAnnotated(0)
                .queryTime(startTime)
                .createdBy(userIdentity.getUserId())
                .createTime(System.currentTimeMillis())
                .actionLogs(getActionLogs(reasoningCollector))
                .lastModifiedTime(System.currentTimeMillis())
                .build();
        queryLogRepository.save(queryLog);
    }

    public String getResponse(ReasoningCollector reasoningCollector) {
        Map<String, String> reasoningsMap = reasoningCollector.getReasoningsMap();
        return reasoningsMap.values().stream().map(MarkdownUtil::markdownToHtml).collect(Collectors.joining("\n"));
    }

    public String getResponseComponents(PlanContext planContext, ReasoningCollector reasoningCollector) {
        // 构建推理响应
        String reasoning = getResponse(reasoningCollector);
        ReasoningResponse reasoningResponse = ReasoningResponse.builder()
                .reasoning(reasoning)
                .build();

        // 获取洞察响应
        InsightResponse insightResponse = asyncDataManager.getResult(planContext.getPlanId(), RedisConfig.CHATBI_INSIGHT_KEY_PREFIX,
                InsightResponse.class);

        // 获取追问响应
        FollowUpResponse followUpResponse = asyncDataManager.getResult(planContext.getPlanId(), RedisConfig.CHATBI_FOLLOWUP_KEY_PREFIX,
                FollowUpResponse.class);

        // 获取图表数据响应（如果有的话）
        ChartDataResponse chartDataResponse = asyncDataManager.getResult(planContext.getPlanId(), RedisConfig.CHATBI_DATA_KEY_PREFIX,
                ChartDataResponse.class);

        // 构建完整的响应组件
        ResponseComponent responseComponent = ResponseComponent.builder()
                .reasoningResponse(reasoningResponse)
                .chartDataResponse(chartDataResponse)
                .insightResponse(insightResponse)
                .followUpResponse(followUpResponse)
                .build();

        return JSON.toJSONString(responseComponent);
    }

    public String getActionLogs(ReasoningCollector reasoningCollector) {
        Map<String, String> actionLog = reasoningCollector.getActionLog();
        return actionLog.values().stream().map(MarkdownUtil::markdownToHtml).collect(Collectors.joining("\n"));
    }

    public List<String> getTags() {
        return Lists.newArrayList("tag1", "tag2");
    }

    public QueryLog buildQueryLog(PlanContext planContext, ReasoningCollector reasoningCollector, long startTime) {
        ConversationContext conversationContext = planContext.getConversationContext();
        UserIdentity userIdentity = conversationContext.getUserIdentity();
        UserIntent userIntent = planContext.getUserIntent();
        long endTime = System.currentTimeMillis();
        QueryLog queryLog = QueryLog.builder()
                .id(IdGenerator.get())
                .traceId(TraceContext.get().getTraceId())
                .sessionId(conversationContext.getSessionId())
                .tenantId(userIdentity.getTenantId())
                .userId(userIdentity.getUserId())
                .query(userIntent.getInstructions())
                .response("ok")
                .responseTime(endTime - startTime)
                .isContextEnabled(0)
                .source("WEB")
                .queryTime(startTime)
                .actionLogs(JSON.toJSONString(reasoningCollector.getActionLog()))
                .build();
        return queryLog;
    }
}
