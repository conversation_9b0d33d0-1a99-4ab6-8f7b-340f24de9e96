package com.fxiaoke.chatbi.service.impl;

import com.facishare.bi.metadata.context.dto.ads.StatView;
import com.facishare.bi.metadata.context.service.ads.IChartService;
import com.facishare.cep.plugin.model.UserInfo;
import com.fxiaoke.chatbi.common.config.KnowledgeProperties;
import com.fxiaoke.chatbi.common.model.Recommendation;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.response.RecommendationResponse;
import com.fxiaoke.chatbi.service.RecommendationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 推荐服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RecommendationServiceImpl implements RecommendationService {

  private final KnowledgeProperties knowledgeProperties;
  private final IChartService chartService;
  List<Recommendation> recommendations;


  @PostConstruct
  public void init() {
    List<String> chartIdList = knowledgeProperties.getRecommendChartIdList();
    if (chartIdList.isEmpty()) {
      log.warn("No recommended chart IDs configured");
      return;
    }

    log.info("Loading {} recommended charts", chartIdList.size());
    UserInfo systemUserInfo = createSystemUserInfo();
    recommendations = getRecommendationsByChartIds(chartIdList, systemUserInfo);
    log.info("Loaded {} recommended charts", recommendations.size());
  }

  /**
   * 根据图表ID列表获取推荐图表列表
   */
  private List<Recommendation> getRecommendationsByChartIds(List<String> chartIds, UserInfo userInfo) {
    if (chartIds == null || chartIds.isEmpty()) {
      return Collections.emptyList();
    }

    List<StatView> views = chartService.getViewListByViewIds(chartIds, userInfo);

    // 将视图列表转换为ID到视图的映射
    Map<String, StatView> viewMap = views.stream().collect(Collectors.toMap(StatView::getViewId, Function.identity()));

    // 按照原始chartIds的顺序创建推荐列表
    List<Recommendation> orderedRecommendations = new ArrayList<>();
    for (String chartId : chartIds) {
      StatView view = viewMap.get(chartId);
      if (view != null) {
        orderedRecommendations.add(createChartRecommendation(view.getViewId(), view.getViewName(), view.getDescription(), view.getChartType()));
      }
    }

    return orderedRecommendations;
  }


  /**
   * 创建系统用户信息，用于获取图表数据
   */
  private UserInfo createSystemUserInfo() {
    UserInfo userInfo = new UserInfo();
    userInfo.setEnterpriseId(-1);
    userInfo.setEnterpriseAccount("-1");
    userInfo.setEmployeeId(-10000);
    return userInfo;
  }

  /**
   * 创建图表类型推荐
   */
  private Recommendation createChartRecommendation(String id, String text, String description, String chartType) {
    Map<String, Object> params = new HashMap<>();
    return Recommendation.builder()
                         .id(id)
                         .type(Recommendation.RecommendationType.CHART)
                         .text(text)
                         .description(description)
                         .chartType(chartType)
                         .params(params)
                         .build();
  }


  @Override
  public RecommendationResponse recommendations(UserIdentity userIdentity) {
    log.info("获取推荐问题, 用户: {}", userIdentity.getUserId());
    return RecommendationResponse.builder().recommendations(recommendations).build();
  }
} 