package com.fxiaoke.chatbi.service;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.request.ChatBiRequest;
import com.fxiaoke.chatbi.common.model.response.ChartDataResponse;
import com.fxiaoke.chatbi.common.model.response.InsightResponse;
import com.fxiaoke.chatbi.common.model.response.ReasoningResponse;
import com.fxiaoke.chatbi.common.model.response.FollowUpResponse;

/**
 * ChatBI service interface
 */
public interface ChatBiService {

    /**
     * 处理查询并返回推理结果（第一阶段）
     *
     * @param request      聊天请求
     * @param userIdentity 用户身份信息
     * @return 推理响应
     */
    ReasoningResponse chat(ChatBiRequest request, UserIdentity userIdentity);

    /**
     * 根据请求ID加载图表数据（第二阶段）
     *
     * @param requestId    请求ID
     * @param userIdentity 用户身份信息
     * @return 图表数据响应
     */
    ChartDataResponse loadChartData(String requestId, UserIdentity userIdentity);

    /**
     * 根据请求ID加载解读数据（第三阶段）
     *
     * @param requestId    请求ID
     * @param userIdentity 用户身份信息
     * @return 解读响应
     */
    InsightResponse loadInsight(String requestId, UserIdentity userIdentity);

    /**
     * 根据请求ID加载追问建议（第四阶段）
     *
     * @param requestId    请求ID
     * @param userIdentity 用户身份信息
     * @return 追问建议响应
     */
    FollowUpResponse loadFollowUpQuestions(String requestId, UserIdentity userIdentity);

    /**
     * 通过SSE处理聊天请求
     * 
     * @param request      聊天请求
     * @param userIdentity 用户身份
     * @param sessionId    会话ID，用于发布SSE事件
     */
    void chatWithSse(ChatBiRequest request, UserIdentity userIdentity, String sessionId);
}