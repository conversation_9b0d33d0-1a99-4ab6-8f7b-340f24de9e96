package com.fxiaoke.chatbi.action.event.listener;

import com.fxiaoke.chatbi.planning.event.NodeStatusEvent;
import com.fxiaoke.chatbi.planning.model.ExecutionStatus;
import com.fxiaoke.chatbi.common.model.LoadingStatus;
import com.fxiaoke.chatbi.common.model.MessageType;
import com.fxiaoke.chatbi.common.model.ResponseType;
import com.fxiaoke.chatbi.common.model.action.ActionType;
import com.fxiaoke.chatbi.common.model.response.ChartDataResponse;
import com.fxiaoke.chatbi.common.sse.event.factory.SseEventFactory;
import com.fxiaoke.chatbi.common.sse.publisher.SseEventPublisher;
import com.fxiaoke.chatbi.common.event.SessionCompletedEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 节点状态SSE事件监听器
 * 监听节点状态事件，发布相应的SSE事件
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class NodeStatusSseEventListener {
    private final SseEventPublisher eventPublisher;
    private final SseEventFactory eventFactory;

    // 记录已经发送过loading事件的会话ID和类型
    private final Map<String, Set<ResponseType>> sessionLoadingEventsSent = new ConcurrentHashMap<>();

    /**
     * 处理节点状态事件
     * 
     * @param event 节点状态事件
     */
    @EventListener
    public void handleNodeStatusEvent(NodeStatusEvent event) {
        String sessionId = event.getSessionId();
        if (StringUtils.isBlank(sessionId)) {
            log.warn("收到节点状态事件但会话ID为空，无法发送SSE事件: actionType={}", event.getActionType());
            return;
        }

        ActionType actionType = event.getActionType();
        ExecutionStatus status = event.getStatus();
        
        log.debug("处理节点状态事件: sessionId={}, actionType={}, status={}", sessionId, actionType, status);

        // 处理节点开始执行（发送loading状态和推理信息）
        if (status == ExecutionStatus.RUNNING) {
            handleNodeStarted(sessionId, actionType, event.getReasoning());
        }
        
        // 节点完成和失败的处理已经由AsyncDataManager处理，这里不需要重复
    }

    /**
     * 处理节点开始执行
     */
    private void handleNodeStarted(String sessionId, ActionType actionType, String reasoning) {
        // 处理推理信息
        if (StringUtils.isNotBlank(reasoning)) {
            eventPublisher.publish(sessionId,
                    eventFactory.createThinkingEvent(reasoning + "\n", true));
            log.debug("已发送思考事件: sessionId={}, actionType={}", sessionId, actionType);
            return;
        }
        
        // 处理loading状态
        switch (actionType) {
            case INTENT_RECOGNITION:
            case KNOWLEDGE_RETRIEVAL:
                // 推理过程已经在上面处理
                break;

            case DATA_QUERY:
                // 检查是否已经发送过CHARTDATA loading事件
                if (!hasLoadingEventBeenSent(sessionId, ResponseType.CHARTDATA)) {
                    // 处理数据查询事件
                    eventPublisher.publish(sessionId,
                            eventFactory.createDataEvent(
                                    ChartDataResponse.builder()
                                            .loadingStatus(LoadingStatus.LOADING)
                                            .messageType(MessageType.CHART_DATA)
                                            .responseType(ResponseType.CHARTDATA)
                                            .build(),
                                    false));
                    markLoadingEventSent(sessionId, ResponseType.CHARTDATA);
                    log.debug("已发送数据查询加载事件: sessionId={}", sessionId);
                } else {
                    log.debug("跳过重复的数据查询加载事件: sessionId={}", sessionId);
                }
                break;

            case DATA_INSIGHT:
                // 其他需要loading状态的处理
                break;

            default:
                log.debug("不处理的Action类型: {}", actionType);
                break;
        }
    }

    /**
     * 检查指定会话的指定类型loading事件是否已经发送
     */
    private boolean hasLoadingEventBeenSent(String sessionId, ResponseType type) {
        Set<ResponseType> sentEvents = sessionLoadingEventsSent.get(sessionId);
        return sentEvents != null && sentEvents.contains(type);
    }

    /**
     * 标记指定会话的指定类型loading事件已发送
     */
    private void markLoadingEventSent(String sessionId, ResponseType type) {
        sessionLoadingEventsSent.computeIfAbsent(sessionId, k -> ConcurrentHashMap.newKeySet()).add(type);
    }

    /**
     * 清理会话记录
     * 当会话完成时，清理相应的记录
     */
    @EventListener
    public void handleSessionComplete(SessionCompletedEvent event) {
        String sessionId = event.getSessionId();
        if (StringUtils.isNotBlank(sessionId)) {
            sessionLoadingEventsSent.remove(sessionId);
            log.debug("已清理会话loading事件记录: sessionId={}", sessionId);
        }
    }
}