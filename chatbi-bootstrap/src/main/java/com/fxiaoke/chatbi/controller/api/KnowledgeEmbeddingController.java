package com.fxiaoke.chatbi.controller.api;

import com.alibaba.fastjson.JSON;
import com.facishare.bi.metadata.context.dto.ads.StatView;
import com.facishare.bi.metadata.context.service.ads.IChartService;
import com.fxiaoke.chatbi.common.dto.ApiResult;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.enums.KnowledgeType;
import com.fxiaoke.chatbi.common.model.request.DeleteChartKnowledgeRequest;
import com.fxiaoke.chatbi.common.model.request.DeleteKnowledgeEmbeddingRequest;
import com.fxiaoke.chatbi.common.model.request.GenerateEmbeddingsRequest;
import com.fxiaoke.chatbi.common.model.request.SaveFeaturesRequest;
import com.fxiaoke.chatbi.common.model.response.ChartEmbeddingResponse;
import com.fxiaoke.chatbi.common.utils.UserInfoConvertUtil;
import com.fxiaoke.chatbi.integration.repository.ch.KnowledgeEmbeddingRepository;
import com.fxiaoke.chatbi.integration.repository.ch.ChartKnowledgeRepository;
import com.fxiaoke.chatbi.knowledge.building.chart.ChartKnowledgeFeatureExtractor;
import com.fxiaoke.chatbi.knowledge.building.metadata.ChartDesc;
import com.fxiaoke.chatbi.knowledge.building.metadata.ChartDescBuilder;
import com.fxiaoke.chatbi.knowledge.service.ChartKnowledgeBuildingService;
import com.fxiaoke.chatbi.knowledge.service.EnterpriseKnowledgeService;
import com.fxiaoke.chatbi.knowledge.service.FieldKnowledgeService;
import com.fxiaoke.chatbi.knowledge.service.KnowledgeBuildingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/embedding")
@RequiredArgsConstructor
@Slf4j
public class KnowledgeEmbeddingController {

    private final ChartKnowledgeFeatureExtractor chartKnowledgeFeatureExtractor;
    private final IChartService iChartService;
    private final ChartDescBuilder chartDescBuilder;
    private final ChartKnowledgeBuildingService chartKnowledgeBuildingService;
    private final KnowledgeBuildingService knowledgeBuildingService;
    private final EnterpriseKnowledgeService enterpriseKnowledgeService;
    private final FieldKnowledgeService fieldKnowledgeService;
    private final KnowledgeEmbeddingRepository knowledgeEmbeddingRepository;
    private final ChartKnowledgeRepository chartKnowledgeRepository;

    @GetMapping("/chart-embeddings/{tenantId}")
    public ApiResult<List<ChartEmbeddingResponse>> getChartEmbeddings(@PathVariable String tenantId) {
        log.info("获取图表特征, tenantId={}", tenantId);

        // 创建用户身份对象
        UserIdentity userIdentity = new UserIdentity();
        userIdentity.setTenantId(tenantId);
        userIdentity.setUserId("-10000");

        // 使用服务层获取图表特征
        List<ChartEmbeddingResponse> embeddingResponses = chartKnowledgeBuildingService.listTenantChartFeatures(userIdentity);

        return ApiResult.success(embeddingResponses);
    }

    /**
     * 根据 viewId 生成特征
     */
    @GetMapping("/generate-features/{viewId}")
    public ApiResult<List<String>> generateFeatures(@PathVariable String viewId, @RequestParam String tenantId) {
        UserIdentity userIdentity = new UserIdentity();
        userIdentity.setTenantId(tenantId);
        userIdentity.setUserId("-10000");
        List<StatView> statViews = iChartService.getViewListByViewIds(List.of(viewId),
                UserInfoConvertUtil.createUserInfo(userIdentity));
        if (statViews.isEmpty()) {
            return ApiResult.error("找不到对应的视图");
        }
        List<ChartDesc> chartDescs = chartDescBuilder.convertViews(statViews);

        // 调用服务方法生成特征
        List<String> features = chartKnowledgeFeatureExtractor
                .generateIndustryQuestions(JSON.toJSONString(chartDescs.get(0)), userIdentity);
        return ApiResult.success(features);
    }

    /**
     * 保存特征
     */
    @PostMapping("/save-features")
    public ApiResult<Void> saveFeatures(@RequestBody SaveFeaturesRequest request, UserIdentity userIdentity) {
        log.info("saveFeatures: viewId:{}, userIdentity:{}", request.getViewId(), userIdentity);
        // 使用新的服务接口替代旧的构建器
        chartKnowledgeBuildingService.buildChartKnowledge(userIdentity, request.getViewId());
        return ApiResult.ok();
    }

    /**
     * 生成图表向量库数据
     */
    @PostMapping("/generate/embeddings")
    public ApiResult<Void> generateEmbeddings(@RequestBody GenerateEmbeddingsRequest generateEmbeddingsRequest) {
        log.info("generateEmbeddings: {}", generateEmbeddingsRequest);
        UserIdentity userIdentity = new UserIdentity();
        userIdentity.setTenantId(generateEmbeddingsRequest.getTenantId());
        userIdentity.setUserId("-10000");

        // 使用新的服务接口替代旧的构建器
        chartKnowledgeBuildingService.buildChartKnowledge(userIdentity, generateEmbeddingsRequest.getViewId());
        return ApiResult.ok();
    }

    /**
     * 生成企业知识库向量数据
     */
    @PostMapping("/generate/knowledge/embeddings")
    public ApiResult<Void> generateKnowledgeEmbeddings(
            @RequestBody GenerateEmbeddingsRequest generateEmbeddingsRequest) {
        log.info("generateEmbeddings: {}", generateEmbeddingsRequest);
        UserIdentity userIdentity = new UserIdentity();
        userIdentity.setTenantId(generateEmbeddingsRequest.getTenantId());
        userIdentity.setUserId("-10000");

        // 使用新的服务接口替代旧的构建器
        enterpriseKnowledgeService.buildEnterpriseKnowledge(userIdentity);
        return ApiResult.ok();
    }

    /**
     * 生成企业维度指标字段向量数据
     */
    @PostMapping("/generate/dimAndMeasure/embeddings")
    public ApiResult<Void> generateDimAndMeasureEmbeddings(
            @RequestBody GenerateEmbeddingsRequest generateEmbeddingsRequest) {
        log.info("generateEmbeddings: {}", generateEmbeddingsRequest);
        UserIdentity userIdentity = new UserIdentity();
        userIdentity.setTenantId(generateEmbeddingsRequest.getTenantId());
        userIdentity.setUserId("-10000");

        // 使用新的服务接口替代旧的构建器
        fieldKnowledgeService.buildFieldKnowledge(userIdentity);
        return ApiResult.ok();
    }

    /**
     * 生成所有类型的知识库向量数据
     */
    @PostMapping("/generate/all/embeddings")
    public ApiResult<Void> generateAllEmbeddings(@RequestBody GenerateEmbeddingsRequest generateEmbeddingsRequest) {
        log.info("generateAllEmbeddings: {}", generateEmbeddingsRequest);
        UserIdentity userIdentity = new UserIdentity();
        userIdentity.setTenantId(generateEmbeddingsRequest.getTenantId());
        userIdentity.setUserId("-10000");

        // 使用顶层服务接口构建所有知识
        knowledgeBuildingService.buildAllKnowledge(userIdentity);
        return ApiResult.ok();
    }

    /**
     * 删除知识向量数据
     */
    @PostMapping("/delete/knowledge-embedding")
    public ApiResult<Void> deleteKnowledgeEmbedding(@RequestBody DeleteKnowledgeEmbeddingRequest request) {
        log.info("Deleting knowledge embeddings for tenant: {}, knowledgeType: {}", request.getTenantId(),
                request.getKnowledgeType());
        knowledgeEmbeddingRepository.deleteKnowledgeEmbedding(request.getTenantId(), request.getKnowledgeType(), request.getKnowledgeIds());
        return ApiResult.ok();
    }

    /**
     * 删除图表知识数据
     */
    @PostMapping("/delete/chart-knowledge")
    public ApiResult<Void> deleteChartKnowledge(@RequestBody DeleteChartKnowledgeRequest request) {
        log.info("Deleting chart knowledge for tenant: {}, viewIds: {}", request.getTenantId(), request.getViewIds());
        chartKnowledgeRepository.deleteByTenantIdAndViewIds(request.getTenantId(), request.getViewIds());
        knowledgeEmbeddingRepository.deleteKnowledgeEmbedding(request.getTenantId(), KnowledgeType.CHART.getCode(), request.getViewIds());
        return ApiResult.ok();
    }
}