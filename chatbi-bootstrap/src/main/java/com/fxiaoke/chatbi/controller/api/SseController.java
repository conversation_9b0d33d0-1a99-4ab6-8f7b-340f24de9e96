package com.fxiaoke.chatbi.controller.api;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.memory.ConversationContext;
import com.fxiaoke.chatbi.common.model.request.ChatBiRequest;
import com.fxiaoke.chatbi.common.sse.emitter.SseEventEmitter;
import com.fxiaoke.chatbi.common.sse.emitter.SseEventEmitterRepository;
import com.fxiaoke.chatbi.common.sse.emitter.impl.SpringSseEventEmitter;
import com.fxiaoke.chatbi.memory.service.ConversationService;
import com.fxiaoke.chatbi.planning.context.PlanContext;
import com.fxiaoke.chatbi.planning.event.PlanStatusEvent;
import com.fxiaoke.chatbi.planning.listener.PlanStatusEventListener;
import com.fxiaoke.chatbi.planning.model.ExecutionStatus;
import com.fxiaoke.chatbi.service.ChatBiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * SSE控制器
 * 处理基于SSE的流式响应接口
 */
@Slf4j
@RestController
@RequestMapping("/api/sse")
@RequiredArgsConstructor
public class SseController {
    private final ExecutorService executorService = Executors.newCachedThreadPool();
    private final ChatBiService chatBiService;
    private final SseEventEmitterRepository emitterRepository;
    private final ApplicationEventPublisher eventPublisher;
    private final PlanStatusEventListener planStatusEventListener;
    private final ConversationService conversationService;


    /**
     * 通过SSE进行聊天
     * 
     * @param request      聊天请求
     * @param userIdentity 用户身份
     * @return SseEmitter实例
     */
    @PostMapping(value = "/stream/chat", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter chatBySSE(@RequestBody ChatBiRequest request, UserIdentity userIdentity) {
        log.info("接收SSE聊天请求: sessionId={}", request.getSessionId());

        clearLastSseEmitter(request);

        // 创建Spring SseEmitter，设置5分钟超时
        SseEmitter springEmitter = new SseEmitter(300_000L);

        // 创建自定义SseEventEmitter并保存到存储库
        SseEventEmitter eventEmitter = new SpringSseEventEmitter(springEmitter, request.getSessionId());
        emitterRepository.save(eventEmitter);

        // 异步处理聊天请求
        executorService.submit(() -> {
            try {
                log.info("开始处理SSE聊天请求: sessionId={}", request.getSessionId());

                // 调用服务处理请求，传递sessionId用于后续事件发布
                chatBiService.chatWithSse(request, userIdentity, request.getSessionId());

                log.info("SSE聊天请求处理完成: sessionId={}", request.getSessionId());
            } catch (Exception e) {
                log.error("处理SSE聊天请求时发生错误: sessionId={}", request.getSessionId(), e);
                // 错误处理由服务层负责，这里不需要额外处理
            }
        });

        return springEmitter;
    }

    private void clearLastSseEmitter(ChatBiRequest request) {
        if (emitterRepository.get(request.getSessionId()) == null) {
            return;
        }

        ConversationContext conversationContext = conversationService.findById(request.getSessionId());

        if (conversationContext != null) {
            eventPublisher.publishEvent(new PlanStatusEvent(new PlanContext("removeLastSseEmitter", conversationContext, request.getLlmModel()), ExecutionStatus.COMPLETED));
        }
    }
}