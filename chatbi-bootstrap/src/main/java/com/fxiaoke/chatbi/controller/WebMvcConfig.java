package com.fxiaoke.chatbi.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

/**
 * Web MVC配置类
 * 用于注册拦截器、参数解析器等MVC组件
 */
@Configuration
@RequiredArgsConstructor
public class WebMvcConfig implements WebMvcConfigurer {

  // 注入用户身份处理器（整合了拦截器和参数解析器功能）
  private final UserIdentityHandler userIdentityHandler;

  /**
   * 添加拦截器
   *
   * @param registry 拦截器注册表
   */
  @Override
  public void addInterceptors(InterceptorRegistry registry) {
    // 注册用户身份验证拦截器，并指定拦截路径
    registry.addInterceptor(userIdentityHandler)
            // 需要身份验证的API路径，可以根据实际情况配置
            .addPathPatterns("/api/charts/**")
            .addPathPatterns("/api/conversations/**")
            .addPathPatterns("/api/messages/**")
            // 不需要身份验证的API路径，例如健康检查、登录接口等
            .excludePathPatterns("/api/health")
            .excludePathPatterns("/api/auth/login")
            .excludePathPatterns("/");
  }

  /**
   * 添加参数解析器
   * 用于将HTTP请求中的用户身份信息注入到控制器方法参数中
   *
   * @param resolvers 参数解析器列表
   */
  @Override
  public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
    resolvers.add(userIdentityHandler);
  }
} 