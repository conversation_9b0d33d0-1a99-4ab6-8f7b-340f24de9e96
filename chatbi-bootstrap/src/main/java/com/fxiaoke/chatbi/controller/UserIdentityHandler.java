package com.fxiaoke.chatbi.controller;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Optional;

/**
 * 用户身份处理器
 * 集成了用户身份解析、拦截验证和参数注入的功能
 * 替代了原来的UserIdentityResolver、UserIdentityInterceptor和UserIdentityArgumentResolver三个类
 */
@Component
@Slf4j
public class UserIdentityHandler implements HandlerInterceptor, HandlerMethodArgumentResolver {

  // 请求头名称常量
  private static final String HEADER_EA = "X-fs-Enterprise-Account";
  private static final String HEADER_TENANT_ID = "X-fs-Enterprise-Id";
  private static final String HEADER_USER_ID = "X-fs-Employee-Id";
  private static final String HEADER_LOCALE = "X-Locale";
  private static final String HEADER_CHANNEL = "X-Channel";

  /**
   * 从HTTP请求中解析用户身份信息
   *
   * @param request HTTP请求
   * @return 用户身份信息对象
   */
  public UserIdentity resolveUserIdentity(HttpServletRequest request) {
    String tenantId = getHeaderValue(request, HEADER_TENANT_ID).orElse(null);
    String userId = getHeaderValue(request, HEADER_USER_ID).orElse(null);
    String locale = getHeaderValue(request, HEADER_LOCALE).orElse("ZH-CN");
    String channel = getHeaderValue(request, HEADER_CHANNEL).orElse("web");
    String ea = getHeaderValue(request, HEADER_EA).orElse(null);

    if (!StringUtils.hasText(tenantId) || !StringUtils.hasText(userId)) {
      throw new IllegalArgumentException(String.format("请求Header中缺少必要的身份信息: tenantId=%s, userId=%s", tenantId, userId));
    }

    return UserIdentity.builder().tenantId(tenantId).ea(ea).userId(userId).locale(locale).channel(channel).build();
  }

  /**
   * 验证用户身份信息是否有效
   *
   * @param userIdentity 用户身份信息
   * @return 是否有效
   */
  public boolean isValid(UserIdentity userIdentity) {
    return userIdentity != null && StringUtils.hasText(userIdentity.getTenantId()) &&
      StringUtils.hasText(userIdentity.getUserId());
  }

  /**
   * 获取请求头中的值
   *
   * @param request    HTTP请求
   * @param headerName 请求头名称
   * @return 请求头值，不存在时返回空
   */
  private Optional<String> getHeaderValue(HttpServletRequest request, String headerName) {
    String value = request.getHeader(headerName);
    return Optional.ofNullable(value);
  }

  // ========== HandlerInterceptor接口实现 ==========

  /**
   * 前置处理方法，在Controller方法执行前调用
   * 对所有需要身份验证的请求路径进行身份验证
   */
  @Override
  public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
    try {
      // 尝试解析用户身份
      UserIdentity userIdentity = resolveUserIdentity(request);

      // 验证用户身份是否有效
      if (!isValid(userIdentity)) {
        log.error("无效的用户身份信息: {}", userIdentity);
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write("{\"error\":\"用户身份信息无效，请确保包含必要的身份信息\"}");
        return false;
      }

      // 将验证通过的用户身份信息存储到请求属性中，供后续使用
      request.setAttribute("userIdentity", userIdentity);

      log.debug("验证用户身份成功: tenantId={}, userId={}", userIdentity.getTenantId(), userIdentity.getUserId());
      return true;
    } catch (Exception e) {
      log.error("用户身份验证失败", e);
      response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
      response.setContentType("application/json;charset=UTF-8");
      response.getWriter().write("{\"error\":\"" + e.getMessage() + "\"}");
      return false;
    }
  }

  // ========== HandlerMethodArgumentResolver接口实现 ==========

  /**
   * 判断是否支持参数类型
   * 只有当参数类型为UserIdentity时才支持
   */
  @Override
  public boolean supportsParameter(MethodParameter parameter) {
    return parameter.getParameterType().equals(UserIdentity.class);
  }

  /**
   * 解析参数
   * 从HTTP请求头中提取用户身份信息并返回UserIdentity对象
   */
  @Override
  public Object resolveArgument(MethodParameter parameter,
                                ModelAndViewContainer mavContainer,
                                NativeWebRequest webRequest,
                                WebDataBinderFactory binderFactory) {

    HttpServletRequest request = webRequest.getNativeRequest(HttpServletRequest.class);
    if (request == null) {
      throw new IllegalStateException("无法获取HTTP请求，无法提取用户身份信息");
    }

    // 从请求属性中获取已验证的用户身份信息
    UserIdentity userIdentity = (UserIdentity) request.getAttribute("userIdentity");
    if (userIdentity != null) {
      return userIdentity;
    }

    // 如果请求属性中没有，则重新解析（通常不会走到这一步，因为拦截器已经处理）
    return resolveUserIdentity(request);
  }
} 