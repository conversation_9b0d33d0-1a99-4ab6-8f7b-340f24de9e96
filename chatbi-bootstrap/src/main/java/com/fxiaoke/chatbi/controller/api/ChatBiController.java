package com.fxiaoke.chatbi.controller.api;

import com.fxiaoke.chatbi.common.dto.ApiResult;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.request.ChatBiRequest;
import com.fxiaoke.chatbi.common.model.response.ReasoningResponse;
import com.fxiaoke.chatbi.service.ChatBiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * ChatBI API控制器
 * 处理内部RPC调用，返回ApiResult格式响应
 */
@RestController
@RequestMapping("/api/chatbi")
@RequiredArgsConstructor
@Slf4j
public class ChatBiController {
    private final ChatBiService chatBiService;

    /**
     * 处理聊天接口请求
     * 内部API调用，使用ApiResult包装响应
     */
    @PostMapping("/chat")
    public ApiResult<ReasoningResponse> chat(@RequestBody ChatBiRequest request, UserIdentity userIdentity) {
        log.info("处理API聊天请求, 用户: {}, 会话ID: {}", userIdentity.getUserId(), request.getSessionId());
        ReasoningResponse response = chatBiService.chat(request, userIdentity);
        return ApiResult.success(response);
    }
}