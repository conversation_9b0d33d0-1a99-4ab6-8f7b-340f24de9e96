package com.fxiaoke.chatbi.controller.api;

import com.fxiaoke.chatbi.common.dto.ApiResult;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.response.RecommendationResponse;
import com.fxiaoke.chatbi.service.RecommendationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 推荐服务API控制器
 * 处理内部RPC调用，返回ApiResult格式响应
 */
@RestController
@RequestMapping("/api/chatbi")
@RequiredArgsConstructor
@Slf4j
public class RecommendationController {
  private final RecommendationService recommendationService;

  /**
   * 获取推荐问题接口
   * 内部API调用，使用ApiResult包装响应
   */
  @PostMapping("/recommendations")
  public ApiResult<RecommendationResponse> recommendations(UserIdentity userIdentity) {
    log.info("Process recommendations request for user: {}", userIdentity.getUserId());
    RecommendationResponse response = recommendationService.recommendations(userIdentity);
    return ApiResult.success(response);
  }
} 