package com.fxiaoke.chatbi.controller;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.request.LoadDataRequest;
import com.fxiaoke.chatbi.common.model.response.ChartDataResponse;
import com.fxiaoke.chatbi.common.model.response.FollowUpResponse;
import com.fxiaoke.chatbi.common.model.response.InsightResponse;
import com.fxiaoke.chatbi.common.model.response.RecommendationResponse;
import com.fxiaoke.chatbi.service.ChatBiService;
import com.fxiaoke.chatbi.service.RecommendationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * ChatBI CEP控制器
 * 处理来自CEP的调用，直接返回响应对象，异常由CEP处理
 * 使用与API相同的URL前缀保持向后兼容
 */
@RestController
@RequestMapping("/chatbi")
@RequiredArgsConstructor
@Slf4j
public class ChatBiCepController {
    private final ChatBiService chatBiService;
    private final RecommendationService recommendationService;

    /**
     * 获取推荐
     * CEP调用接口，异常直接抛出由CEP处理
     */
    @PostMapping("/recommendations")
    public RecommendationResponse recommendations(UserIdentity userIdentity) {
        return recommendationService.recommendations(userIdentity);
    }

    /**
     * 加载图表数据
     * CEP调用接口，异常直接抛出由CEP处理
     */
    @PostMapping("/loadData")
    public ChartDataResponse loadData(@RequestBody LoadDataRequest request, UserIdentity userIdentity) {
        return chatBiService.loadChartData(request.getRequestId(), userIdentity);
    }

    /**
     * 加载解读数据
     * CEP调用接口，异常直接抛出由CEP处理
     */
    @PostMapping("/loadInsight")
    public InsightResponse loadInsight(@RequestBody LoadDataRequest request, UserIdentity userIdentity) {
        return chatBiService.loadInsight(request.getRequestId(), userIdentity);
    }

    /**
     * 加载追问建议
     * CEP调用接口，异常直接抛出由CEP处理
     */
    @PostMapping("/loadFollowUp")
    public FollowUpResponse loadFollowUp(@RequestBody LoadDataRequest request, UserIdentity userIdentity) {
        return chatBiService.loadFollowUpQuestions(request.getRequestId(), userIdentity);
    }
} 