package com.fxiaoke.chatbi.controller.api;

import com.fxiaoke.chatbi.common.dto.ApiResult;
import com.fxiaoke.chatbi.common.model.request.RefreshPromptRequest;
import com.fxiaoke.chatbi.prompts.PromptTemplateService;
import com.fxiaoke.chatbi.common.model.prompts.PromptTemplateType;
import com.fxiaoke.chatbi.prompts.TemplateVariable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/v1/prompts")
public class PromptController {

    private final PromptTemplateService promptTemplateService;

    public PromptController(PromptTemplateService promptTemplateService) {
        this.promptTemplateService = promptTemplateService;
    }

    /**
     * 获取所有模板类型
     */
    @GetMapping("/types")
    public ApiResult<List<Map<String, String>>> getAllTemplateTypes() {
        try {
            List<Map<String, String>> typesList = Arrays.stream(PromptTemplateType.values())
                .map(type -> {
                    Map<String, String> typeInfo = new HashMap<>();
                    typeInfo.put("name", type.name());
                    typeInfo.put("templateName", type.getTemplateName());
                    typeInfo.put("description", type.getDescription());
                    return typeInfo;
                })
                .collect(Collectors.toList());
            return ApiResult.success(typesList);
        } catch (Exception e) {
            log.error("获取模板类型列表失败", e);
            return ApiResult.error("获取模板类型列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定类型的提示词模板
     */
    @GetMapping("/{templateType}")
    public ApiResult<String> getPromptTemplate(@PathVariable String templateType) {
        try {
            PromptTemplateType type = PromptTemplateType.valueOf(templateType.toUpperCase());
            String template = promptTemplateService.getTemplate(type);
            return ApiResult.success(template);
        } catch (IllegalArgumentException e) {
            log.error("无效的模板类型: {}", templateType, e);
            return ApiResult.error("无效的模板类型: " + templateType);
        } catch (Exception e) {
            log.error("获取提示词模板失败", e);
            return ApiResult.error("获取提示词模板失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定类型的模板元数据
     */
    @GetMapping("/metadata/{templateType}")
    public ApiResult<Map<String, Object>> getTemplateMetadata(@PathVariable String templateType) {
        try {
            PromptTemplateType type = PromptTemplateType.valueOf(templateType.toUpperCase());
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("description", type.getDescription());
            
            // 设置示例数据（如果有的话）
            if (type == PromptTemplateType.CHART_QUESTION_GEN) {
                metadata.put("demo", promptTemplateService.getDemoContent(type));
            }
            
            return ApiResult.success(metadata);
        } catch (IllegalArgumentException e) {
            log.error("无效的模板类型: {}", templateType, e);
            return ApiResult.error("无效的模板类型: " + templateType);
        } catch (Exception e) {
            log.error("获取模板元数据失败", e);
            return ApiResult.error("获取模板元数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取指定类型的模板变量列表
     */
    @GetMapping("/variables/{templateType}")
    public ApiResult<List<TemplateVariable>> getTemplateVariables(@PathVariable String templateType) {
        try {
            PromptTemplateType type = PromptTemplateType.valueOf(templateType.toUpperCase());
            List<TemplateVariable> variables = promptTemplateService.getTemplateVariables(type);
            return ApiResult.success(variables);
        } catch (IllegalArgumentException e) {
            log.error("无效的模板类型: {}", templateType, e);
            return ApiResult.error("无效的模板类型: " + templateType);
        } catch (Exception e) {
            log.error("获取模板变量失败", e);
            return ApiResult.error("获取模板变量失败: " + e.getMessage());
        }
    }
    
    /**
     * 刷新指定类型的提示词模板
     */
    @PostMapping("/refresh")
    public ApiResult<Void> refreshPromptTemplate(@Valid @RequestBody RefreshPromptRequest request) {
        try {
            PromptTemplateType type = PromptTemplateType.valueOf(request.getTemplateType().toUpperCase());
            promptTemplateService.refreshTemplate(type, request.getContent());
            return ApiResult.success(null);
        } catch (IllegalArgumentException e) {
            log.error("无效的模板类型: {}", request.getTemplateType(), e);
            return ApiResult.error("无效的模板类型: " + request.getTemplateType());
        } catch (Exception e) {
            log.error("刷新提示词模板失败", e);
            return ApiResult.error("刷新提示词模板失败: " + e.getMessage());
        }
    }
} 