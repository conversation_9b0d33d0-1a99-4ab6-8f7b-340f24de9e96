package com.fxiaoke.chatbi.controller.api;

import com.fxiaoke.chatbi.common.dto.ApiResult;
import com.fxiaoke.chatbi.common.exception.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import java.util.Locale;

import static com.fxiaoke.chatbi.common.dto.ApiResult.INTERNAL_ERROR;

/**
 * API异常处理器
 * 只处理controller.api包下的控制器异常，将其转换为统一的ApiResult响应格式
 * controller包下的CEP控制器异常将直接抛出由CEP处理
 */
@RestControllerAdvice(basePackages = "com.fxiaoke.chatbi.controller.api")
public class ApiExceptionHandler {

    private final MessageResolver messageResolver;
    
    @Autowired
    public ApiExceptionHandler(MessageResolver messageResolver) {
        this.messageResolver = messageResolver;
    }
    
    /**
     * 处理基础异常
     */
    @ExceptionHandler(BaseException.class)
    public ApiResult<?> handleBaseException(BaseException e, HttpServletRequest request) {
        String localeStr = request.getHeader("X-Locale");
        Locale locale = localeStr != null ? Locale.forLanguageTag(localeStr) : Locale.CHINA;
        
        String message = messageResolver.resolveMessage(e, locale);
        
        return ApiResult.builder()
                .code(INTERNAL_ERROR)
                .message(message)
                .i18nCode(e.getI18nKey())
                .build();
    }
    
    /**
     * 处理ActionException
     */
    @ExceptionHandler(ActionException.class)
    public ApiResult<?> handleActionException(ActionException e, HttpServletRequest request) {
        return handleBaseException(e, request);
    }
    
    /**
     * 处理PlanningException
     */
    @ExceptionHandler(PlanningException.class)
    public ApiResult<?> handlePlanningException(PlanningException e, HttpServletRequest request) {
        return handleBaseException(e, request);
    }
    
    /**
     * 处理KnowledgeException
     */
    @ExceptionHandler(KnowledgeException.class)
    public ApiResult<?> handleKnowledgeException(KnowledgeException e, HttpServletRequest request) {
        return handleBaseException(e, request);
    }
    
    /**
     * 处理MemoryException
     */
    @ExceptionHandler(MemoryException.class)
    public ApiResult<?> handleMemoryException(MemoryException e, HttpServletRequest request) {
        return handleBaseException(e, request);
    }
    
    /**
     * 处理IntentAnalysisException
     */
    @ExceptionHandler(IntentAnalysisException.class)
    public ApiResult<?> handleIntentAnalysisException(IntentAnalysisException e, HttpServletRequest request) {
        return handleBaseException(e, request);
    }
    
    /**
     * 处理所有其他异常
     */
    @ExceptionHandler(Exception.class)
    public ApiResult<?> handleException(Exception e, HttpServletRequest request) {
        ChatbiErrorCodeEnum errorEnum = ChatbiErrorCodeEnum.SYSTEM_ERROR;
        BaseException be = new BaseException(errorEnum, e);
        return handleBaseException(be, request);
    }
} 