<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fxiaoke</groupId>
        <artifactId>fs-bi-agent</artifactId>
        <version>9.5.0-SNAPSHOT</version>
    </parent>

    <artifactId>chatbi-bootstrap</artifactId>
    <packaging>war</packaging>
    <name>chatbi-bootstrap</name>
    <description>ChatBI 启动模块，应用入口和Web控制器</description>

    <dependencies>
        <!-- 项目模块依赖 -->
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>chatbi-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>chatbi-planning</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>chatbi-knowledge</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>chatbi-memory</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>chatbi-action</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>chatbi-monitoring</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>chatbi-integration</artifactId>
        </dependency>

        <!-- Spring Boot 核心依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- 纷享特有依赖 -->
        <dependency>
            <groupId>com.fxiaoke.boot</groupId>
            <artifactId>actuator-ext-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.boot</groupId>
            <artifactId>metrics-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.cloud</groupId>
            <artifactId>cms-spring-cloud-starter</artifactId>
        </dependency>

        <!-- 数据库相关 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!-- 业务相关依赖 -->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-bi-common-entities</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-bi-metadata-context</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-rest-client-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-paas-ai-api</artifactId>
        </dependency>

        <!-- 工具类依赖 -->
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>http-spring-support</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>jdbc-support</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>metrics-oss</artifactId>
        </dependency>

        <!-- ClickHouse -->
        <dependency>
            <groupId>com.clickhouse</groupId>
            <artifactId>clickhouse-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.clickhouse</groupId>
            <artifactId>clickhouse-data</artifactId>
        </dependency>
        <dependency>
            <groupId>com.clickhouse</groupId>
            <artifactId>clickhouse-client</artifactId>
        </dependency>

        <!-- Markdown处理 -->
        <dependency>
            <groupId>com.vladsch.flexmark</groupId>
            <artifactId>flexmark-all</artifactId>
        </dependency>

        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.fxiaoke.chatbi.ChatBIApplication</mainClass>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>