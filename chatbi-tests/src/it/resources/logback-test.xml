<configuration scan="false" scanPeriod="60 seconds" debug="false">
  <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
    <!-- 常规业务务必打印traceId和userId方便跟踪问题，异常栈可以去掉一些常规类，避免很长堆栈信息 -->
    <encoder>
      <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%rEx{full,
        org.apache.shiro,
        java.lang.Thread,
        javassist,
        sun.reflect,
        org.springframework,
        org.apache,
        org.eclipse.jetty,
        $Proxy,
        java.net,
        java.io,
        javax.servlet,
        org.junit,
        com.mysql,
        com.sun,
        org.mybatis.spring,
        cglib,
        CGLIB,
        java.util.concurrent,
        okhttp,
        org.jboss,
        }%n
      </pattern>
    </encoder>
  </appender>
  
  <!-- 文件输出 -->
  <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${catalina.home:-../}/logs/fs-bi-agent.log</file>
    <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
      <fileNamePattern>${user.home}/logs/fs-bi-agent-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
      <maxFileSize>10MB</maxFileSize>
      <maxHistory>7</maxHistory>
      <totalSizeCap>100MB</totalSizeCap>
    </rollingPolicy>
    <encoder>
      <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
      <charset>utf8</charset>
    </encoder>
  </appender>
  
  <!-- 异步输出日志避免阻塞服务 -->
  <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
    <queueSize>512</queueSize>
    <appender-ref ref="FILE"/>
    <includeCallerData>true</includeCallerData>
  </appender>

  <!-- 配置基础组件为WARN级别，避免打印过多影响服务自己日志 -->
  <logger name="druid.sql" level="WARN"/>
  <logger name="org.springframework" level="WARN"/>
  <logger name="org.apache" level="WARN"/>
  <logger name="com.ibatis.common.jdbc.SimpleDataSource" level="WARN"/>
  <logger name="com.ibatis.common.jdbc.ScriptRunner" level="WARN"/>
  <logger name="com.ibatis.sqlmap.engine.impl.SqlMapClientDelegate" level="WARN"/>
  <logger name="java.sql.Connection" level="WARN"/>
  <logger name="java.sql.Statement" level="WARN"/>
  <logger name="java.sql.PreparedStatement" level="WARN"/>
  <logger name="org.mybatis" level="WARN"/>
  
  <!-- 应用日志级别 -->
  <logger name="com.fxiaoke.chatbi" level="DEBUG"/>
  
  <!-- Spring Boot 相关日志 -->
  <logger name="org.springframework.boot" level="INFO"/>
  <logger name="org.springframework.web" level="INFO"/>

  <!-- ABTestLogger 文件输出 -->
  <appender name="ABTEST_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${catalina.home:-../}/logs/abtest.log</file>
    <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
      <fileNamePattern>${catalina.home:-../}/logs/abtest-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
      <maxFileSize>10MB</maxFileSize>
      <maxHistory>7</maxHistory>
      <totalSizeCap>100MB</totalSizeCap>
    </rollingPolicy>
    <encoder>
      <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
      <charset>utf8</charset>
    </encoder>
  </appender>

  <!-- ABTestLogger 配置 -->
  <logger name="ABTestLogger" level="INFO" additivity="false">
    <appender-ref ref="ABTEST_FILE"/>
  </logger>

  <root level="INFO">
    <appender-ref ref="STDOUT"/>
    <appender-ref ref="ASYNC"/>
  </root>
</configuration>