spring.application.name=fs-bi-agent
spring.profiles.active=fstest
process.profile=fstest
spring.config.import=optional:cms:${spring.application.name}

spring.main.allow-bean-definition-overriding=true
spring.main.allow-circular-references=true

# 禁用自动mock
spring.test.mockmvc.auto-configure=false
spring.test.mock.enabled=false

spring.main.web-application-type=servlet

spring.datasource.url=jdbc:h2:mem:chatbi
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false
management.health.redis.enabled=false