package com.fxiaoke.chatbi.retrieval.service;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.integration.model.ch.ChartKnowledge;
import com.fxiaoke.chatbi.knowledge.retrieval.ChartRankingService;
import com.fxiaoke.chatbi.common.model.knowledge.ChannelType;
import com.fxiaoke.chatbi.knowledge.retrieval.channel.RecallChannel;
import com.fxiaoke.chatbi.knowledge.retrieval.channel.RecallMethod;
import com.fxiaoke.common.Pair;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles({"fstest"})
@TestPropertySource(locations = "classpath:application.properties")
public class ChartRankingServiceTest {

    @Autowired
    private ChartRankingService chartRankingService;

    private UserIdentity testUser;
    private Map<RecallChannel, List<ChartKnowledge>> mockChannelResults;
    private List<String> mockFieldIds;

    // 模拟召回渠道
    private static class MockRecallChannel implements RecallChannel {
        private final String name;
        private final ChannelType type;
        private final RecallMethod method;

        public MockRecallChannel(String name, ChannelType type, RecallMethod method) {
            this.name = name;
            this.type = type;
            this.method = method;
        }

        @Override
        public List<ChartKnowledge> recall(com.fxiaoke.chatbi.common.model.intent.UserIntent intent, UserIdentity identity) {
            return Collections.emptyList();
        }

        @Override
        public String getChannelName() {
            return name;
        }

        @Override
        public ChannelType getChannelType() {
            return type;
        }

        @Override
        public RecallMethod getRecallMethod() {
            return method;
        }
    }

    @BeforeEach
    void setUp() {
        // 设置测试用户
        testUser = UserIdentity.builder()
                .userId("1001")
                .tenantId("82313")
                .build();

        // 创建模拟渠道
        RecallChannel fieldIdChannel = new MockRecallChannel("字段ID召回", ChannelType.TENANT, RecallMethod.FIELD_ID);
        RecallChannel keywordChannel = new MockRecallChannel("关键词召回", ChannelType.TENANT, RecallMethod.KEYWORD);
        RecallChannel vectorChannel = new MockRecallChannel("向量召回", ChannelType.TENANT, RecallMethod.VECTOR);
        RecallChannel systemChannel = new MockRecallChannel("系统库召回", ChannelType.SYSTEM, RecallMethod.FIELD_ID);

        // 创建模拟图表数据
        mockChannelResults = new HashMap<>();
        
        // 字段ID召回图表
        List<ChartKnowledge> fieldIdCharts = new ArrayList<>();
        fieldIdCharts.add(createMockChartEmbedding("chart1", Arrays.asList("field1", "field2"), 10, 0.8));
        fieldIdCharts.add(createMockChartEmbedding("chart2", Arrays.asList("field1", "field3"), 5, 0.7));
        mockChannelResults.put(fieldIdChannel, fieldIdCharts);
        
        // 关键词召回图表
        List<ChartKnowledge> keywordCharts = new ArrayList<>();
        keywordCharts.add(createMockChartEmbedding("chart3", Arrays.asList("field2", "field4"), 8, 0.6));
        keywordCharts.add(createMockChartEmbedding("chart1", Arrays.asList("field1", "field2"), 10, 0.8)); // 重复图表，测试去重
        mockChannelResults.put(keywordChannel, keywordCharts);
        
        // 向量召回图表
        List<ChartKnowledge> vectorCharts = new ArrayList<>();
        vectorCharts.add(createMockChartEmbedding("chart4", Arrays.asList("field3", "field5"), 3, 0.9));
        vectorCharts.add(createMockChartEmbedding("chart5", Arrays.asList("field1", "field5"), 15, 0.7));
        mockChannelResults.put(vectorChannel, vectorCharts);
        
        // 系统库图表
        List<ChartKnowledge> systemCharts = new ArrayList<>();
        systemCharts.add(createMockChartEmbedding("chart6", Arrays.asList("field1", "field3"), 20, 0.85));
        mockChannelResults.put(systemChannel, systemCharts);

        // 模拟字段ID列表
        mockFieldIds = Arrays.asList("field1", "field2", "field3");
    }

    // 创建模拟图表嵌入对象
    private ChartKnowledge createMockChartEmbedding(String viewId, List<String> fieldIds, int usageCount, double vectorScore) {
        ChartKnowledge chart = new ChartKnowledge();
        chart.setViewId(viewId);
        chart.setFieldIds(fieldIds);
        chart.setUsageCount(usageCount);
        chart.setVectorScore(vectorScore);
        return chart;
    }

    @Test
    @DisplayName("测试图表排序 - 基本功能")
    void testRankCharts() {
        // 调用排序方法
        List<Pair<String, String>> rankedViewIds = chartRankingService.rankCharts(mockChannelResults, mockFieldIds, 10, new UserIdentity());

        // 验证结果不为空
        assertNotNull(rankedViewIds, "排序结果不应为null");
        assertFalse(rankedViewIds.isEmpty(), "排序结果不应为空");

        // 打印排序结果
        System.out.println("排序后的图表ID: " + rankedViewIds);

        // 验证结果中没有重复
        Set<String> uniqueIds = rankedViewIds.stream().map(stringStringPair -> stringStringPair.first).collect(Collectors.toSet());
        assertEquals(uniqueIds.size(), rankedViewIds.size(), "排序结果中不应有重复");
    }

    @Test
    @DisplayName("测试图表排序 - 空结果处理")
    void testRankChartsWithEmptyInput() {
        // 测试空输入
        List<Pair<String, String>> emptyResult = chartRankingService.rankCharts(new HashMap<>(), mockFieldIds, 10, new UserIdentity());
        assertTrue(emptyResult.isEmpty(), "空输入应返回空列表");

        // 测试null输入
        List<Pair<String, String>> nullResult = chartRankingService.rankCharts(null, mockFieldIds, 10, new UserIdentity());
        assertTrue(nullResult.isEmpty(), "null输入应返回空列表");
    }

    @Test
    @DisplayName("测试图表排序 - 限制返回数量")
    void testRankChartsWithLimit() {
        // 限制返回前2个结果
        List<Pair<String, String>> limitedResult = chartRankingService.rankCharts(mockChannelResults, mockFieldIds, 2, new UserIdentity());

        // 验证结果数量
        assertEquals(2, limitedResult.size(), "返回结果数量应等于指定的限制");
        System.out.println("限制返回前2个结果: " + limitedResult);

        // 验证完整排序结果
        List<Pair<String, String>> fullResult = chartRankingService.rankCharts(mockChannelResults, mockFieldIds, 100, new UserIdentity());
        System.out.println("完整排序结果: " + fullResult);

        // 验证限制结果是完整结果的子集
        for (int i = 0; i < limitedResult.size(); i++) {
            assertEquals(fullResult.get(i), limitedResult.get(i), "限制结果应是完整结果的前N个");
        }
    }

    @Test
    @DisplayName("测试图表排序 - 字段匹配度影响")
    void testFieldMatchImpact() {
        // 创建一个只包含field1的字段列表
        List<String> singleFieldId = Collections.singletonList("field1");

        // 调用排序方法
        List<Pair<String, String>> rankedViewIds = chartRankingService.rankCharts(mockChannelResults, singleFieldId, 10, new UserIdentity());

        System.out.println("使用单字段排序结果: " + rankedViewIds);
        System.out.println("使用多字段排序结果: " + chartRankingService.rankCharts(mockChannelResults, mockFieldIds, 10, new UserIdentity()));
        
        // 由于排序逻辑复杂，这里只验证结果不为空
        assertFalse(rankedViewIds.isEmpty(), "排序结果不应为空");
    }
} 