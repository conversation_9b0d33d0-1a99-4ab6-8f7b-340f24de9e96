package com.fxiaoke.chatbi.retrieval.service;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.intent.ExtractedInfo;
import com.fxiaoke.chatbi.common.model.knowledge.ChannelType;
import com.fxiaoke.chatbi.knowledge.retrieval.ChartRetriever;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest
@ActiveProfiles({"fstest"})
@TestPropertySource(locations = "classpath:application.properties")
public class ChartRetrieverTest {

  private UserIdentity testUser;
  @Autowired
  private ChartRetriever chartRetriever;

  @BeforeEach
  void setUp() {
    // 设置测试用户
    testUser = UserIdentity.builder()
                           .userId("1000")
                           .tenantId("82313")
                           .build();
  }

  @Test
  @DisplayName("测试基于向量搜索检索图表 - 不带结构化信息")
  void testSearchByVectorWithoutExtractedInfo() {
    // 测试数据 - 常见分析场景
    String query = "根据柱状图，哪些产品名称的销售金额在特定日期范围内表现最佳？";
    
    // 调用方法
    List<String> viewIds = chartRetriever.searchByVector(query, testUser, null, ChannelType.SYSTEM);
    
    // 验证结果
    assertNotNull(viewIds, "返回的图表ID列表不应为null");
    System.out.println("向量搜索结果数量: " + viewIds.size());
    System.out.println("向量搜索结果: " + viewIds);
  }
  
  @Test
  @DisplayName("测试基于向量搜索检索图表 - 带结构化信息")
  void testSearchByVectorWithExtractedInfo() {
    // 测试数据
    String query = "各区域销售额同比环比分析";
    
    // 准备结构化信息
    ExtractedInfo extractedInfo = new ExtractedInfo();
    extractedInfo.setAnalysisType("趋势分析");
    extractedInfo.setMeasures(Arrays.asList("销售额", "同比增长率", "环比增长率"));
    extractedInfo.setDimensions(Arrays.asList("区域", "日期"));
    extractedInfo.setTimeRange("最近12个月");
    extractedInfo.setChartType("组合图");
    
    // 调用方法
    List<String> viewIds = chartRetriever.searchByVector(query, testUser, extractedInfo, ChannelType.TENANT);
    
    // 验证结果
    assertNotNull(viewIds, "返回的图表ID列表不应为null");
    System.out.println("带结构化信息的向量搜索结果数量: " + viewIds.size());
    System.out.println("带结构化信息的向量搜索结果: " + viewIds);
  }
  
  @Test
  @DisplayName("测试基于关键词检索图表")
  void testSearchByKeywords() {
    // 测试数据 - 常见业务关键词
    List<String> keywords = Arrays.asList("销售额", "区域", "产品", "同比");
    
    // 调用方法
    List<String> viewIds = chartRetriever.searchByKeywords(keywords, testUser, ChannelType.TENANT);
    
    // 验证结果
    assertNotNull(viewIds, "返回的图表ID列表不应为null");
    System.out.println("关键词搜索结果数量: " + viewIds.size());
    System.out.println("关键词搜索结果: " + viewIds);
  }
  
  @Test
  @DisplayName("测试基于字段ID检索图表")
  void testSearchByFieldIds() {
    // 测试数据 - 模拟字段ID
    // 注意：这里使用测试环境中实际存在的字段ID，如果测试失败请替换为实际存在的ID
    List<String> fieldIds = Arrays.asList("sales_amount", "region", "product_name");
    
    // 调用方法
    List<String> viewIds = chartRetriever.searchByFieldIds(fieldIds, testUser, ChannelType.TENANT);
    
    // 验证结果
    assertNotNull(viewIds, "返回的图表ID列表不应为null");
    System.out.println("字段ID搜索结果数量: " + viewIds.size());
    System.out.println("字段ID搜索结果: " + viewIds);
  }
  
  @Test
  @DisplayName("测试综合性能 - 多种搜索方式效果对比")
  void testComprehensiveSearchPerformance() {
    System.out.println("========= 开始综合搜索性能测试 =========");
    
    // 1. 向量搜索
    long startTime = System.currentTimeMillis();
    List<String> vectorResults = chartRetriever.searchByVector("销售业绩分析", testUser, null, ChannelType.TENANT);
    long vectorTime = System.currentTimeMillis() - startTime;
    
    // 2. 关键词搜索
    startTime = System.currentTimeMillis();
    List<String> keywordResults = chartRetriever.searchByKeywords(Arrays.asList("销售", "业绩", "分析"), testUser, ChannelType.TENANT);
    long keywordTime = System.currentTimeMillis() - startTime;
    
    // 3. 字段ID搜索
    startTime = System.currentTimeMillis();
    List<String> fieldIdResults = chartRetriever.searchByFieldIds(Arrays.asList("sales_performance"), testUser, ChannelType.TENANT);
    long fieldIdTime = System.currentTimeMillis() - startTime;
    
    // 输出性能对比
    System.out.println("向量搜索: 结果数=" + vectorResults.size() + ", 耗时=" + vectorTime + "ms");
    System.out.println("关键词搜索: 结果数=" + keywordResults.size() + ", 耗时=" + keywordTime + "ms");
    System.out.println("字段ID搜索: 结果数=" + fieldIdResults.size() + ", 耗时=" + fieldIdTime + "ms");
    System.out.println("========= 综合搜索性能测试结束 =========");
    
    // 验证结果
    assertTrue(vectorTime > 0, "向量搜索应该有时间消耗");
    assertTrue(keywordTime > 0, "关键词搜索应该有时间消耗");
    assertTrue(fieldIdTime > 0, "字段ID搜索应该有时间消耗");
  }
}
