package com.fxiaoke.chatbi.knowledge.builder;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.knowledge.service.ChartKnowledgeBuildingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 图表知识构建测试
 */
@Slf4j
@ActiveProfiles("fstest")
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class ChartKnowledgeBuilderTest {

    @Autowired
    private ChartKnowledgeBuildingService chartKnowledgeBuildingService;

    private String testSysTenant = "-1";
    private String testTenant = "82313";

    private String testUserId = "-10000";

    private String testSysUserId="-10000";

    private String testViewId = "BI_5992632037aa1b6eebec45ee";

    @Test
    @Disabled("手动运行")
    public void build() {
        Assertions.assertTrue(StringUtils.isNotBlank(testTenant));
        Assertions.assertTrue(StringUtils.isNotBlank(testUserId));
        
        UserIdentity testUser = new UserIdentity();
        testUser.setTenantId(testTenant);
        testUser.setUserId(testUserId);
        
        // 使用新的服务接口替代旧的构建器
        chartKnowledgeBuildingService.buildAllChartKnowledge(testUser);
    }

    @Test
    @Disabled("手动运行")
    public void buildWithSysUser(){
        Assertions.assertTrue(StringUtils.isNotBlank(testSysTenant));
        
        UserIdentity testSysUser = new UserIdentity();
        testSysUser.setTenantId(testSysTenant);
        testSysUser.setUserId(testSysUserId);
        
        // 使用新的服务接口替代旧的构建器
        chartKnowledgeBuildingService.buildAllChartKnowledge(testSysUser);
    }

    @Test
    @Disabled("手动运行")
    public void buildSingleChart(){
        Assertions.assertTrue(StringUtils.isNotBlank(testSysTenant));
        Assertions.assertTrue(StringUtils.isNotBlank(testViewId));
        
        UserIdentity testUser = new UserIdentity();
        testUser.setTenantId(testSysTenant);
        testUser.setUserId(testUserId);
        
        // 使用新的服务接口替代旧的构建器
        chartKnowledgeBuildingService.buildChartKnowledge(testUser, testViewId);
    }
}