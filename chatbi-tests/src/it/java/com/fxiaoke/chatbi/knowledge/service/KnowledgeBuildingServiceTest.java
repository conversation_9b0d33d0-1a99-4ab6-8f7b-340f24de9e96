package com.fxiaoke.chatbi.knowledge.service;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 知识建设服务集成测试
 * 测试各种知识类型的构建方法
 */
@Slf4j
@ActiveProfiles("fstest")
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class KnowledgeBuildingServiceTest {

    @Autowired
    private KnowledgeBuildingService knowledgeBuildingService;

    // 测试租户ID，使用测试环境租户
    private final String testSysTenant = "-1";
    private final String testTenant = "82313";

    // 测试用户ID
    private final String testUserId = "-10000";

    // 系统用户ID
    private final String testSysUserId = "-10000";

    // 测试图表ID
    private final String testViewId = "BI_67ca595a64e82893fc929dd5";

    /**
     * 测试构建所有知识
     */
    @Test
    @Disabled("手动运行，可能耗时较长")
    public void testBuildAllKnowledge() {
        log.info("开始测试构建所有知识");
        Assertions.assertTrue(StringUtils.isNotBlank(testTenant));
        Assertions.assertTrue(StringUtils.isNotBlank(testUserId));
        
        UserIdentity testUser = UserIdentity.builder()
                .tenantId(testTenant)
                .userId(testUserId)
                .ea(testTenant) // 设置ea属性，与tenantId保持一致
                .build();
        
        knowledgeBuildingService.buildAllKnowledge(testUser);
        log.info("完成测试构建所有知识");
    }

    /**
     * 测试构建单个图表知识
     */
    @Test
    @Disabled("手动运行")
    public void testBuildChartKnowledge() {
        log.info("开始测试构建单个图表知识");
        Assertions.assertTrue(StringUtils.isNotBlank(testTenant));
        Assertions.assertTrue(StringUtils.isNotBlank(testViewId));
        
        UserIdentity testUser = UserIdentity.builder()
                .tenantId(testTenant)
                .userId(testUserId)
                .ea(testTenant) // 设置ea属性，与tenantId保持一致
                .build();
        
        knowledgeBuildingService.buildChartKnowledge(testUser, testViewId);
        log.info("完成测试构建单个图表知识");
    }

    /**
     * 测试构建所有图表知识
     */
    @Test
    @Disabled("手动运行，可能耗时较长")
    public void testBuildAllChartKnowledge() {
        log.info("开始测试构建所有图表知识");
        Assertions.assertTrue(StringUtils.isNotBlank(testTenant));
        
        UserIdentity testUser = UserIdentity.builder()
                .tenantId(testTenant)
                .userId(testUserId)
                .ea(testTenant) // 设置ea属性，与tenantId保持一致
                .build();
        
        knowledgeBuildingService.buildAllChartKnowledge(testUser);
        log.info("完成测试构建所有图表知识");
    }

    /**
     * 测试使用系统用户构建所有图表知识
     */
    @Test
    @Disabled("手动运行，可能耗时较长")
    public void testBuildAllChartKnowledgeWithSysUser() {
        log.info("开始测试使用系统用户构建所有图表知识");
        Assertions.assertTrue(StringUtils.isNotBlank(testTenant));
        
        UserIdentity testSysUser = UserIdentity.builder()
                .tenantId(testTenant)
                .userId(testSysUserId)
                .ea(testTenant) // 设置ea属性，与tenantId保持一致
                .build();
        
        knowledgeBuildingService.buildAllChartKnowledge(testSysUser);
        log.info("完成测试使用系统用户构建所有图表知识");
    }

    /**
     * 测试构建企业知识
     */
    @Test
    @Disabled("手动运行")
    public void testBuildEnterpriseKnowledge() {
        log.info("开始测试构建企业知识");
        Assertions.assertTrue(StringUtils.isNotBlank(testSysTenant));
        
        UserIdentity testUser = UserIdentity.builder()
                .tenantId(testSysTenant)
                .userId(testUserId)
                .ea(testSysTenant) // 设置ea属性，与tenantId保持一致
                .build();
        
        knowledgeBuildingService.buildEnterpriseKnowledge(testUser);
        log.info("完成测试构建企业知识");
    }

    /**
     * 测试构建字段知识
     */
    @Test
    @Disabled("手动运行")
    public void testBuildFieldKnowledge() {
        log.info("开始测试构建字段知识");
        Assertions.assertTrue(StringUtils.isNotBlank(testTenant));
        
        UserIdentity testUser = UserIdentity.builder()
                .tenantId(testTenant)
                .userId(testUserId)
                .ea(testTenant) // 设置ea属性，与tenantId保持一致
                .build();
        
        knowledgeBuildingService.buildFieldKnowledge(testUser);
        log.info("完成测试构建字段知识");
    }
}
