package com.fxiaoke.chatbi;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.request.ChatBiRequest;
import com.fxiaoke.chatbi.common.model.response.ChartDataResponse;
import com.fxiaoke.chatbi.common.model.response.FollowUpResponse;
import com.fxiaoke.chatbi.common.model.response.InsightResponse;
import com.fxiaoke.chatbi.common.model.response.ReasoningResponse;
import com.fxiaoke.chatbi.service.ChatBiService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * ChatBI主流程集成测试
 */
@Slf4j
@SpringBootTest
public class ChatBiMainFlowIT {

  @Autowired
  private ChatBiService chatBiService;

  @Test
  void testMainFlow() {
    // 1. 准备测试数据
    UserIdentity userIdentity = UserIdentity.builder().userId("1000").tenantId("82313").ea("82313").build();

    String sessionId = "test_" + System.currentTimeMillis();

    // 2. 执行主流程测试
    // 2.1 发起对话
    ChatBiRequest request = ChatBiRequest.builder()
                                         .sessionId(sessionId)
                                         .instructions("分析销售部门最近3个月的业绩情况")
                                         .build();

    log.info("开始测试主流程 - 发起对话请求");
    ReasoningResponse response = chatBiService.chat(request, userIdentity);
    String requestId = response.getRequestId();

    // 2.2 获取图表数据
    log.info("开始测试主流程 - 获取图表数据");
    ChartDataResponse chartData = chatBiService.loadChartData(requestId, userIdentity);

    // 2.3 获取数据洞察
    log.info("开始测试主流程 - 获取数据洞察");
    InsightResponse insight = chatBiService.loadInsight(requestId, userIdentity);

    // 2.4 获取追问建议
    log.info("开始测试主流程 - 获取追问建议");
    FollowUpResponse followUp = chatBiService.loadFollowUpQuestions(requestId, userIdentity);

    // 3. 执行多轮对话测试
    log.info("开始测试主流程 - 发起追问");
    ChatBiRequest followUpRequest = ChatBiRequest.builder()
                                                 .sessionId(sessionId)
                                                 .instructions("这个趋势和去年同期相比如何？")
                                                 .build();

    ReasoningResponse followUpResponse = chatBiService.chat(followUpRequest, userIdentity);
  }
} 