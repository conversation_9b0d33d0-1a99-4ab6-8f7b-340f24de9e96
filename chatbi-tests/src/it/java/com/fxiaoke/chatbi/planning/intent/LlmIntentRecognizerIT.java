package com.fxiaoke.chatbi.planning.intent;

import com.fxiaoke.chatbi.action.impl.intent.LlmIntentRecognizer;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.memory.ConversationContext;
import com.fxiaoke.chatbi.common.model.intent.UserIntent;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * LlmIntentRecognizer集成测试
 * 使用真实的服务实例进行测试，不使用任何mock
 */
@SpringBootTest
@ActiveProfiles("fstest")
class LlmIntentRecognizerIT {

    @Autowired
    private LlmIntentRecognizer intentRecognizer;

    private ConversationContext context;

    @BeforeEach
    void setUp() {
        // 设置基本的会话上下文
        context = new ConversationContext();
        context.setSessionId("test-session");
        UserIdentity userIdentity = new UserIdentity();
        userIdentity.setUserId("1000");
        userIdentity.setTenantId("82313");
        context.setUserIdentity(userIdentity);
    }

    @Test
    void testSalesPerformanceAnalysis() {
        // 准备测试数据
        String query = "分析最近一个季度的销售业绩";

        // 执行测试
        UserIntent intent = intentRecognizer.recognize(query, context);

        // 验证结果
        assertNotNull(intent, "意图不应为空");
        assertNotNull(intent.getIntentId(), "意图ID不应为空");
        assertEquals(query, intent.getInstructions(), "原始指令应该保持不变");
    }

    @Test
    void testVagueQuery() {
        // 准备测试数据
        String query = "帮我看看";

        // 执行测试
        UserIntent intent = intentRecognizer.recognize(query, context);

        // 验证结果
        assertNotNull(intent, "意图不应为空");
        assertTrue(intent.isNeedsClarification(), "模糊查询应该需要澄清");
        assertNotNull(intent.getClarificationQuestion(), "应该提供澄清问题");
    }

    @Test
    void testNullContext() {
        // 准备测试数据
        String query = "分析销售趋势";

        // 执行测试
        UserIntent intent = intentRecognizer.recognize(query, null);

        // 验证结果
        assertNotNull(intent, "意图不应为空");
        assertTrue(intent.isNeedsClarification(), "空上下文应该需要澄清");
        assertNotNull(intent.getClarificationQuestion(), "应该提供澄清问题");
    }

    @Test
    void testEmptyQuery() {
        // 准备测试数据
        String query = "";

        // 执行测试
        UserIntent intent = intentRecognizer.recognize(query, context);

        // 验证结果
        assertNotNull(intent, "意图不应为空");
        assertTrue(intent.isNeedsClarification(), "空查询应该需要澄清");
        assertNotNull(intent.getClarificationQuestion(), "应该提供澄清问题");
    }

    @Test
    void testContextAwareQuery() {
        // 第一次查询
        String firstQuery = "分析最近一个季度的销售业绩";
        UserIntent firstIntent = intentRecognizer.recognize(firstQuery, context);
        assertNotNull(firstIntent, "第一次意图不应为空");

        // 后续查询
        String followUpQuery = "与去年同期相比如何？";
        UserIntent followUpIntent = intentRecognizer.recognize(followUpQuery, context);

        // 验证结果
        assertNotNull(followUpIntent, "后续意图不应为空");
        assertNotNull(followUpIntent.getIntentId(), "意图ID不应为空");
        assertEquals(followUpQuery, followUpIntent.getInstructions(), "原始指令应该保持不变");
    }
} 