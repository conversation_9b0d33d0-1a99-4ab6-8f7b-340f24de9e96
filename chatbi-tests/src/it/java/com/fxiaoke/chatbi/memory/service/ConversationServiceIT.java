package com.fxiaoke.chatbi.memory.service;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.memory.ConversationContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ConversationService集成测试
 * 验证会话服务的核心功能
 */
@SpringBootTest
@ActiveProfiles({"fstest"})
public class ConversationServiceIT {

    @Autowired
    private ConversationService conversationService;

    private UserIdentity testUser;

    private final String sessionId  = UUID.randomUUID().toString();

    @BeforeEach
    void setUp() {
        // 设置测试用户
        testUser = UserIdentity.builder()
                .userId("1000")
                .tenantId("82313")
                .build();
    }

    @Test
    void shouldCreateNewContext() {
        // when
        ConversationContext context = conversationService.createContext(testUser, sessionId);

        // then
        assertNotNull(context, "创建的上下文不应为空");
        assertNotNull(context.getSessionId(), "会话ID不应为空");
        assertEquals(testUser, context.getUserIdentity(), "用户身份信息应匹配");
    }

    @Test
    void shouldGetOrCreateContext() {
        // given
        ConversationContext created = conversationService.createContext(testUser, sessionId);
        String sessionId = created.getSessionId();

        // when
        ConversationContext retrieved = conversationService.getOrCreateContext(sessionId, testUser);

        // then
        assertNotNull(retrieved, "获取的上下文不应为空");
        assertEquals(sessionId, retrieved.getSessionId(), "会话ID应匹配");
        assertEquals(testUser, retrieved.getUserIdentity(), "用户身份信息应匹配");
    }

    @Test
    void shouldUpdateContext() {
        // given
        ConversationContext context = conversationService.createContext(testUser, sessionId);
        String lastIntent = "分析近30天的销售趋势";
        
        // when
        context.setLastIntent(lastIntent);
        conversationService.updateContext(context);
        
        // then
        ConversationContext updated = conversationService.findById(context.getSessionId());
        assertNotNull(updated, "更新后的上下文不应为空");
        assertEquals(lastIntent, updated.getLastIntent(), "最后的意图应该被更新");
    }

    @Test
    void shouldFindByUser() {
        // given
        conversationService.createContext(testUser, sessionId);
        conversationService.createContext(testUser, sessionId);

        // when
        List<ConversationContext> contexts = conversationService.findByUser(testUser);

        // then
        assertNotNull(contexts, "用户的会话列表不应为空");
        assertTrue(contexts.size() >= 2, "应至少包含两个会话");
        contexts.forEach(context -> {
            assertEquals(testUser.getUserId(), context.getUserIdentity().getUserId(), 
                "所有会话都应属于测试用户");
        });
    }

    @Test
    void shouldFindById() {
        // given
        ConversationContext created = conversationService.createContext(testUser, sessionId);

        // when
        ConversationContext found = conversationService.findById(created.getSessionId());

        // then
        assertNotNull(found, "通过ID查找的上下文不应为空");
        assertEquals(created.getSessionId(), found.getSessionId(), "会话ID应匹配");
        assertEquals(created.getUserIdentity(), found.getUserIdentity(), "用户身份信息应匹配");
    }

    @Test
    void shouldReturnNullForNonExistentSession() {
        // when
        ConversationContext context = conversationService.findById("non_existent_session_id");

        // then
        assertNull(context, "不存在的会话ID应返回null");
    }
} 