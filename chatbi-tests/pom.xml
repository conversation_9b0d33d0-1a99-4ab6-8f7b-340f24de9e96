<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.fxiaoke</groupId>
    <artifactId>fs-bi-agent</artifactId>
    <version>9.5.0-SNAPSHOT</version>
  </parent>

  <artifactId>chatbi-tests</artifactId>
  <packaging>jar</packaging>
  <name>chatbi-tests</name>
  <description>ChatBI 测试模块，提供单元测试和集成测试</description>

  <dependencies>
    <!-- 模块依赖 -->
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>chatbi-common</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>chatbi-planning</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>chatbi-knowledge</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>chatbi-memory</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>chatbi-action</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>chatbi-monitoring</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>chatbi-integration</artifactId>
    </dependency>

    <!-- 测试依赖 -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-junit-jupiter</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.h2database</groupId>
      <artifactId>h2</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>chatbi-bootstrap</artifactId>
      <type>war</type>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <!-- 单元测试插件 -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>3.2.5</version>
        <configuration>
          <argLine>
            --add-opens java.base/java.lang=ALL-UNNAMED
            --add-opens java.base/java.lang.reflect=ALL-UNNAMED
            --add-opens java.base/java.io=ALL-UNNAMED
            --add-opens java.base/java.util=ALL-UNNAMED
            --add-opens java.base/java.text=ALL-UNNAMED
            --add-opens java.desktop/java.awt.font=ALL-UNNAMED
          </argLine>
        </configuration>
      </plugin>
      <!-- 集成测试插件 -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-failsafe-plugin</artifactId>
        <version>3.2.5</version>
        <configuration>
          <argLine>
            --add-opens java.base/java.lang=ALL-UNNAMED
            --add-opens java.base/java.lang.reflect=ALL-UNNAMED
            --add-opens java.base/java.io=ALL-UNNAMED
            --add-opens java.base/java.util=ALL-UNNAMED
            --add-opens java.base/java.text=ALL-UNNAMED
            --add-opens java.desktop/java.awt.font=ALL-UNNAMED
          </argLine>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>integration-test</goal>
              <goal>verify</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <!-- 添加测试源码目录插件 -->
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>add-integration-test-source</id>
            <phase>generate-test-sources</phase>
            <goals>
              <goal>add-test-source</goal>
            </goals>
            <configuration>
              <sources>
                <source>src/it/java</source>
              </sources>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <!-- 禁用注解处理以解决循环依赖问题 -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <proc>none</proc>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>