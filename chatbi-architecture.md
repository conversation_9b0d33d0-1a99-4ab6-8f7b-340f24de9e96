flowchart TB
    subgraph 交互层
        direction BT
        A1[多轮对话]
        A2[反馈与纠错机制]
        A3[可视化交互组件]
        A4[对话上下文管理]
        A8[推理过程]
    end

    subgraph 应用层
        direction BT
        B1[图表召回]
        B2[查数]
        B3[推荐]
        B4[数据解读]
        B5[归因分析]
        B6[趋势预测]
        B7[图表生成]
    end

    subgraph 核心引擎
        direction BT
        C1[记忆管理]
        subgraph 记忆管理模块
            direction TB
            M1[短期记忆]
            subgraph 短期记忆详细
                M1a[会话上下文]
                M1b[临时缓存]
            end
            M2[中期记忆]
            subgraph 中期记忆详细
                M2a[用户偏好]
                M2b[交互历史]
            end
            M3[长期记忆]
            subgraph 长期记忆详细
                M3a[用户档案]
                M3b[业务数据]
                M3c[知识库]
            end
        end
        C2[指令预处理]
        C3[意图识别]
        C4[信息提取]
        C5[任务规划]
        subgraph RAG模块
            direction BT
            R1[多路召回]
            R2[重排序]
            R3[反馈机制]
            subgraph 多路召回
                direction BT
                MR1[关键字召回]
                MR2[向量召回]
                MR3[元数据召回]
            end
        end
        C7[推理过程]
        C8[企业知识库]
        C9[元数据图谱]
        C10[Text2DSL]
    end

    subgraph 基础平台层
        direction BT
        D1[大模型]
        D2[Prompt模版]
        D3[BI平台]
    end

    subgraph 基础设施
        direction BT
        E1[Clickhouse]
        E2[Postgresql]
        E3[ElasticSearch]
        E4[Redis]
        E5[日志分析]
        E6[监控告警]
    end

    交互层 --> 应用层 --> 核心引擎 --> 基础平台层 --> 基础设施