package com.fxiaoke.chatbi.monitoring.log;

import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.AppenderBase;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 内存日志存储器
 * 用于在内存中保存最近2分钟的日志，并支持按traceId过滤
 */
@Slf4j
public class MemoryAppender extends AppenderBase<ILoggingEvent> {

    /**
     * 日志存储，key为traceId
     * 使用Guava Cache自动过期功能，保留2分钟
     */
    private static final Cache<String, List<ILoggingEvent>> logStore = CacheBuilder.newBuilder()
            .expireAfterWrite(2, TimeUnit.MINUTES)
            .build();

    @Override
    protected void append(ILoggingEvent event) {
        try {
            String traceId = event.getMDCPropertyMap().get("traceId");
            if (traceId != null) {
                List<ILoggingEvent> logs = logStore.getIfPresent(traceId);
                if (logs == null) {
                    logs = new ArrayList<>();
                }
                logs.add(event);
                logStore.put(traceId, logs);
            }
        } catch (Exception e) {
            log.warn("Failed to append log to memory store", e);
        }
    }

    /**
     * 获取指定traceId的所有日志
     *
     * @param traceId 跟踪ID
     * @return 日志列表
     */
    public static List<ILoggingEvent> getLogsByTraceId(String traceId) {
        List<ILoggingEvent> logs = logStore.getIfPresent(traceId);
        return logs != null ? new ArrayList<>(logs) : new ArrayList<>();
    }

    /**
     * 获取指定traceId的日志内容
     *
     * @param traceId 跟踪ID
     * @return 日志内容列表
     */
    public static List<String> getLogContentsByTraceId(String traceId) {
        return getLogsByTraceId(traceId).stream()
                .map(ILoggingEvent::getFormattedMessage)
                .collect(Collectors.toList());
    }

    /**
     * 清理指定traceId的日志
     *
     * @param traceId 跟踪ID
     */
    public static void clearLogs(String traceId) {
        logStore.invalidate(traceId);
    }

    /**
     * 清理所有日志
     */
    public static void clearAllLogs() {
        logStore.invalidateAll();
    }

    /**
     * 获取当前存储的traceId列表
     *
     * @return traceId列表
     */
    public static List<String> getTraceIds() {
        return new ArrayList<>(logStore.asMap().keySet());
    }
} 