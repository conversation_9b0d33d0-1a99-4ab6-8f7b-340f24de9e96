package com.fxiaoke.chatbi.monitoring.log;

import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.spi.ILoggingEvent;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 内存日志工具类
 */
public class LogMemoryUtils {

    private static final String MEMORY_APPENDER_NAME = "MEMORY";
    private static volatile boolean initialized = false;

    /**
     * 初始化内存日志appender
     */
    public static synchronized void initMemoryAppender() {
        if (initialized) {
            return;
        }

        LoggerContext context = (LoggerContext) LoggerFactory.getILoggerFactory();
        Logger rootLogger = context.getLogger(Logger.ROOT_LOGGER_NAME);

        // 检查是否已经存在内存appender
        if (rootLogger.getAppender(MEMORY_APPENDER_NAME) == null) {
            MemoryAppender memoryAppender = new MemoryAppender();
            memoryAppender.setContext(context);
            memoryAppender.setName(MEMORY_APPENDER_NAME);
            memoryAppender.start();
            rootLogger.addAppender(memoryAppender);
        }

        initialized = true;
    }

    /**
     * 获取指定traceId的所有日志
     *
     * @param traceId 跟踪ID
     * @return 日志事件列表
     */
    public static List<ILoggingEvent> getLogsByTraceId(String traceId) {
        initMemoryAppender();
        return MemoryAppender.getLogsByTraceId(traceId);
    }

    /**
     * 获取指定traceId的日志内容
     *
     * @param traceId 跟踪ID
     * @return 日志内容列表
     */
    public static List<String> getLogContentsByTraceId(String traceId) {
        initMemoryAppender();
        return MemoryAppender.getLogContentsByTraceId(traceId);
    }

    /**
     * 清理指定traceId的日志
     *
     * @param traceId 跟踪ID
     */
    public static void clearLogs(String traceId) {
        MemoryAppender.clearLogs(traceId);
    }

    /**
     * 清理所有日志
     */
    public static void clearAllLogs() {
        MemoryAppender.clearAllLogs();
    }

    /**
     * 获取当前存储的所有traceId
     *
     * @return traceId列表
     */
    public static List<String> getTraceIds() {
        return MemoryAppender.getTraceIds();
    }
} 