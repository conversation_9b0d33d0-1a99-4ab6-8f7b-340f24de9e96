# ChatBI: CRM领域数据分析助手

基于认知架构的智能BI助手，帮助业务人员通过自然语言交互获取数据洞察。

## 项目结构

项目采用Maven多模块结构，以单体应用方式部署：

```
chatbi/
├── pom.xml                              # 主POM文件
├── README.md                            # 项目说明文档
├── docs/                                # 文档目录
│   ├── architecture/                    # 架构文档
│   ├── api/                             # API文档
│   └── deployment/                      # 部署文档
│
├── chatbi-common/                       # 公共模块(JAR)
│   ├── pom.xml
│   └── src/
│       └── main/java/com/fxiaoke/chatbi/common/
│           ├── constants/               # 常量定义
│           ├── dto/                     # 数据传输对象
│           ├── enums/                   # 枚举定义
│           ├── exceptions/              # 异常类
│           ├── utils/                   # 工具类
│           ├── model/                   # 共享模型类
│           └── config/                  # 配置相关(原config模块)
│               ├── properties/          # 配置属性类
│               └── service/             # 配置服务实现
│
├── chatbi-planning/                     # 规划系统模块(JAR)
│   ├── pom.xml
│   └── src/
│       └── main/java/com/fxiaoke/chatbi/planning/
│           ├── api/                     # 模块内部接口
│           ├── config/                  # 模块配置类
│           ├── service/                 # 服务实现
│           │   ├── intent/              # 意图识别
│           │   ├── generator/           # 计划生成
│           │   └── optimizer/           # 计划优化
│           ├── model/                   # 模型类
│           └── utils/                   # 工具类
│
├── chatbi-knowledge/                    # 知识系统模块(JAR)
│   ├── pom.xml
│   └── src/
│       └── main/java/com/fxiaoke/chatbi/knowledge/
│           ├── api/                     # 模块内部接口
│           ├── config/                  # 模块配置类
│           ├── service/                 # 服务实现
│           │   ├── domain/              # 领域知识
│           │   ├── rules/               # 业务规则
│           │   └── methods/             # 分析方法
│           ├── repository/              # 存储层
│           │   ├── keyword/             # 关键词索引
│           │   └── vector/              # 向量索引
│           └── model/                   # 模型类
│
├── chatbi-memory/                       # 记忆系统模块(JAR)
│   ├── pom.xml
│   └── src/
│       └── main/java/com/fxiaoke/chatbi/memory/
│           ├── api/                     # 模块内部接口
│           ├── config/                  # 模块配置类
│           ├── service/                 # 服务实现
│           │   ├── shortterm/           # 短期记忆
│           │   ├── longterm/            # 长期记忆
│           │   └── working/             # 工作记忆
│           ├── repository/              # 存储层
│           ├── model/                   # 模型类
│           └── strategy/                # 记忆策略
│
├── chatbi-action/                       # 执行系统模块(JAR)
│   ├── pom.xml
│   └── src/
│       └── main/java/com/fxiaoke/chatbi/action/
│           ├── api/                     # 模块内部接口
│           ├── config/                  # 模块配置类
│           ├── service/                 # 服务实现
│           │   ├── registry/            # 动作注册
│           │   ├── executor/            # 执行器
│           │   └── processor/           # 结果处理
│           ├── handlers/                # 动作处理器
│           ├── model/                   # 模型类
│           └── integration/             # 外部系统集成
│               └── bi/                  # BI系统集成
│
├── chatbi-monitoring/                   # 监控模块(JAR)
│   ├── pom.xml
│   └── src/
│       └── main/java/com/fxiaoke/chatbi/monitoring/
│           ├── config/                  # 配置类
│           ├── service/                 # 服务实现
│           │   ├── metrics/             # 指标收集
│           │   └── logging/             # 日志服务
│           └── model/                   # 模型类
│
├── chatbi-integration/                  # 集成模块(JAR)
│   ├── pom.xml
│   └── src/
│       └── main/java/com/fxiaoke/chatbi/integration/
│           ├── config/                  # 配置类
│           ├── service/                 # 服务实现
│           │   ├── llm/                 # LLM集成
│           │   ├── datasource/          # 数据源集成
│           │   └── external/            # 其他外部系统
│           └── model/                   # 模型类
│
├── chatbi-bootstrap/                    # 启动模块(WAR)
│   ├── pom.xml
│   └── src/
│       ├── main/java/com/fxiaoke/chatbi/
│       │   ├── ChatBIApplication.java   # 应用入口
│       │   ├── controller/              # REST控制器
│       │   ├── filter/                  # 过滤器
│       │   ├── interceptor/             # 拦截器
│       │   └── security/                # 安全配置
│       └── main/resources/
│           ├── application.yml          # 应用配置
│           ├── application-dev.yml      # 开发环境配置
│           └── application-prod.yml     # 生产环境配置
│
└── chatbi-tests/                        # 测试模块(JAR)
    ├── pom.xml
    └── src/
        ├── test/java/com/fxiaoke/chatbi/  # 单元测试
        └── it/java/com/fxiaoke/chatbi/    # 集成测试
```

## 模块职责说明

### chatbi-common
公共模块，包含共享工具、模型、配置等基础设施代码。

### chatbi-planning
规划系统模块，负责理解用户意图并生成执行计划。

### chatbi-knowledge
知识系统模块，提供领域知识和业务规则支持。

### chatbi-memory
记忆系统模块，管理对话上下文和用户会话。

### chatbi-action
执行系统模块，执行具体动作并处理结果。

### chatbi-monitoring
监控模块，收集系统指标和日志信息。

### chatbi-integration
集成模块，负责与外部系统如LLM、数据源的集成。

### chatbi-bootstrap
启动模块，作为应用入口，包含控制器和配置。唯一的WAR包。

### chatbi-tests
测试模块，包含单元测试和集成测试。

## 技术栈

- Java 17+
- Spring Boot
- Maven
- JUnit 5
- LLM集成
- 数据可视化

## 构建与运行

### 构建项目
```bash
mvn clean package
```

### 运行应用
```bash
java -jar chatbi-bootstrap/target/chatbi-bootstrap-[版本].war
```

## 开发指南

详细开发指南请参考 [docs/architecture/](docs/architecture/) 目录中的文档。

## 项目概述

ChatBI 是一款专注于 CRM 领域的智能数据分析助手，通过自然语言交互方式，帮助业务人员快速获取数据洞察，无需复杂的数据操作技能。项目基于 Spring Boot 框架开发，集成了大语言模型（LLM）能力，实现了智能图表召回、多轮对话、数据分析和可视化等核心功能。

## 已实现的核心功能

### 1. 图表召回系统
- **智能匹配**：通过 `DataAnalysisAction` 类解析用户自然语言查询，提取关键信息并匹配最相关的预设图表
- **相关度评分**：为每个匹配结果提供置信度评分，确保返回最相关的图表
- **参数转换**：将自然语言提取的参数转换为 BI 系统可识别的查询参数

### 2. 多轮对话能力
- **上下文管理**：通过 `ChatBiServiceImpl` 维护对话历史，支持指代词和省略表达的理解
- **历史消息处理**：实现了历史消息的格式化和传递机制，保持对话连贯性
- **延续性分析**：支持基于前序对话的延续性提问和深入分析

### 3. 全量维度表管理
- **维度体系**：构建了完整的数据维度体系，支持多维度数据分析
- **ID 映射服务**：通过 `IdMappingService` 实现短 ID 和长 ID 的映射转换，减少 token 占用
- **异步上下文传递**：解决了异步环境中 ThreadLocal 上下文丢失的问题

### 4. 演示场景构建
- **场景化展示**：完成了多个核心图表的场景化展示
- **完整流程**：每个场景包含查询-分析-展示的完整流程
- **业务场景覆盖**：支持 CRM 领域的典型业务场景分析需求

### 5. 智能推荐功能
- **相关分析推荐**：通过 `RecommendationService` 基于当前查询自动生成相关的分析方向
- **预设推荐问题**：支持快速匹配和响应预设的推荐问题
- **个性化推荐**：根据用户历史行为和当前分析提供个性化推荐

### 6. 图表解读生成
- **自动解读**：使用 `ChartInsightService` 自动生成图表数据解读
- **多层次解读**：提供快速洞察和完整洞察两种解读深度
- **关键特征识别**：自动识别数据中的趋势、异常点和关键特征

### 7. ECharts 转换能力
- **数据转换**：通过 `EChartsConverterService` 将 BI 数据转换为 ECharts 配置
- **多图表支持**：支持多种图表类型的自动转换和优化
- **前端展示优化**：确保图表在前端展示的美观性和交互性

### 8. 提示词模板管理
- **模板系统**：实现了 `PromptTemplateService` 管理各类提示词模板
- **场景覆盖**：支持数据分析、图表解读等多种场景的专业提示词
- **动态加载**：提供模板的动态加载和更新机制

### 9. 长短字符字典映射
- **ID 映射**：通过 `IdMappingService` 实现长 ID 到短 ID 的映射转换
- **上下文管理**：使用 `ThreadLocal` 实现请求级别的 ID 映射管理
- **异步传递**：支持在异步环境中传递映射上下文，解决线程间上下文丢失问题

## 技术架构

### 1. 后端架构
- **框架选择**：基于 Spring Boot 框架，使用纷享 Spring Cloud 父 POM
- **模块化设计**：
  - `controller`：处理 HTTP 请求和响应
  - `service`：实现核心业务逻辑
  - `model`：定义数据模型和实体类
  - `llm`：大语言模型集成和交互
  - `integration`：外部系统集成
  - `config`：系统配置
  - `utils`：通用工具类

### 2. 数据处理流程
1. **用户查询接收**：通过 `ChatBiController` 接收用户自然语言查询
2. **意图解析**：使用 `DataAnalysisAction` 解析用户意图和查询参数
3. **图表匹配**：根据解析结果匹配最相关的预设图表
4. **数据查询**：通过 `ChartDataQueryAction` 执行图表数据查询
5. **数据转换**：使用 `EChartsConverterService` 将查询结果转换为 ECharts 格式
6. **解读生成**：通过 `ChartInsightService` 生成图表解读
7. **结果返回**：构建完整响应并返回给用户

### 3. LLM 集成架构
- **代理模式**：通过 `PAASLlmProxy` 封装 LLM 调用逻辑
- **提示词管理**：使用 `PromptTemplateService` 管理各类提示词模板
- **知识生成**：通过 `KnowledgePromptGenerator` 生成领域知识提示词
- **上下文管理**：实现多轮对话的上下文管理和传递

### 4. 异步处理架构
- **异步注解**：使用 `@Async` 注解实现非阻塞的数据处理
- **上下文传递**：通过 `MappingContext` 解决异步环境中的上下文传递问题
- **Redis 缓存**：使用 Redis 存储异步处理结果，支持客户端轮询获取

## 系统流程

1. **用户发起查询**：用户通过前端发送自然语言查询
2. **初始响应**：系统返回初始加载状态和请求 ID
3. **异步处理**：
   - 分析用户查询意图，提取关键参数
   - 匹配最相关的预设图表
   - 执行图表数据查询，应用 ID 映射
   - 转换为 ECharts 格式并生成解读
   - 提供相关推荐，构建完整响应
4. **结果获取**：客户端通过请求 ID 轮询获取处理结果
5. **交互继续**：用户可以基于结果继续提问，形成多轮对话

## 技术亮点

1. **异步处理模式**：使用 `@Async` 注解实现非阻塞的数据处理流程，提高系统响应速度
2. **上下文管理**：通过 `MappingContext` 解决异步环境中的上下文传递问题
3. **模块化设计**：各功能模块职责清晰，如图表查询、数据转换、解读生成等
4. **可扩展架构**：支持新增图表类型、分析维度和解读模板
5. **ID 映射优化**：通过短 ID 映射减少了 token 占用，提高了系统效率
6. **完善的日志**：详细的日志记录，便于问题排查和性能监控
7. **提示词工程**：精心设计的提示词模板，提高 LLM 输出质量和稳定性

## 项目特色

1. **领域专注**：专注于 CRM 领域的数据分析需求，提供针对性的解决方案
2. **自然交互**：通过自然语言交互，降低数据分析的技术门槛
3. **智能解读**：自动生成图表解读，帮助用户快速理解数据洞察
4. **多轮对话**：支持多轮对话，实现渐进式的深入分析
5. **知识赋能**：集成领域知识，提供更专业的分析结果和建议

## 未来发展方向

1. **更深入的数据分析**：增强归因分析、预测分析等高级分析能力
2. **更丰富的可视化**：支持更多类型的图表和交互式可视化
3. **更智能的推荐**：基于用户行为和业务场景的更精准推荐
4. **更完善的知识体系**：扩充领域知识库，提升分析的专业性
5. **更强的个性化**：根据用户偏好和使用习惯提供个性化体验

这个项目展示了如何将大语言模型能力与传统 BI 系统结合，创造出更智能、更易用的数据分析工具，为 CRM 领域的业务人员提供了强大的数据洞察支持。