# 业务逻辑流程图

```mermaid
flowchart TD
    A[用户登录] --> B[身份验证]
    B --> |认证成功 生成UserIdentity| C[首页仪表盘]
    
    C --> D{选择分析类型}
    D --> |销售业绩总览| E1[加载销售总览数据]
    D --> |销售代表业绩| E2[加载代表业绩数据] 
    D --> |产品销售分析| E3[加载产品销售数据]
    D --> |区域销售分析| E4[加载区域销售数据]
    
    E1 --> F1[生成总体业绩图表]
    E2 --> F2[生成代表业绩对比图]
    E3 --> F3[生成产品销售占比图]
    E4 --> F4[生成区域销售热力图]
    
    F1 --> G[业绩数据筛选/过滤]
    F2 --> G
    F3 --> G
    F4 --> G
    
    G --> H{深入分析选择}
    H --> |同比/环比分析| I1[时间维度对比]
    H --> |多维下钻| I2[维度细分分析]
    H --> |异常检测| I3[销售异常识别]
    H --> |预测分析| I4[趋势预测]
    
    I1 --> J[生成分析报告]
    I2 --> J
    I3 --> J
    I4 --> J
    
    J --> K[导出/分享分析结果]
    J --> L[设置定期监控任务]
    
    subgraph 数据权限控制
        M[UserIdentity验证] --> |验证tenantId| N[租户数据隔离]
        M --> |验证userId| O[用户权限过滤]
    end
    
    C --> M
    E1 --> M
    E2 --> M
    E3 --> M
    E4 --> M
``` 