package com.fxiaoke.chatbi.planning.context;

import com.fxiaoke.chatbi.planning.flow.Flow;
import lombok.Getter;

import java.util.List;

/**
 * 执行计划
 * 包含计划ID和要执行的流程列表
 */
@Getter
public class Plan {
  private final String planId;
  private final List<Flow> flows;
  private final PlanResultGenerator<?> resultGenerator;

  public Plan(String planId, List<Flow> flows, PlanResultGenerator<?> resultGenerator) {
    this.planId = planId;
    this.flows = flows;
    this.resultGenerator = resultGenerator;
  }
}
