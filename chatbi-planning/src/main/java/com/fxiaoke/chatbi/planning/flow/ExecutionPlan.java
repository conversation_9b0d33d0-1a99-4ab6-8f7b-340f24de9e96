package com.fxiaoke.chatbi.planning.flow;

import com.fxiaoke.chatbi.common.model.action.ActionType;
import lombok.Getter;

import java.util.*;

/**
 * 流程执行计划
 * 定义Actions的执行顺序和依赖关系，支持并行执行
 */
@Getter
public class ExecutionPlan {
  // 只保留阶段列表
  private final List<FlowStage> stages;

  // 流程节点
  public static class FlowNode {
    private final String nodeId;
    private final ActionType actionType;
    private final Set<String> dependsOnNodeIds;
    private final boolean async;
    public FlowNode(String nodeId, ActionType actionType, Set<String> dependsOnNodeIds, boolean async) {
      this.nodeId = nodeId;
      this.actionType = actionType;
      this.dependsOnNodeIds = dependsOnNodeIds == null ? Collections.emptySet() : Collections.unmodifiableSet(new HashSet<>(dependsOnNodeIds));
      this.async = async;
    }
    public FlowNode(ActionType actionType, Set<String> dependsOnNodeIds, boolean async) {
      this(actionType.getCode(), actionType, dependsOnNodeIds, async);
    }
    public String getNodeId() { return nodeId; }
    public ActionType getActionType() { return actionType; }
    public Set<String> getDependsOnNodeIds() { return dependsOnNodeIds; }
    public boolean isAsync() { return async; }
  }

  // 阶段对象
  public static class FlowStage {
    private final Set<FlowNode> nodes;
    private final boolean async; // 阶段级别异步标记（可选）
    public FlowStage(Set<FlowNode> nodes, boolean async) {
      this.nodes = Collections.unmodifiableSet(new HashSet<>(nodes));
      this.async = async;
    }
    public Set<FlowNode> getNodes() { return nodes; }
    public boolean isAsync() { return async; }
  }

  // 只用阶段列表构造
  public ExecutionPlan(List<FlowStage> stages) {
    this.stages = Collections.unmodifiableList(new ArrayList<>(stages));
  }

  // 静态工厂方法
  public static ExecutionPlan createWithStages(List<FlowStage> stages) {
    return new ExecutionPlan(stages);
  }

  // 动态判断节点是否异步
  public boolean isAsyncNode(FlowNode node) {
    return node.isAsync();
  }

  // 动态判断节点依赖是否满足
  public boolean areNodeDependenciesSatisfied(FlowNode node, Set<String> completedNodeIds) {
    return completedNodeIds.containsAll(node.getDependsOnNodeIds());
  }

  // 获取所有节点
  public List<FlowNode> getAllNodes() {
    List<FlowNode> all = new ArrayList<>();
    for (FlowStage stage : stages) {
      all.addAll(stage.getNodes());
    }
    return all;
  }
} 