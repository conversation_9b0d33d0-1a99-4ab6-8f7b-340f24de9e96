package com.fxiaoke.chatbi.planning.context;

import com.fxiaoke.chatbi.common.config.KnowledgeProperties;
import com.fxiaoke.chatbi.common.model.LoadingStatus;
import com.fxiaoke.chatbi.common.model.MessageType;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.response.ReasoningResponse;
import com.fxiaoke.chatbi.integration.utils.MarkdownUtil;
import com.fxiaoke.chatbi.planning.flow.analysis.AsyncDataManager;
import com.fxiaoke.chatbi.planning.model.FlowResult;
import com.fxiaoke.chatbi.planning.model.FlowType;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * 分析类型的计划结果生成器
 * 只负责生成基础响应，异步数据（图表、解读、追问）由Flow执行过程写入Redis
 */
@Slf4j
@Component
public class AnalysisPlanResultGenerator implements PlanResultGenerator<ReasoningResponse> {

    private final AsyncDataManager asyncDataManager;

  @Autowired
  private KnowledgeProperties knowledgeProperties;


  public AnalysisPlanResultGenerator(AsyncDataManager asyncDataManager) {
      this.asyncDataManager = asyncDataManager;
    }

    @SneakyThrows
    @Override
  public ReasoningResponse generate(Map<FlowType, FlowResult> flowResults, PlanContext context) {
    log.info("生成分析结果, 计划ID: {}", context.getPlanId());
      Map<String, String> reasoningsMap = context.getReasoningCollector().getReasoningsMap();
      Map<String, String> actionLogMap = context.getReasoningCollector().getActionLog();

      String prompt = reasoningsMap.values().stream().map(MarkdownUtil::markdownToHtml).collect(Collectors.joining("\n"));
      String actionLog = actionLogMap.values().stream().map(MarkdownUtil::markdownToHtml).collect(Collectors.joining("\n"));
      UserIdentity userIdentity = context.getConversationContext().getUserIdentity();
      if(knowledgeProperties.getActionLogGray().containsKey(userIdentity.getTenantId()) && knowledgeProperties.getActionLogGray().get(userIdentity.getTenantId()).contains(userIdentity.getUserId())) {
        asyncDataManager.saveActionLog(context.getPlanId(), actionLog);
      }
      return ReasoningResponse.builder()
                            .requestId(context.getPlanId())
                            .reasoning(prompt)
                            .message(prompt)
                            .loadingStatus(LoadingStatus.LOADING)
                            .messageType(MessageType.CHART_DATA)
                            .build();
  }
} 