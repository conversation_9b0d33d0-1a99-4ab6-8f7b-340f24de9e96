package com.fxiaoke.chatbi.planning.context;

import com.fxiaoke.chatbi.planning.model.FlowResult;
import com.fxiaoke.chatbi.planning.model.FlowType;

import java.util.Map;

/**
 * 计划结果生成器接口
 * 负责根据Flow执行结果和计划上下文生成最终的计划结果
 * @param <T> 生成结果的类型
 */
@FunctionalInterface
public interface PlanResultGenerator<T> {
  
  /**
   * 生成计划结果
   *
   * @param flowResults Flow执行结果集合
   * @param context 计划上下文
   * @return 生成的结果
   */
  T generate(Map<FlowType, FlowResult> flowResults, PlanContext context);
}
