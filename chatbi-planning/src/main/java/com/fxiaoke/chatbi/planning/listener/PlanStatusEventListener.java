package com.fxiaoke.chatbi.planning.listener;

import com.fxiaoke.chatbi.planning.event.PlanStatusEvent;
import com.fxiaoke.chatbi.planning.model.ExecutionStatus;
import com.fxiaoke.chatbi.common.sse.event.factory.SseEventFactory;
import com.fxiaoke.chatbi.common.sse.publisher.SseEventPublisher;
import com.fxiaoke.chatbi.common.event.SessionCompletedEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 计划状态事件监听器
 * 专门监听计划的状态变更事件，处理会话生命周期管理
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PlanStatusEventListener {

    private final SseEventPublisher eventPublisher;
    private final SseEventFactory eventFactory;
    private final ApplicationEventPublisher applicationEventPublisher;

    /**
     * 监听计划状态变更
     */
    @EventListener
    public void handlePlanStatus(PlanStatusEvent event) {
        String planId = event.getPlanId();
        ExecutionStatus status = event.getStatus();
        
        switch (status) {
            case COMPLETED:
                log.info("📋 计划执行完成: planId={}", planId);
                // 计划完成时结束会话
                finishSession(event);
                break;
                
            case FAILED:
                log.error("📋 计划执行失败: planId={}, error={}", planId, event.getErrorMessage());
                // 计划失败时也结束会话
                finishSession(event);
                break;
                
            default:
                log.debug("📋 计划状态变更: planId={}, status={}", planId, status);
        }
    }

    /**
     * 完成会话
     */
    public void finishSession(PlanStatusEvent event) {
        try {
            String sessionId = event.getSessionId();
            String planId = event.getPlanId();
            
            // 发送完成事件
            eventPublisher.publish(sessionId, eventFactory.createFinishEvent());
            
            // 完成会话，通知清理资源
            eventPublisher.complete(sessionId);
            
            // 发布会话完成事件，清理缓存
            applicationEventPublisher.publishEvent(new SessionCompletedEvent(sessionId));
     
            log.info("🎯 会话已完成: sessionId={}, planId={}", sessionId, planId);
        } catch (Exception e) {
            log.error("完成会话时发生异常: planId={}", event.getPlanId(), e);
        }
    }
} 