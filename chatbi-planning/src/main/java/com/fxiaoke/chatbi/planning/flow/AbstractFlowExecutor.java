package com.fxiaoke.chatbi.planning.flow;

import com.fxiaoke.chatbi.action.core.Action;
import com.fxiaoke.chatbi.action.core.ActionFactory;
import com.fxiaoke.chatbi.action.core.ActionResult;
import com.fxiaoke.chatbi.common.exception.ChatbiErrorCodeEnum;
import com.fxiaoke.chatbi.common.exception.PlanningException;
import com.fxiaoke.chatbi.common.model.action.ActionContext;
import com.fxiaoke.chatbi.common.model.action.ActionInput;
import com.fxiaoke.chatbi.common.model.action.ActionType;
import com.fxiaoke.chatbi.planning.context.PlanContext;
import com.fxiaoke.chatbi.planning.executor.AsyncExecutor;
import com.fxiaoke.chatbi.planning.model.FlowResult;
import com.fxiaoke.chatbi.planning.tracker.PlanStatusTracker;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 流程执行器抽象基类
 */
@Slf4j
public abstract class AbstractFlowExecutor implements FlowExecutor {

  protected final ActionFactory actionFactory;
  protected final AsyncExecutor asyncExecutor;
  protected final PlanStatusTracker statusTracker;

  protected AbstractFlowExecutor(ActionFactory actionFactory, AsyncExecutor asyncExecutor, PlanStatusTracker statusTracker) {
    this.actionFactory = actionFactory;
    this.asyncExecutor = asyncExecutor;
    this.statusTracker = statusTracker;
  }

  @Override
  public FlowResult execute(ExecutionPlan plan, PlanContext context) {
    String planId = context.getPlanId();
    log.info("开始执行计划ID: {}", planId);

    Map<ActionType, ActionResult<?>> results = new LinkedHashMap<>();
    Map<String, ExecutionPlan.FlowNode> nodeMap = new HashMap<>();
    Map<String, Integer> dependencyCount = new HashMap<>();
    Map<String, List<String>> reverseDependencies = new HashMap<>();
    Set<String> completedNodeIds = Collections.synchronizedSet(new HashSet<>());

    // 1. 初始化节点、依赖计数、反向依赖
    for (ExecutionPlan.FlowNode node : plan.getAllNodes()) {
      nodeMap.put(node.getNodeId(), node);
      dependencyCount.put(node.getNodeId(), node.getDependsOnNodeIds().size());
      for (String dep : node.getDependsOnNodeIds()) {
        reverseDependencies.computeIfAbsent(dep, k -> new ArrayList<>()).add(node.getNodeId());
      }
    }

    // 2. 初始化计划状态追踪
    int totalNodes = plan.getAllNodes().size();
    statusTracker.startPlan(planId, totalNodes, context);

    // 复制初始依赖为0的节点列表
    List<String> initialNodes = new ArrayList<>();
    for (Map.Entry<String, Integer> entry : dependencyCount.entrySet()) {
      if (entry.getValue() == 0) {
        initialNodes.add(entry.getKey());
      }
    }

    // 调度初始节点
    for (String nodeId : initialNodes) {
      scheduleNode(nodeId, nodeMap, dependencyCount, reverseDependencies, completedNodeIds, results, context);
    }

    log.info("流程异步派发完成, 计划ID: {}", context.getPlanId());
    return FlowResult.of(results);
  }

  // DAG调度核心：节点完成后自动推进依赖节点
  private void scheduleNode(String nodeId,
                            Map<String, ExecutionPlan.FlowNode> nodeMap,
                            Map<String, Integer> dependencyCount,
                            Map<String, List<String>> reverseDependencies,
                            Set<String> completedNodeIds,
                            Map<ActionType, ActionResult<?>> results,
                            PlanContext context) {
    ExecutionPlan.FlowNode node = nodeMap.get(nodeId);
    ActionType actionType = node.getActionType();
    String planId = context.getPlanId();

    // 记录调用堆栈和当前线程
    log.info("调度节点: {}, 线程: {}, 堆栈: {}",
            nodeId, Thread.currentThread().getName(),
            Thread.currentThread().getStackTrace());

    // 记录当前所有节点的依赖计数
    log.info("调度前依赖计数: {}", dependencyCount);

    if (node.isAsync()) {
      Action<ActionInput, ?> action = actionFactory.getAction(actionType);
      ActionContext actionContext = context.createActionContext();
      ActionInput input = createActionInput(actionType, context, results);
      results.put(actionType, ActionResult.empty());
      log.info("=============================="+actionType);
      
      // 发布节点开始事件
      statusTracker.startNode(planId, nodeId, actionType, true);
      
      // 异步执行，传入状态追踪器用于回调
      asyncExecutor.execute(action, actionContext, input, context, results, statusTracker, nodeId).thenRun(() -> {
        completedNodeIds.add(nodeId);
        for (String dependentId : reverseDependencies.getOrDefault(nodeId, Collections.emptyList())) {
          int count = dependencyCount.computeIfPresent(dependentId, (k, v) -> v - 1);
          if (count == 0) {
            scheduleNode(dependentId, nodeMap, dependencyCount, reverseDependencies, completedNodeIds, results, context);
          }
        }
      });
    } else {
      log.info("同步节点执行后依赖计数: {}", dependencyCount);

      try {
        // 发布节点开始事件
        statusTracker.startNode(planId, nodeId, actionType, false);
        
        ActionResult<?> result = executeAction(actionType, context, results);
        results.put(actionType, result);
        completedNodeIds.add(nodeId);
        
        // 同步节点执行成功，通知状态追踪器并传递结果
        statusTracker.completeNode(planId, nodeId, actionType, false, result);
        
        for (String dependentId : reverseDependencies.getOrDefault(nodeId, Collections.emptyList())) {
          int count = dependencyCount.computeIfPresent(dependentId, (k, v) -> v - 1);
          if (count == 0) {
            scheduleNode(dependentId, nodeMap, dependencyCount, reverseDependencies, completedNodeIds, results, context);
          }
        }
      } catch (Exception e) {
        // 同步节点执行失败，通知状态追踪器
        statusTracker.failNode(planId, nodeId, actionType, false, e.getMessage());
        throw e;
      }
    }
  }

  /**
   * 执行单个Action
   */
  private ActionResult<?> executeAction(ActionType actionType,
                                        PlanContext context,
                                        Map<ActionType, ActionResult<?>> results) {
    ActionContext actionContext = context.createActionContext();
    ActionInput input = createActionInput(actionType, context, results);
    Action<ActionInput, ?> action = actionFactory.getAction(actionType);
    ActionResult<?> result = action.execute(input, actionContext);

    if (!result.isSuccess()) {
      throw new PlanningException(ChatbiErrorCodeEnum.PLAN_EXECUTION_ERROR, String.format("执行Action[%s]失败: %s", actionType.getDesc(), result.getErrorMsg()));
    }

    return result;
  }

  protected abstract ActionInput createActionInput(ActionType actionType,
                                                   PlanContext context,
                                                   Map<ActionType, ActionResult<?>> results);

}