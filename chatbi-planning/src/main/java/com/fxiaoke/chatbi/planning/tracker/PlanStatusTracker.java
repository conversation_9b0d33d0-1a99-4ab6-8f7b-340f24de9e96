package com.fxiaoke.chatbi.planning.tracker;

import com.fxiaoke.chatbi.action.core.ActionResult;
import com.fxiaoke.chatbi.common.model.action.ActionType;
import com.fxiaoke.chatbi.planning.context.PlanContext;
import com.fxiaoke.chatbi.planning.event.NodeStatusEvent;
import com.fxiaoke.chatbi.planning.event.PlanStatusEvent;
import com.fxiaoke.chatbi.planning.model.ExecutionStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 计划状态追踪器
 * 负责追踪计划和节点的执行状态，发布状态变更事件
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PlanStatusTracker {

    private final ApplicationEventPublisher eventPublisher;
    
    // 简单内存存储，记录计划的总节点数和完成节点数
    private final Map<String, AtomicInteger> planTotalNodes = new ConcurrentHashMap<>();
    private final Map<String, AtomicInteger> planCompletedNodes = new ConcurrentHashMap<>();
    private final Map<String, ExecutionStatus> planStatus = new ConcurrentHashMap<>();
    // 存储PlanContext映射
    private final Map<String, PlanContext> planContexts = new ConcurrentHashMap<>();
    // 存储节点执行开始时间
    private final Map<String, Long> nodeStartTimes = new ConcurrentHashMap<>();

    /**
     * 开始计划执行
     */
    public synchronized void startPlan(String planId, int totalNodes, PlanContext planContext) {
        planTotalNodes.put(planId, new AtomicInteger(totalNodes));
        planCompletedNodes.put(planId, new AtomicInteger(0));
        planStatus.put(planId, ExecutionStatus.RUNNING);
        planContexts.put(planId, planContext);
        log.info("计划开始执行: planId={}, totalNodes={}", planId, totalNodes);
    }

    /**
     * 节点开始执行
     */
    public synchronized void startNode(String planId, String nodeId, ActionType actionType, boolean async) {
        PlanContext planContext = planContexts.get(planId);
        if (planContext == null) {
            log.error("无法找到PlanContext，跳过节点开始事件: planId={}, nodeId={}", planId, nodeId);
            return;
        }
        
        // 记录开始时间
        String nodeKey = planId + ":" + nodeId;
        nodeStartTimes.put(nodeKey, System.currentTimeMillis());
        
        eventPublisher.publishEvent(NodeStatusEvent.started(planContext, nodeId, actionType, async));
        
        log.info("节点开始执行: planId={}, nodeId={}, actionType={}, async={}", 
                planId, nodeId, actionType, async);
    }

    /**
     * 节点执行完成（带结果）
     */
    public synchronized void completeNode(String planId, String nodeId, ActionType actionType,
                                          boolean async, ActionResult<?> result) {
        // 检查计划是否已经失败，如果已失败则不处理完成事件
        ExecutionStatus currentStatus = planStatus.get(planId);
        if (currentStatus == ExecutionStatus.FAILED) {
            log.warn("计划已失败，忽略节点完成事件: planId={}, nodeId={}", planId, nodeId);
            return;
        }
        
        PlanContext planContext = planContexts.get(planId);
        if (planContext == null) {
            log.error("无法找到PlanContext，跳过节点完成事件: planId={}, nodeId={}", planId, nodeId);
            return;
        }
        
        // 计算执行时间
        String nodeKey = planId + ":" + nodeId;
        Long startTime = nodeStartTimes.remove(nodeKey);
        long executionTime = startTime != null ? System.currentTimeMillis() - startTime : 0;
        
        eventPublisher.publishEvent(NodeStatusEvent.completed(planContext, nodeId, actionType, async, result, executionTime));
        
        // 检查计划是否完成
        checkPlanCompletion(planId);
        
        log.info("节点执行完成: planId={}, nodeId={}, actionType={}, async={}, executionTime={}ms", 
                planId, nodeId, actionType, async, executionTime);
    }

    /**
     * 节点执行失败
     */
    public synchronized void failNode(String planId, String nodeId, ActionType actionType, 
                        boolean async, String errorMessage) {
        // 检查计划是否已经失败，避免重复失败
        ExecutionStatus currentStatus = planStatus.get(planId);
        if (currentStatus == ExecutionStatus.FAILED) {
            log.warn("计划已失败，忽略重复失败事件: planId={}, nodeId={}", planId, nodeId);
            return;
        }
        
        PlanContext planContext = planContexts.get(planId);
        if (planContext == null) {
            log.error("无法找到PlanContext，跳过节点失败事件: planId={}, nodeId={}", planId, nodeId);
            // 即使没有PlanContext，也要执行计划失败逻辑
            failPlan(planId, "节点执行失败: " + nodeId);
            return;
        }
        
        // 计算执行时间（失败也需要记录）
        String nodeKey = planId + ":" + nodeId;
        Long startTime = nodeStartTimes.remove(nodeKey);
        long executionTime = startTime != null ? System.currentTimeMillis() - startTime : 0;
        
        eventPublisher.publishEvent(NodeStatusEvent.failed(planContext, nodeId, actionType, async, errorMessage, executionTime));
        
        // 计划失败
        failPlan(planId, "节点执行失败: " + nodeId);
        
        log.error("节点执行失败: planId={}, nodeId={}, error={}, executionTime={}ms", 
                planId, nodeId, errorMessage, executionTime);
    }

    /**
     * 计划执行失败
     */
    private void failPlan(String planId, String errorMessage) {
        planStatus.put(planId, ExecutionStatus.FAILED);
        
        // 使用PlanContext创建事件
        PlanContext planContext = planContexts.get(planId);
        if (planContext != null) {
            eventPublisher.publishEvent(new PlanStatusEvent(planContext, ExecutionStatus.FAILED, errorMessage));
        } else {
            log.error("无法找到PlanContext，跳过状态事件发布: planId={}", planId);
        }
        
        // 清理状态
        cleanPlanStatus(planId);
        
        log.error("计划执行失败: planId={}, error={}", planId, errorMessage);
    }

    /**
     * 检查计划是否完成
     */
    private void checkPlanCompletion(String planId) {
        AtomicInteger total = planTotalNodes.get(planId);
        AtomicInteger completed = planCompletedNodes.get(planId);
        
        if (total != null && completed != null) {
            int completedCount = completed.incrementAndGet();
            if (completedCount >= total.get()) {
                // 再次检查状态，确保没有被其他线程修改为失败
                ExecutionStatus currentStatus = planStatus.get(planId);
                if (currentStatus == ExecutionStatus.RUNNING) {
                    // 计划完成
                    planStatus.put(planId, ExecutionStatus.COMPLETED);
                    
                    // 使用PlanContext创建事件
                    PlanContext planContext = planContexts.get(planId);
                    if (planContext != null) {
                        eventPublisher.publishEvent(new PlanStatusEvent(planContext, ExecutionStatus.COMPLETED));
                    } else {
                        log.error("无法找到PlanContext，跳过状态事件发布: planId={}", planId);
                    }
                    
                    // 清理状态
                    cleanPlanStatus(planId);
                    
                    log.info("计划执行完成: planId={}", planId);
                }
            }
        }
    }

    /**
     * 清理计划状态
     */
    private void cleanPlanStatus(String planId) {
        planTotalNodes.remove(planId);
        planCompletedNodes.remove(planId);
        planStatus.remove(planId);
        planContexts.remove(planId);
        
        // 清理该计划下所有节点的执行时间记录
        nodeStartTimes.entrySet().removeIf(entry -> entry.getKey().startsWith(planId + ":"));
    }

    /**
     * 获取计划状态
     */
    public ExecutionStatus getPlanStatus(String planId) {
        return planStatus.get(planId);
    }
} 