package com.fxiaoke.chatbi.planning.context;

import com.fxiaoke.chatbi.common.event.SessionCompletedEvent;
import com.fxiaoke.chatbi.common.model.MessageType;
import com.fxiaoke.chatbi.common.model.response.ReasoningResponse;
import com.fxiaoke.chatbi.common.sse.event.factory.SseEventFactory;
import com.fxiaoke.chatbi.common.sse.publisher.SseEventPublisher;
import com.fxiaoke.chatbi.planning.model.FlowResult;
import com.fxiaoke.chatbi.planning.model.FlowType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 澄清类型的计划结果生成器
 * 只负责生成基础响应，异步数据由Flow执行过程写入Redis
 */
@Slf4j
@Component
public class ClarificationPlanResultGenerator implements PlanResultGenerator<ReasoningResponse> {

    private final SseEventPublisher eventPublisher;
    private final SseEventFactory eventFactory;
    private final ApplicationEventPublisher applicationEventPublisher;

    public ClarificationPlanResultGenerator(SseEventPublisher eventPublisher, SseEventFactory eventFactory, ApplicationEventPublisher applicationEventPublisher) {
        this.eventPublisher = eventPublisher;
        this.eventFactory = eventFactory;
        this.applicationEventPublisher = applicationEventPublisher;
    }

    @Override
    public ReasoningResponse generate(Map<FlowType, FlowResult> flowResults, PlanContext context) {
        log.info("生成澄清结果, 计划ID: {}", context.getPlanId());
        String clarificationQuestion = context.getUserIntent().getClarificationQuestion();

        eventPublisher.publish(context.getSessionId(), eventFactory.createTipsEvent(clarificationQuestion, false));

        // 发送完成事件
        eventPublisher.publish(context.getSessionId(), eventFactory.createFinishEvent());

        // 完成会话，通知清理资源
        eventPublisher.complete(context.getSessionId());

        // 发布会话完成事件，清理缓存
        applicationEventPublisher.publishEvent(new SessionCompletedEvent(context.getSessionId()));

        return ReasoningResponse.builder()
                .requestId(context.getPlanId())
                .message(clarificationQuestion)
                .messageType(MessageType.SYSTEM)
                .clarificationQuestion(clarificationQuestion)
                .build();
    }
} 