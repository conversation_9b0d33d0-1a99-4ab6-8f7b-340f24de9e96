package com.fxiaoke.chatbi.planning.flow.analysis;

import com.fxiaoke.chatbi.planning.context.PlanContext;
import com.fxiaoke.chatbi.planning.flow.FlowResultHandler;
import com.fxiaoke.chatbi.planning.model.FlowResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 分析意图结果处理器
 * 处理分析流程的执行结果
 */
@Slf4j
@Component
public class AnalysisResultHandler implements FlowResultHandler {

  @Override
  public void handleResult(PlanContext context, FlowResult flowResult) {

  }
}