package com.fxiaoke.chatbi.planning.flow.analysis;

import com.fxiaoke.chatbi.action.core.ActionFactory;
import com.fxiaoke.chatbi.action.core.ActionResult;
import com.fxiaoke.chatbi.common.model.action.ActionInput;
import com.fxiaoke.chatbi.common.model.action.ActionType;
import com.fxiaoke.chatbi.common.model.action.input.DataQueryInput;
import com.fxiaoke.chatbi.common.model.action.input.FollowUpQuestionInput;
import com.fxiaoke.chatbi.common.model.action.input.InsightInput;
import com.fxiaoke.chatbi.common.model.action.input.QueryDSLInput;
import com.fxiaoke.chatbi.common.model.action.output.DataQueryOutput;
import com.fxiaoke.chatbi.common.model.action.output.QueryDSLOutput;
import com.fxiaoke.chatbi.common.model.dto.ChartResultData;
import com.fxiaoke.chatbi.common.model.dto.SaleProcessAnalysisResult;
import com.fxiaoke.chatbi.common.model.dto.SpecialChartResult;
import com.fxiaoke.chatbi.common.model.dto.StandardChartResult;
import com.fxiaoke.chatbi.planning.context.PlanContext;
import com.fxiaoke.chatbi.planning.executor.AsyncExecutor;
import com.fxiaoke.chatbi.planning.flow.AbstractFlowExecutor;
import com.fxiaoke.chatbi.planning.tracker.PlanStatusTracker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;


@Component
@Slf4j
public class AnalysisFlowExecutor extends AbstractFlowExecutor {

  protected AnalysisFlowExecutor(ActionFactory actionFactory, AsyncExecutor asyncExecutor, PlanStatusTracker statusTracker) {
    super(actionFactory, asyncExecutor, statusTracker);
  }

  @Override
  protected ActionInput createActionInput(ActionType actionType,
                                          PlanContext context,
                                          Map<ActionType, ActionResult<?>> results) {
    switch (actionType) {
      case QUERY_DSL:
        return createQueryDSLInput(context);
      case DATA_QUERY:
        return createDataQueryInput(context, results);
      case DATA_INSIGHT:
        return createDataInsightInput(context, results);
      case FOLLOW_UP_QUESTION:
        return createFollowUpQuestionInput(context, results);
      case REASONING_POLISHING:
        return createReasoningPolishingInput();
      default:
        log.warn("未知的Action类型: {}", actionType);
        return null;
    }
  }


  /**
   * 创建查询计划输入
   */
  private QueryDSLInput createQueryDSLInput(PlanContext context) {
    QueryDSLInput input = new QueryDSLInput();
    input.setKnowledgeScope(context.getKnowledgeScope());
    input.setUserIntent(context.getUserIntent());
    return input;
  }

  /**
   * 创建数据查询输入
   */
  private DataQueryInput createDataQueryInput(PlanContext context, Map<ActionType, ActionResult<?>> results) {
    DataQueryInput input = new DataQueryInput();

    // 获取查询计划结果
    @SuppressWarnings("unchecked") ActionResult<QueryDSLOutput> matchingResult = (ActionResult<QueryDSLOutput>) results.get(ActionType.QUERY_DSL);
    input.setPlanId(context.getPlanId());

    input.setQueryArg(matchingResult.getData().getChartQueryDSL());
    return input;
  }

  /**
   * 创建数据洞察输入
   */
  private InsightInput createDataInsightInput(PlanContext context, Map<ActionType, ActionResult<?>> results) {
    InsightInput input = new InsightInput();

    // 获取数据查询结果
    @SuppressWarnings("unchecked") ActionResult<DataQueryOutput> queryResult = (ActionResult<DataQueryOutput>) results.get(ActionType.DATA_QUERY);

    input.setPlanId(context.getPlanId());

    input.setQueryOutput(queryResult.getData());
    return input;
  }

  /**
   * 创建推荐输入
   */
  private FollowUpQuestionInput createFollowUpQuestionInput(PlanContext context,
                                                            Map<ActionType, ActionResult<?>> results) {
    FollowUpQuestionInput input = new FollowUpQuestionInput();
    // 依赖DATA_QUERY结果
    @SuppressWarnings("unchecked") ActionResult<DataQueryOutput> queryResult = (ActionResult<DataQueryOutput>) results.get(ActionType.DATA_QUERY);
    if (queryResult != null && queryResult.getData() != null) {
        ChartResultData resultData = queryResult.getData().getResultData();
        if (resultData instanceof SpecialChartResult resultData1) {
          SaleProcessAnalysisResult saleProcessAnalysisResult = resultData1.getSaleProcessAnalysisResult();
          input.setQueryResult(saleProcessAnalysisResult.getStages());
        } else {
            StandardChartResult resultData1 = (StandardChartResult) resultData;
            input.setQueryResult(resultData1.getSimpleChartResult());
        }
      // 如有更多依赖字段，可在此补充
    } else {
      log.warn("生成FollowUpQuestionInput时未获取到DATA_QUERY结果");
    }
    input.setInstructions(context.getUserIntent().getInstructions());
    return input;
  }

  /**
   * 创建推理润色输入
   */
  private ActionInput createReasoningPolishingInput() {
    // 推理润色使用空输入，因为它会从上下文中获取所需信息
    return new ActionInput() {
    };
  }


}
