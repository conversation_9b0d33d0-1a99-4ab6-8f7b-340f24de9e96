package com.fxiaoke.chatbi.planning.listener;

import com.fxiaoke.chatbi.planning.event.NodeStatusEvent;
import com.fxiaoke.chatbi.planning.model.ExecutionStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 节点状态事件监听器
 * 专门监听节点的状态变更事件
 */
@Slf4j
@Component
public class NodeStatusEventListener {

    /**
     * 监听节点状态变更
     */
    @EventListener
    public void handleNodeStatus(NodeStatusEvent event) {
        String planId = event.getPlanId();
        String nodeId = event.getNodeId();
        ExecutionStatus status = event.getStatus();
        boolean async = event.isAsync();
        
        switch (status) {
            case COMPLETED:
                log.info("🔗 节点执行完成: planId={}, nodeId={}, actionType={}, async={}", 
                        planId, nodeId, event.getActionType(), async);
                // 这里可以添加节点完成后的处理逻辑
                // 例如：性能统计、审计日志、监控指标等
                handleNodeCompletion(event);
                break;
                
            case FAILED:
                log.error("🔗 节点执行失败: planId={}, nodeId={}, actionType={}, async={}, error={}", 
                        planId, nodeId, event.getActionType(), async, event.getErrorMessage());
                // 这里可以添加节点失败后的处理逻辑
                // 例如：错误统计、告警通知、重试策略等
                handleNodeFailure(event);
                break;
                
            default:
                log.debug("🔗 节点状态变更: planId={}, nodeId={}, status={}", planId, nodeId, status);
        }
    }
    
    /**
     * 处理节点完成事件
     */
    private void handleNodeCompletion(NodeStatusEvent event) {
        // 可以在这里添加节点完成后的扩展逻辑
        // 例如：
        // - 更新性能指标
        // - 记录执行时间
        // - 发送通知
        log.debug("处理节点完成事件: nodeId={}, actionType={}", 
                event.getNodeId(), event.getActionType());
    }
    
    /**
     * 处理节点失败事件
     */
    private void handleNodeFailure(NodeStatusEvent event) {
        // 可以在这里添加节点失败后的扩展逻辑
        // 例如：
        // - 错误统计
        // - 告警通知
        // - 重试机制
        log.debug("处理节点失败事件: nodeId={}, actionType={}, error={}", 
                event.getNodeId(), event.getActionType(), event.getErrorMessage());
    }
} 