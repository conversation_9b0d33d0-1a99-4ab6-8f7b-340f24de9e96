package com.fxiaoke.chatbi.planning.flow;

import com.fxiaoke.chatbi.planning.context.PlanContext;
import com.fxiaoke.chatbi.planning.model.FlowResult;

/**
 * 流程结果处理器接口
 * 负责处理不同意图类型的流程执行结果并生成适当的响应
 */
public interface FlowResultHandler {
  /**
   * 处理流程执行结果
   *
   * @param context    计划上下文
   * @param flowResult 流程执行结果
   * @return 处理后的响应
   */
  void handleResult(PlanContext context, FlowResult flowResult);
} 