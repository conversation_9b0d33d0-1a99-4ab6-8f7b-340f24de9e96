package com.fxiaoke.chatbi.planning.model;

import com.fxiaoke.chatbi.common.model.intent.IntentType;
import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.ArrayList;

@Getter
public enum FlowType {
  ANALYSIS(Lists.newArrayList(IntentType.ANALYSIS)), CLARIFICATION(Lists.newArrayList(IntentType.CLARIFICATION));

  private final ArrayList<IntentType> intentTypes;

  FlowType(ArrayList<IntentType> intentTypes) {
    this.intentTypes = intentTypes;
  }
}
