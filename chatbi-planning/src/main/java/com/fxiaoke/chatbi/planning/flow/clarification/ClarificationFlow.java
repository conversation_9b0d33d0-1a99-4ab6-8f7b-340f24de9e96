package com.fxiaoke.chatbi.planning.flow.clarification;

import com.fxiaoke.chatbi.planning.context.PlanContext;
import com.fxiaoke.chatbi.planning.flow.ExecutionPlan;
import com.fxiaoke.chatbi.planning.flow.Flow;
import com.fxiaoke.chatbi.planning.model.FlowType;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Component;

import java.util.Collections;

@Component
public class ClarificationFlow implements Flow {
  @Override
  public String getFlowName() {
    return getFlowType().name();
  }

  @Override
  public FlowType getFlowType() {
    return FlowType.CLARIFICATION;
  }

  @Override
  public ExecutionPlan getExecutionPlan(PlanContext context) {
    return new ExecutionPlan(Lists.newArrayList());
  }
}
