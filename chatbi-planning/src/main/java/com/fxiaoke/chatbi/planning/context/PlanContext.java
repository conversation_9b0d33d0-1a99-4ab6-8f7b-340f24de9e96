package com.fxiaoke.chatbi.planning.context;

import com.fxiaoke.chatbi.common.model.action.ActionContext;
import com.fxiaoke.chatbi.common.model.intent.KnowledgeScope;
import com.fxiaoke.chatbi.common.model.intent.UserIntent;
import com.fxiaoke.chatbi.common.model.memory.ConversationContext;
import com.fxiaoke.chatbi.common.model.reasoning.ReasoningCollector;
import lombok.Data;

/**
 * Plan执行上下文
 * 简化版本 - 专注于固定流程的数据管理
 */
@Data
public class PlanContext {
    /**
     * 计划ID
     */
    private final String planId;

    /**
     * 会话上下文
     */
    private final ConversationContext conversationContext;

    /**
     * LLM模型
     */
    private String llmModel;

    /**
     * 用户意图
     */
    private UserIntent userIntent;

    /**
     * 知识范围
     */
    private KnowledgeScope knowledgeScope;

    /**
     * 推理收集器
     */
    private ReasoningCollector reasoningCollector = new ReasoningCollector();

    /**
     * 执行开始时间
     */
    private final long startTime = System.currentTimeMillis();

    /**
     * 会话ID
     * 用于SSE事件发布
     */
    private String sessionId;

    /**
     * 创建带LLM模型的上下文
     */
    public PlanContext(String planId, ConversationContext conversationContext, String llmModel) {
        this.planId = planId;
        this.conversationContext = conversationContext;
        this.llmModel = llmModel;
        this.sessionId = conversationContext.getSessionId(); // 默认使用会话上下文的sessionId
    }

    /**
     * 创建带知识范围的上下文
     */
    public PlanContext(String planId, ConversationContext conversationContext, KnowledgeScope knowledgeScope) {
        this.planId = planId;
        this.conversationContext = conversationContext;
        this.knowledgeScope = knowledgeScope;
        this.sessionId = conversationContext.getSessionId(); // 默认使用会话上下文的sessionId
    }

    /**
     * 创建Action上下文
     */
    public ActionContext createActionContext() {
        ActionContext actionContext = new ActionContext(conversationContext.getSessionId());
        actionContext.setUserIdentity(conversationContext.getUserIdentity());
        actionContext.setLlmModel(llmModel);
        actionContext.setReasoningCollector(reasoningCollector);
        actionContext.setSessionId(sessionId); // 设置sessionId
        return actionContext;
    }
}