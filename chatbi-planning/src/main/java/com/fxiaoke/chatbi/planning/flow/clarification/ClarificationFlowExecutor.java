package com.fxiaoke.chatbi.planning.flow.clarification;

import com.fxiaoke.chatbi.action.core.ActionFactory;
import com.fxiaoke.chatbi.action.core.ActionResult;
import com.fxiaoke.chatbi.common.model.action.ActionInput;
import com.fxiaoke.chatbi.common.model.action.ActionType;
import com.fxiaoke.chatbi.planning.context.PlanContext;
import com.fxiaoke.chatbi.planning.executor.AsyncExecutor;
import com.fxiaoke.chatbi.planning.flow.AbstractFlowExecutor;
import com.fxiaoke.chatbi.planning.tracker.PlanStatusTracker;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class ClarificationFlowExecutor extends AbstractFlowExecutor {

  protected ClarificationFlowExecutor(ActionFactory actionFactory, AsyncExecutor asyncExecutor, PlanStatusTracker statusTracker) {
    super(actionFactory, asyncExecutor, statusTracker);
  }

  @Override
  protected ActionInput createActionInput(ActionType actionType,
                                          PlanContext context,
                                          Map<ActionType, ActionResult<?>> results) {
    // 简化实现，返回空ActionInput
    return new ActionInput() {
    };
  }
}
