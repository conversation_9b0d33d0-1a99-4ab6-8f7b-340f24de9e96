package com.fxiaoke.chatbi.planning.flow;

import com.fxiaoke.chatbi.common.model.intent.IntentType;
import com.fxiaoke.chatbi.planning.context.PlanContext;
import com.fxiaoke.chatbi.planning.model.FlowType;

public interface Flow {
  /**
   * 获取流程名称
   *
   * @return 流程名称
   */
  String getFlowName();

  /**
   * 获取流程类型
   *
   * @return 流程类型
   */
  FlowType getFlowType();

  /**
   * 创建此流程的执行计划
   *
   * @param context 计划上下文
   * @return 执行计划
   */
  ExecutionPlan getExecutionPlan(PlanContext context);

  /**
   * 判断是否支持特定意图类型
   *
   * @param intentType 意图类型
   * @return 是否支持
   */
  default boolean supports(IntentType intentType) {
    return getFlowType().getIntentTypes().contains(intentType);
  }

}
