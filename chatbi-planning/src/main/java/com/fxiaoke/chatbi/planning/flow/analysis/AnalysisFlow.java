package com.fxiaoke.chatbi.planning.flow.analysis;

import com.fxiaoke.chatbi.common.model.action.ActionType;
import com.fxiaoke.chatbi.planning.flow.ExecutionPlan;
import com.fxiaoke.chatbi.planning.flow.Flow;
import com.fxiaoke.chatbi.planning.model.FlowType;
import com.fxiaoke.chatbi.planning.context.PlanContext;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class AnalysisFlow implements Flow {
  @Override
  public String getFlowName() {
    return getFlowType().name();
  }

  @Override
  public FlowType getFlowType() {
    return FlowType.ANALYSIS;
  }

  @Override
  public ExecutionPlan getExecutionPlan(PlanContext planContext) {
    List<ExecutionPlan.FlowStage> stages = new ArrayList<>();

    // 第一阶段：查询计划 (独立执行)
    ExecutionPlan.FlowNode queryDslNode = new ExecutionPlan.FlowNode(
        ActionType.QUERY_DSL, Collections.emptySet(), false);
    stages.add(new ExecutionPlan.FlowStage(Collections.singleton(queryDslNode), false));

    // 第二阶段：数据查询 (单独异步执行)
    ExecutionPlan.FlowNode dataQueryNode = new ExecutionPlan.FlowNode(
        ActionType.DATA_QUERY, Set.of(ActionType.QUERY_DSL.getCode()), true);
    stages.add(new ExecutionPlan.FlowStage(Collections.singleton(dataQueryNode), true));

    // 第三阶段：数据洞察和推荐 (依赖数据查询，可并行异步)
    ExecutionPlan.FlowNode insightNode = new ExecutionPlan.FlowNode(
        ActionType.DATA_INSIGHT, Set.of(ActionType.DATA_QUERY.getCode()), true);
    ExecutionPlan.FlowNode followUpNode = new ExecutionPlan.FlowNode(
        ActionType.FOLLOW_UP_QUESTION, Set.of(ActionType.DATA_QUERY.getCode()), true);
    stages.add(new ExecutionPlan.FlowStage(Set.of(insightNode, followUpNode), false));

    return ExecutionPlan.createWithStages(stages);
  }
}
