package com.fxiaoke.chatbi.planning.flow.clarification;

import com.fxiaoke.chatbi.planning.context.PlanContext;
import com.fxiaoke.chatbi.planning.flow.FlowResultHandler;
import com.fxiaoke.chatbi.planning.model.FlowResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 澄清意图结果处理器
 * 处理澄清流程的执行结果
 */
@Slf4j
@Component
public class ClarificationResultHandler implements FlowResultHandler {
    @Override
    public void handleResult(PlanContext context, FlowResult flowResult) {

    }
}