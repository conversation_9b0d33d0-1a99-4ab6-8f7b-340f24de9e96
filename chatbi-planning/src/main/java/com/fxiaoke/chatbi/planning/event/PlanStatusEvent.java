package com.fxiaoke.chatbi.planning.event;

import com.fxiaoke.chatbi.planning.context.PlanContext;
import com.fxiaoke.chatbi.planning.model.ExecutionStatus;
import lombok.Getter;

/**
 * 计划状态事件
 * 计划执行完成或失败时发布
 */
@Getter
public class PlanStatusEvent {
    private final String planId;
    private final ExecutionStatus status;
    private final String errorMessage;
    private final long timestamp;
    private final PlanContext planContext;

    public PlanStatusEvent(PlanContext planContext, ExecutionStatus status) {
        this(planContext, status, null);
    }

    public PlanStatusEvent(PlanContext planContext, ExecutionStatus status, String errorMessage) {
        this.planContext = planContext;
        this.planId = planContext.getPlanId();
        this.status = status;
        this.errorMessage = errorMessage;
        this.timestamp = System.currentTimeMillis();
    }
    
    /**
     * 获取SessionId
     */
    public String getSessionId() {
        return planContext.getSessionId();
    }
} 