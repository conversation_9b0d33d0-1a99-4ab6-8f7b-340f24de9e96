package com.fxiaoke.chatbi.planning.executor;

import com.fxiaoke.chatbi.action.core.Action;
import com.fxiaoke.chatbi.action.core.ActionResult;
import com.fxiaoke.chatbi.common.model.LoadingStatus;
import com.fxiaoke.chatbi.common.model.action.ActionContext;
import com.fxiaoke.chatbi.common.model.action.ActionInput;
import com.fxiaoke.chatbi.common.model.action.ActionType;
import com.fxiaoke.chatbi.common.model.action.output.DataQueryOutput;
import com.fxiaoke.chatbi.common.model.action.output.FollowUpQuestionOutput;
import com.fxiaoke.chatbi.common.model.action.output.InsightOutput;
import com.fxiaoke.chatbi.planning.context.PlanContext;
import com.fxiaoke.chatbi.planning.flow.analysis.AsyncDataManager;
import com.fxiaoke.chatbi.planning.tracker.PlanStatusTracker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.RejectedExecutionException;

/**
 * 异步Action执行器
 * 负责异步执行Action并保存结果
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class AsyncExecutor {

  private final AsyncDataManager asyncDataManager;
  @Resource(name = "monitorAsyncTaskExecutor")
  private TaskExecutor monitorAsyncTaskExecutor;

  /**
   * 异步执行Action，返回CompletableFuture
   */
  public CompletableFuture<Void> execute(Action<ActionInput, ?> action,
                                         ActionContext actionContext,
                                         ActionInput input,
                                         PlanContext context, 
                                         Map<ActionType, ActionResult<?>> results,
                                         PlanStatusTracker statusTracker,
                                         String nodeId) {
    return CompletableFuture.runAsync(() -> {
      String requestId = context.getPlanId();
      String planId = context.getPlanId();
      ActionType actionType = action.getType();
      
      log.info("开始异步执行Action: {}, 请求ID: {}, nodeId: {}", actionType.getDesc(), requestId, nodeId);
      try {
        ActionResult<?> result = action.execute(input, actionContext);
        if (!result.isSuccess()) {
          log.error("异步Action执行失败: {}, 请求ID: {}, 错误: {}", actionType.getDesc(), requestId, result.getErrorMsg());
          asyncDataManager.saveErrorResponse(requestId, result.getErrorMsg());
          
          // 通知状态追踪器节点失败
          statusTracker.failNode(planId, nodeId, actionType, true, result.getErrorMsg());
          return;
        }
        results.put(actionType, result);
        saveActionResult(actionType, result, requestId, actionContext.getSessionId());
        
        // 通知状态追踪器节点完成，传递结果
        statusTracker.completeNode(planId, nodeId, actionType, true, result);
        
        log.info("异步Action执行完成: {}, 请求ID: {}, nodeId: {}", actionType.getDesc(), requestId, nodeId);
      } catch (RejectedExecutionException ree) {
        log.error("线程池已满，异步Action被拒绝: {}, 请求ID: {}", actionType.getDesc(), requestId, ree);
        asyncDataManager.saveErrorResponse(requestId, "线程池已满，任务被拒绝");
        
        // 通知状态追踪器节点失败
        statusTracker.failNode(planId, nodeId, actionType, true, "线程池已满，任务被拒绝");
      } catch (Exception e) {
        log.error("异步Action执行异常: {}, 请求ID: {}", actionType.getDesc(), requestId, e);
        asyncDataManager.saveErrorResponse(requestId, e.getMessage());
        
        // 通知状态追踪器节点失败
        statusTracker.failNode(planId, nodeId, actionType, true, e.getMessage());
      }
    }, monitorAsyncTaskExecutor);
  }

  /**
   * 根据Action类型安全保存结果，异常保护
   */
  @SuppressWarnings("unchecked")
  private void saveActionResult(ActionType actionType, ActionResult<?> result, String requestId, String sessionId) {
    try {
      switch (actionType) {
        case DATA_QUERY:
          asyncDataManager.saveQueryResult(requestId, (ActionResult<DataQueryOutput>) result, LoadingStatus.COMPLETED, sessionId);
          break;
        case DATA_INSIGHT:
          asyncDataManager.saveInsightResult(requestId, (ActionResult<InsightOutput>) result, sessionId);
          break;
        case FOLLOW_UP_QUESTION:
          asyncDataManager.saveFollowUpQuestionResult(requestId, (ActionResult<FollowUpQuestionOutput>) result, sessionId);
          break;
        default:
          log.warn("未知的异步Action类型: {}", actionType);
      }
    } catch (Exception e) {
      log.error("保存Action结果异常: {}, 请求ID: {}", actionType.getDesc(), requestId, e);
      asyncDataManager.saveErrorResponse(requestId, "保存结果异常: " + e.getMessage());
    }
  }
}
