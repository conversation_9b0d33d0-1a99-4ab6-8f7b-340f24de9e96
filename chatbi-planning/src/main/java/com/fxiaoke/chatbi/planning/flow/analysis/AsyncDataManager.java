package com.fxiaoke.chatbi.planning.flow.analysis;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.chatbi.action.core.ActionResult;
import com.fxiaoke.chatbi.common.cache.ChatRedisService;
import com.fxiaoke.chatbi.common.config.RedisConfig;
import com.fxiaoke.chatbi.common.model.LoadingStatus;
import com.fxiaoke.chatbi.common.model.MessageType;
import com.fxiaoke.chatbi.common.model.ResponseType;
import com.fxiaoke.chatbi.common.model.action.output.DataQueryOutput;
import com.fxiaoke.chatbi.common.model.action.output.FollowUpQuestionOutput;
import com.fxiaoke.chatbi.common.model.action.output.InsightOutput;
import com.fxiaoke.chatbi.common.model.dto.ChartResultData;
import com.fxiaoke.chatbi.common.model.dto.SpecialChartResult;
import com.fxiaoke.chatbi.common.model.dto.StandardChartResult;
import com.fxiaoke.chatbi.common.model.response.*;
import com.fxiaoke.chatbi.common.sse.event.factory.SseEventFactory;
import com.fxiaoke.chatbi.common.sse.publisher.SseEventPublisher;
import com.fxiaoke.chatbi.integration.utils.MarkdownUtil;
import com.fxiaoke.chatbi.common.event.SessionCompletedEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 异步数据管理器
 * 负责管理异步Action的结果数据，包括：
 * 1. 保存Action执行结果
 * 2. 保存错误信息
 * 3. 查询执行状态和结果
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AsyncDataManager {

    private final ChatRedisService redisService;
    private final SseEventPublisher eventPublisher;
    private final SseEventFactory eventFactory;

    private static final int DEFAULT_EXPIRE_SECONDS = 24 * 3600; // 默认过期时间1天

    /**
     * 通用结果保存方法
     *
     * @param requestId     请求ID
     * @param result        结果对象
     * @param keyPrefix     Redis键前缀
     * @param expireSeconds 过期时间(秒)
     */
    private void saveResult(String requestId, Object result, String keyPrefix, long expireSeconds) {
        String key = keyPrefix + requestId;
        try {
            redisService.set(key, JSON.toJSONString(result), expireSeconds);
            log.info("已保存结果, 请求ID: {}, 类型: {}", requestId, result.getClass().getSimpleName());
        } catch (Exception e) {
            log.error("保存结果失败, 请求ID: {}", requestId, e);
        }
    }

    /**
     * 获取结果
     *
     * @param <T>        结果类型
     * @param requestId  请求ID
     * @param keyPrefix  Redis键前缀
     * @param resultType 结果类型
     * @return 结果对象，如果不存在或发生错误返回null
     */
    public <T> T getResult(String requestId, String keyPrefix, Class<T> resultType) {
        String key = keyPrefix + requestId;
        try {
            String json = redisService.get(key);
            if (json == null) {
                return null;
            }
            return JSON.parseObject(json, resultType);
        } catch (Exception e) {
            log.error("获取结果失败, 请求ID: {}, 类型: {}", requestId, resultType.getSimpleName(), e);
            return null;
        }
    }

    /**
     * 保存任意类型的响应
     *
     * @param <T>      响应类型
     * @param response 响应对象
     */
    private <T extends AsyncResponse> void saveResponse(T response) {
        String requestId = response.getRequestId();
        String keyPrefix = getKeyPrefixForResponseType(response.getClass());

        saveResult(requestId, response, keyPrefix, DEFAULT_EXPIRE_SECONDS);
    }

    /**
     * 从查询结果转换并保存图表数据响应
     *
     * @param requestId     请求ID
     * @param queryResult   查询结果
     * @param loadingStatus 加载状态
     * @param sessionId     会话ID
     */
    public void saveQueryResult(String requestId, ActionResult<DataQueryOutput> queryResult, LoadingStatus loadingStatus, String sessionId) {
        ChartResultData resultData = queryResult.getData().getResultData();
        ChartDataResponse chartDataResponse;
        if (resultData instanceof SpecialChartResult specialChartResult) {
            // 特殊处理销售过程分析结果
            chartDataResponse = ChartDataResponse.builder()
                    .requestId(requestId)
                    .queryData(specialChartResult.getSaleProcessAnalysisResult())
                    .chartConfigData(specialChartResult.getChartConfig())
                    .loadingStatus(loadingStatus)
                    .messageType(MessageType.CHART_DATA)
                    .title(queryResult.getData().getTitle())
                    .build();
        } else {
            StandardChartResult resultData1 = (StandardChartResult) resultData;
            chartDataResponse = ChartDataResponse.builder()
                    .requestId(requestId)
                    .queryData(resultData1.getQueryData())
                    .chartConfigData(resultData1.getChartConfig())
                    .loadingStatus(loadingStatus)
                    .messageType(MessageType.CHART_DATA)
                    .title(queryResult.getData().getTitle())
                    .build();
        }

        saveResponse(chartDataResponse);

        // 只发送完成状态事件，避免重复发送loading状态
        if (loadingStatus == LoadingStatus.COMPLETED) {
            // 使用新的事件发布机制发送事件
            eventPublisher.publish(sessionId,
                    eventFactory.createDataEvent(
                            ChartDataResponse.builder()
                                    .loadingStatus(LoadingStatus.COMPLETED)
                                    .responseType(ResponseType.CHARTDATA)
                                    .requestId(requestId)
                                    .build(),
                            false));

            log.info("已发送图表数据完成事件, 请求ID: {}, 状态: {}", requestId, loadingStatus);
        }

        log.info("已保存图表数据响应, 请求ID: {}, 状态: {}", requestId, loadingStatus);
    }

    /**
     * 从洞察结果转换并保存洞察响应
     *
     * @param requestId     请求ID
     * @param insightResult 洞察结果
     * @param sessionId     会话ID
     */
    public void saveInsightResult(String requestId, ActionResult<InsightOutput> insightResult, String sessionId) {
        InsightResponse insightResponse = InsightResponse.builder()
                .requestId(requestId)
                .quickInsight(MarkdownUtil.markdownToHtml(insightResult.getData().getQuickInsight()))
                .fullInsight(MarkdownUtil.markdownToHtml(insightResult.getData().getFullInsight()))
                .loadingStatus(LoadingStatus.COMPLETED)
                .responseType(ResponseType.CHARTINSIGHT)
                .build();

        saveResponse(insightResponse);

        // 只发送完成事件，避免重复发送loading状态
        // 使用新的事件发布机制发送事件
        eventPublisher.publish(sessionId,
                eventFactory.createDataEvent(InsightResponse.builder()
                        .requestId(requestId)
                        .quickInsight(insightResult.getData().getQuickInsight())
                        .fullInsight(insightResult.getData().getFullInsight())
                        .loadingStatus(LoadingStatus.COMPLETED)
                        .responseType(ResponseType.CHARTINSIGHT)
                        .build(), true));


        log.info("已保存洞察响应并完成会话, 请求ID: {}, 会话ID: {}", requestId, sessionId);
    }

    /**
     * 保存日志
     *
     * @param requestId     请求ID
     * @param actionLogData 日志结果
     */
    public void saveActionLog(String requestId, String actionLogData) {
        ActionLogResponse actionLogResponse = ActionLogResponse.builder()
                .requestId(requestId)
                .actionLog(actionLogData)
                .loadingStatus(LoadingStatus.COMPLETED)
                .build();

        saveResponse(actionLogResponse);
        log.info("已保存动作日志, 请求ID: {}", requestId);
    }

    /**
     * 从推荐结果转换并保存追问响应
     *
     * @param requestId              请求ID
     * @param followUpQuestionResult 推荐结果
     * @param sessionId              会话ID
     */
    public void saveFollowUpQuestionResult(String requestId,
            ActionResult<FollowUpQuestionOutput> followUpQuestionResult, String sessionId) {
        List<String> followUpQuestions = followUpQuestionResult.getData().getFollowUpQuestions();
        if (followUpQuestions != null && !followUpQuestions.isEmpty()) {
            FollowUpResponse followUpResponse = FollowUpResponse.builder()
                    .requestId(requestId)
                    .followUpQuestions(followUpQuestions)
                    .loadingStatus(LoadingStatus.COMPLETED)
                    .build();

            // 发送追问建议事件
            eventPublisher.publish(sessionId, eventFactory.createSuggestEvent(followUpQuestions, false));

            // 保存响应数据
            saveResponse(followUpResponse);
            
            log.info("已保存追问响应, 请求ID: {}", requestId);
        }
    }

    /**
     * 获取响应类型对应的键前缀
     */
    private String getKeyPrefixForResponseType(Class<?> responseType) {
        if (ChartDataResponse.class.isAssignableFrom(responseType)) {
            return RedisConfig.CHATBI_DATA_KEY_PREFIX;
        } else if (InsightResponse.class.isAssignableFrom(responseType)) {
            return RedisConfig.CHATBI_INSIGHT_KEY_PREFIX;
        } else if (FollowUpResponse.class.isAssignableFrom(responseType)) {
            return RedisConfig.CHATBI_FOLLOWUP_KEY_PREFIX;
        } else if (ReasoningResponse.class.isAssignableFrom(responseType)) {
            return RedisConfig.CHATBI_DATA_KEY_PREFIX;
        } else if (ActionLogResponse.class.isAssignableFrom(responseType)) {
            return RedisConfig.CHATBI_ACRTION_LOG_KEY_PREFIX;
        } else {
            log.warn("未知的响应类型: {}, 使用默认键前缀", responseType.getSimpleName());
            return RedisConfig.CHATBI_DATA_KEY_PREFIX;
        }
    }

    /**
     * 保存错误响应
     *
     * @param requestId    请求ID
     * @param errorMessage 错误信息
     * @param sessionId    会话ID
     */
    public void saveErrorResponse(String requestId, String errorMessage, String sessionId) {
        ChartDataResponse errorResponse = ChartDataResponse.builder()
                .requestId(requestId)
                .loadingStatus(LoadingStatus.ERROR)
                .actionLog(errorMessage) // 使用actionLog字段存储错误信息
                .responseType(ResponseType.CHARTDATA)
                .build();

        saveResponse(errorResponse);

        // 发送错误事件
        if (sessionId != null) {
            eventPublisher.publish(sessionId,
                    eventFactory.createDataEvent(errorResponse, false));

            // 发送完成事件
            eventPublisher.publish(sessionId,
                    eventFactory.createFinishEvent());

            // 完成会话，通知清理资源
            eventPublisher.complete(sessionId);
        }

        log.error("已保存错误响应, 请求ID: {}, 错误信息: {}", requestId, errorMessage);
    }

    /**
     * 保存错误响应
     *
     * @param requestId    请求ID
     * @param errorMessage 错误信息
     */
    public void saveErrorResponse(String requestId, String errorMessage) {
        saveErrorResponse(requestId, errorMessage, null);
    }
}