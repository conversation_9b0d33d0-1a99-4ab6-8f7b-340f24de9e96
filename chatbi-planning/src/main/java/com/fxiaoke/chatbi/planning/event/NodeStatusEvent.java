package com.fxiaoke.chatbi.planning.event;

import com.fxiaoke.chatbi.action.core.ActionResult;
import com.fxiaoke.chatbi.common.model.action.ActionType;
import com.fxiaoke.chatbi.planning.context.PlanContext;
import com.fxiaoke.chatbi.planning.model.ExecutionStatus;
import lombok.Getter;

/**
 * 节点状态事件
 * 节点执行完成或失败时发布，完全替代ActionEvent
 */
@Getter
public class NodeStatusEvent {
    private final String planId;
    private final String nodeId;
    private final ActionType actionType;
    private final ExecutionStatus status;
    private final boolean async;
    private final String errorMessage;
    private final PlanContext planContext;
    private final ActionResult<?> actionResult;
    private final long executionTimeMs;
    private final long timestamp;

    /**
     * 完整构造函数
     */
    public NodeStatusEvent(PlanContext planContext, String nodeId, ActionType actionType,
                          ExecutionStatus status, boolean async, String errorMessage, 
                          ActionResult<?> actionResult, long executionTimeMs) {
        this.planContext = planContext;
        this.planId = planContext.getPlanId();
        this.nodeId = nodeId;
        this.actionType = actionType;
        this.status = status;
        this.async = async;
        this.errorMessage = errorMessage;
        this.actionResult = actionResult;
        this.executionTimeMs = executionTimeMs;
        this.timestamp = System.currentTimeMillis();
    }
    
    /**
     * 创建节点开始事件
     */
    public static NodeStatusEvent started(PlanContext planContext, String nodeId, ActionType actionType, boolean async) {
        return new NodeStatusEvent(planContext, nodeId, actionType, ExecutionStatus.RUNNING, async, null, null, 0);
    }
    
    /**
     * 创建节点完成事件
     */
    public static NodeStatusEvent completed(PlanContext planContext, String nodeId, ActionType actionType, 
                                          boolean async, ActionResult<?> result, long executionTimeMs) {
        return new NodeStatusEvent(planContext, nodeId, actionType, ExecutionStatus.COMPLETED, async, null, result, executionTimeMs);
    }
    
    /**
     * 创建节点失败事件
     */
    public static NodeStatusEvent failed(PlanContext planContext, String nodeId, ActionType actionType, 
                                       boolean async, String errorMessage, long executionTimeMs) {
        return new NodeStatusEvent(planContext, nodeId, actionType, ExecutionStatus.FAILED, async, errorMessage, null, executionTimeMs);
    }
    
    /**
     * 获取SessionId
     */
    public String getSessionId() {
        return planContext.getSessionId();
    }
    
    /**
     * 获取推理信息
     */
    public String getReasoning() {
        if (planContext.getReasoningCollector() != null) {
            return planContext.getReasoningCollector().getReasoningsMap().get(actionType.name());
        }
        return null;
    }
    
    /**
     * 是否执行成功
     */
    public boolean isSuccess() {
        return status == ExecutionStatus.COMPLETED;
    }
} 