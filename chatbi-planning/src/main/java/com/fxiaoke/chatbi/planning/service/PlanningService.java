package com.fxiaoke.chatbi.planning.service;

import com.fxiaoke.chatbi.common.exception.ChatbiErrorCodeEnum;
import com.fxiaoke.chatbi.common.exception.PlanningException;
import com.fxiaoke.chatbi.common.model.intent.IntentAnalysisResult;
import com.fxiaoke.chatbi.common.model.intent.IntentType;
import com.fxiaoke.chatbi.common.model.memory.ConversationContext;
import com.fxiaoke.chatbi.common.model.reasoning.ReasoningCollector;
import com.fxiaoke.chatbi.planning.context.*;
import com.fxiaoke.chatbi.planning.flow.Flow;
import com.fxiaoke.chatbi.planning.flow.FlowExecutor;
import com.fxiaoke.chatbi.planning.flow.FlowResultHandler;
import com.fxiaoke.chatbi.planning.flow.analysis.AnalysisFlow;
import com.fxiaoke.chatbi.planning.flow.analysis.AnalysisFlowExecutor;
import com.fxiaoke.chatbi.planning.flow.analysis.AnalysisResultHandler;
import com.fxiaoke.chatbi.planning.flow.clarification.ClarificationFlow;
import com.fxiaoke.chatbi.planning.flow.clarification.ClarificationFlowExecutor;
import com.fxiaoke.chatbi.planning.flow.clarification.ClarificationResultHandler;
import com.fxiaoke.chatbi.planning.model.FlowResult;
import com.fxiaoke.chatbi.planning.model.FlowType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;

/**
 * 计划服务
 * 负责创建和执行分析计划
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PlanningService {
  private final AnalysisFlow analysisFlow;
  private final ClarificationFlow clarificationFlow;
  private final AnalysisFlowExecutor analysisFlowExecutor;
  private final ClarificationFlowExecutor clarificationFlowExecutor;
  private final AnalysisResultHandler analysisResultHandler;
  private final ClarificationResultHandler clarificationResultHandler;
  private final AnalysisPlanResultGenerator analysisPlanResultGenerator;
  private final ClarificationPlanResultGenerator clarificationPlanResultGenerator;

  private final Map<FlowType, FlowExecutor> flowExecutorMap = Maps.newHashMap();
  private final Map<FlowType, FlowResultHandler> flowResultHandlerMap = Maps.newHashMap();
  private final Map<IntentType, PlanResultGenerator<?>> resultGeneratorMap = Maps.newHashMap();
  private final Map<IntentType, List<Flow>> intentFlowsMap = Maps.newHashMap();

  @PostConstruct
  public void init() {
    // 初始化Flow相关映射
    flowExecutorMap.put(FlowType.ANALYSIS, analysisFlowExecutor);
    flowExecutorMap.put(FlowType.CLARIFICATION, clarificationFlowExecutor);
    flowResultHandlerMap.put(FlowType.ANALYSIS, analysisResultHandler);
    flowResultHandlerMap.put(FlowType.CLARIFICATION, clarificationResultHandler);

    // 初始化ResultGenerator映射
    resultGeneratorMap.put(IntentType.ANALYSIS, analysisPlanResultGenerator);
    resultGeneratorMap.put(IntentType.CLARIFICATION, clarificationPlanResultGenerator);
    
    // 初始化意图对应的Flow列表
    intentFlowsMap.put(IntentType.ANALYSIS, Lists.newArrayList(analysisFlow));
    intentFlowsMap.put(IntentType.CLARIFICATION, Lists.newArrayList(clarificationFlow));
  }

  /**
   * 初始化计划上下文
   *
   * @param analysisResult
   * @param sessionContext
   * @param llmModel
   * @param planId
   * @param reasoningCollector
   * @return
   */
  public PlanContext initPlanContext(IntentAnalysisResult analysisResult,
                                     ConversationContext sessionContext,
                                     String llmModel,
                                     String planId,
                                     ReasoningCollector reasoningCollector) {
    // 创建计划上下文
    PlanContext planContext = new PlanContext(planId, sessionContext, llmModel);
    planContext.setUserIntent(analysisResult.getIntent());
    planContext.setKnowledgeScope(analysisResult.getKnowledgeScope());
    planContext.setLlmModel(llmModel);
    planContext.setReasoningCollector(reasoningCollector);
    return planContext;
  }

  /**
   * 创建计划
   *
   * @param planContext
   * @return
   */
  public Plan createPlan(PlanContext planContext) {
    List<Flow> flows = orchestrateFlows(planContext);

    // 从Map中获取对应的结果生成器
    IntentType intentType = planContext.getUserIntent().getIntentType();
    PlanResultGenerator<?> resultGenerator = resultGeneratorMap.get(intentType);
    if (resultGenerator == null) {
      log.error("未找到意图类型对应的结果生成器: {}", intentType);
      throw new PlanningException(ChatbiErrorCodeEnum.SYSTEM_ERROR, "未找到意图类型对应的结果生成器: " + intentType);
    }

    return new Plan(planContext.getPlanId(), flows, resultGenerator);
  }

  /**
   * 根据意图类型编排执行流程
   */
  private List<Flow> orchestrateFlows(PlanContext context) {
    try {
      IntentType intentType = context.getUserIntent().getIntentType();
      log.info("编排意图类型: {}, 计划ID: {}", intentType, context.getPlanId());
      
      // 从Map中获取对应的Flow列表
      List<Flow> flows = intentFlowsMap.get(intentType);
      if (flows == null) {
        log.warn("未知意图类型: {}, 计划ID: {}", intentType, context.getPlanId());
        throw new PlanningException(ChatbiErrorCodeEnum.INTENT_ANALYSIS_FAILED, "无法识别的意图类型: " + intentType);
      }

      log.info("流程编排完成, 计划ID: {}, 流程数: {}", context.getPlanId(), flows.size());
      return flows;
    } catch (PlanningException e) {
      throw e;
    } catch (Exception e) {
      log.error("流程编排异常, 计划ID: {}", context.getPlanId(), e);
      throw new PlanningException(ChatbiErrorCodeEnum.INTENT_ANALYSIS_FAILED, "流程编排异常: " + e.getMessage());
    }
  }

  /**
   * 执行计划
   *
   * @param planContext 计划上下文
   * @param plan        要执行的计划
   * @return 生成的计划结果
   */
  public <T> T executePlan(PlanContext planContext, Plan plan) {
    List<Flow> flows = plan.getFlows();
    Map<FlowType, FlowResult> flowResults = Maps.newLinkedHashMap();

    for (Flow flow : flows) {
      log.info("执行流程: {}, 计划ID: {}", flow.getFlowName(), planContext.getPlanId());

      // 获取执行器
      FlowExecutor flowExecutor = flowExecutorMap.get(flow.getFlowType());

      // 执行流程
      FlowResult flowResult = flowExecutor.execute(flow.getExecutionPlan(planContext), planContext);

      // 获取结果处理器
      FlowResultHandler flowResultHandler = flowResultHandlerMap.get(flow.getFlowType());

      // 处理结果
      flowResultHandler.handleResult(planContext, flowResult);

      // 保存流程结果
      flowResults.put(flow.getFlowType(), flowResult);
    }

    // 使用计划中的结果生成器生成最终结果
    @SuppressWarnings("unchecked") PlanResultGenerator<T> resultGenerator = (PlanResultGenerator<T>) plan.getResultGenerator();
    return resultGenerator.generate(flowResults, planContext);
  }

} 