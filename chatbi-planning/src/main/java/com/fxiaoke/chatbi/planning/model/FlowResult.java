package com.fxiaoke.chatbi.planning.model;

import com.fxiaoke.chatbi.action.core.ActionResult;
import com.fxiaoke.chatbi.common.model.action.ActionType;
import lombok.Builder;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 流程执行结果
 */
@Data
@Builder
public class FlowResult {
  /**
   * 各个Action的执行结果
   * 使用LinkedHashMap保持执行顺序
   */
  private final Map<ActionType, ActionResult<?>> actionResults;

  /**
   * 获取指定类型的Action结果
   */
  @SuppressWarnings("unchecked")
  public <T> Optional<ActionResult<T>> getActionResult(ActionType actionType) {
    ActionResult<?> result = actionResults.get(actionType);
    return Optional.ofNullable((ActionResult<T>) result);
  }

  /**
   * 创建结果
   */
  public static FlowResult of(Map<ActionType, ActionResult<?>> results) {
    return FlowResult.builder()
            .actionResults(new LinkedHashMap<>(results))
            .build();
  }
} 