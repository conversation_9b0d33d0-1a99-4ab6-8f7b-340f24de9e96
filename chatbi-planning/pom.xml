<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.fxiaoke</groupId>
    <artifactId>fs-bi-agent</artifactId>
    <version>9.5.0-SNAPSHOT</version>
  </parent>

  <artifactId>chatbi-planning</artifactId>
  <packaging>jar</packaging>
  <name>chatbi-planning</name>
  <description>ChatBI 规划系统模块，负责意图识别和计划生成</description>

  <dependencies>
    <!-- 模块依赖 -->
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>chatbi-common</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>chatbi-memory</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>chatbi-integration</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>chatbi-knowledge</artifactId>
    </dependency>

    <!-- Spring依赖 -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
    </dependency>

    <!-- AI相关依赖 -->
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-paas-ai-api</artifactId>
    </dependency>

    <!-- 测试依赖 -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>chatbi-action</artifactId>
    </dependency>
  </dependencies>
</project> 