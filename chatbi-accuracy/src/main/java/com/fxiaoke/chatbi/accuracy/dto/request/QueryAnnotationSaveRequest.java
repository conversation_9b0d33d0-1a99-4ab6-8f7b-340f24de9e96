package com.fxiaoke.chatbi.accuracy.dto.request;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 问题级标注保存请求（重构后支持组件标注）
 */
@Data
public class QueryAnnotationSaveRequest {

    /**
     * 关联的问答记录ID
     */
    @NotBlank(message = "记录ID不能为空")
    private String requestId;

    /**
     * 关联的会话ID
     */
    @NotBlank(message = "会话ID不能为空")
    private String sessionId;

    /**
     * 租户ID
     */
    @NotBlank(message = "租户ID不能为空")
    private String tenantId;

    /**
     * 4个组件标注详情，JSON格式
     */
    private String componentAnnotations;

    /**
     * 单轮回答整体评分：1-5分
     */
    @NotNull(message = "单轮评分不能为空")
    @Min(value = 1, message = "评分不能小于1")
    @Max(value = 5, message = "评分不能大于5")
    private Integer roundScore;

    /**
     * 单轮标签列表
     */
    private List<String> roundTags;

    /**
     * 单轮回答的综合评价
     */
    private String roundComments;

    /**
     * 附件信息JSON
     */
    private String attachments;
}