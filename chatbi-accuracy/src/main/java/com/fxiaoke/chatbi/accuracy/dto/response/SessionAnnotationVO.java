package com.fxiaoke.chatbi.accuracy.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Session级标注展示VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SessionAnnotationVO {
    
    /**
     * Session标注记录ID
     */
    private String id;
    
    /**
     * Session整体质量评分：1-5分
     */
    private Integer sessionScore;
    
    /**
     * Session标签
     */
    private List<String> sessionTags;
    
    /**
     * Session的综合文字评价
     */
    private String sessionComments;
    
    /**
     * 标注人ID
     */
    private String createdBy;
    
    /**
     * 创建时间
     */
    private Long createTime;
    
    /**
     * 最后修改人ID
     */
    private String lastModifiedBy;
    
    /**
     * 最后修改时间
     */
    private Long lastModifiedTime;
}
