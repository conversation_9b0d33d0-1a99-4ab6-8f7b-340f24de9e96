package com.fxiaoke.chatbi.accuracy.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 问题级标注展示VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryAnnotationVO {
    
    /**
     * 标注记录ID
     */
    private String id;
    
    /**
     * 4个组件标注详情
     */
    private ComponentAnnotationVO componentAnnotations;
    
    /**
     * 单轮回答整体评分：1-5分
     */
    private Integer roundScore;
    
    /**
     * 单轮标签
     */
    private List<String> roundTags;
    
    /**
     * 单轮回答的综合评价
     */
    private String roundComments;
    
    /**
     * 标注人ID
     */
    private String createdBy;
    
    /**
     * 标注时间
     */
    private Long createTime;
    
    /**
     * 最后修改时间
     */
    private Long lastModifiedTime;
}
