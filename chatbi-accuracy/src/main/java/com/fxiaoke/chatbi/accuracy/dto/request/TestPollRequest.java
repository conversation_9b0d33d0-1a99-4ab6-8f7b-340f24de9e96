package com.fxiaoke.chatbi.accuracy.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 测试轮询请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TestPollRequest {
    
    /**
     * Agent服务的完整API地址 (如: http://10.112.5.251:19378/chatbi/loadData)
     */
    private String agentUrl;
    
    /**
     * 租户ID
     */
    private Long tenantId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 请求ID
     */
    private String requestId;
} 