package com.fxiaoke.chatbi.accuracy.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 图表召回测试结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChartRecallTestResult {
    /**
     * 是否匹配
     */
    private boolean matched;
    
    /**
     * 匹配分数
     */
    private double score;
    
    /**
     * 召回图表列表
     */
    private List<RecalledChartInfo> recalledCharts;
    
    /**
     * 测试查询
     */
    private String testQuery;
    
    /**
     * 目标图表ID
     */
    private String targetViewId;
}
