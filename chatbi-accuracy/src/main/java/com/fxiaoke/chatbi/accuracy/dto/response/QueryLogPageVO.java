package com.fxiaoke.chatbi.accuracy.dto.response;

import com.fxiaoke.chatbi.integration.dto.QueryLogListVO;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 问答记录分页响应VO
 */
@Data
@Builder
public class QueryLogPageVO {
    
    /**
     * 记录列表
     */
    private List<QueryLogListVO> records;
    
    /**
     * 总记录数
     */
    private long total;
    
    /**
     * 总页数
     */
    private int pages;
    
    /**
     * 当前页码
     */
    private int currentPage;
    
    /**
     * 每页大小
     */
    private int pageSize;
    
    /**
     * 统计信息
     */
    private AnnotationStatisticsVO statistics;
} 