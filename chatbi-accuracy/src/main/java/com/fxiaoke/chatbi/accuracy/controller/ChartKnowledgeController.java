package com.fxiaoke.chatbi.accuracy.controller;

import com.fxiaoke.chatbi.accuracy.dto.request.*;
import com.fxiaoke.chatbi.accuracy.dto.response.ChartRecallTestResult;
import com.fxiaoke.chatbi.accuracy.dto.response.ChartLibraryResponse;
import com.fxiaoke.chatbi.accuracy.service.ChartKnowledgeService;
import com.fxiaoke.chatbi.common.dto.ApiResult;
import com.fxiaoke.chatbi.common.model.OperationContext;
import com.fxiaoke.chatbi.integration.model.ch.ChartKnowledge;
import com.fxiaoke.chatbi.integration.model.ch.KnowledgeEmbedding;
import com.fxiaoke.chatbi.knowledge.service.ChartKnowledgeBuildingService;
import com.facishare.bi.metadata.context.dto.dw.Schema;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 图表知识库管理控制器
 */
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
@Slf4j
public class ChartKnowledgeController {

    private final ChartKnowledgeService chartKnowledgeService;
    private final ChartKnowledgeBuildingService chartKnowledgeBuildService;

    /**
     * 获取图表列表
     */
    @PostMapping("/getChartKnowledgeList")
    public ApiResult<List<ChartKnowledge>> getChartKnowledgeList(@RequestBody(required = false) ChartKnowledgeQueryRequest request, OperationContext context) {
        log.info("获取图表列表, 管理员: {}, 目标租户: {}", context.getAdminId(), context.getTargetTenantId());
        List<ChartKnowledge> charts = chartKnowledgeService.listCharts(request, context);
        return ApiResult.success(charts);
    }

    /**
     * 获取图表数量统计
     */
    @PostMapping("/getChartCount")
    public ApiResult<Integer> getChartCount(@RequestBody(required = false) Object request, OperationContext context) {
        log.info("获取图表数量, 管理员: {}, 目标租户: {}", context.getAdminId(), context.getTargetTenantId());
        List<ChartKnowledge> charts = chartKnowledgeService.listCharts(null, context);
        return ApiResult.success(charts != null ? charts.size() : 0);
    }

    /**
     * 获取图表特征
     */
    @PostMapping("/getChartFeaturesByViewId")
    public ApiResult<List<KnowledgeEmbedding>> getChartFeaturesByViewId(@RequestBody ChartFeatureQueryRequest request, OperationContext context) {
        log.info("获取图表特征, 管理员: {}, 目标租户: {}, 图表ID: {}", context.getAdminId(), context.getTargetTenantId(), request.getViewId());
        List<KnowledgeEmbedding> features = chartKnowledgeService.getChartFeatures(request.getViewId(), context);
        return ApiResult.success(features);
    }

    /**
     * 保存图表特征
     */
    @PostMapping("/saveChartFeature")
    public ApiResult<Void> saveChartFeature(@RequestBody ChartFeatureSaveRequest request, OperationContext context) {
        log.info("保存图表特征, 管理员: {}, 目标租户: {}, 图表ID: {}", context.getAdminId(), context.getTargetTenantId(), request.getViewId());
        chartKnowledgeService.saveChartFeature(request.getViewId(), request.getFeature(), context);
        return ApiResult.ok();
    }

    /**
     * 删除图表特征
     */
    @PostMapping("/deleteChartFeature")
    public ApiResult<Void> deleteChartFeature(@RequestBody ChartFeatureDeleteRequest request, OperationContext context) {
        log.info("删除图表特征, 管理员: {}, 目标租户: {}, 图表ID: {}, 特征ID: {}",
                context.getAdminId(), context.getTargetTenantId(), request.getViewId(), request.getFeatureId());
        chartKnowledgeService.deleteChartFeature(request.getViewId(), request.getFeatureId(), context);
        return ApiResult.ok();
    }

    /**
     * 测试图表特征
     */
    @PostMapping("/testChartFeature")
    public ApiResult<ChartRecallTestResult> testChartFeature(@RequestBody ChartFeatureTestRequest request, OperationContext context) {
        log.info("测试图表特征, 管理员: {}, 目标租户: {}, 图表ID: {}", context.getAdminId(), context.getTargetTenantId(), request.getViewId());
        ChartRecallTestResult result = chartKnowledgeService.testChartFeature(request.getViewId(), request.getTestQuery(), request.getFeatures(), context);
        return ApiResult.success(result);
    }

    /**
     * 保存图表（重建图表知识）
     */
    @PostMapping("/saveChart")
    public ApiResult<Void> saveChart(@RequestBody Map<String, Object> request, OperationContext context) {
        String viewId = (String) request.get("viewId");
        log.info("保存图表, 管理员: {}, 目标租户: {}, 图表ID: {}", context.getAdminId(), context.getTargetTenantId(), viewId);

        try {
            // 使用图表知识构建服务重建图表知识
            chartKnowledgeBuildService.buildChartKnowledge(context.toUserIdentity(), viewId);
            log.info("图表知识重建成功, 图表ID: {}", viewId);
            return ApiResult.ok();
        } catch (Exception e) {
            log.error("图表知识重建失败, 图表ID: {}", viewId, e);
            return ApiResult.error("图表知识重建失败: " + e.getMessage());
        }
    }

    /**
     * 删除图表
     */
    @PostMapping("/deleteChart")
    public ApiResult<Void> deleteChart(@RequestBody Map<String, Object> request, OperationContext context) {
        String viewId = (String) request.get("viewId");
        log.info("删除图表, 管理员: {}, 目标租户: {}, 图表ID: {}", context.getAdminId(), context.getTargetTenantId(), viewId);

        try {
            // 获取该图表的所有特征ID
            List<KnowledgeEmbedding> features = chartKnowledgeService.getChartFeatures(viewId, context);

            if (!features.isEmpty()) {
                // 提取特征ID列表
                List<String> featureIds = features.stream()
                        .map(KnowledgeEmbedding::getId)
                        .collect(java.util.stream.Collectors.toList());

                // 使用批量删除（更适合ClickHouse）
                chartKnowledgeService
                        .batchDeleteChartFeatures(viewId, featureIds, context);

                log.info("图表知识删除成功, 图表ID: {}, 删除特征数量: {}", viewId, features.size());
            } else {
                log.info("图表无特征需要删除, 图表ID: {}", viewId);
            }

            return ApiResult.ok();
        } catch (Exception e) {
            log.error("图表知识删除失败, 图表ID: {}", viewId, e);
            return ApiResult.error("图表知识删除失败: " + e.getMessage());
        }
    }

    /**
     * 获取图表库数据
     * 包含图表列表、主题列表和统计信息，用于图表库页面
     */
    @PostMapping("/getChartLibraryData")
    public ApiResult<ChartLibraryResponse> getChartLibraryData(@RequestBody(required = false) Object request, OperationContext context) {
        log.info("获取图表库数据, 管理员: {}, 目标租户: {}", context.getAdminId(), context.getTargetTenantId());
        try {
            ChartLibraryResponse response = chartKnowledgeService.getChartLibraryData(context);
            return ApiResult.success(response);
        } catch (Exception e) {
            log.error("获取图表库数据失败", e);
            return ApiResult.error("获取图表库数据失败: " + e.getMessage());
        }
    }
}
