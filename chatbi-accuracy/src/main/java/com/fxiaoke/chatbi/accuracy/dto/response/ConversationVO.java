package com.fxiaoke.chatbi.accuracy.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 单轮对话展示VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConversationVO {
    
    /**
     * 对话记录ID
     */
    private String conversationId;
    
    /**
     * 轮次序号
     */
    private Integer round;
    
    /**
     * 用户问题
     */
    private String userQuestion;
    
    /**
     * AI回答的4个组件
     */
    private AIResponseVO aiResponse;
    
    /**
     * 问题级标注信息
     */
    private QueryAnnotationVO annotation;
    
    /**
     * 附件列表
     */
    private List<AttachmentVO> attachments;
    
    /**
     * 问询时间
     */
    private Long queryTime;
    
    /**
     * 响应时间（毫秒）
     */
    private Long responseTime;
}
