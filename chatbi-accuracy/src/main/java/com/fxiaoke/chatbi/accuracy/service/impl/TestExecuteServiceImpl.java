package com.fxiaoke.chatbi.accuracy.service.impl;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.chatbi.accuracy.dto.request.TestExecuteRequest;
import com.fxiaoke.chatbi.accuracy.dto.request.TestPollRequest;
import com.fxiaoke.chatbi.accuracy.service.TestExecuteService;
import com.fxiaoke.chatbi.common.model.request.ChatBiRequest;
import com.fxiaoke.chatbi.common.model.request.LoadDataRequest;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.UUID;

/**
 * 测试执行服务实现 - 使用OkHttpSupport
 */
@Service
@Slf4j
public class TestExecuteServiceImpl implements TestExecuteService {

    @Autowired
    @Qualifier("httpClientSupport")
    private OkHttpSupport client;

    @Override
    public Object executeTest(TestExecuteRequest request) {
        log.info("执行测试: URL={}, 问题={}", request.getAgentUrl(), request.getQuery());

        try {
            // 构建请求数据
            ChatBiRequest chatBiRequest = new ChatBiRequest();
            chatBiRequest.setInstructions(request.getQuery());
            chatBiRequest.setSessionId(UUID.randomUUID().toString());

            // 构建HTTP请求 - 直接使用传入的完整地址
            Request httpRequest = new Request.Builder()
                    .url(request.getAgentUrl())
                    .addHeader("Content-Type", "application/json")
                    .addHeader("X-fs-Enterprise-Id", request.getTenantId())
                    .addHeader("X-fs-Employee-Id", request.getUserId())
                    .addHeader("x-fs-ei", request.getTenantId())
                    .addHeader("x-fs-locale", "zh_CN")
                    .post(RequestBody.create(
                            MediaType.parse("application/json"),
                            JSON.toJSONString(chatBiRequest)
                    ))
                    .build();

            // HTTP调用
            String responseBody = (String) client.syncExecute(httpRequest, new SyncCallback() {
                @Override
                public Object response(Response response) throws IOException {
                    return response.body() != null ? response.body().string() : "";
                }
            });

            // 直接返回Agent服务的JSON响应
            return JSON.parse(responseBody);

        } catch (Exception e) {
            log.error("测试执行异常", e);
            throw new RuntimeException("调用Agent服务失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Object pollData(TestPollRequest request) {
        log.debug("轮询数据: URL={}, 请求ID={}", request.getAgentUrl(), request.getRequestId());

        try {
            // 构建LoadDataRequest
            LoadDataRequest loadDataRequest = new LoadDataRequest();
            loadDataRequest.setRequestId(request.getRequestId());

            // 构建HTTP请求
            Request httpRequest = new Request.Builder()
                    .url(request.getAgentUrl())
                    .addHeader("Content-Type", "application/json")
                    .addHeader("X-fs-Enterprise-Id", String.valueOf(request.getTenantId()))
                    .addHeader("X-fs-Employee-Id", String.valueOf(request.getUserId()))
                    .addHeader("x-fs-ei", String.valueOf(request.getTenantId()))
                    .addHeader("x-fs-locale", "zh_CN")
                    .post(RequestBody.create(
                            MediaType.parse("application/json"),
                            JSON.toJSONString(loadDataRequest)
                    ))
                    .build();

            // HTTP调用
            String responseBody = (String) client.syncExecute(httpRequest, new SyncCallback() {
                @Override
                public Object response(Response response) throws IOException {
                    return response.body() != null ? response.body().string() : "";
                }
            });

            // 解析并返回结果
            return JSON.parse(responseBody);

        } catch (Exception e) {
            log.error("轮询数据异常: URL={}, 请求ID={}", request.getAgentUrl(), request.getRequestId(), e);
            throw new RuntimeException("轮询数据失败: " + e.getMessage(), e);
        }
    }

} 