package com.fxiaoke.chatbi.accuracy.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AI回答的4个组件VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AIResponseVO {

    /**
     * 推理分析组件
     */
    private String reasoningResponse;

    /**
     * 图表数据组件
     */
    private String chartDataResponse;

    /**
     * 数据洞察组件
     */
    private String insightResponse;

    /**
     * 追问建议组件
     */
    private String followUpResponse;

    /**
     * 执行日志（JSON格式）
     */
    private String actionLogs;
}
