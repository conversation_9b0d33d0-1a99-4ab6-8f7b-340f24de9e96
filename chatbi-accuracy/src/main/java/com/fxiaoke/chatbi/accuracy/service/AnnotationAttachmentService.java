package com.fxiaoke.chatbi.accuracy.service;

import com.fxiaoke.chatbi.accuracy.dto.response.AttachmentVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 标注附件服务接口
 * 支持图片文件的上传、删除、查询功能
 */
public interface AnnotationAttachmentService {

    /**
     * 上传附件
     *
     * @param file 上传的文件
     * @param description 附件描述
     * @param annotationId 关联的标注ID
     * @return 附件信息
     */
    AttachmentVO uploadAttachment(MultipartFile file, String description, String annotationId);

    /**
     * 删除附件
     *
     * @param attachmentId 附件ID
     * @return 是否删除成功
     */
    boolean deleteAttachment(String attachmentId);

    /**
     * 根据标注ID查询附件列表
     *
     * @param annotationId 标注ID
     * @return 附件列表
     */
    List<AttachmentVO> getAttachmentsByAnnotationId(String annotationId);

    /**
     * 根据附件ID查询附件信息
     *
     * @param attachmentId 附件ID
     * @return 附件信息
     */
    AttachmentVO getAttachmentById(String attachmentId);
}
