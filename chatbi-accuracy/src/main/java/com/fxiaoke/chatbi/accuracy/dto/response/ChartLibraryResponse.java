package com.fxiaoke.chatbi.accuracy.dto.response;

import com.fxiaoke.chatbi.integration.model.ch.ChartKnowledge;
import com.facishare.bi.metadata.context.dto.dw.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 图表库响应DTO
 * 包含图表列表和主题列表，用于图表库页面展示
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChartLibraryResponse {
    
    /**
     * 图表列表
     */
    private List<ChartKnowledge> charts;
    
    /**
     * 主题列表
     */
    private List<Schema> themes;
    
    /**
     * 图表总数
     */
    private Integer totalCharts;
    
    /**
     * 主题总数
     */
    private Integer totalThemes;
    
    /**
     * 数据统计信息
     */
    private StatInfo statInfo;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StatInfo {
        /**
         * 按图表类型分组的数量
         */
        private List<CategoryCount> chartTypeStats;
        
        /**
         * 按主题分组的图表数量
         */
        private List<CategoryCount> themeStats;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CategoryCount {
        private String category;
        private String categoryName;
        private Integer count;
    }
} 