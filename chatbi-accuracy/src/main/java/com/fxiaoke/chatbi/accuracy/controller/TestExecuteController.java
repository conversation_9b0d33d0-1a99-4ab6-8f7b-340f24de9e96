package com.fxiaoke.chatbi.accuracy.controller;

import com.fxiaoke.chatbi.accuracy.dto.request.TestExecuteRequest;
import com.fxiaoke.chatbi.accuracy.dto.request.TestPollRequest;
import com.fxiaoke.chatbi.accuracy.service.TestExecuteService;
import com.fxiaoke.chatbi.common.dto.ApiResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 测试执行控制器
 */
@RestController
@RequestMapping("/api/test")
@RequiredArgsConstructor
@Slf4j
public class TestExecuteController {
    
    private final TestExecuteService testExecuteService;
    
    /**
     * 执行测试
     */
    @PostMapping("/executeTest")
    public ApiResult<Object> executeTest(@RequestBody TestExecuteRequest request) {
        log.info("收到测试执行请求, Agent地址: {}, 租户: {}, 用户: {}, 问题: {}", 
                request.getAgentUrl(), request.getTenantId(), request.getUserId(), request.getQuery());
        
        try {
            Object response = testExecuteService.executeTest(request);
            return ApiResult.success(response);
        } catch (Exception e) {
            log.error("测试执行失败", e);
            return ApiResult.error("测试执行失败: " + e.getMessage());
        }
    }
    
    /**
     * 轮询异步数据
     */
    @PostMapping("/pollData")
    public ApiResult<Object> pollData(@RequestBody TestPollRequest request) {
        log.debug("轮询异步数据, URL: {}, 请求ID: {}", request.getAgentUrl(), request.getRequestId());
        
        try {
            Object response = testExecuteService.pollData(request);
            return ApiResult.success(response);
        } catch (Exception e) {
            log.error("轮询数据失败", e);
            return ApiResult.error("轮询数据失败: " + e.getMessage());
        }
    }
} 