package com.fxiaoke.chatbi.accuracy.service;

import com.fxiaoke.chatbi.accuracy.dto.request.ChartKnowledgeQueryRequest;
import com.fxiaoke.chatbi.accuracy.dto.response.ChartRecallTestResult;
import com.fxiaoke.chatbi.accuracy.dto.response.ChartLibraryResponse;
import com.fxiaoke.chatbi.common.model.OperationContext;
import com.fxiaoke.chatbi.integration.model.ch.ChartKnowledge;
import com.fxiaoke.chatbi.integration.model.ch.KnowledgeEmbedding;
import com.facishare.bi.metadata.context.dto.dw.Schema;

import java.util.List;
import java.util.Map;

/**
 * 图表知识库管理服务
 */
public interface ChartKnowledgeService {
    /**
     * 获取图表列表
     *
     * @param request 查询请求
     * @param context 操作上下文
     * @return 图表列表
     */
    List<ChartKnowledge> listCharts(ChartKnowledgeQueryRequest request, OperationContext context);
    
    /**
     * 获取图表特征
     *
     * @param viewId 图表ID
     * @param context 操作上下文
     * @return 特征列表
     */
    List<KnowledgeEmbedding> getChartFeatures(String viewId, OperationContext context);
    
    /**
     * 保存图表特征
     *
     * @param viewId 图表ID
     * @param feature 特征
     * @param context 操作上下文
     */
    void saveChartFeature(String viewId, KnowledgeEmbedding feature, OperationContext context);
    
    /**
     * 删除图表特征
     *
     * @param viewId 图表ID
     * @param featureId 特征ID
     * @param context 操作上下文
     */
    void deleteChartFeature(String viewId, String featureId, OperationContext context);

    /**
     * 批量删除图表特征
     *
     * @param viewId 图表ID
     * @param featureIds 特征ID列表
     * @param context 操作上下文
     */
    void batchDeleteChartFeatures(String viewId, List<String> featureIds, OperationContext context);

    /**
     * 测试图表特征召回效果
     *
     * @param viewId     图表ID
     * @param testQuery  测试查询文本
     * @param features   特征列表
     * @param context    操作上下文
     * @return 测试结果
     */
    ChartRecallTestResult testChartFeature(String viewId, String testQuery, List<String> features, OperationContext context);

    /**
     * 获取图表库数据
     * 包含图表列表、主题列表和统计信息
     *
     * @param context 操作上下文
     * @return 图表库响应数据
     */
    ChartLibraryResponse getChartLibraryData(OperationContext context);
}
