package com.fxiaoke.chatbi.accuracy;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;

/**
 * ChatBI准确率验证与迭代测试平台启动类
 */
@SpringBootApplication(exclude = {
    DataSourceAutoConfiguration.class,
    HibernateJpaAutoConfiguration.class,
    MongoAutoConfiguration.class,
    MongoDataAutoConfiguration.class,
    RedisAutoConfiguration.class
})
@ComponentScan(basePackages = {
    "com.fxiaoke.chatbi.accuracy",
    "com.fxiaoke.chatbi.integration",
    "com.fxiaoke.chatbi.knowledge",
    "com.fxiaoke.chatbi.common.config",
    "com.fxiaoke.chatbi.prompts"
})
public class AccuracyApplication extends SpringBootServletInitializer {
    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(AccuracyApplication.class);
    }

    public static void main(String[] args) {
        SpringApplication.run(AccuracyApplication.class, args);
    }
}
