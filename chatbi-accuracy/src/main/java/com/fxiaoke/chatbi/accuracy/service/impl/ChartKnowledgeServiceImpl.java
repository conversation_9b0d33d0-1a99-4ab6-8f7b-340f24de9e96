package com.fxiaoke.chatbi.accuracy.service.impl;

import com.fxiaoke.chatbi.accuracy.dto.request.ChartKnowledgeQueryRequest;
import com.fxiaoke.chatbi.accuracy.dto.response.ChartRecallTestResult;
import com.fxiaoke.chatbi.accuracy.dto.response.RecalledChartInfo;
import com.fxiaoke.chatbi.accuracy.dto.response.ChartLibraryResponse;
import com.fxiaoke.chatbi.accuracy.service.ChartKnowledgeService;
import com.fxiaoke.chatbi.common.model.OperationContext;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.enums.KnowledgeType;
import com.fxiaoke.chatbi.common.model.knowledge.ChannelType;
import com.fxiaoke.chatbi.integration.model.ch.ChartKnowledge;
import com.fxiaoke.chatbi.integration.model.ch.KnowledgeEmbedding;
import com.fxiaoke.chatbi.integration.repository.ch.ChartKnowledgeRepository;
import com.fxiaoke.chatbi.integration.repository.ch.KnowledgeEmbeddingRepository;
import com.fxiaoke.chatbi.knowledge.retrieval.ChartRetriever;
import com.fxiaoke.chatbi.knowledge.embedding.core.TextVectorizer;
import com.fxiaoke.chatbi.common.utils.UserInfoConvertUtil;
import com.fxiaoke.chatbi.knowledge.dictionary.EasyStatChartTypeEnum;
import com.facishare.bi.metadata.context.service.dw.ISchemaQueryService;
import com.facishare.bi.metadata.context.dto.dw.Schema;
import com.facishare.bi.metadata.context.dto.dw.arg.QuerySchemasArg;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 图表知识库管理服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ChartKnowledgeServiceImpl implements ChartKnowledgeService {
    
    private final ChartKnowledgeRepository chartKnowledgeRepository;
    private final KnowledgeEmbeddingRepository knowledgeEmbeddingRepository;
    private final ChartRetriever chartRetriever;
    private final TextVectorizer textVectorizer;
    private final ISchemaQueryService schemaQueryService;

    @Override
    public List<ChartKnowledge> listCharts(ChartKnowledgeQueryRequest request, OperationContext context) {
        log.info("获取图表列表, 请求: {}", request);
        
        UserIdentity userIdentity = context.toUserIdentity();
        String tenantId = userIdentity.getTenantId();
        
        List<ChartKnowledge> chartKnowledges = chartKnowledgeRepository.findAllByTenant(tenantId);
        return chartKnowledges;
    }

    @Override
    public List<KnowledgeEmbedding> getChartFeatures(String viewId, OperationContext context) {
        log.info("获取图表特征, 图表ID: {}", viewId);
        UserIdentity userIdentity = context.toUserIdentity();
        List<KnowledgeEmbedding> embeddings = knowledgeEmbeddingRepository.findByKnowledgeId(
                userIdentity,
                ChannelType.TENANT,
                KnowledgeType.CHART.getCode(),
                viewId
        );
        
        return embeddings;
    }

    @Override
    public void saveChartFeature(String viewId, KnowledgeEmbedding feature, OperationContext context) {
        log.info("保存图表特征, 图表ID: {}, 特征: {}", viewId, feature.getFeature());
        
        UserIdentity userIdentity = context.toUserIdentity();
        
        try {
            float[] embedding = textVectorizer.vectorize(feature.getFeature(), userIdentity);
            
            KnowledgeEmbedding knowledgeEmbedding = KnowledgeEmbedding.builder()
                    .id(feature.getId() != null ? feature.getId() : UUID.randomUUID().toString())
                    .tenantId(userIdentity.getTenantId())
                    .knowledgeType(KnowledgeType.CHART.getCode())
                    .knowledgeId(viewId)
                    .feature(feature.getFeature())
                    .weight(feature.getWeight() > 0 ? feature.getWeight() : 1.0f)
                    .embedding(embedding)
                    .build();
            
            knowledgeEmbeddingRepository.saveKnowledgeEmbeddings(
                    Collections.singletonList(knowledgeEmbedding),
                    userIdentity.getTenantId()
            );
            
            log.info("图表特征保存成功, 特征ID: {}", knowledgeEmbedding.getId());
        } catch (Exception e) {
            log.error("保存图表特征失败", e);
            throw new RuntimeException("保存图表特征失败", e);
        }
    }

    @Override
    public void deleteChartFeature(String viewId, String featureId, OperationContext context) {
        log.info("删除图表特征, 图表ID: {}, 特征ID: {}", viewId, featureId);
        
        UserIdentity userIdentity = context.toUserIdentity();
        
        try {
            knowledgeEmbeddingRepository.deleteById(featureId, userIdentity.getTenantId());
            log.info("图表特征删除成功, 特征ID: {}", featureId);
        } catch (Exception e) {
            log.error("删除图表特征失败, 特征ID: {}", featureId, e);
            throw new RuntimeException("删除图表特征失败", e);
        }
    }

    /**
     * 批量删除图表特征
     */
    @Override
    public void batchDeleteChartFeatures(String viewId, List<String> featureIds, OperationContext context) {
        log.info("批量删除图表特征, 图表ID: {}, 特征数量: {}", viewId, featureIds.size());
        
        if (CollectionUtils.isEmpty(featureIds)) {
            log.warn("特征ID列表为空，跳过删除操作");
            return;
        }
        
        UserIdentity userIdentity = context.toUserIdentity();
        
        try {
            knowledgeEmbeddingRepository.batchDeleteByIds(featureIds, userIdentity.getTenantId());
            log.info("图表特征批量删除成功, 特征数量: {}", featureIds.size());
        } catch (Exception e) {
            log.error("批量删除图表特征失败, 特征数量: {}", featureIds.size(), e);
            throw new RuntimeException("批量删除图表特征失败", e);
        }
    }

    @Override
    public ChartRecallTestResult testChartFeature(String viewId, String testQuery, List<String> features, OperationContext context) {
        // 判断是全局测试还是针对特定图表的测试
        boolean isGlobalTest = (viewId == null || viewId.trim().isEmpty());
        
        if (isGlobalTest) {
            log.info("执行全局召回测试, 测试问题: {}", testQuery);
        } else {
            log.info("执行针对性召回测试, 目标图表ID: {}, 测试问题: {}, 特征列表: {}", viewId, testQuery, features);
        }
        
        // 转换为UserIdentity对象
        UserIdentity userIdentity = context.toUserIdentity();
        
        // 使用图表检索器进行测试
        try {
            // 使用新的方法获取知识嵌入列表，包含真正的命中特征
            List<KnowledgeEmbedding> matchedEmbeddings = chartRetriever.searchEmbeddingWithScores(testQuery, userIdentity, null, ChannelType.TENANT);
            
            // 创建ChartRecallTestResult对象
            ChartRecallTestResult testResult = new ChartRecallTestResult();
            testResult.setTestQuery(testQuery);
            testResult.setTargetViewId(viewId);
            
            // 获取召回的图表并设置到结果中
            List<RecalledChartInfo> recalledCharts = new ArrayList<>();
            
            if (!matchedEmbeddings.isEmpty()) {
                // 提取所有图表ID
                Set<String> chartIds = matchedEmbeddings.stream()
                        .map(KnowledgeEmbedding::getKnowledgeId)
                        .collect(Collectors.toSet());
                
                // 获取所有召回图表的详细信息
                List<ChartKnowledge> chartKnowledges = chartKnowledgeRepository.batchGetByViewIds(
                        new ArrayList<>(chartIds),
                        userIdentity,
                        ChannelType.TENANT
                );
                
                // 创建图表ID到图表对象的映射
                Map<String, ChartKnowledge> chartMap = chartKnowledges.stream()
                        .collect(Collectors.toMap(ChartKnowledge::getViewId, chart -> chart));
                
                // 构建RecalledChartInfo列表
                for (KnowledgeEmbedding embedding : matchedEmbeddings) {
                    ChartKnowledge chart = chartMap.get(embedding.getKnowledgeId());
                    if (chart != null) {
                        RecalledChartInfo recalledChart = new RecalledChartInfo();
                        recalledChart.setViewId(chart.getViewId());
                        recalledChart.setViewName(chart.getViewName());
                        recalledChart.setChartType(chart.getChartType());
                        recalledChart.setSpec(chart.getSpec());
                        recalledChart.setScore(embedding.getVectorScore());
                        recalledChart.setSchemaId(chart.getSchemaId());
                        recalledChart.setTenantId(chart.getTenantId());
                        // 使用真正命中的特征和权重
                        recalledChart.setMatchedFeature(embedding.getFeature());
                        recalledChart.setFeatureWeight((double) embedding.getWeight());
                        recalledCharts.add(recalledChart);
                    }
                }
            }
            
            testResult.setRecalledCharts(recalledCharts);
            
            if (isGlobalTest) {
                // 全局测试：不检查特定目标，总是返回成功
                testResult.setMatched(true);
                testResult.setScore(recalledCharts.isEmpty() ? 0.0 : recalledCharts.get(0).getScore());
                log.info("全局召回测试完成, 召回图表数量: {}", recalledCharts.size());
            } else {
                // 针对性测试：检查是否匹配目标图表
                boolean matched = matchedEmbeddings.stream()
                        .anyMatch(embedding -> viewId.equals(embedding.getKnowledgeId()));
                testResult.setMatched(matched);
                
                // 设置匹配分数
                double score = matchedEmbeddings.stream()
                        .filter(embedding -> viewId.equals(embedding.getKnowledgeId()))
                        .mapToDouble(KnowledgeEmbedding::getVectorScore)
                        .findFirst()
                        .orElse(0.0);
                testResult.setScore(score);
                
                log.info("针对性召回测试完成, 目标图表匹配: {}, 得分: {}", matched, score);
            }
            
            return testResult;
        } catch (Exception e) {
            log.error("测试图表特征时发生错误", e);
            
            // 创建失败的测试结果
            ChartRecallTestResult testResult = new ChartRecallTestResult();
            testResult.setMatched(false);
            testResult.setScore(0.0);
            testResult.setRecalledCharts(Collections.emptyList());
            testResult.setTestQuery(testQuery);
            testResult.setTargetViewId(viewId);
            
            return testResult;
        }
    }

    @Override
    public ChartLibraryResponse getChartLibraryData(OperationContext context) {
        log.info("获取图表库数据");
        
        UserIdentity userIdentity = context.toUserIdentity();
        String tenantId = userIdentity.getTenantId();
        
        try {
            // 获取图表列表
            List<ChartKnowledge> charts = chartKnowledgeRepository.findAllByTenant(tenantId);
            
            // 获取主题列表
            List<Schema> themes = Collections.emptyList();
            Map<String, String> schemaIdToNameMap = new HashMap<>();
            
            // 提取所有唯一的主题ID
            Set<String> schemaIdSet = charts.stream()
                    .map(ChartKnowledge::getSchemaId)
                    .filter(schemaId -> schemaId != null && !schemaId.trim().isEmpty())
                    .collect(Collectors.toSet());
            
            if (!schemaIdSet.isEmpty()) {
                // 批量查询所有主题信息
                QuerySchemasArg schemasArg = new QuerySchemasArg();
                schemasArg.setIncludeMeasures(false);
                schemasArg.setIncludeDimensions(false);
                schemasArg.setSchemaIds(new ArrayList<>(schemaIdSet));

                themes = schemaQueryService.querySchemas(
                        schemasArg,
                        UserInfoConvertUtil.createUserInfo(userIdentity)
                );
                
                // 创建主题ID到主题名称的映射
                schemaIdToNameMap = themes.stream()
                        .collect(Collectors.toMap(
                                Schema::getSchemaId,
                                schema -> schema.getSchemaName() != null ? schema.getSchemaName() : schema.getSchemaId(),
                                (existing, replacement) -> existing
                        ));
                
                log.info("获取到租户主题数量: {}", themes.size());
            }
            
            // 构建统计信息
            ChartLibraryResponse.StatInfo statInfo = buildStatInfo(charts, schemaIdToNameMap);
            
            return ChartLibraryResponse.builder()
                    .charts(charts)
                    .themes(themes)
                    .totalCharts(charts.size())
                    .totalThemes(themes.size())
                    .statInfo(statInfo)
                    .build();
                    
        } catch (Exception e) {
            log.error("获取图表库数据失败", e);
            return ChartLibraryResponse.builder()
                    .charts(Collections.emptyList())
                    .themes(Collections.emptyList())
                    .totalCharts(0)
                    .totalThemes(0)
                    .build();
        }
    }
    
    /**
     * 构建统计信息
     */
    private ChartLibraryResponse.StatInfo buildStatInfo(List<ChartKnowledge> charts, Map<String, String> schemaIdToNameMap) {
        // 按图表类型统计
        Map<String, Long> chartTypeCountMap = charts.stream()
                .collect(Collectors.groupingBy(
                        chart -> chart.getChartType() != null ? chart.getChartType() : "未知",
                        Collectors.counting()
                ));
        
        List<ChartLibraryResponse.CategoryCount> chartTypeStats = chartTypeCountMap.entrySet().stream()
                .map(entry -> ChartLibraryResponse.CategoryCount.builder()
                        .category(entry.getKey())
                        .categoryName(getChartTypeName(entry.getKey()))
                        .count(entry.getValue().intValue())
                        .build())
                .sorted((a, b) -> b.getCount().compareTo(a.getCount()))
                .collect(Collectors.toList());
        
        // 按主题统计
        Map<String, Long> themeCountMap = charts.stream()
                .filter(chart -> chart.getSchemaId() != null)
                .collect(Collectors.groupingBy(
                        ChartKnowledge::getSchemaId,
                        Collectors.counting()
                ));
        
        List<ChartLibraryResponse.CategoryCount> themeStats = themeCountMap.entrySet().stream()
                .map(entry -> ChartLibraryResponse.CategoryCount.builder()
                        .category(entry.getKey())
                        .categoryName(schemaIdToNameMap.getOrDefault(entry.getKey(), entry.getKey()))
                        .count(entry.getValue().intValue())
                        .build())
                .sorted((a, b) -> b.getCount().compareTo(a.getCount()))
                .collect(Collectors.toList());
        
        return ChartLibraryResponse.StatInfo.builder()
                .chartTypeStats(chartTypeStats)
                .themeStats(themeStats)
                .build();
    }
    
    /**
     * 获取图表类型中文名称
     */
    private String getChartTypeName(String chartType) {
        if (chartType == null || chartType.trim().isEmpty()) {
            return "未知";
        }
        
        // 使用EasyStatChartTypeEnum获取标准的图表类型名称
        for (EasyStatChartTypeEnum typeEnum : EasyStatChartTypeEnum.values()) {
            if (chartType.equalsIgnoreCase(typeEnum.getKey())) {
                return typeEnum.getDescription();
            }
        }
        
        // 如果没有找到匹配的枚举，返回原始值
        return chartType;
    }
}
