package com.fxiaoke.chatbi.accuracy.config;

import com.fxiaoke.common.http.spring.HttpSupportFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * HTTP客户端配置 - 使用OkHttpSupport
 */
@Configuration
public class HttpClientConfig {
    
    @Bean(name = "httpClientSupport")
    public HttpSupportFactoryBean httpClientSupport() {
        HttpSupportFactoryBean httpSupportFactoryBean = new HttpSupportFactoryBean();
        httpSupportFactoryBean.setConfigName("fs-bi-httputils");
        return httpSupportFactoryBean;
    }
} 