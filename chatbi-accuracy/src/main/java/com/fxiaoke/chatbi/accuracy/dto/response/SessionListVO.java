package com.fxiaoke.chatbi.accuracy.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Session列表展示VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SessionListVO {
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 租户ID
     */
    private String tenantId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 对话轮次数量
     */
    private Integer conversationCount;
    
    /**
     * 第一个问题（用于展示）
     */
    private String firstQuestion;
    
    /**
     * 最后一次对话时间
     */
    private Long lastQueryTime;
    
    /**
     * 是否已标注：0-未标注，1-已标注
     */
    private Integer isAnnotated;
    
    /**
     * Session整体评分（如果已标注）
     */
    private Integer sessionScore;
    
    /**
     * Session标签（如果已标注）
     */
    private List<String> sessionTags;
    
    /**
     * 创建时间
     */
    private Long createTime;
}
