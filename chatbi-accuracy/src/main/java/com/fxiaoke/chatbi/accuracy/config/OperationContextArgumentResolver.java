package com.fxiaoke.chatbi.accuracy.config;

import com.fxiaoke.chatbi.common.model.OperationContext;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import java.util.Arrays;

/**
 * OperationContext参数解析器
 * 用于在控制器方法中解析OperationContext参数
 */
@Component
public class OperationContextArgumentResolver implements HandlerMethodArgumentResolver {
    // 使用新的请求头名称
    private static final String HEADER_TARGET_TENANT_ID = "X-Admin-Target-Tenant-Id";
    private static final String HEADER_ADMIN_ID = "X-Admin-Id";
    private static final String HEADER_ADMIN_NAME = "X-Admin-Name";

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return parameter.getParameterType().equals(OperationContext.class);
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest, WebDataBinderFactory binderFactory) {
        // 从请求头中获取用户信息，只使用新的请求头格式
        String adminId = webRequest.getHeader(HEADER_ADMIN_ID);
        String targetTenantId = webRequest.getHeader(HEADER_TARGET_TENANT_ID);
        String adminName = webRequest.getHeader(HEADER_ADMIN_NAME);

        // 如果请求头中没有信息，则使用默认值
        if (adminId == null || adminId.isEmpty()) {
            adminId = "-10000";
        }
        if (targetTenantId == null || targetTenantId.isEmpty()) {
            targetTenantId = "-1";
        }
        if (adminName == null || adminName.isEmpty()) {
            adminName = "system";
        }

        // 创建OperationContext对象
        return OperationContext.builder().adminId(adminId).adminName(adminName).targetTenantId(targetTenantId).roles(Arrays.asList("ADMIN", "VIEWER")).build();
    }
} 