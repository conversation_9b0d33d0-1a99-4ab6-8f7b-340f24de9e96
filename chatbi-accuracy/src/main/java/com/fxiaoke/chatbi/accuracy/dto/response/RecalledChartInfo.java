package com.fxiaoke.chatbi.accuracy.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 召回图表信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecalledChartInfo {
    /**
     * 图表ID
     */
    private String viewId;
    
    /**
     * 图表名称
     */
    private String viewName;
    
    /**
     * 图表类型
     */
    private String chartType;
    
    /**
     * 图表详细定义(JSON格式)
     */
    private String spec;
    
    /**
     * 得分
     */
    private double score;
    
    /**
     * 统计主题ID
     */
    private String schemaId;
    
    /**
     * 租户ID
     */
    private String tenantId;
    
    /**
     * 命中的特征内容
     */
    private String matchedFeature;
    
    /**
     * 特征权重
     */
    private Double featureWeight;
}
