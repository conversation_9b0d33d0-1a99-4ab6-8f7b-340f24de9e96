package com.fxiaoke.chatbi.accuracy.dto.request;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Session级标注保存请求
 */
@Data
public class SessionAnnotationSaveRequest {
    
    /**
     * 关联的会话ID
     */
    @NotBlank(message = "会话ID不能为空")
    private String sessionId;
    
    /**
     * 租户ID
     */
    @NotBlank(message = "租户ID不能为空")
    private String tenantId;
    
    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;
    
    /**
     * Session整体质量评分：1-5分
     */
    @NotNull(message = "Session评分不能为空")
    @Min(value = 1, message = "评分不能小于1")
    @Max(value = 5, message = "评分不能大于5")
    private Integer sessionScore;
    
    /**
     * Session标签列表
     */
    private List<String> sessionTags;
    
    /**
     * Session的综合文字评价
     */
    private String sessionComments;
}
