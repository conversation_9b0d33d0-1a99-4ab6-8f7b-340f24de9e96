package com.fxiaoke.chatbi.accuracy.service.impl;

import com.fxiaoke.chatbi.accuracy.dto.response.AttachmentVO;
import com.fxiaoke.chatbi.accuracy.service.AnnotationAttachmentService;
import com.fxiaoke.chatbi.accuracy.util.FileUploadUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 标注附件服务实现类
 * 基于本地文件系统的简单实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AnnotationAttachmentServiceImpl implements AnnotationAttachmentService {

    @Value("${chatbi.annotation.upload.path:/tmp/chatbi-uploads}")
    private String uploadPath;

    @Value("${chatbi.annotation.upload.max-size:10485760}") // 10MB
    private long maxFileSize;

    @Value("${chatbi.annotation.upload.allowed-types:jpg,jpeg,png,gif,bmp}")
    private String allowedTypes;

    // 内存存储附件信息（生产环境应该使用数据库）
    private final Map<String, AttachmentVO> attachmentStorage = new ConcurrentHashMap<>();
    private final Map<String, List<String>> annotationAttachments = new ConcurrentHashMap<>();

    @Override
    public AttachmentVO uploadAttachment(MultipartFile file, String description, String annotationId) {
        try {
            log.info("开始上传附件: filename={}, size={}, annotationId={}", 
                    file.getOriginalFilename(), file.getSize(), annotationId);

            // 验证文件
            validateFile(file);

            // 确保上传目录存在
            ensureUploadDirectory();

            // 生成文件信息
            String fileId = UUID.randomUUID().toString();
            String originalFilename = file.getOriginalFilename();
            String fileExtension = getFileExtension(originalFilename);
            String savedFilename = fileId + "." + fileExtension;
            String relativePath = "attachments/" + savedFilename;
            Path filePath = Paths.get(uploadPath, relativePath);

            // 保存文件
            Files.createDirectories(filePath.getParent());
            Files.copy(file.getInputStream(), filePath);

            // 计算文件哈希
            String fileHash = calculateFileHash(file.getBytes());

            // 创建附件信息
            AttachmentVO attachment = AttachmentVO.builder()
                    .id(fileId)
                    .filename(originalFilename)
                    .url("/api/chatAnnotation/attachment/download/" + fileId)
                    .type(getFileType(fileExtension))
                    .description(description)
                    .size(file.getSize())
                    .hash(fileHash)
                    .uploadTime(System.currentTimeMillis())
                    .build();

            // 存储附件信息
            attachmentStorage.put(fileId, attachment);
            
            // 关联到标注
            if (StringUtils.hasText(annotationId)) {
                annotationAttachments.computeIfAbsent(annotationId, k -> new ArrayList<>()).add(fileId);
            }

            log.info("附件上传成功: fileId={}, filename={}, size={}", 
                    fileId, originalFilename, file.getSize());
            
            return attachment;

        } catch (Exception e) {
            log.error("附件上传失败: filename={}, annotationId={}", 
                    file.getOriginalFilename(), annotationId, e);
            throw new RuntimeException("附件上传失败: " + e.getMessage());
        }
    }

    @Override
    public boolean deleteAttachment(String attachmentId) {
        try {
            log.info("开始删除附件: attachmentId={}", attachmentId);

            AttachmentVO attachment = attachmentStorage.get(attachmentId);
            if (attachment == null) {
                log.warn("附件不存在: attachmentId={}", attachmentId);
                return false;
            }

            // 删除文件
            String filename = attachment.getFilename();
            String fileExtension = getFileExtension(filename);
            String savedFilename = attachmentId + "." + fileExtension;
            Path filePath = Paths.get(uploadPath, "attachments", savedFilename);
            
            try {
                Files.deleteIfExists(filePath);
            } catch (IOException e) {
                log.warn("删除文件失败: {}", filePath, e);
            }

            // 删除存储信息
            attachmentStorage.remove(attachmentId);
            
            // 从标注关联中移除
            annotationAttachments.values().forEach(list -> list.remove(attachmentId));

            log.info("附件删除成功: attachmentId={}", attachmentId);
            return true;

        } catch (Exception e) {
            log.error("删除附件失败: attachmentId={}", attachmentId, e);
            return false;
        }
    }

    @Override
    public List<AttachmentVO> getAttachmentsByAnnotationId(String annotationId) {
        try {
            List<String> attachmentIds = annotationAttachments.get(annotationId);
            if (attachmentIds == null || attachmentIds.isEmpty()) {
                return Collections.emptyList();
            }

            return attachmentIds.stream()
                    .map(attachmentStorage::get)
                    .filter(Objects::nonNull)
                    .sorted(Comparator.comparing(AttachmentVO::getUploadTime).reversed())
                    .collect(ArrayList::new, (list, item) -> list.add(item), ArrayList::addAll);

        } catch (Exception e) {
            log.error("查询标注附件失败: annotationId={}", annotationId, e);
            return Collections.emptyList();
        }
    }

    @Override
    public AttachmentVO getAttachmentById(String attachmentId) {
        return attachmentStorage.get(attachmentId);
    }

    // ===== 私有辅助方法 =====

    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        if (file.getSize() > maxFileSize) {
            throw new IllegalArgumentException("文件大小超过限制: " + (maxFileSize / 1024 / 1024) + "MB");
        }

        String filename = file.getOriginalFilename();
        if (!StringUtils.hasText(filename)) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        String fileExtension = getFileExtension(filename).toLowerCase();
        String[] allowedTypesArray = allowedTypes.toLowerCase().split(",");
        boolean isAllowed = Arrays.asList(allowedTypesArray).contains(fileExtension);
        
        if (!isAllowed) {
            throw new IllegalArgumentException("不支持的文件类型: " + fileExtension + 
                    "，支持的类型: " + allowedTypes);
        }
    }

    private void ensureUploadDirectory() throws IOException {
        Path uploadDir = Paths.get(uploadPath);
        if (!Files.exists(uploadDir)) {
            Files.createDirectories(uploadDir);
        }
        
        Path attachmentDir = Paths.get(uploadPath, "attachments");
        if (!Files.exists(attachmentDir)) {
            Files.createDirectories(attachmentDir);
        }
    }

    private String getFileExtension(String filename) {
        if (!StringUtils.hasText(filename)) {
            return "";
        }
        
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filename.length() - 1) {
            return filename.substring(lastDotIndex + 1);
        }
        
        return "";
    }

    private String getFileType(String extension) {
        if (!StringUtils.hasText(extension)) {
            return "unknown";
        }
        
        String ext = extension.toLowerCase();
        if (Arrays.asList("jpg", "jpeg", "png", "gif", "bmp").contains(ext)) {
            return "image";
        }
        
        return "file";
    }

    private String calculateFileHash(byte[] fileBytes) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(fileBytes);
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            log.warn("计算文件哈希失败", e);
            return "";
        }
    }
}
