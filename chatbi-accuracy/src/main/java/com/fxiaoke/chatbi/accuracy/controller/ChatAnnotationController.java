package com.fxiaoke.chatbi.accuracy.controller;

import com.fxiaoke.chatbi.accuracy.dto.request.*;
import com.fxiaoke.chatbi.accuracy.dto.response.*;
import com.fxiaoke.chatbi.accuracy.service.ChatAnnotationService;
import com.fxiaoke.chatbi.common.dto.ApiResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 对话标注控制器（重构后）
 * 支持Session级别的多轮对话标注
 */
@Slf4j
@RestController
@RequestMapping("/api/chatAnnotation")
@RequiredArgsConstructor
public class ChatAnnotationController {

    private final ChatAnnotationService chatAnnotationService;

    /**
     * 获取Session列表
     */
    @PostMapping("/getSessionList")
    public ResponseEntity<ApiResult<Page<SessionListVO>>> getSessionList(
            @Valid @RequestBody SessionListRequest request) {
        try {
            log.info("开始获取Session列表: tenantId={}, page={}, size={}", 
                    request.getTenantId(), request.getPage(), request.getSize());

            Page<SessionListVO> page = chatAnnotationService.getSessionList(request);

            log.info("获取Session列表成功: 返回{}条记录, 总计{}条", 
                    page.getContent().size(), page.getTotalElements());
            
            return ResponseEntity.ok(ApiResult.success(page));
        } catch (Exception e) {
            log.error("获取Session列表失败: request={}", request, e);
            return ResponseEntity.ok(ApiResult.error("获取Session列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取Session详情
     */
    @PostMapping("/getSessionDetail")
    public ResponseEntity<ApiResult<SessionDetailVO>> getSessionDetail(
            @Valid @RequestBody SessionDetailRequest request) {
        try {
            log.info("开始获取Session详情: sessionId={}, tenantId={}", 
                    request.getSessionId(), request.getTenantId());

            SessionDetailVO sessionDetail = chatAnnotationService.getSessionDetail(request);
            
            if (sessionDetail == null) {
                log.warn("Session不存在: sessionId={}", request.getSessionId());
                return ResponseEntity.ok(ApiResult.error("Session不存在"));
            }

            log.info("获取Session详情成功: sessionId={}, conversationCount={}", 
                    request.getSessionId(), sessionDetail.getConversationCount());
            
            return ResponseEntity.ok(ApiResult.success(sessionDetail));
        } catch (Exception e) {
            log.error("获取Session详情失败: sessionId={}, tenantId={}", 
                    request.getSessionId(), request.getTenantId(), e);
            return ResponseEntity.ok(ApiResult.error("获取Session详情失败: " + e.getMessage()));
        }
    }

    /**
     * 保存问题级标注
     */
    @PostMapping("/saveQueryAnnotation")
    public ResponseEntity<ApiResult<Boolean>> saveQueryAnnotation(
            @Valid @RequestBody QueryAnnotationSaveRequest request) {
        try {
            log.info("开始保存问题级标注: requestId={}, sessionId={}", 
                    request.getRequestId(), request.getSessionId());

            boolean success = chatAnnotationService.saveQueryAnnotation(request);

            log.info("保存问题级标注完成: requestId={}, success={}", 
                    request.getRequestId(), success);
            
            return ResponseEntity.ok(ApiResult.success(success));
        } catch (Exception e) {
            log.error("保存问题级标注失败: requestId={}", request.getRequestId(), e);
            return ResponseEntity.ok(ApiResult.error("保存问题级标注失败: " + e.getMessage()));
        }
    }

    /**
     * 保存Session级标注
     */
    @PostMapping("/saveSessionAnnotation")
    public ResponseEntity<ApiResult<Boolean>> saveSessionAnnotation(
            @Valid @RequestBody SessionAnnotationSaveRequest request) {
        try {
            log.info("开始保存Session级标注: sessionId={}, tenantId={}", 
                    request.getSessionId(), request.getTenantId());

            boolean success = chatAnnotationService.saveSessionAnnotation(request);

            log.info("保存Session级标注完成: sessionId={}, success={}", 
                    request.getSessionId(), success);
            
            return ResponseEntity.ok(ApiResult.success(success));
        } catch (Exception e) {
            log.error("保存Session级标注失败: sessionId={}", request.getSessionId(), e);
            return ResponseEntity.ok(ApiResult.error("保存Session级标注失败: " + e.getMessage()));
        }
    }

    /**
     * 获取标注统计信息
     */
    @GetMapping("/getStatistics")
    public ResponseEntity<ApiResult<AnnotationStatisticsVO>> getStatistics() {
        try {
            log.info("开始获取标注统计信息");

            AnnotationStatisticsVO statistics = chatAnnotationService.getAnnotationStatistics();

            log.info("获取标注统计信息成功: total={}, annotated={}, pending={}", 
                    statistics.getTotal(), statistics.getAnnotated(), statistics.getPending());
            
            return ResponseEntity.ok(ApiResult.success(statistics));
        } catch (Exception e) {
            log.error("获取标注统计信息失败", e);
            return ResponseEntity.ok(ApiResult.error("获取标注统计信息失败: " + e.getMessage()));
        }
    }

    /**
     * 删除问题级标注
     */
    @PostMapping("/deleteQueryAnnotation")
    public ResponseEntity<ApiResult<Boolean>> deleteQueryAnnotation(
            @RequestParam String annotationId) {
        try {
            log.info("开始删除问题级标注: annotationId={}", annotationId);

            boolean success = chatAnnotationService.deleteQueryAnnotation(annotationId);

            log.info("删除问题级标注完成: annotationId={}, success={}", annotationId, success);
            
            return ResponseEntity.ok(ApiResult.success(success));
        } catch (Exception e) {
            log.error("删除问题级标注失败: annotationId={}", annotationId, e);
            return ResponseEntity.ok(ApiResult.error("删除问题级标注失败: " + e.getMessage()));
        }
    }

    /**
     * 删除Session级标注
     */
    @PostMapping("/deleteSessionAnnotation")
    public ResponseEntity<ApiResult<Boolean>> deleteSessionAnnotation(
            @RequestParam String annotationId) {
        try {
            log.info("开始删除Session级标注: annotationId={}", annotationId);

            boolean success = chatAnnotationService.deleteSessionAnnotation(annotationId);

            log.info("删除Session级标注完成: annotationId={}, success={}", annotationId, success);
            
            return ResponseEntity.ok(ApiResult.success(success));
        } catch (Exception e) {
            log.error("删除Session级标注失败: annotationId={}", annotationId, e);
            return ResponseEntity.ok(ApiResult.error("删除Session级标注失败: " + e.getMessage()));
        }
    }
}
