package com.fxiaoke.chatbi.accuracy.service.impl;

import com.fxiaoke.chatbi.accuracy.dto.request.*;
import com.fxiaoke.chatbi.accuracy.dto.response.*;
import com.fxiaoke.chatbi.accuracy.service.ChatAnnotationService;
import com.fxiaoke.chatbi.integration.model.ch.accuracy.*;
import com.fxiaoke.chatbi.integration.repository.ch.accuracy.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 对话标注服务实现类（重构后）
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChatAnnotationServiceImpl implements ChatAnnotationService {

    private final QueryLogRepository queryLogRepository;
    private final QueryAnnotationRepository queryAnnotationRepository;
    private final SessionAnnotationRepository sessionAnnotationRepository;
    private final ObjectMapper objectMapper;

    @Override
    public Page<SessionListVO> getSessionList(SessionListRequest request) {
        try {
            log.info("开始查询Session列表: request={}", request);
            
            // 计算偏移量
            int offset = (request.getPage() - 1) * request.getSize();
            
            // 查询Session列表
            List<com.fxiaoke.chatbi.integration.model.ch.accuracy.SessionSummary> sessions = queryLogRepository.findSessionsByTenantId(
                    request.getTenantId(), offset, request.getSize());

            // 统计总数
            long total = queryLogRepository.countSessionsByTenantId(request.getTenantId());

            // 转换为VO
            List<SessionListVO> sessionVOs = sessions.stream()
                    .map(this::convertToSessionListVO)
                    .collect(Collectors.toList());
            
            log.info("查询Session列表成功: total={}, current={}", total, sessionVOs.size());
            return new PageImpl<>(sessionVOs, PageRequest.of(request.getPage() - 1, request.getSize()), total);
            
        } catch (Exception e) {
            log.error("查询Session列表失败: request={}", request, e);
            return new PageImpl<>(Collections.emptyList(), 
                    PageRequest.of(request.getPage() - 1, request.getSize()), 0);
        }
    }

    @Override
    public SessionDetailVO getSessionDetail(SessionDetailRequest request) {
        try {
            log.info("开始查询Session详情: sessionId={}, tenantId={}", 
                    request.getSessionId(), request.getTenantId());
            
            // 查询Session下的所有问答记录
            List<QueryLog> queryLogs = queryLogRepository.findBySessionIdAndTenantId(
                    request.getSessionId(), request.getTenantId());
            
            if (queryLogs.isEmpty()) {
                log.warn("Session不存在或无对话记录: sessionId={}", request.getSessionId());
                return null;
            }
            
            // 查询Session级标注
            Optional<SessionAnnotation> sessionAnnotation = sessionAnnotationRepository
                    .findBySessionIdAndTenantId(request.getSessionId(), request.getTenantId());
            
            // 查询所有问题级标注
            List<QueryAnnotation> queryAnnotations = queryAnnotationRepository
                    .findBySessionIdAndTenantId(request.getSessionId(), request.getTenantId());
            Map<String, QueryAnnotation> annotationMap = queryAnnotations.stream()
                    .collect(Collectors.toMap(QueryAnnotation::getRequestId, a -> a));
            
            // 构建SessionDetailVO
            SessionDetailVO sessionDetail = SessionDetailVO.builder()
                    .sessionId(request.getSessionId())
                    .tenantId(request.getTenantId())
                    .userId(queryLogs.get(0).getUserId())
                    .conversationCount(queryLogs.size())
                    .currentAnnotatingRound(1)
                    .conversations(buildConversationList(queryLogs, annotationMap))
                    .sessionAnnotation(sessionAnnotation.map(this::convertToSessionAnnotationVO).orElse(null))
                    .createTime(queryLogs.stream().mapToLong(QueryLog::getCreateTime).min().orElse(0L))
                    .build();
            
            log.info("查询Session详情成功: sessionId={}, conversationCount={}", 
                    request.getSessionId(), sessionDetail.getConversationCount());
            return sessionDetail;
            
        } catch (Exception e) {
            log.error("查询Session详情失败: sessionId={}, tenantId={}", 
                    request.getSessionId(), request.getTenantId(), e);
            return null;
        }
    }

    @Override
    public boolean saveQueryAnnotation(QueryAnnotationSaveRequest request) {
        try {
            log.info("开始保存问题级标注: requestId={}, sessionId={}", 
                    request.getRequestId(), request.getSessionId());
            
            // 检查问答记录是否存在
            Optional<QueryLog> queryLogOpt = queryLogRepository.findById(request.getRequestId());
            if (!queryLogOpt.isPresent()) {
                log.warn("问答记录不存在: requestId={}", request.getRequestId());
                return false;
            }
            
            // 查找现有标注
            Optional<QueryAnnotation> existingAnnotation = queryAnnotationRepository
                    .findByRequestId(request.getRequestId());
            
            QueryAnnotation annotation;
            if (existingAnnotation.isPresent()) {
                // 更新现有标注
                annotation = existingAnnotation.get();
                updateQueryAnnotationFromRequest(annotation, request);
                queryAnnotationRepository.update(annotation);
            } else {
                // 创建新标注
                annotation = createQueryAnnotationFromRequest(request, queryLogOpt.get());
                queryAnnotationRepository.save(annotation);
            }
            
            // 更新问答记录的标注状态
            QueryLog queryLog = queryLogOpt.get();
            queryLog.setIsAnnotated(1);
            queryLogRepository.update(queryLog);
            
            log.info("保存问题级标注成功: requestId={}, annotationId={}", 
                    request.getRequestId(), annotation.getId());
            return true;
            
        } catch (Exception e) {
            log.error("保存问题级标注失败: requestId={}", request.getRequestId(), e);
            return false;
        }
    }

    @Override
    public boolean saveSessionAnnotation(SessionAnnotationSaveRequest request) {
        try {
            log.info("开始保存Session级标注: sessionId={}, tenantId={}", 
                    request.getSessionId(), request.getTenantId());
            
            // 查找现有Session标注
            Optional<SessionAnnotation> existingAnnotation = sessionAnnotationRepository
                    .findBySessionIdAndTenantId(request.getSessionId(), request.getTenantId());
            
            SessionAnnotation annotation;
            if (existingAnnotation.isPresent()) {
                // 更新现有标注
                annotation = existingAnnotation.get();
                updateSessionAnnotationFromRequest(annotation, request);
                sessionAnnotationRepository.update(annotation);
            } else {
                // 创建新标注
                annotation = createSessionAnnotationFromRequest(request);
                sessionAnnotationRepository.save(annotation);
            }
            
            log.info("保存Session级标注成功: sessionId={}, annotationId={}", 
                    request.getSessionId(), annotation.getId());
            return true;
            
        } catch (Exception e) {
            log.error("保存Session级标注失败: sessionId={}", request.getSessionId(), e);
            return false;
        }
    }

    @Override
    public AnnotationStatisticsVO getAnnotationStatistics() {
        try {
            long total = queryLogRepository.countAll();
            long annotated = queryLogRepository.countAnnotated();
            return AnnotationStatisticsVO.builder()
                    .total(total)
                    .annotated(annotated)
                    .pending(total - annotated)
                    .build();
        } catch (Exception e) {
            log.error("获取标注统计信息失败", e);
            return AnnotationStatisticsVO.builder().total(0L).annotated(0L).pending(0L).build();
        }
    }

    @Override
    public boolean deleteQueryAnnotation(String annotationId) {
        try {
            log.info("开始删除问题级标注: annotationId={}", annotationId);
            boolean success = queryAnnotationRepository.deleteById(annotationId);
            log.info("删除问题级标注完成: annotationId={}, success={}", annotationId, success);
            return success;
        } catch (Exception e) {
            log.error("删除问题级标注失败: annotationId={}", annotationId, e);
            return false;
        }
    }

    @Override
    public boolean deleteSessionAnnotation(String annotationId) {
        try {
            log.info("开始删除Session级标注: annotationId={}", annotationId);
            boolean success = sessionAnnotationRepository.deleteById(annotationId);
            log.info("删除Session级标注完成: annotationId={}, success={}", annotationId, success);
            return success;
        } catch (Exception e) {
            log.error("删除Session级标注失败: annotationId={}", annotationId, e);
            return false;
        }
    }

    // ===== 私有辅助方法 =====

    private List<ConversationVO> buildConversationList(List<QueryLog> queryLogs, Map<String, QueryAnnotation> annotationMap) {
        List<ConversationVO> conversations = new ArrayList<>();
        List<QueryLog> sortedLogs = queryLogs.stream()
                .sorted(Comparator.comparing(QueryLog::getQueryTime))
                .collect(Collectors.toList());

        for (int i = 0; i < sortedLogs.size(); i++) {
            QueryLog queryLog = sortedLogs.get(i);
            QueryAnnotation annotation = annotationMap.get(queryLog.getId());
            conversations.add(convertToConversationVO(queryLog, i + 1, annotation));
        }

        return conversations;
    }

    private SessionListVO convertToSessionListVO(com.fxiaoke.chatbi.integration.model.ch.accuracy.SessionSummary sessionSummary) {
        try {
            log.debug("转换Session数据: {}", sessionSummary);

            SessionListVO vo = SessionListVO.builder()
                    .sessionId(sessionSummary.getSessionId())
                    .tenantId(sessionSummary.getTenantId())
                    .userId(sessionSummary.getUserId())
                    .conversationCount(sessionSummary.getConversationCount())
                    .firstQuestion(sessionSummary.getFirstQuestion())
                    .lastQueryTime(sessionSummary.getLastQueryTime())
                    .isAnnotated(sessionSummary.getIsAnnotated())
                    .sessionScore(null) // SessionSummary中没有sessionScore，需要单独查询
                    .createTime(sessionSummary.getCreateTime())
                    .build();

            log.debug("转换后的SessionListVO: {}", vo);
            return vo;
        } catch (Exception e) {
            log.error("转换SessionListVO失败: sessionSummary={}", sessionSummary, e);
            throw new RuntimeException("转换SessionListVO失败", e);
        }
    }



    private ConversationVO convertToConversationVO(QueryLog queryLog, int round, QueryAnnotation annotation) {
        return ConversationVO.builder()
                .conversationId(queryLog.getId())
                .round(round)
                .userQuestion(queryLog.getQuery())
                .aiResponse(parseAIResponse(queryLog))
                .annotation(annotation != null ? convertToQueryAnnotationVO(annotation) : null)
                .attachments(annotation != null ? parseAttachments(annotation.getAttachments()) : Collections.emptyList())
                .queryTime(queryLog.getQueryTime())
                .responseTime(queryLog.getResponseTime())
                .build();
    }

    private AIResponseVO parseAIResponse(QueryLog queryLog) {
        try {
            String responseComponents = queryLog.getResponseComponents();
            if (!StringUtils.hasText(responseComponents)) {
                return AIResponseVO.builder().build();
            }

            Map<String, Object> components = objectMapper.readValue(responseComponents, Map.class);

            // actionLogs直接从QueryLog的actionLogs字段获取
            String actionLogs = queryLog.getActionLogs();

            return AIResponseVO.builder()
                    .reasoningResponse(extractResponseContent(components.get("reasoningResponse")))
                    .chartDataResponse(objectMapper.writeValueAsString(components.get("chartDataResponse")))
                    .insightResponse(extractResponseContent(components.get("insightResponse")))
                    .followUpResponse(objectMapper.writeValueAsString(components.get("followUpResponse")))
                    .actionLogs(actionLogs)
                    .build();
        } catch (Exception e) {
            log.warn("解析AI回答组件失败: {}", e.getMessage());
            return AIResponseVO.builder().build();
        }
    }

    /**
     * 从Response对象中提取内容
     */
    private String extractResponseContent(Object responseObj) {
        if (responseObj == null) {
            return null;
        }

        try {
            if (responseObj instanceof Map) {
                Map<String, Object> responseMap = (Map<String, Object>) responseObj;
                // 尝试获取content字段，如果没有则返回整个对象的JSON
                Object content = responseMap.get("content");
                if (content != null) {
                    return content.toString();
                }
                return objectMapper.writeValueAsString(responseObj);
            }
            return responseObj.toString();
        } catch (Exception e) {
            log.warn("提取Response内容失败: {}", e.getMessage());
            return responseObj.toString();
        }
    }

    private QueryAnnotationVO convertToQueryAnnotationVO(QueryAnnotation annotation) {
        return QueryAnnotationVO.builder()
                .id(annotation.getId())
                .componentAnnotations(parseComponentAnnotations(annotation.getComponentAnnotations()))
                .roundScore(annotation.getRoundScore())
                .roundTags(annotation.getRoundTags())
                .roundComments(annotation.getRoundComments())
                .createdBy(annotation.getCreatedBy())
                .createTime(annotation.getCreateTime())
                .lastModifiedTime(annotation.getLastModifiedTime())
                .build();
    }

    private ComponentAnnotationVO parseComponentAnnotations(String componentAnnotationsJson) {
        try {
            if (!StringUtils.hasText(componentAnnotationsJson)) {
                return ComponentAnnotationVO.builder().build();
            }

            Map<String, Object> annotations = objectMapper.readValue(componentAnnotationsJson, Map.class);
            return ComponentAnnotationVO.builder()
                    .reasoning(parseComponentItem((Map<String, Object>) annotations.get("reasoning")))
                    .chartData(parseComponentItem((Map<String, Object>) annotations.get("chartData")))
                    .insight(parseComponentItem((Map<String, Object>) annotations.get("insight")))
                    .followUp(parseComponentItem((Map<String, Object>) annotations.get("followUp")))
                    .build();
        } catch (Exception e) {
            log.warn("解析组件标注失败: {}", e.getMessage());
            return ComponentAnnotationVO.builder().build();
        }
    }

    private ComponentAnnotationVO.ComponentItemVO parseComponentItem(Map<String, Object> itemMap) {
        if (itemMap == null) {
            return ComponentAnnotationVO.ComponentItemVO.builder().build();
        }

        return ComponentAnnotationVO.ComponentItemVO.builder()
                .score((Integer) itemMap.get("score"))
                .tags((List<String>) itemMap.get("tags"))
                .comments((String) itemMap.get("comments"))
                .build();
    }

    private List<AttachmentVO> parseAttachments(String attachmentsJson) {
        try {
            if (!StringUtils.hasText(attachmentsJson)) {
                return Collections.emptyList();
            }

            List<Map<String, Object>> attachmentMaps = objectMapper.readValue(attachmentsJson, List.class);
            return attachmentMaps.stream()
                    .map(this::convertToAttachmentVO)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.warn("解析附件信息失败: {}", e.getMessage());
            return Collections.emptyList();
        }
    }

    private AttachmentVO convertToAttachmentVO(Map<String, Object> attachmentMap) {
        return AttachmentVO.builder()
                .id((String) attachmentMap.get("id"))
                .filename((String) attachmentMap.get("filename"))
                .url((String) attachmentMap.get("url"))
                .type((String) attachmentMap.get("type"))
                .description((String) attachmentMap.get("description"))
                .size(((Number) attachmentMap.get("size")).longValue())
                .hash((String) attachmentMap.get("hash"))
                .uploadTime(((Number) attachmentMap.get("uploadTime")).longValue())
                .build();
    }

    private SessionAnnotationVO convertToSessionAnnotationVO(SessionAnnotation annotation) {
        return SessionAnnotationVO.builder()
                .id(annotation.getId())
                .sessionScore(annotation.getSessionScore())
                .sessionTags(annotation.getSessionTags())
                .sessionComments(annotation.getSessionComments())
                .createdBy(annotation.getCreatedBy())
                .createTime(annotation.getCreateTime())
                .lastModifiedBy(annotation.getLastModifiedBy())
                .lastModifiedTime(annotation.getLastModifiedTime())
                .build();
    }

    private QueryAnnotation createQueryAnnotationFromRequest(QueryAnnotationSaveRequest request, QueryLog queryLog) {
        return QueryAnnotation.builder()
                .id(UUID.randomUUID().toString())
                .requestId(request.getRequestId())
                .sessionId(request.getSessionId())
                .tenantId(request.getTenantId())
                .componentAnnotations(request.getComponentAnnotations())
                .roundScore(request.getRoundScore())
                .roundTags(request.getRoundTags())
                .roundComments(request.getRoundComments())
                .attachments(request.getAttachments())
                .createdBy(queryLog.getUserId())
                .createTime(System.currentTimeMillis())
                .lastModifiedTime(System.currentTimeMillis())
                .isDeleted(0)
                .build();
    }

    private void updateQueryAnnotationFromRequest(QueryAnnotation annotation, QueryAnnotationSaveRequest request) {
        annotation.setComponentAnnotations(request.getComponentAnnotations());
        annotation.setRoundScore(request.getRoundScore());
        annotation.setRoundTags(request.getRoundTags());
        annotation.setRoundComments(request.getRoundComments());
        annotation.setAttachments(request.getAttachments());
        annotation.setLastModifiedTime(System.currentTimeMillis());
    }

    private SessionAnnotation createSessionAnnotationFromRequest(SessionAnnotationSaveRequest request) {
        return SessionAnnotation.builder()
                .id(UUID.randomUUID().toString())
                .sessionId(request.getSessionId())
                .tenantId(request.getTenantId())
                .userId(request.getUserId())
                .sessionScore(request.getSessionScore())
                .sessionTags(request.getSessionTags())
                .sessionComments(request.getSessionComments())
                .createdBy(request.getUserId())
                .createTime(System.currentTimeMillis())
                .lastModifiedBy(request.getUserId())
                .lastModifiedTime(System.currentTimeMillis())
                .isDeleted(0)
                .build();
    }

    private void updateSessionAnnotationFromRequest(SessionAnnotation annotation, SessionAnnotationSaveRequest request) {
        annotation.setSessionScore(request.getSessionScore());
        annotation.setSessionTags(request.getSessionTags());
        annotation.setSessionComments(request.getSessionComments());
        annotation.setLastModifiedBy(request.getUserId());
        annotation.setLastModifiedTime(System.currentTimeMillis());
    }
}
