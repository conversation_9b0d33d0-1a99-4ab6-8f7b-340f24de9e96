package com.fxiaoke.chatbi.accuracy.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Session详情展示VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SessionDetailVO {
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 租户ID
     */
    private String tenantId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 对话轮次数量
     */
    private Integer conversationCount;
    
    /**
     * 当前标注的轮次
     */
    private Integer currentAnnotatingRound;
    
    /**
     * 对话列表
     */
    private List<ConversationVO> conversations;
    
    /**
     * Session整体标注信息
     */
    private SessionAnnotationVO sessionAnnotation;
    
    /**
     * 创建时间
     */
    private Long createTime;
}
