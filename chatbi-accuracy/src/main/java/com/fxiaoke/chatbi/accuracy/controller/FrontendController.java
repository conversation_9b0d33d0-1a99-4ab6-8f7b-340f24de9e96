package com.fxiaoke.chatbi.accuracy.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.ui.Model;

/**
 * 前端页面控制器
 */
@Controller
public class FrontendController {

    /**
     * 首页
     */
    @GetMapping("/")
    public String index(Model model) {
        model.addAttribute("title", "ChatBI 评估测试平台");
        return "index";
    }

    /**
     * 图表知识管理页面
     */
    @GetMapping("/chart")
    public String chartKnowledge(Model model) {
        model.addAttribute("title", "图表知识管理 - ChatBI 评估测试平台");
        return "chart-knowledge";
    }

    /**
     * 图表库页面
     */
    @GetMapping("/chart-library")
    public String chartLibrary(Model model) {
        model.addAttribute("title", "图表库 - ChatBI 评估测试平台");
        return "chart-library";
    }

    /**
     * 测试执行页面
     */
    @GetMapping("/test")
    public String testExecution(Model model) {
        model.addAttribute("title", "测试执行 - ChatBI 评估测试平台");
        return "test";
    }

    /**
     * 问答记录标注页面
     */
    @GetMapping("/annotation")
    public String annotation(Model model) {
        model.addAttribute("title", "问答记录标注 - ChatBI 评估测试平台");
        return "annotation";
    }

    /**
     * 测试集管理页面
     */
    @GetMapping("/testset")
    public String testSet(Model model) {
        model.addAttribute("title", "测试集管理 - ChatBI 评估测试平台");
        return "testset";
    }
}
