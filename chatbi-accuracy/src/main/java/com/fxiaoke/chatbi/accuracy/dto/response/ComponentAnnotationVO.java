package com.fxiaoke.chatbi.accuracy.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 组件标注展示VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ComponentAnnotationVO {
    
    /**
     * 推理组件标注
     */
    private ComponentItemVO reasoning;
    
    /**
     * 图表数据组件标注
     */
    private ComponentItemVO chartData;
    
    /**
     * 洞察组件标注
     */
    private ComponentItemVO insight;
    
    /**
     * 追问建议组件标注
     */
    private ComponentItemVO followUp;
    
    /**
     * 单个组件标注项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ComponentItemVO {
        
        /**
         * 组件评分：1-5分
         */
        private Integer score;
        
        /**
         * 组件标签
         */
        private List<String> tags;
        
        /**
         * 组件评价
         */
        private String comments;
    }
}
