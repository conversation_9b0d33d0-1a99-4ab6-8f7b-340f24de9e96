package com.fxiaoke.chatbi.accuracy.dto.request;

import lombok.Data;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.ZoneId;

/**
 * 问答记录筛选条件Bean
 * 统一管理所有筛选参数和相关处理逻辑
 */
@Data
public class QueryLogFilterRequest {
    
    /**
     * 标注状态：pending-待标注，annotated-已标注，reviewed-已审核
     */
    private String status;
    
    /**
     * 租户ID
     */
    private String tenantId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 开始日期过滤 (YYYY-MM-DD格式)
     */
    private String startDate;
    
    /**
     * 结束日期过滤 (YYYY-MM-DD格式)
     */
    private String endDate;
    
    /**
     * 问题关键词搜索
     */
    private String question;
    
    /**
     * 页码
     */
    private Integer page = 1;
    
    /**
     * 页大小
     */
    private Integer size = 10;
    
    /**
     * 来源类型
     */
    private String source;
    
    /**
     * 用户反馈：1-点赞，-1-点踩，0-无反馈
     */
    private Integer feedback;
    
    /**
     * 标注状态：0-未标注，1-已标注
     */
    private Integer isAnnotated;
    
    /**
     * 获取开始时间戳（毫秒）
     * @return 开始时间戳，解析失败返回null
     */
    public Long getStartTimeMillis() {
        if (!StringUtils.hasText(startDate)) {
            return null;
        }
        try {
            LocalDate date = LocalDate.parse(startDate);
            return date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 获取结束时间戳（毫秒）
     * @return 结束时间戳，解析失败返回null
     */
    public Long getEndTimeMillis() {
        if (!StringUtils.hasText(endDate)) {
            return null;
        }
        try {
            LocalDate date = LocalDate.parse(endDate);
            // 结束日期设置为当天的23:59:59
            return date.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli() - 1;
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 检查是否有有效的筛选条件
     * @return true-有筛选条件，false-无筛选条件
     */
    public boolean hasFilters() {
        return StringUtils.hasText(tenantId) ||
               StringUtils.hasText(userId) ||
               StringUtils.hasText(source) ||
               StringUtils.hasText(startDate) ||
               StringUtils.hasText(endDate) ||
               StringUtils.hasText(question) ||
               feedback != null ||
               isAnnotated != null;
    }
    
    /**
     * 检查租户ID是否有效
     * @return true-有效，false-无效
     */
    public boolean hasTenantId() {
        return StringUtils.hasText(tenantId);
    }
    
    /**
     * 检查用户ID是否有效
     * @return true-有效，false-无效
     */
    public boolean hasUserId() {
        return StringUtils.hasText(userId);
    }
    
    /**
     * 检查来源是否有效
     * @return true-有效，false-无效
     */
    public boolean hasSource() {
        return StringUtils.hasText(source);
    }
    
    /**
     * 检查是否有时间范围筛选
     * @return true-有时间筛选，false-无时间筛选
     */
    public boolean hasTimeRange() {
        return StringUtils.hasText(startDate) || StringUtils.hasText(endDate);
    }
    
    /**
     * 检查是否有问题关键词搜索
     * @return true-有关键词，false-无关键词
     */
    public boolean hasQuestion() {
        return StringUtils.hasText(question);
    }
    
    /**
     * 检查是否有反馈筛选
     * @return true-有反馈筛选，false-无反馈筛选
     */
    public boolean hasFeedback() {
        return feedback != null;
    }
    
    /**
     * 检查是否有标注状态筛选
     * @return true-有标注状态筛选，false-无标注状态筛选
     */
    public boolean hasAnnotationStatus() {
        return isAnnotated != null;
    }
} 