package com.fxiaoke.chatbi.accuracy.controller;

import com.fxiaoke.chatbi.accuracy.dto.response.AttachmentVO;
import com.fxiaoke.chatbi.accuracy.service.AnnotationAttachmentService;
import com.fxiaoke.chatbi.common.dto.ApiResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.nio.file.Paths;
import java.util.List;

/**
 * 标注附件控制器
 * 提供附件上传、下载、删除功能
 */
@Slf4j
@RestController
@RequestMapping("/api/chatAnnotation/attachment")
@RequiredArgsConstructor
public class AnnotationAttachmentController {

    private final AnnotationAttachmentService attachmentService;

    /**
     * 上传附件
     */
    @PostMapping("/upload")
    public ResponseEntity<ApiResult<AttachmentVO>> uploadAttachment(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "description", required = false) String description,
            @RequestParam(value = "annotationId", required = false) String annotationId) {
        try {
            log.info("开始上传附件: filename={}, size={}, annotationId={}", 
                    file.getOriginalFilename(), file.getSize(), annotationId);

            AttachmentVO attachment = attachmentService.uploadAttachment(file, description, annotationId);

            log.info("附件上传成功: attachmentId={}, filename={}", 
                    attachment.getId(), attachment.getFilename());
            
            return ResponseEntity.ok(ApiResult.success(attachment));
        } catch (Exception e) {
            log.error("附件上传失败: filename={}, annotationId={}", 
                    file.getOriginalFilename(), annotationId, e);
            return ResponseEntity.ok(ApiResult.error("附件上传失败: " + e.getMessage()));
        }
    }

    /**
     * 下载附件
     */
    @GetMapping("/download/{attachmentId}")
    public ResponseEntity<Resource> downloadAttachment(@PathVariable String attachmentId) {
        try {
            log.info("开始下载附件: attachmentId={}", attachmentId);

            AttachmentVO attachment = attachmentService.getAttachmentById(attachmentId);
            if (attachment == null) {
                log.warn("附件不存在: attachmentId={}", attachmentId);
                return ResponseEntity.notFound().build();
            }

            // 构建文件路径
            String filename = attachment.getFilename();
            String fileExtension = getFileExtension(filename);
            String savedFilename = attachmentId + "." + fileExtension;
            File file = Paths.get(getUploadPath(), "attachments", savedFilename).toFile();

            if (!file.exists()) {
                log.warn("文件不存在: {}", file.getAbsolutePath());
                return ResponseEntity.notFound().build();
            }

            Resource resource = new FileSystemResource(file);
            
            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, 
                    "attachment; filename=\"" + attachment.getFilename() + "\"");
            
            // 根据文件类型设置Content-Type
            MediaType mediaType = getMediaType(fileExtension);
            
            log.info("附件下载成功: attachmentId={}, filename={}", attachmentId, filename);
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentType(mediaType)
                    .body(resource);

        } catch (Exception e) {
            log.error("附件下载失败: attachmentId={}", attachmentId, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 删除附件
     */
    @PostMapping("/delete/{attachmentId}")
    public ResponseEntity<ApiResult<Boolean>> deleteAttachment(@PathVariable String attachmentId) {
        try {
            log.info("开始删除附件: attachmentId={}", attachmentId);

            boolean success = attachmentService.deleteAttachment(attachmentId);

            log.info("删除附件完成: attachmentId={}, success={}", attachmentId, success);
            
            return ResponseEntity.ok(ApiResult.success(success));
        } catch (Exception e) {
            log.error("删除附件失败: attachmentId={}", attachmentId, e);
            return ResponseEntity.ok(ApiResult.error("删除附件失败: " + e.getMessage()));
        }
    }

    /**
     * 根据标注ID查询附件列表
     */
    @GetMapping("/list/{annotationId}")
    public ResponseEntity<ApiResult<List<AttachmentVO>>> getAttachmentsByAnnotationId(
            @PathVariable String annotationId) {
        try {
            log.info("开始查询标注附件: annotationId={}", annotationId);

            List<AttachmentVO> attachments = attachmentService.getAttachmentsByAnnotationId(annotationId);

            log.info("查询标注附件成功: annotationId={}, count={}", annotationId, attachments.size());
            
            return ResponseEntity.ok(ApiResult.success(attachments));
        } catch (Exception e) {
            log.error("查询标注附件失败: annotationId={}", annotationId, e);
            return ResponseEntity.ok(ApiResult.error("查询标注附件失败: " + e.getMessage()));
        }
    }

    /**
     * 获取附件信息
     */
    @GetMapping("/info/{attachmentId}")
    public ResponseEntity<ApiResult<AttachmentVO>> getAttachmentInfo(@PathVariable String attachmentId) {
        try {
            log.info("开始查询附件信息: attachmentId={}", attachmentId);

            AttachmentVO attachment = attachmentService.getAttachmentById(attachmentId);
            
            if (attachment == null) {
                log.warn("附件不存在: attachmentId={}", attachmentId);
                return ResponseEntity.ok(ApiResult.error("附件不存在"));
            }

            log.info("查询附件信息成功: attachmentId={}, filename={}", 
                    attachmentId, attachment.getFilename());
            
            return ResponseEntity.ok(ApiResult.success(attachment));
        } catch (Exception e) {
            log.error("查询附件信息失败: attachmentId={}", attachmentId, e);
            return ResponseEntity.ok(ApiResult.error("查询附件信息失败: " + e.getMessage()));
        }
    }

    // ===== 私有辅助方法 =====

    private String getFileExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return "";
        }
        
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filename.length() - 1) {
            return filename.substring(lastDotIndex + 1);
        }
        
        return "";
    }

    private MediaType getMediaType(String fileExtension) {
        if (fileExtension == null || fileExtension.isEmpty()) {
            return MediaType.APPLICATION_OCTET_STREAM;
        }
        
        String ext = fileExtension.toLowerCase();
        switch (ext) {
            case "jpg":
            case "jpeg":
                return MediaType.IMAGE_JPEG;
            case "png":
                return MediaType.IMAGE_PNG;
            case "gif":
                return MediaType.IMAGE_GIF;
            case "bmp":
                return MediaType.valueOf("image/bmp");
            case "webp":
                return MediaType.valueOf("image/webp");
            default:
                return MediaType.APPLICATION_OCTET_STREAM;
        }
    }

    private String getUploadPath() {
        // 这里应该从配置中获取，暂时硬编码
        return "/tmp/chatbi-uploads";
    }
}
