package com.fxiaoke.chatbi.accuracy.service;

import com.fxiaoke.chatbi.accuracy.dto.request.*;
import com.fxiaoke.chatbi.accuracy.dto.response.*;
import org.springframework.data.domain.Page;

/**
 * 对话标注服务接口（重构后）
 * 支持Session级别的多轮对话标注
 */
public interface ChatAnnotationService {

    /**
     * 分页查询Session列表
     *
     * @param request 筛选条件
     * @return Session分页结果
     */
    Page<SessionListVO> getSessionList(SessionListRequest request);

    /**
     * 获取Session详情（包含多轮对话）
     *
     * @param request 请求参数
     * @return Session详情
     */
    SessionDetailVO getSessionDetail(SessionDetailRequest request);

    /**
     * 保存问题级标注
     *
     * @param request 问题级标注请求
     * @return 是否保存成功
     */
    boolean saveQueryAnnotation(QueryAnnotationSaveRequest request);

    /**
     * 保存Session级标注
     *
     * @param request Session级标注请求
     * @return 是否保存成功
     */
    boolean saveSessionAnnotation(SessionAnnotationSaveRequest request);

    /**
     * 获取标注统计信息
     *
     * @return 统计信息
     */
    AnnotationStatisticsVO getAnnotationStatistics();

    /**
     * 删除问题级标注
     *
     * @param annotationId 标注ID
     * @return 是否删除成功
     */
    boolean deleteQueryAnnotation(String annotationId);

    /**
     * 删除Session级标注
     *
     * @param annotationId 标注ID
     * @return 是否删除成功
     */
    boolean deleteSessionAnnotation(String annotationId);
}
