package com.fxiaoke.chatbi.accuracy.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.util.Arrays;
import java.util.UUID;

/**
 * 文件上传工具类
 */
@Slf4j
public class FileUploadUtil {

    // 支持的图片格式
    private static final String[] ALLOWED_IMAGE_EXTENSIONS = {
            "jpg", "jpeg", "png", "gif", "bmp", "webp"
    };

    // 最大文件大小 (10MB)
    private static final long MAX_FILE_SIZE = 10 * 1024 * 1024;

    /**
     * 验证文件是否为支持的图片格式
     *
     * @param filename 文件名
     * @return 是否为支持的图片格式
     */
    public static boolean isValidImageFile(String filename) {
        if (!StringUtils.hasText(filename)) {
            return false;
        }

        String extension = getFileExtension(filename).toLowerCase();
        return Arrays.asList(ALLOWED_IMAGE_EXTENSIONS).contains(extension);
    }

    /**
     * 验证文件大小
     *
     * @param fileSize 文件大小（字节）
     * @return 是否在允许范围内
     */
    public static boolean isValidFileSize(long fileSize) {
        return fileSize > 0 && fileSize <= MAX_FILE_SIZE;
    }

    /**
     * 获取文件扩展名
     *
     * @param filename 文件名
     * @return 扩展名（不包含点号）
     */
    public static String getFileExtension(String filename) {
        if (!StringUtils.hasText(filename)) {
            return "";
        }

        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filename.length() - 1) {
            return filename.substring(lastDotIndex + 1);
        }

        return "";
    }

    /**
     * 生成唯一的文件名
     *
     * @param originalFilename 原始文件名
     * @return 唯一文件名
     */
    public static String generateUniqueFilename(String originalFilename) {
        String extension = getFileExtension(originalFilename);
        String uuid = UUID.randomUUID().toString().replace("-", "");
        
        if (StringUtils.hasText(extension)) {
            return uuid + "." + extension;
        } else {
            return uuid;
        }
    }

    /**
     * 确保目录存在
     *
     * @param directoryPath 目录路径
     * @throws IOException 创建目录失败
     */
    public static void ensureDirectoryExists(String directoryPath) throws IOException {
        Path path = Paths.get(directoryPath);
        if (!Files.exists(path)) {
            Files.createDirectories(path);
            log.info("创建目录: {}", directoryPath);
        }
    }

    /**
     * 计算文件MD5哈希值
     *
     * @param fileBytes 文件字节数组
     * @return MD5哈希值
     */
    public static String calculateMD5Hash(byte[] fileBytes) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(fileBytes);
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            log.warn("计算MD5哈希失败", e);
            return "";
        }
    }

    /**
     * 格式化文件大小
     *
     * @param bytes 字节数
     * @return 格式化后的大小字符串
     */
    public static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 获取支持的图片格式列表
     *
     * @return 支持的图片格式数组
     */
    public static String[] getSupportedImageExtensions() {
        return ALLOWED_IMAGE_EXTENSIONS.clone();
    }

    /**
     * 获取最大文件大小
     *
     * @return 最大文件大小（字节）
     */
    public static long getMaxFileSize() {
        return MAX_FILE_SIZE;
    }
}
