package com.fxiaoke.chatbi.accuracy.dto.request;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * Session列表查询请求
 */
@Data
public class SessionListRequest {
    
    /**
     * 租户ID
     */
    @NotBlank(message = "租户ID不能为空")
    private String tenantId;
    
    /**
     * 关键词搜索（用户问题、AI回答内容）
     */
    private String keyword;
    
    /**
     * 用户ID搜索
     */
    private String userId;
    
    /**
     * 开始时间（时间戳毫秒）
     */
    private Long startTime;
    
    /**
     * 结束时间（时间戳毫秒）
     */
    private Long endTime;
    
    /**
     * 标注状态筛选：0-待标注，1-已标注
     */
    private Integer annotationStatus;
    
    /**
     * 页码（从1开始）
     */
    @Min(value = 1, message = "页码不能小于1")
    private Integer page = 1;
    
    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小不能小于1")
    private Integer size = 20;
}
