package com.fxiaoke.chatbi.accuracy.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 附件展示VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AttachmentVO {
    
    /**
     * 附件ID
     */
    private String id;
    
    /**
     * 文件名
     */
    private String filename;
    
    /**
     * 文件URL
     */
    private String url;
    
    /**
     * 文件类型
     */
    private String type;
    
    /**
     * 附件描述
     */
    private String description;
    
    /**
     * 文件大小（字节）
     */
    private Long size;
    
    /**
     * 文件哈希值
     */
    private String hash;
    
    /**
     * 上传时间
     */
    private Long uploadTime;
}
