<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatBI 对话标注平台</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.2/css/bootstrap.min.css" rel="stylesheet"
          onerror="this.onerror=null;this.href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css';">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"
          onerror="this.onerror=null;this.href='https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css';">
    <!-- 自定义样式 -->
    <link th:href="@{/css/custom.css?v=20241203-2}" rel="stylesheet">
</head>
<body class="annotation-page">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>
                ChatBI 评估测试平台
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/chart-library}">
                            <i class="fas fa-database me-1"></i>图表库
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/chart}">
                            <i class="fas fa-chart-bar me-1"></i>图表知识管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/test}">
                            <i class="fas fa-play-circle me-1"></i>测试执行
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" th:href="@{/annotation}">
                            <i class="fas fa-tags me-1"></i>问答记录标注
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/testset}">
                            <i class="fas fa-database me-1"></i>测试集管理
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="container-fluid py-2" style="background-color: #f8f9fa; height: calc(100vh - 56px); overflow: hidden;">
        <div class="row h-100">
            <!-- 左侧：搜索和Session列表 -->
            <div class="col-md-3 h-100">
                <div class="card h-100 d-flex flex-column">
                    <div class="card-header py-2">
                        <h6 class="mb-0"><i class="fas fa-search me-2"></i>搜索 & Session列表</h6>
                    </div>
                    <div class="card-body p-2 flex-grow-1 d-flex flex-column">
                        <!-- 搜索筛选区域 -->
                        <div class="mb-2">
                            <label class="form-label-sm">关键词搜索</label>
                            <input type="text" id="keywordSearch" class="form-control form-control-sm" placeholder="搜索问题内容...">
                        </div>

                        <div class="row mb-2">
                            <div class="col-6">
                                <label class="form-label-sm">租户ID</label>
                                <input type="text" id="tenantFilter" class="form-control form-control-sm" placeholder="请输入租户ID（如：82313）" value="82313">
                            </div>
                            <div class="col-6">
                                <label class="form-label-sm">用户ID</label>
                                <input type="text" id="userIdSearch" class="form-control form-control-sm" placeholder="用户ID">
                            </div>
                        </div>

                        <div class="row mb-2">
                            <div class="col-6">
                                <label class="form-label-sm">时间范围</label>
                                <select id="timeRangeFilter" class="form-select form-select-sm">
                                    <option value="">全部时间</option>
                                    <option value="today">今天</option>
                                    <option value="yesterday">昨天</option>
                                    <option value="week">本周</option>
                                    <option value="month">本月</option>
                                </select>
                            </div>
                            <div class="col-6">
                                <label class="form-label-sm">标注状态</label>
                                <select id="annotationStatusFilter" class="form-select form-select-sm">
                                    <option value="">全部</option>
                                    <option value="0">待标注</option>
                                    <option value="1">已标注</option>
                                </select>
                            </div>
                        </div>

                        <button id="searchBtn" class="btn btn-primary btn-sm w-100 mb-2">
                            <i class="fas fa-search me-1"></i>搜索
                        </button>

                        <!-- Session列表 -->
                        <div class="border-top pt-2 flex-grow-1 d-flex flex-column">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <small class="text-muted">Session列表</small>
                                <span id="sessionCount" class="badge bg-secondary">0</span>
                            </div>
                            <div id="sessionList" class="session-list flex-grow-1" style="overflow-y: auto;">
                                <!-- Session列表将通过JavaScript动态加载 -->
                                <div class="text-center text-muted py-3">
                                    <i class="fas fa-spinner fa-spin me-2"></i>加载中...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 中间：对话展示区 -->
            <div class="col-md-6 h-100">
                <div class="card h-100 d-flex flex-column">
                    <div class="card-header py-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0"><i class="fas fa-comments me-2"></i>对话展示区</h6>
                            <div class="text-muted small">
                                <span id="currentSessionTitle">请选择一个Session</span>
                                <span id="conversationCount"></span>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-2 flex-grow-1" style="overflow-y: auto;">
                        <div id="conversationDisplay">
                            <!-- 对话内容将通过JavaScript动态加载 -->
                            <div class="text-center text-muted py-5">
                                <i class="fas fa-comments fa-3x mb-3"></i>
                                <p>请从左侧选择一个Session查看对话内容</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧：快速标注区 -->
            <div class="col-md-3 h-100">
                <div class="card h-100 d-flex flex-column">
                    <div class="card-header py-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0"><i class="fas fa-edit me-2"></i>快速标注区</h6>
                            <small id="currentAnnotationTitle" class="text-muted"></small>
                        </div>
                    </div>
                    <div class="card-body p-2 flex-grow-1" style="overflow-y: auto;">
                        <div id="annotationPanel">
                            <!-- 标注表单将通过JavaScript动态加载 -->
                            <div class="text-center text-muted py-5">
                                <i class="fas fa-edit fa-3x mb-3"></i>
                                <p>请选择一轮对话进行标注</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Toast容器 -->
    <div id="toastContainer" class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;"></div>

    <!-- 全局加载遮罩 -->
    <div id="globalLoading" class="d-none position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center"
         style="background-color: rgba(0,0,0,0.5); z-index: 10000;">
        <div class="spinner-border text-light" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
    </div>

    <!-- JavaScript -->
    <!-- jQuery -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"
            onerror="this.onerror=null;this.src='https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js';"></script>

    <!-- Bootstrap -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.2/js/bootstrap.bundle.min.js"
            onerror="this.onerror=null;this.src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js';"></script>

    <!-- 通用工具库 - 必须在其他业务脚本之前加载 -->
    <script th:src="@{/js/common.js?v=20241203-2}"></script>

    <!-- 新的对话标注JavaScript -->
    <script th:src="@{/js/chat-annotation.js?v=20241203-4}"></script>

    <!-- 页面配置 -->
    <script th:inline="javascript">
        // 页面配置
        const PAGE_CONFIG = {
            apiBaseUrl: '/api/chatAnnotation',
            defaultTenantId: 'default'
        };
    </script>
</body>
</html>
