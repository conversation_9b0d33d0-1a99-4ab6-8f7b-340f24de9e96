<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试执行 - ChatBI 评估测试平台</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.2/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义CSS -->
    <link th:href="@{/css/custom.css}" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>
                ChatBI 评估测试平台
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/chart-library}">
                            <i class="fas fa-database me-1"></i>图表库
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/chart}">
                            <i class="fas fa-chart-bar me-1"></i>图表知识管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" th:href="@{/test}">
                            <i class="fas fa-play-circle me-1"></i>测试执行
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/annotation}">
                            <i class="fas fa-tags me-1"></i>问答记录标注
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/testset}">
                            <i class="fas fa-database me-1"></i>测试集管理
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="container-fluid py-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <h2>
                    <i class="fas fa-play-circle me-2 text-success"></i>
                    测试执行
                </h2>
                <p class="text-muted">模拟用户请求，对Agent召回全链路进行快速测试</p>
            </div>
        </div>

        <div class="row">
            <!-- 左侧：测试输入区 -->
            <div class="col-lg-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-edit me-2"></i>
                            测试输入
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="testForm">
                            <!-- Agent配置 -->
                            <div class="mb-3">
                                <label for="agentUrl" class="form-label">Agent地址</label>
                                <select class="form-select" id="agentUrl">
                                    <option value="http://************:19378">112环境 (http://************:19378)</option>
                                    <option value="http://************:19546">FoneShare环境 (http://************:19546)</option>
                                    <option value="http://localhost:8080">本地环境 (http://localhost:8080)</option>
                                </select>
                                <div class="form-text">选择要测试的Agent环境</div>
                            </div>

                            <!-- 租户ID -->
                            <div class="mb-3">
                                <label for="tenantId" class="form-label">租户ID</label>
                                <input type="number" class="form-control" id="tenantId" 
                                       value="82313" placeholder="输入租户ID">
                            </div>

                            <!-- 用户ID -->
                            <div class="mb-3">
                                <label for="userId" class="form-label">用户ID</label>
                                <input type="number" class="form-control" id="userId" 
                                       value="-10000" placeholder="输入用户ID">
                            </div>

                            <!-- 测试问题 -->
                            <div class="mb-3">
                                <label for="testQuery" class="form-label">测试问题</label>
                                <textarea class="form-control" id="testQuery" rows="4" 
                                          placeholder="输入要测试的问题，例如：显示最近一个月的销售额趋势"></textarea>
                            </div>



                            <!-- 执行按钮 -->
                            <div class="d-grid">
                                <button type="submit" class="btn btn-success btn-lg" id="executeBtn">
                                    <i class="fas fa-play me-2"></i>
                                    执行测试
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 右侧：结果展示区 -->
            <div class="col-lg-6">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            测试结果
                        </h5>
                        <div>
                            <span class="badge bg-secondary" id="statusBadge">待执行</span>
                            <span class="badge bg-info ms-2" id="timeBadge" style="display: none;"></span>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- 加载状态 -->
                        <div id="loadingArea" style="display: none;" class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">执行中...</span>
                            </div>
                            <p class="mt-3 text-muted">正在执行测试，请稍候...</p>
                        </div>

                        <!-- 结果展示 -->
                        <div id="resultArea" style="display: none;">
                            <!-- API调用信息 -->
                            <div id="apiInfoSection" class="mb-3" style="display: none;">
                                <h6><i class="fas fa-link me-2"></i>API调用信息</h6>
                                <div class="bg-light border rounded p-3">
                                    <div class="row">
                                        <div class="col-12 mb-2">
                                            <small class="text-muted">推理过程:</small>
                                            <div id="chatApiUrl" class="font-monospace small"></div>
                                        </div>
                                        <div class="col-4 mb-2">
                                            <small class="text-muted">图表数据:</small>
                                            <div id="loadDataUrl" class="font-monospace small"></div>
                                        </div>
                                        <div class="col-4 mb-2">
                                            <small class="text-muted">数据解读:</small>
                                            <div id="loadInsightUrl" class="font-monospace small"></div>
                                        </div>
                                        <div class="col-4 mb-2">
                                            <small class="text-muted">追问建议:</small>
                                            <div id="loadFollowUpUrl" class="font-monospace small"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 推理过程 -->
                            <div id="reasoningSection" class="mb-3">
                                <h6><i class="fas fa-brain me-2"></i>推理过程</h6>
                                <div id="reasoningContent" class="bg-light border rounded p-3">
                                    <!-- 动态填充推理内容 -->
                                </div>
                            </div>



                            <!-- 图表数据 -->
                            <div id="chartSection" class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6><i class="fas fa-chart-bar me-2"></i>图表数据</h6>
                                    <div id="chartStatus" class="badge bg-secondary">待加载</div>
                                </div>
                                <div id="chartContent" class="bg-light border rounded p-3" style="max-height: 400px; overflow-y: auto;">
                                    <div class="text-center py-3">
                                        <div class="spinner-border spinner-border-sm text-primary me-2" role="status"></div>
                                        <span class="text-muted">正在获取图表数据...</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 数据解读 -->
                            <div id="insightSection" class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6><i class="fas fa-lightbulb me-2"></i>数据解读</h6>
                                    <div id="insightStatus" class="badge bg-secondary">待加载</div>
                                </div>
                                <div id="insightContent" class="bg-light border rounded p-3" style="max-height: 400px; overflow-y: auto;">
                                    <div class="text-center py-3">
                                        <div class="spinner-border spinner-border-sm text-primary me-2" role="status"></div>
                                        <span class="text-muted">正在获取数据解读...</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 追问建议 -->
                            <div id="followUpSection" class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6><i class="fas fa-question-circle me-2"></i>追问建议</h6>
                                    <div id="followUpStatus" class="badge bg-secondary">待加载</div>
                                </div>
                                <div id="followUpContent" class="bg-light border rounded p-3" style="max-height: 400px; overflow-y: auto;">
                                    <div class="text-center py-3">
                                        <div class="spinner-border spinner-border-sm text-primary me-2" role="status"></div>
                                        <span class="text-muted">正在获取追问建议...</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 错误信息 -->
                        <div id="errorArea" style="display: none;" class="alert alert-danger">
                            <h6>执行失败</h6>
                            <div id="errorMessage"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </main>

    <!-- 页脚 -->
    <footer class="bg-light text-center py-3 mt-5">
        <div class="container">
            <span class="text-muted">ChatBI 评估测试平台 &copy; 2024</span>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.2/js/bootstrap.bundle.min.js"></script>
    <script th:src="@{/js/common.js}"></script>
    <script th:src="@{/js/test.js}"></script>
</body>
</html> 