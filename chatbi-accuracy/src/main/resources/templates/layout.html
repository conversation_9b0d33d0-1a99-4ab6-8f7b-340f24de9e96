<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" th:fragment="layout (title, content)">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${title ?: 'ChatBI 评估测试平台'}">ChatBI 评估测试平台</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.2/css/bootstrap.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.bootcdn.net/ajax/libs/datatables/1.13.7/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义CSS -->
    <link th:href="@{/css/custom.css}" rel="stylesheet">
    
    <!-- 页面特定的CSS -->
    <th:block th:if="${pageStyle}">
        <link th:href="@{${pageStyle}}" rel="stylesheet">
    </th:block>
</head>
<body>
    <!-- 导航栏组件 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary" th:fragment="navbar">
        <div class="container-fluid">
            <!-- 品牌Logo -->
            <a class="navbar-brand" href="/" th:href="@{/}">
                <i class="fas fa-chart-line me-2"></i>
                ChatBI 评估测试平台
            </a>
            
            <!-- 移动端切换按钮 -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                    aria-controls="navbarNav" aria-expanded="false" aria-label="切换导航">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <!-- 导航菜单 -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- 主导航菜单 -->
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/}"
                           th:classappend="${#strings.equals(#httpServletRequest.requestURI, '/')} ? 'active'">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/chart}"
                           th:classappend="${#strings.contains(#httpServletRequest.requestURI, '/chart')} ? 'active'">
                            <i class="fas fa-chart-bar me-1"></i>图表知识管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/test}"
                           th:classappend="${#strings.contains(#httpServletRequest.requestURI, '/test')} ? 'active'">
                            <i class="fas fa-play-circle me-1"></i>测试执行
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/annotation}"
                           th:classappend="${#strings.contains(#httpServletRequest.requestURI, '/annotation')} ? 'active'">
                            <i class="fas fa-tags me-1"></i>问答记录标注
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/testset}"
                           th:classappend="${#strings.contains(#httpServletRequest.requestURI, '/testset')} ? 'active'">
                            <i class="fas fa-database me-1"></i>测试集管理
                        </a>
                    </li>
                </ul>
                
                <!-- 用户菜单 -->
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" 
                           aria-expanded="false">
                            <i class="fas fa-user me-1"></i>开发者
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="#" onclick="showSettings()">
                                    <i class="fas fa-cog me-2"></i>设置
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="#" onclick="handleLogout()">
                                    <i class="fas fa-sign-out-alt me-2"></i>退出
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="main-content" role="main">
        <div class="container-fluid py-4">
            <!-- 面包屑导航 -->
            <nav aria-label="breadcrumb" th:if="${breadcrumbs}" class="mb-3">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a th:href="@{/}"><i class="fas fa-home"></i></a>
                    </li>
                    <th:block th:each="crumb : ${breadcrumbs}">
                        <li class="breadcrumb-item" th:classappend="${crumbStat.last} ? 'active'" 
                            th:aria-current="${crumbStat.last} ? 'page'">
                            <a th:if="${!crumbStat.last}" th:href="${crumb.url}" th:text="${crumb.name}"></a>
                            <span th:if="${crumbStat.last}" th:text="${crumb.name}"></span>
                        </li>
                    </th:block>
                </ol>
            </nav>
            
            <!-- 页面内容插槽 -->
            <div th:replace="${content}">
                <!-- 页面内容将在这里插入 -->
            </div>
        </div>
    </main>

    <!-- 页脚组件 -->
    <footer class="footer bg-light text-center py-3 mt-auto" th:fragment="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6 text-md-start">
                    <span class="text-muted">
                        <i class="fas fa-chart-line me-1"></i>
                        ChatBI 评估测试平台 &copy; 2024
                    </span>
                </div>
                <div class="col-md-6 text-md-end">
                    <span class="text-muted">
                        <small>版本 1.0.0 | 构建时间: <span th:text="${#dates.format(#dates.createNow(), 'yyyy-MM-dd')}"></span></small>
                    </span>
                </div>
            </div>
        </div>
    </footer>

    <!-- 全局加载遮罩 -->
    <div id="globalLoading" class="global-loading d-none">
        <div class="loading-content">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <div class="mt-2">加载中...</div>
        </div>
    </div>

    <!-- Toast容器 -->
    <div id="toastContainer" class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;">
        <!-- Toast消息将动态插入这里 -->
    </div>

    <!-- JavaScript库 -->
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.2/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/datatables/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/datatables/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    
    <!-- 通用JavaScript -->
    <script th:src="@{/js/common.js}"></script>
    
    <!-- 页面特定的JavaScript -->
    <th:block th:if="${pageScript}">
        <script th:src="@{${pageScript}}"></script>
    </th:block>
    
    <!-- 内联脚本 -->
    <script th:inline="javascript">
        // 全局配置
        window.APP_CONFIG = {
            contextPath: /*[[@{/}]]*/ '/',
            currentPage: /*[[${#httpServletRequest.requestURI}]]*/ '/',
            csrfToken: /*[[${_csrf?.token}]]*/ null,
            csrfHeader: /*[[${_csrf?.headerName}]]*/ null
        };
        
        // 全局函数
        function showSettings() {
            Utils.showInfo('设置功能开发中...');
        }
        
        function handleLogout() {
            Utils.confirm('确定要退出系统吗？', function() {
                window.location.href = '/logout';
            });
        }
        
        // 页面加载完成后的初始化
        $(document).ready(function() {
            // 初始化工具提示
            $('[data-bs-toggle="tooltip"]').tooltip();
            
            // 初始化弹出框
            $('[data-bs-toggle="popover"]').popover();
            
            // 设置CSRF令牌
            if (window.APP_CONFIG.csrfToken) {
                $.ajaxSetup({
                    beforeSend: function(xhr) {
                        xhr.setRequestHeader(window.APP_CONFIG.csrfHeader, window.APP_CONFIG.csrfToken);
                    }
                });
            }
            
            console.log('布局模板初始化完成');
        });
    </script>
</body>
</html>