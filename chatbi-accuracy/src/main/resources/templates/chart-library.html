<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图表库 - ChatBI 评估测试平台</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.2/css/bootstrap.min.css" rel="stylesheet" 
          onerror="this.onerror=null;this.href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css';">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" rel="stylesheet"
          onerror="this.onerror=null;this.href='https://cdnjs.cloudflare.com/ajax/libs/datatables/1.13.7/css/dataTables.bootstrap5.min.css';">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"
          onerror="this.onerror=null;this.href='https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css';">
    
    <style>
        .library-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border: none;
            border-radius: 12px;
            transition: transform 0.2s ease-in-out;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
        }
        
        .stat-card.charts {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .stat-card.themes {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        
        .filter-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid #e9ecef;
        }
        
        .column-toggle {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            border: 1px solid #e9ecef;
        }
        
        .column-toggle .form-check {
            margin-bottom: 0.5rem;
        }
        
        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        
        .table-responsive {
            border-radius: 12px;
        }
        
        .table {
            margin-bottom: 0;
        }
        
        .table th {
            background-color: #343a40;
            color: white;
            border-color: #454d55;
            font-weight: 600;
            font-size: 0.875rem;
            white-space: nowrap;
            padding: 0.75rem 0.5rem;
        }
        
        .table td {
            vertical-align: middle;
            padding: 0.75rem 0.5rem;
            font-size: 0.875rem;
            border-color: #dee2e6;
        }
        
        .table-striped > tbody > tr:nth-of-type(odd) > td {
            background-color: rgba(0, 0, 0, 0.025);
        }
        
        .table-hover tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.075);
        }
        
        /* 优化列宽度 */
        table#chartLibraryTable th:nth-child(1) { width: 12%; } /* ViewID */
        table#chartLibraryTable th:nth-child(2) { width: 15%; } /* 图表名称 */
        table#chartLibraryTable th:nth-child(3) { width: 8%; }  /* 图表类型 */
        table#chartLibraryTable th:nth-child(4) { width: 8%; }  /* 主题ID */
        table#chartLibraryTable th:nth-child(5) { width: 12%; } /* 主题名称 */
        table#chartLibraryTable th:nth-child(6) { width: 6%; }  /* 租户ID */
        table#chartLibraryTable th:nth-child(7) { width: 15%; } /* 图表规格 */
        table#chartLibraryTable th:nth-child(8) { width: 8%; }  /* 维度字段 */
        table#chartLibraryTable th:nth-child(9) { width: 8%; }  /* 指标字段 */
        table#chartLibraryTable th:nth-child(10) { width: 8%; } /* 过滤字段 */
        table#chartLibraryTable th:nth-child(11) { width: 6%; } /* 使用次数 */
        table#chartLibraryTable th:nth-child(12) { width: 12%; } /* 最后修改时间 */
        
        .chart-type-badge {
            font-size: 0.75em;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-weight: 500;
        }
        
        .theme-badge {
            background-color: #e3f2fd;
            color: #1976d2;
            border: 1px solid #2196f3;
            border-radius: 12px;
            font-size: 0.8em;
            padding: 0.2rem 0.5rem;
            display: inline-block;
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .tenant-badge {
            background-color: #fff3e0;
            color: #f57c00;
            border: 1px solid #ff9800;
            border-radius: 8px;
            font-size: 0.75em;
            padding: 0.15rem 0.4rem;
            font-weight: 500;
        }
        
        .spec-preview {
            max-width: 200px;
            max-height: 40px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-family: 'Courier New', monospace;
            font-size: 0.7em;
            color: #666;
            cursor: pointer;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 2px 6px;
            background-color: #f8f9fa;
        }
        
        .spec-preview:hover {
            background-color: #e9ecef;
            border-color: #007bff;
        }
        
        /* 文本截断样式 */
        .text-truncate-table {
            max-width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        /* 字段数组显示样式 */
        .field-list {
            max-width: 120px;
        }
        
        .field-item {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            padding: 2px 6px;
            margin: 1px;
            font-size: 0.75em;
            display: inline-block;
        }
        
        /* 响应式优化 */
        @media (max-width: 1200px) {
            table#chartLibraryTable th:nth-child(7) { width: 12%; } /* 缩小图表规格列 */
            table#chartLibraryTable th:nth-child(8),
            table#chartLibraryTable th:nth-child(9),
            table#chartLibraryTable th:nth-child(10) { width: 6%; } /* 缩小字段列 */
        }
        
        @media (max-width: 992px) {
            .table-responsive {
                font-size: 0.8rem;
            }
        }
        
        .search-section {
            background: white;
            padding: 1rem 0;
            margin-bottom: 1rem;
            border-bottom: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>
                ChatBI 评估测试平台
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" th:href="@{/chart-library}">
                            <i class="fas fa-database me-1"></i>图表库
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/chart}">
                            <i class="fas fa-chart-bar me-1"></i>图表知识管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/test}">
                            <i class="fas fa-play-circle me-1"></i>测试执行
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/annotation}">
                            <i class="fas fa-tags me-1"></i>问答记录标注
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/testset}">
                            <i class="fas fa-database me-1"></i>测试集管理
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="container-fluid py-4" style="background-color: #f8f9fa; min-height: calc(100vh - 56px);">
        <!-- 页面标题和统计 -->
        <div class="library-header text-white p-4 mb-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2 class="mb-1">
                        <i class="fas fa-database me-3"></i>图表库
                    </h2>
                    <p class="mb-0 opacity-75">系统图表资源总览，为产品经理和决策者提供数据可视化资产视图</p>
                </div>
                <div class="col-md-6">
                    <div class="row g-3">
                        <div class="col-6">
                            <div class="card stat-card charts text-white h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                    <h4 class="mb-1" id="totalCharts">0</h4>
                                    <small>图表总数</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="card stat-card themes text-white h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-layer-group fa-2x mb-2"></i>
                                    <h4 class="mb-1" id="totalThemes">0</h4>
                                    <small>主题总数</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索和过滤区域 -->
        <div class="search-section shadow-sm rounded">
            <div class="filter-section">
                <div class="row g-3 align-items-end">
                    <div class="col-md-3">
                        <label for="searchInput" class="form-label fw-bold">
                            <i class="fas fa-search me-1"></i>关键字搜索
                        </label>
                        <input type="text" class="form-control" id="searchInput" 
                               placeholder="搜索图表名称、规格等...">
                    </div>
                    <div class="col-md-2">
                        <label for="chartTypeFilter" class="form-label fw-bold">
                            <i class="fas fa-chart-pie me-1"></i>图表类型
                        </label>
                        <select class="form-select" id="chartTypeFilter">
                            <option value="">全部类型</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="themeFilter" class="form-label fw-bold">
                            <i class="fas fa-layer-group me-1"></i>主题
                        </label>
                        <select class="form-select" id="themeFilter">
                            <option value="">全部主题</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="tenantFilter" class="form-label fw-bold">
                            <i class="fas fa-building me-1"></i>租户ID
                        </label>
                        <input type="text" class="form-control" id="tenantFilter" 
                               placeholder="租户ID">
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-primary me-2" id="refreshBtn">
                            <i class="fas fa-sync-alt me-1"></i>刷新数据
                        </button>
                        <button class="btn btn-outline-secondary" id="resetFiltersBtn">
                            <i class="fas fa-undo me-1"></i>重置过滤
                        </button>
                    </div>
                </div>
            </div>

            <!-- 列显示控制 -->
            <div class="column-toggle">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <strong><i class="fas fa-columns me-1"></i>列显示控制</strong>
                    <div>
                        <button class="btn btn-sm btn-outline-primary me-1" id="showAllColumns">
                            <i class="fas fa-eye me-1"></i>显示全部
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" id="hideAllColumns">
                            <i class="fas fa-eye-slash me-1"></i>隐藏全部
                        </button>
                    </div>
                </div>
                <div class="row" id="columnToggles">
                    <!-- 列控制开关将通过JavaScript生成 -->
                </div>
            </div>
        </div>

        <!-- 图表表格 -->
        <div class="table-container">
            <div class="table-responsive">
                <table id="chartLibraryTable" class="table table-striped table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th data-column="viewId">ViewID</th>
                            <th data-column="viewName">图表名称</th>
                            <th data-column="chartType">图表类型</th>
                            <th data-column="schemaId">主题ID</th>
                            <th data-column="schemaName">主题名称</th>
                            <th data-column="tenantId">租户ID</th>
                            <th data-column="spec">图表规格</th>
                            <th data-column="dimensionNames">维度字段</th>
                            <th data-column="measureNames">指标字段</th>
                            <th data-column="filterNames">过滤字段</th>
                            <th data-column="usageCount">使用次数</th>
                            <th data-column="lastModifiedTime">最后修改时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 数据将通过Ajax加载 -->
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <!-- JSON规格查看模态框 -->
    <div class="modal fade" id="specModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-code me-2"></i>
                        图表规格详情
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <h6 id="specChartInfo" class="text-primary"></h6>
                    </div>
                    <pre id="specContent" class="bg-light p-3 rounded" style="max-height: 400px; overflow-y: auto;"></pre>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="copySpecToClipboard()">
                        <i class="fas fa-copy me-1"></i>复制JSON
                    </button>
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <!-- jQuery -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js" 
            onerror="this.onerror=null;this.src='https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js';"></script>
    
    <!-- Bootstrap -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.2/js/bootstrap.bundle.min.js"
            onerror="this.onerror=null;this.src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js';"></script>
    
    <!-- DataTables -->
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"
            onerror="this.onerror=null;this.src='https://cdnjs.cloudflare.com/ajax/libs/datatables/1.13.7/js/jquery.dataTables.min.js';"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"
            onerror="this.onerror=null;this.src='https://cdnjs.cloudflare.com/ajax/libs/datatables/1.13.7/js/dataTables.bootstrap5.min.js';"></script>
    
    <!-- 通用工具库 - 必须在其他业务脚本之前加载 -->
    <script th:src="@{/js/common.js}"></script>
    
    <!-- 图表库脚本 -->
    <script th:src="@{/js/chart-library.js}"></script>
    
    <script>
        // 复制JSON到剪贴板
        function copySpecToClipboard() {
            const content = document.getElementById('specContent').textContent;
            navigator.clipboard.writeText(content).then(function() {
                const btn = event.target.closest('button');
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check me-1"></i>已复制';
                btn.classList.add('btn-success');
                btn.classList.remove('btn-secondary');
                
                setTimeout(function() {
                    btn.innerHTML = originalText;
                    btn.classList.remove('btn-success');
                    btn.classList.add('btn-secondary');
                }, 2000);
            }).catch(function(err) {
                console.error('复制失败:', err);
                alert('复制失败，请手动选择文本复制');
            });
        }
    </script>

    <!-- Toast容器 -->
    <div id="toastContainer" class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;"></div>

    <!-- 全局加载遮罩 -->
    <div id="globalLoading" class="d-none position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center"
         style="background-color: rgba(0,0,0,0.5); z-index: 10000;">
        <div class="spinner-border text-light" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
    </div>
</body>
</html>