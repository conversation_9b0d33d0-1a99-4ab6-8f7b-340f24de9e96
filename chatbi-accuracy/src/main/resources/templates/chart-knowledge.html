<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图表知识管理 - ChatBI 评估测试平台</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.2/css/bootstrap.min.css" rel="stylesheet" 
          onerror="this.onerror=null;this.href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css';">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" rel="stylesheet"
          onerror="this.onerror=null;this.href='https://cdnjs.cloudflare.com/ajax/libs/datatables/1.13.7/css/dataTables.bootstrap5.min.css';">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"
          onerror="this.onerror=null;this.href='https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css';">
    <!-- 自定义CSS -->
    <link th:href="@{/css/custom.css}" rel="stylesheet">
    
    <style>
        /* 加载状态样式 */
        .loading-spinner {
            display: inline-block;
            width: 12px;
            height: 12px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 测试结果样式 */
        .test-result-high { background-color: #d4edda !important; }
        .test-result-medium { background-color: #fff3cd !important; }
        .test-result-low { background-color: #f8d7da !important; }
        .test-result-score { font-weight: bold; }
        
        /* 权重徽章样式 */
        .weight-badge { font-size: 0.8em; }
        
        /* 错误提示样式 */
        .error-banner {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: #dc3545;
            color: white;
            padding: 10px;
            text-align: center;
            z-index: 9999;
            display: none;
        }
        
        /* JSON规格显示样式 */
        .spec-preview {
            max-width: 200px;
            max-height: 60px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-family: 'Courier New', monospace;
            font-size: 0.8em;
            color: #666;
            cursor: pointer;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 4px 8px;
            background-color: #f8f9fa;
        }
        
        .spec-preview:hover {
            background-color: #e9ecef;
            border-color: #007bff;
        }
        
        .spec-full {
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            white-space: pre-wrap;
            background-color: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        /* 特征显示样式 */
        .feature-item {
            display: inline-block;
            margin: 2px;
            padding: 4px 8px;
            background-color: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 12px;
            font-size: 0.8em;
            color: #1976d2;
        }
        
        .weight-badge {
            background-color: #4caf50;
            color: white;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 0.75em;
            margin-left: 4px;
        }
        
        /* 测试驱动布局样式 */
        .test-driven-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            border-radius: 8px 8px 0 0;
        }
        
        .test-area-highlight {
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
            border: 2px solid #007bff;
            border-radius: 12px;
        }
        
        .section-card {
            transition: transform 0.2s ease-in-out;
        }
        
        .section-card:hover {
            transform: translateY(-2px);
        }
        
        .chart-list-header {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        }
        
        .feature-mgmt-header {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
        }
        
        .test-input-focus {
            border: 2px solid #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        
        .recall-result-row {
            transition: background-color 0.3s ease;
        }
        
        .recall-result-row:hover {
            background-color: rgba(0, 123, 255, 0.05);
        }
        
        .rank-badge {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .rank-1 { background-color: #ffd700; color: #333; }
        .rank-2 { background-color: #c0c0c0; color: #333; }
        .rank-3 { background-color: #cd7f32; color: white; }
        .rank-other { background-color: #6c757d; color: white; }
    </style>
</head>
<body>
    <!-- 错误提示横幅 -->
    <div id="errorBanner" class="error-banner">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <span id="errorMessage">部分资源加载失败，页面功能可能受限</span>
        <button type="button" class="btn btn-sm btn-outline-light ms-3" onclick="location.reload()">
            <i class="fas fa-refresh me-1"></i>重新加载
        </button>
    </div>

    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>
                ChatBI 评估测试平台
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/chart-library}">
                            <i class="fas fa-database me-1"></i>图表库
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" th:href="@{/chart}">
                            <i class="fas fa-chart-bar me-1"></i>图表知识管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/test}">
                            <i class="fas fa-play-circle me-1"></i>测试执行
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/annotation}">
                            <i class="fas fa-tags me-1"></i>问答记录标注
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/testset}">
                            <i class="fas fa-database me-1"></i>测试集管理
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="container-fluid py-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2>
                        <i class="fas fa-chart-bar me-2 text-primary"></i>
                        图表知识管理
                    </h2>
                    <div>
                        <span class="badge bg-info me-2" id="chartCountBadge">
                            <i class="fas fa-chart-line me-1"></i>
                            图表总数: <span id="chartCount">0</span>
                        </span>
                        <button class="btn btn-primary" id="refreshBtn">
                            <i class="fas fa-sync-alt me-1"></i>刷新数据
                        </button>
                    </div>
                </div>
                <p class="text-muted">通过调整特征，提升图表在向量召回上的效果</p>
            </div>
        </div>

        <!-- 🧪 召回测试区域 (置顶，突出显示) -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card test-area-highlight">
                    <div class="card-header test-driven-header text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h4 class="card-title mb-0">
                                <i class="fas fa-flask me-2"></i>
                                🧪 召回测试区域
                            </h4>
                            <div>
                                <span class="badge bg-light text-primary">
                                    <i class="fas fa-bullseye me-1"></i>测试驱动优化
                                </span>
                                <span class="badge bg-success ms-2" id="testModeIndicator">
                                    <i class="fas fa-globe me-1"></i>全局测试模式
                                </span>
                            </div>
                        </div>
                        <small class="text-light">
                            <i class="fas fa-info-circle me-1"></i>
                            通过测试问题验证图表召回效果，指导特征优化方向
                        </small>
                    </div>
                    <div class="card-body">
                        <!-- 测试问题输入区域 -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <label for="testQuestion" class="form-label fw-bold">
                                    <i class="fas fa-question-circle me-1 text-primary"></i>
                                    测试问题
                                </label>
                                <div class="input-group input-group-lg">
                                    <input type="text" class="form-control" id="testQuestion" 
                                           placeholder="输入问题测试召回效果，例如：销售业绩分析、月度趋势图表...">
                                    <button class="btn btn-primary btn-lg" type="button" id="testRecallBtn">
                                        <i class="fas fa-search me-2"></i>开始测试
                                    </button>
                                </div>
                                <div class="form-text">
                                    <i class="fas fa-lightbulb me-1"></i>
                                    <strong>智能测试模式：</strong>
                                    <span class="text-success">未选择图表时</span> = 全局召回测试；
                                    <span class="text-primary">选择图表后</span> = 针对性优化测试
                                </div>
                            </div>
                        </div>
                        
                        <!-- 召回结果表格 (实时显示) -->
                        <div id="testResults" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-line me-2 text-success"></i>
                                    召回结果
                                </h5>
                                <div>
                                    <span class="badge bg-success fs-6" id="matchedBadge" style="display: none;">
                                        <i class="fas fa-check me-1"></i>目标图表匹配成功
                                    </span>
                                    <span class="badge bg-warning fs-6" id="notMatchedBadge" style="display: none;">
                                        <i class="fas fa-exclamation-triangle me-1"></i>目标图表未在前列
                                    </span>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table id="recallResultTable" class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>排名</th>
                                            <th>ViewID</th>
                                            <th>图表名称</th>
                                            <th>图表规格</th>
                                            <th>命中特征</th>
                                            <th>特征权重</th>
                                            <th>得分</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- 测试结果将通过Ajax加载 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 下方：图表列表 + 特征管理 -->
        <div class="row">
            <!-- 左侧：📊 图表列表 (选择优化目标) -->
            <div class="col-lg-6">
                <div class="card section-card h-100">
                    <div class="card-header chart-list-header text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>
                            📊 图表列表
                        </h5>
                        <small class="text-light">选择需要优化的目标图表</small>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="chartTable" class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>ViewID</th>
                                        <th>图表名称</th>
                                        <th>图表规格</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 数据将通过Ajax加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧：⚙️ 特征管理 (针对性优化) -->
            <div class="col-lg-6">
                <div class="card section-card h-100">
                    <div class="card-header feature-mgmt-header text-dark">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-cogs me-2"></i>
                                    ⚙️ 特征管理
                                </h5>
                                <small>针对选中图表进行特征优化</small>
                            </div>
                            <button class="btn btn-sm btn-success" id="addFeatureBtn" disabled>
                                <i class="fas fa-plus me-1"></i>添加特征
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="noChartSelected" class="text-center text-muted py-5">
                            <i class="fas fa-hand-pointer fa-3x mb-3"></i>
                            <p class="fs-5">请先从左侧选择一个图表</p>
                            <small class="text-muted">选择图表后可以管理其特征，提升召回效果</small>
                        </div>
                        
                        <div id="featureManagement" style="display: none;">
                            <div class="mb-3 p-3 bg-light rounded">
                                <h6 id="selectedChartInfo" class="text-primary mb-1"></h6>
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    特征数量: <span id="featureCount" class="fw-bold">0</span>
                                </small>
                            </div>
                            
                            <!-- 特征列表 -->
                            <div class="table-responsive">
                                <table id="featureTable" class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>特征</th>
                                            <th>权重</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- 特征数据将通过Ajax加载 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 添加/编辑特征模态框 -->
    <div class="modal fade" id="featureModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="featureModalTitle">添加特征</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="featureForm">
                        <input type="hidden" id="featureId">
                        <input type="hidden" id="featureViewId">
                        <div class="mb-3">
                            <label for="featureName" class="form-label">特征描述</label>
                            <textarea class="form-control" id="featureName" rows="3" required 
                                      placeholder="请输入特征描述，例如：销售业绩分析、月度销售趋势等"></textarea>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                特征描述应该准确反映图表的用途和内容
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="featureWeight" class="form-label">权重</label>
                            <input type="number" class="form-control" id="featureWeight" 
                                   min="0" max="1" step="0.1" value="1.0" required>
                            <div class="form-text">权重范围：0-1，数值越大优先级越高</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveFeatureBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JSON规格查看模态框 -->
    <div class="modal fade" id="specModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-code me-2"></i>
                        图表规格详情
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <h6 id="specChartInfo" class="text-primary"></h6>
                    </div>
                    <div class="spec-full" id="specContent">
                        <!-- JSON内容将在这里显示 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="copySpecToClipboard()">
                        <i class="fas fa-copy me-1"></i>复制JSON
                    </button>
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // 简化的错误处理
        window.addEventListener('error', function(e) {
            if (e.target.tagName === 'LINK' || e.target.tagName === 'SCRIPT') {
                console.error('资源加载失败:', e.target.src || e.target.href);
                // 显示错误横幅
                document.getElementById('errorMessage').textContent = '部分资源加载失败，功能可能受限';
                document.getElementById('errorBanner').style.display = 'block';
            }
        });
        
        // 隐藏错误横幅
        function hideErrorBanner() {
            document.getElementById('errorBanner').style.display = 'none';
        }
        
        // 从元素中获取数据并显示JSON规格详情
        function showSpecModalFromElement(element) {
            const viewId = element.getAttribute('data-view-id');
            const viewName = element.getAttribute('data-view-name');
            const specData = element.getAttribute('data-spec');
            
            let spec;
            try {
                spec = JSON.parse(specData);
            } catch (e) {
                spec = specData;
            }
            
            showSpecModal(viewId, viewName, spec);
        }
        
        // 显示JSON规格详情
        function showSpecModal(viewId, viewName, spec) {
            document.getElementById('specChartInfo').textContent = `${viewName} (${viewId})`;
            
            // 格式化JSON
            let formattedSpec = '';
            try {
                if (typeof spec === 'string') {
                    formattedSpec = JSON.stringify(JSON.parse(spec), null, 2);
                } else {
                    formattedSpec = JSON.stringify(spec, null, 2);
                }
            } catch (e) {
                formattedSpec = spec || '无规格数据';
            }
            
            document.getElementById('specContent').textContent = formattedSpec;
            
            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('specModal'));
            modal.show();
        }
        
        // 复制JSON到剪贴板
        function copySpecToClipboard() {
            const content = document.getElementById('specContent').textContent;
            navigator.clipboard.writeText(content).then(function() {
                // 简单提示
                const btn = event.target.closest('button');
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check me-1"></i>已复制';
                btn.classList.add('btn-success');
                btn.classList.remove('btn-secondary');
                
                setTimeout(function() {
                    btn.innerHTML = originalText;
                    btn.classList.remove('btn-success');
                    btn.classList.add('btn-secondary');
                }, 2000);
            }).catch(function(err) {
                console.error('复制失败:', err);
                alert('复制失败，请手动选择文本复制');
            });
        }
        

    </script>
    
    <!-- jQuery -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js" 
            onerror="this.onerror=null;this.src='https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js';"></script>
    
    <!-- Bootstrap -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.2/js/bootstrap.bundle.min.js"
            onerror="this.onerror=null;this.src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js';"></script>
    
    <!-- DataTables -->
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"
            onerror="this.onerror=null;this.src='https://cdnjs.cloudflare.com/ajax/libs/datatables/1.13.7/js/jquery.dataTables.min.js';"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"
            onerror="this.onerror=null;this.src='https://cdnjs.cloudflare.com/ajax/libs/datatables/1.13.7/js/dataTables.bootstrap5.min.js';"></script>
    
    <!-- 应用脚本 -->
    <script th:src="@{/js/common.js}"></script>
    <script th:src="@{/js/chart-knowledge.js}"></script>
    
    <script>
        // 页面加载完成后隐藏错误横幅（如果没有错误）
        setTimeout(function() {
            if (typeof $ !== 'undefined') {
                hideErrorBanner();
                console.log('页面资源加载完成');
            }
        }, 2000);
    </script>
</body>
</html>