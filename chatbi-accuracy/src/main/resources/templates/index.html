<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="context-path" th:content="${#request.contextPath}">
    <title>ChatBI 评估测试平台 - 首页</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.2/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义CSS -->
    <link th:href="@{/css/custom.css}" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" th:href="@{/}">
                <i class="fas fa-chart-line me-2"></i>
                ChatBI 评估测试平台
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" th:href="@{/}">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/chart-library}">
                            <i class="fas fa-database me-1"></i>图表库
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/chart}">
                            <i class="fas fa-chart-bar me-1"></i>图表知识管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/test}">
                            <i class="fas fa-play-circle me-1"></i>测试执行
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/annotation}">
                            <i class="fas fa-tags me-1"></i>问答记录标注
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/testset}">
                            <i class="fas fa-database me-1"></i>测试集管理
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="container-fluid py-4">
        <!-- 欢迎横幅 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="jumbotron bg-gradient-primary text-white p-5 rounded">
                    <h1 class="display-4">
                        <i class="fas fa-chart-line me-3"></i>
                        ChatBI 评估测试平台
                    </h1>
                    <p class="lead">智能化数据分析Agent的全链路测试与评估系统</p>
                    <hr class="my-4">
                    <p>通过图表知识管理、测试执行、问答标注和测试集管理，全面提升ChatBI系统的准确性和可靠性。</p>
                </div>
            </div>
        </div>

        <!-- 功能模块卡片 -->
        <div class="row g-4">
            <!-- 图表知识管理 -->
            <div class="col-lg-6 col-xl-3">
                <div class="card h-100 shadow-sm hover-card">
                    <div class="card-body text-center">
                        <div class="feature-icon bg-primary text-white rounded-circle mx-auto mb-3">
                            <i class="fas fa-chart-bar fa-2x"></i>
                        </div>
                        <h5 class="card-title">图表知识管理</h5>
                        <p class="card-text text-muted">
                            管理图表特征，优化向量召回效果，提升图表检索准确性
                        </p>
                        <div class="mt-auto">
                            <a th:href="@{/chart}" class="btn btn-primary">
                                <i class="fas fa-arrow-right me-1"></i>进入管理
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 测试执行 -->
            <div class="col-lg-6 col-xl-3">
                <div class="card h-100 shadow-sm hover-card">
                    <div class="card-body text-center">
                        <div class="feature-icon bg-success text-white rounded-circle mx-auto mb-3">
                            <i class="fas fa-play-circle fa-2x"></i>
                        </div>
                        <h5 class="card-title">测试执行</h5>
                        <p class="card-text text-muted">
                            对Agent全链路进行快速测试，查看召回结果和执行日志
                        </p>
                        <div class="mt-auto">
                            <a th:href="@{/test}" class="btn btn-success">
                                <i class="fas fa-arrow-right me-1"></i>开始测试
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 问答记录标注 -->
            <div class="col-lg-6 col-xl-3">
                <div class="card h-100 shadow-sm hover-card">
                    <div class="card-body text-center">
                        <div class="feature-icon bg-warning text-white rounded-circle mx-auto mb-3">
                            <i class="fas fa-tags fa-2x"></i>
                        </div>
                        <h5 class="card-title">问答记录标注</h5>
                        <p class="card-text text-muted">
                            人工标注用户问答记录，构建高质量测试用例数据集
                        </p>
                        <div class="mt-auto">
                            <a th:href="@{/annotation}" class="btn btn-warning">
                                <i class="fas fa-arrow-right me-1"></i>开始标注
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 测试集管理 -->
            <div class="col-lg-6 col-xl-3">
                <div class="card h-100 shadow-sm hover-card">
                    <div class="card-body text-center">
                        <div class="feature-icon bg-info text-white rounded-circle mx-auto mb-3">
                            <i class="fas fa-database fa-2x"></i>
                        </div>
                        <h5 class="card-title">测试集管理</h5>
                        <p class="card-text text-muted">
                            基于测试用例构建测试集，进行批量测试和定量评估
                        </p>
                        <div class="mt-auto">
                            <a th:href="@{/testset}" class="btn btn-info">
                                <i class="fas fa-arrow-right me-1"></i>管理测试集
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统状态概览 -->
        <div class="row mt-5">
            <div class="col-12">
                <h3 class="mb-4">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    系统状态概览
                </h3>
            </div>
        </div>

        <div class="row g-4">
            <!-- 图表数量统计 -->
            <div class="col-md-3">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h2 class="text-primary" id="chartCount">-</h2>
                        <p class="card-text">图表总数</p>
                    </div>
                </div>
            </div>

            <!-- 测试用例数量 -->
            <div class="col-md-3">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h2 class="text-success" id="testCaseCount">-</h2>
                        <p class="card-text">测试用例</p>
                    </div>
                </div>
            </div>

            <!-- 标注记录数量 -->
            <div class="col-md-3">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h2 class="text-warning" id="annotationCount">-</h2>
                        <p class="card-text">标注记录</p>
                    </div>
                </div>
            </div>

            <!-- 最近测试准确率 -->
            <div class="col-md-3">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h2 class="text-info" id="accuracyRate">-</h2>
                        <p class="card-text">准确率</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="row mt-5">
            <div class="col-12">
                <h3 class="mb-4">
                    <i class="fas fa-bolt me-2"></i>
                    快速操作
                </h3>
            </div>
        </div>

        <div class="row g-3">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-search me-1"></i>
                            快速测试
                        </h6>
                        <div class="input-group mb-2">
                            <input type="text" class="form-control" id="quickTestInput" placeholder="输入测试问题...">
                            <button class="btn btn-primary" type="button" id="quickTestBtn">
                                <i class="fas fa-play"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-plus me-1"></i>
                            添加图表特征
                        </h6>
                        <a th:href="@{/chart}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-plus me-1"></i>新增特征
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-history me-1"></i>
                            最近测试记录
                        </h6>
                        <a th:href="@{/test}" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-eye me-1"></i>查看记录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-light text-center py-3 mt-5">
        <div class="container">
            <span class="text-muted">ChatBI 评估测试平台 &copy; 2024</span>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.2/js/bootstrap.bundle.min.js"></script>
    <script th:src="@{/js/common.js}"></script>
    <script th:src="@{/js/index.js}"></script>
</body>
</html>