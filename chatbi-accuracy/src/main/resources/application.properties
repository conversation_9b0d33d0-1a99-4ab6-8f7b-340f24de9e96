spring.application.name=chatbi-accuracy
spring.config.import=optional:cms:fs-bi-agent

server.port=8080
server.servlet.context-path=/agent-test

spring.profiles.active=fstest
spring.main.allow-bean-definition-overriding=true
spring.main.allow-circular-references=true

spring.mvc.static-path-pattern=/**
spring.web.resources.static-locations=classpath:/static/

spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html
spring.thymeleaf.mode=HTML
spring.thymeleaf.encoding=UTF-8
spring.thymeleaf.cache=false
spring.thymeleaf.servlet.content-type=text/html

spring.freemarker.enabled=false

logging.level.root=INFO
logging.level.com.fxiaoke.chatbi=DEBUG

management.health.redis.enabled=false

spring.aop.proxy-target-class=true
