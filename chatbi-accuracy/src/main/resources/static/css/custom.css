/* ChatBI 评估测试平台 - 自定义样式 */

/* ===== 导航栏样式 ===== */
.navbar-brand {
    font-weight: 600;
    font-size: 1.2rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: all 0.2s ease;
    border-radius: 4px;
    margin: 0 2px;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    font-weight: 600;
}

.breadcrumb {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 8px 16px;
    margin-bottom: 0;
}

.breadcrumb-item a {
    text-decoration: none;
    color: #6c757d;
}

.breadcrumb-item a:hover {
    color: #007bff;
}

/* ===== ChatBI对话标注平台样式 ===== */

/* 三栏布局样式 */
.session-item {
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.session-item:hover {
    background-color: #f8f9fa !important;
    border-left-color: #007bff;
}

.session-item.bg-light.border-primary {
    background-color: #e3f2fd !important;
    border-left-color: #007bff;
    border-width: 3px;
}

.conversation-item {
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.conversation-item:hover {
    background-color: #f8f9fa !important;
    border-left-color: #28a745;
}

/* 选中的对话项样式 - 使用更好的视觉效果，强制覆盖所有可能的样式 */
.conversation-item.selected-conversation {
    background-color: #e3f2fd !important;
    border-left: 4px solid #2196f3 !important;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2) !important;
    color: #333 !important;
}

.conversation-item.selected-conversation h6 {
    color: #1565c0 !important;
    font-weight: 600 !important;
}

.conversation-item.selected-conversation .badge {
    background-color: #1976d2 !important;
    color: white !important;
}

.conversation-item.selected-conversation .text-muted {
    color: #424242 !important;
}

.conversation-item.selected-conversation .bg-light {
    background-color: #f8f9fa !important;
    color: #333 !important;
}

.conversation-item.selected-conversation * {
    color: inherit !important;
}

.conversation-item.selected-conversation .text-muted {
    color: #6c757d !important;
}

/* 保持原有的bg-primary样式作为备用 */
.conversation-item.bg-primary {
    background-color: #007bff !important;
    color: white !important;
    border-left-color: #0056b3;
}

.conversation-item.bg-primary * {
    color: white !important;
}

/* 组件标注样式 */
.component-annotation {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 8px;
    background-color: #f8f9fa;
    transition: all 0.2s ease;
    margin-bottom: 0.5rem;
}

.component-annotation:hover {
    border-color: #007bff;
    box-shadow: 0 2px 4px rgba(0,123,255,0.1);
}

.component-annotation .form-label {
    margin-bottom: 4px;
    font-size: 12px;
    font-weight: 600;
}

.component-score, .component-tags, .component-comments {
    font-size: 11px;
    padding: 2px 6px;
}

.component-annotation .row {
    margin-bottom: 4px;
}

.component-annotation textarea {
    resize: none;
}

/* 表单样式优化 */
.form-label-sm {
    font-size: 11px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 2px;
}

.form-control-sm, .form-select-sm {
    font-size: 12px;
    padding: 3px 6px;
}

/* 标注页面特定的紧凑布局 */
.annotation-page .card-header {
    padding: 0.5rem 0.75rem;
}

.annotation-page .card-body {
    padding: 0.5rem;
}

.annotation-page .mb-2 {
    margin-bottom: 0.5rem !important;
}

.annotation-page .mb-3 {
    margin-bottom: 0.75rem !important;
}

.annotation-page .py-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
}

/* 评分按钮组样式 */
.btn-group .btn-sm {
    font-size: 12px;
    padding: 4px 8px;
}

/* 滚动条样式 */
.card-body::-webkit-scrollbar {
    width: 6px;
}

.card-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.card-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.card-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* ===== CSS变量定义 ===== */
:root {
    /* 主色调 */
    --primary-color: #007bff;
    --primary-dark: #0056b3;
    --success-color: #28a745;
    --success-dark: #1e7e34;
    --warning-color: #ffc107;
    --warning-dark: #e0a800;
    --info-color: #17a2b8;
    --info-dark: #117a8b;
    --danger-color: #dc3545;
    --danger-dark: #c82333;
    
    /* 中性色 */
    --gray-50: #f8f9fa;
    --gray-100: #e9ecef;
    --gray-200: #dee2e6;
    --gray-300: #ced4da;
    --gray-400: #adb5bd;
    --gray-500: #6c757d;
    --gray-600: #495057;
    --gray-700: #343a40;
    --gray-800: #212529;
    
    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 3rem;
    
    /* 圆角 */
    --border-radius-sm: 4px;
    --border-radius-md: 6px;
    --border-radius-lg: 8px;
    --border-radius-xl: 12px;
    
    /* 阴影 */
    --shadow-sm: 0 2px 10px rgba(0,0,0,0.08);
    --shadow-md: 0 4px 12px rgba(0,0,0,0.15);
    --shadow-lg: 0 10px 25px rgba(0,0,0,0.1);
    --shadow-xl: 0 10px 40px rgba(0,0,0,0.15);
    
    /* 过渡 */
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;
}

/* ===== 全局样式 ===== */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--gray-50);
    line-height: 1.6;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* ===== 布局样式 ===== */
.main-content {
    flex: 1;
}

.footer {
    margin-top: auto;
}

/* ===== 导航栏样式 ===== */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
    transition: var(--transition-fast);
}

.navbar-brand:hover {
    transform: scale(1.05);
}

.nav-link {
    font-weight: 500;
    transition: var(--transition-normal);
    border-radius: var(--border-radius-md);
    margin: 0 var(--spacing-xs);
}

.nav-link:hover {
    transform: translateY(-1px);
    background-color: rgba(255, 255, 255, 0.1);
}

.nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-md);
}

/* ===== 面包屑导航样式 ===== */
.breadcrumb {
    background-color: transparent;
    padding: var(--spacing-sm) 0;
    margin-bottom: var(--spacing-md);
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: var(--gray-500);
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

.breadcrumb-item a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* ===== 全局加载遮罩样式 ===== */
.global-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9998;
}

.loading-content {
    background-color: white;
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    text-align: center;
    color: var(--gray-700);
    font-weight: 500;
}

/* ===== Toast样式优化 ===== */
.toast-container .toast {
    min-width: 300px;
    box-shadow: var(--shadow-md);
    border: none;
}

.toast-header {
    background-color: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.toast-body {
    padding: var(--spacing-md);
    font-weight: 500;
}

/* 主页样式 */
.jumbotron {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.feature-icon {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.hover-card {
    transition: all 0.3s ease;
    border: none;
}

.hover-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
}

.table-hover tbody tr:hover {
    background-color: rgba(0,123,255,0.05);
}

/* 按钮样式 */
.btn {
    font-weight: 500;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    border: none;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
    border: none;
}

/* 模态框样式 */
.modal-content {
    border: none;
    box-shadow: 0 10px 40px rgba(0,0,0,0.15);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
}

/* 表单样式 */
.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* 状态指示器 */
.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
}

.status-success {
    background-color: #28a745;
}

.status-warning {
    background-color: #ffc107;
}

.status-danger {
    background-color: #dc3545;
}

.status-info {
    background-color: #17a2b8;
}

/* 加载动画 */
.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    display: inline-block;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .jumbotron {
        padding: 2rem 1rem;
    }
    
    .jumbotron h1 {
        font-size: 2rem;
    }
    
    .feature-icon {
        width: 60px;
        height: 60px;
    }
}

/* DataTables样式优化 */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate {
    margin-bottom: 1rem;
}

.dataTables_wrapper .dataTables_filter input {
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 0.375rem 0.75rem;
    margin-left: 0.5rem;
}

/* 权重徽章样式 */
.weight-badge {
    font-size: 0.75em;
    padding: 0.25em 0.5em;
    border-radius: 0.25rem;
    font-weight: 600;
}

/* 测试结果分数样式 */
.test-result-score {
    font-weight: bold;
    font-size: 1.1em;
}

.test-result-high {
    color: #28a745;
}

.test-result-medium {
    color: #ffc107;
}

.test-result-low {
    color: #dc3545;
}

/* 工具提示样式 */
.tooltip-inner {
    max-width: 300px;
    text-align: left;
    background-color: #333;
    color: white;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* 警告框样式 */
.alert {
    border: none;
    border-radius: 8px;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    color: #155724;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: #721c24;
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: #856404;
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    color: #0c5460;
}

/* 页脚样式 */
footer {
    border-top: 1px solid #e9ecef;
    background-color: #f8f9fa !important;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 图表知识管理页面特定样式 */
#chartCountBadge {
    animation: pulse 2s infinite;
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
    box-shadow: 0 2px 4px rgba(0,123,255,0.3);
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

#featureCount {
    font-weight: 700;
    color: #007bff;
    font-size: 1.2em;
}

#matchedBadge {
    animation: bounce 1s ease-in-out;
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
    font-weight: 600;
}

#notMatchedBadge {
    animation: shake 0.5s ease-in-out;
    background: linear-gradient(45deg, #dc3545, #e74c3c);
    border: none;
    font-weight: 600;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* 表格行样式 */
.table-warning {
    background-color: rgba(255, 193, 7, 0.1) !important;
    border-color: rgba(255, 193, 7, 0.2) !important;
}

/* 文本换行 */
.text-break {
    word-break: break-word !important;
    overflow-wrap: break-word !important;
}

/* 表格活跃行 */
.table-active {
    background-color: rgba(0, 123, 255, 0.1) !important;
    border-color: rgba(0, 123, 255, 0.2) !important;
}

/* 刷新按钮动画 */
#refreshBtn:hover i {
    animation: spin 1s linear infinite;
}

/* 测试结果区域 */
#testResults {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background-color: #f8f9fa;
}

/* 特征管理区域 */
#featureManagement {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background-color: white;
}

/* 响应式优化 */
@media (max-width: 992px) {
    #chartCountBadge {
        font-size: 0.9rem;
        padding: 0.4rem 0.8rem;
    }
    
    .text-break {
        font-size: 0.85rem;
    }
}

@media (max-width: 768px) {
    #chartCountBadge {
        font-size: 0.8rem;
        padding: 0.3rem 0.6rem;
        margin-bottom: 0.5rem;
    }
    
    .text-break {
        font-size: 0.8rem;
    }
    
    .btn-sm {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
}

/* 测试执行页面特定样式 */
.log-item {
    border-left: 3px solid #007bff;
    padding-left: 1rem;
}

.log-item:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.border-start {
    border-left: 1px solid #dee2e6 !important;
}

.border-2 {
    border-width: 2px !important;
}

.border-3 {
    border-width: 3px !important;
}

#actionLogs {
    max-height: 300px;
    overflow-y: auto;
}

#actionLogs .log-item {
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    border-radius: 4px;
}

#recallResults .table {
    font-size: 0.9rem;
}

#recallResults .badge {
    font-size: 0.75rem;
}

.spinner-border {
    animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
    to { transform: rotate(360deg); }
}

#statusBadge {
    font-weight: 600;
    padding: 0.5rem 1rem;
}

#timeBadge {
    font-weight: 600;
    padding: 0.5rem 1rem;
}

/* 文本截断 */
.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px;
}

/* 警告容器 */
.alert-container {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 1050;
    max-width: 400px;
    width: 100%;
}

.alert-container .alert {
    margin-bottom: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* 测试详情模态框 */
#testDetailModal .modal-dialog {
    max-width: 90%;
}

#testDetailModal .table td {
    vertical-align: top;
    padding: 0.75rem;
}

#testDetailModal .table td:first-child {
    font-weight: 600;
    background-color: #f8f9fa;
    width: 150px;
}

#detailActionLogs {
    max-height: 200px;
    overflow-y: auto;
}

#detailActionLogs .log-item {
    font-size: 0.85rem;
}