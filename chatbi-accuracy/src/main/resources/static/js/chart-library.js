/**
 * 图表库页面JavaScript
 * 功能：数据加载、搜索过滤、列显示控制
 */

// 全局变量
let chartLibraryData = null;
let chartTable = null;
let themeMap = new Map(); // 主题ID到名称的映射
let chartTypeNameMap = new Map(); // 图表类型到中文名称的映射

// 列配置
const columnConfig = [
    { key: 'viewId', title: 'ViewID', defaultVisible: true },
    { key: 'viewName', title: '图表名称', defaultVisible: true },
    { key: 'chartType', title: '图表类型', defaultVisible: true },
    { key: 'schemaId', title: '主题ID', defaultVisible: true },
    { key: 'schemaName', title: '主题名称', defaultVisible: true },
    { key: 'tenantId', title: '租户ID', defaultVisible: true },
    { key: 'spec', title: '图表规格', defaultVisible: true },
    { key: 'dimensionNames', title: '维度字段', defaultVisible: true },
    { key: 'measureNames', title: '指标字段', defaultVisible: true },
    { key: 'filterNames', title: '过滤字段', defaultVisible: true },
    { key: 'usageCount', title: '使用次数', defaultVisible: true },
    { key: 'lastModifiedTime', title: '最后修改时间', defaultVisible: true }
];

$(document).ready(function() {
    console.log('图表库页面初始化');
    
    // 初始化页面
    initPage();
    
    // 绑定事件
    bindEvents();
});

/**
 * 初始化页面
 */
function initPage() {
    // 生成列控制开关
    generateColumnToggles();
    
    // 加载图表库数据
    loadChartLibraryData();
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 刷新按钮
    $('#refreshBtn').click(function() {
        loadChartLibraryData();
    });
    
    // 重置过滤器按钮
    $('#resetFiltersBtn').click(function() {
        resetFilters();
    });
    
    // 搜索输入框
    $('#searchInput').on('keyup', function() {
        applyCustomFilters();
    });
    
    // 图表类型过滤
    $('#chartTypeFilter').change(function() {
        applyCustomFilters();
    });
    
    // 主题过滤
    $('#themeFilter').change(function() {
        applyCustomFilters();
    });
    
    // 租户ID过滤
    $('#tenantFilter').on('keyup', function() {
        applyCustomFilters();
    });
    
    // 显示/隐藏全部列
    $('#showAllColumns').click(function() {
        toggleAllColumns(true);
    });
    
    $('#hideAllColumns').click(function() {
        toggleAllColumns(false);
    });
}

/**
 * 加载图表库数据
 */
function loadChartLibraryData() {
    console.log('开始加载图表库数据');
    
    // 显示加载状态
    showLoading();
    
    API.post('/getChartLibraryData', {})
        .then(response => {
            console.log('图表库数据加载成功', response);
            
            if (response.code === 200) {
                chartLibraryData = response.data;
                
                // 更新统计信息
                updateStatistics();
                
                // 构建主题映射
                buildThemeMap();
                
                // 构建图表类型名称映射
                buildChartTypeNameMap();
                
                // 更新过滤器选项
                updateFilterOptions();
                
                // 初始化表格
                initDataTable();
                
                hideLoading();
            } else {
                console.error('加载图表库数据失败:', response.message);
                Utils.message.error('加载数据失败: ' + response.message);
                hideLoading();
            }
        })
        .catch(error => {
            console.error('请求失败:', error);
            Utils.message.error('请求失败，请检查网络连接');
            hideLoading();
        });
}

/**
 * 更新统计信息
 */
function updateStatistics() {
    if (!chartLibraryData) return;
    
    $('#totalCharts').text(chartLibraryData.totalCharts || 0);
    $('#totalThemes').text(chartLibraryData.totalThemes || 0);
}

/**
 * 构建主题映射
 */
function buildThemeMap() {
    if (!chartLibraryData || !chartLibraryData.themes) return;
    
    themeMap.clear();
    chartLibraryData.themes.forEach(theme => {
        themeMap.set(theme.schemaId, theme.schemaName || theme.schemaId);
    });
}

/**
 * 构建图表类型名称映射
 */
function buildChartTypeNameMap() {
    if (!chartLibraryData || !chartLibraryData.statInfo || !chartLibraryData.statInfo.chartTypeStats) return;
    
    chartTypeNameMap.clear();
    chartLibraryData.statInfo.chartTypeStats.forEach(stat => {
        chartTypeNameMap.set(stat.category, stat.categoryName);
    });
}

/**
 * 更新过滤器选项
 */
function updateFilterOptions() {
    if (!chartLibraryData) return;
    
    // 更新图表类型过滤器
    const chartTypes = [...new Set(chartLibraryData.charts.map(chart => chart.chartType))].filter(type => type);
    const chartTypeSelect = $('#chartTypeFilter');
    chartTypeSelect.empty().append('<option value="">全部类型</option>');
    
    if (chartLibraryData.statInfo && chartLibraryData.statInfo.chartTypeStats) {
        chartLibraryData.statInfo.chartTypeStats.forEach(stat => {
            chartTypeSelect.append(`<option value="${stat.category}">${stat.categoryName} (${stat.count})</option>`);
        });
    }
    
    // 更新主题过滤器
    const themeSelect = $('#themeFilter');
    themeSelect.empty().append('<option value="">全部主题</option>');
    
    if (chartLibraryData.statInfo && chartLibraryData.statInfo.themeStats) {
        chartLibraryData.statInfo.themeStats.forEach(stat => {
            themeSelect.append(`<option value="${stat.category}">${stat.categoryName} (${stat.count})</option>`);
        });
    }
}

/**
 * 初始化DataTable
 */
function initDataTable() {
    if (!chartLibraryData || !chartLibraryData.charts) return;
    
    // 销毁现有表格
    if (chartTable) {
        chartTable.destroy();
        $('#chartLibraryTable tbody').empty();
    }
    
    // 准备表格数据
    const tableData = chartLibraryData.charts.map(chart => {
        return {
            viewId: chart.viewId || '',
            viewName: chart.viewName || '',
            chartType: formatChartType(chart.chartType),
            schemaId: chart.schemaId || '',
            schemaName: themeMap.get(chart.schemaId) || chart.schemaId || '',
            tenantId: formatTenantId(chart.tenantId),
            spec: formatSpec(chart.spec, chart.viewId, chart.viewName),
            dimensionNames: formatArrayField(chart.dimensionNames),
            measureNames: formatArrayField(chart.measureNames),
            filterNames: formatArrayField(chart.filterNames),
            usageCount: chart.usageCount || 0,
            lastModifiedTime: formatDateTime(chart.lastModifiedTime),
            // 原始数据，用于过滤
            _chartType: chart.chartType,
            _schemaId: chart.schemaId,
            _tenantId: chart.tenantId,
            _spec: chart.spec
        };
    });
    
    // 初始化DataTable
    chartTable = $('#chartLibraryTable').DataTable({
        data: tableData,
        columns: [
            { data: 'viewId', title: 'ViewID' },
            { data: 'viewName', title: '图表名称' },
            { data: 'chartType', title: '图表类型' },
            { data: 'schemaId', title: '主题ID' },
            { data: 'schemaName', title: '主题名称' },
            { data: 'tenantId', title: '租户ID' },
            { data: 'spec', title: '图表规格' },
            { data: 'dimensionNames', title: '维度字段' },
            { data: 'measureNames', title: '指标字段' },
            { data: 'filterNames', title: '过滤字段' },
            { data: 'usageCount', title: '使用次数' },
            { data: 'lastModifiedTime', title: '最后修改时间' }
        ],
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "全部"]],
        order: [[11, 'desc']], // 按最后修改时间降序
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/zh.json'
        },
        responsive: true,
        scrollX: true,
        searching: false, // 禁用默认搜索功能
        dom: '<"d-flex justify-content-between"<"d-flex align-items-center"l><"d-flex align-items-center">>rtip',
        initComplete: function() {
            console.log('DataTable初始化完成');
            
            // 应用默认列显示设置
            applyDefaultColumnVisibility();
        }
    });
    
    // 添加自定义过滤函数
    $.fn.dataTable.ext.search.push(function(settings, data, dataIndex) {
        if (settings.nTable.id !== 'chartLibraryTable') {
            return true;
        }
        
        const searchValue = $('#searchInput').val().toLowerCase();
        const chartType = $('#chartTypeFilter').val();
        const theme = $('#themeFilter').val();
        const tenant = $('#tenantFilter').val().toLowerCase();
        
        const rowData = chartTable.row(dataIndex).data();
        
        // 关键字搜索 - 搜索图表名称、ViewID、规格等
        if (searchValue) {
            const searchableText = [
                rowData.viewId,
                rowData.viewName,
                rowData.schemaName,
                JSON.stringify(rowData._spec || ''),
                (rowData.dimensionNames || '').replace(/<[^>]*>/g, ''),
                (rowData.measureNames || '').replace(/<[^>]*>/g, ''),
                (rowData.filterNames || '').replace(/<[^>]*>/g, '')
            ].join(' ').toLowerCase();
            
            if (searchableText.indexOf(searchValue) === -1) {
                return false;
            }
        }
        
        // 图表类型过滤
        if (chartType && rowData._chartType !== chartType) {
            return false;
        }
        
        // 主题过滤
        if (theme && rowData._schemaId !== theme) {
            return false;
        }
        
        // 租户过滤
        if (tenant && rowData._tenantId.toLowerCase().indexOf(tenant) === -1) {
            return false;
        }
        
        return true;
    });
}

/**
 * 应用自定义过滤器
 */
function applyCustomFilters() {
    if (chartTable) {
        chartTable.draw();
    }
}

/**
 * 重置过滤器
 */
function resetFilters() {
    $('#searchInput').val('');
    $('#chartTypeFilter').val('');
    $('#themeFilter').val('');
    $('#tenantFilter').val('');
    
    if (chartTable) {
        chartTable.draw();
    }
}

/**
 * 生成列控制开关
 */
function generateColumnToggles() {
    const container = $('#columnToggles');
    container.empty();
    
    columnConfig.forEach((col, index) => {
        const colHtml = `
            <div class="col-md-3 col-sm-4 col-6">
                <div class="form-check">
                    <input class="form-check-input column-toggle-cb" type="checkbox" 
                           data-column="${index}" ${col.defaultVisible ? 'checked' : ''}>
                    <label class="form-check-label" style="font-size: 0.9em;">
                        ${col.title}
                    </label>
                </div>
            </div>
        `;
        container.append(colHtml);
    });
    
    // 绑定列切换事件
    $('.column-toggle-cb').change(function() {
        if (chartTable) {
            const columnIndex = parseInt($(this).data('column'));
            const column = chartTable.column(columnIndex);
            column.visible($(this).is(':checked'));
        }
    });
}

/**
 * 应用默认列显示设置
 */
function applyDefaultColumnVisibility() {
    if (!chartTable) return;
    
    columnConfig.forEach((col, index) => {
        chartTable.column(index).visible(col.defaultVisible);
    });
}

/**
 * 切换所有列显示状态
 */
function toggleAllColumns(visible) {
    $('.column-toggle-cb').prop('checked', visible).trigger('change');
}

/**
 * 格式化图表类型
 */
function formatChartType(chartType) {
    if (!chartType) return '<span class="text-muted">未知</span>';
    
    // 使用后端返回的中文名称映射
    const chineseTypeName = chartTypeNameMap.get(chartType) || chartType;
    return `<span class="badge bg-primary chart-type-badge">${chineseTypeName}</span>`;
}

/**
 * 格式化租户ID
 */
function formatTenantId(tenantId) {
    if (!tenantId) return '<span class="text-muted">-</span>';
    
    return `<span class="tenant-badge">${tenantId}</span>`;
}

/**
 * 格式化规格JSON
 */
function formatSpec(spec, viewId, viewName) {
    if (!spec) return '<span class="text-muted">无规格</span>';
    
    const preview = typeof spec === 'string' ? spec : JSON.stringify(spec);
    const truncated = preview.length > 50 ? preview.substring(0, 50) + '...' : preview;
    
    // 为每个规格生成唯一ID，避免字符串转义问题
    const specId = 'spec_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    
    // 将规格数据存储在全局对象中
    if (!window.specDataStore) {
        window.specDataStore = {};
    }
    window.specDataStore[specId] = {
        viewId: viewId,
        viewName: viewName,
        spec: spec
    };
    
    return `<div class="spec-preview" onclick="showSpecModalById('${specId}')">
        ${truncated}
    </div>`;
}

/**
 * 格式化数组字段
 */
function formatArrayField(arrayData) {
    if (!arrayData || !Array.isArray(arrayData) || arrayData.length === 0) {
        return '<span class="text-muted">无</span>';
    }
    
    // 完全显示所有字段，不截断
    return arrayData.map(item => `<span class="badge bg-light text-dark me-1 mb-1">${item}</span>`).join('');
}

/**
 * 格式化日期时间
 */
function formatDateTime(dateTime) {
    if (!dateTime) return '<span class="text-muted">-</span>';
    
    try {
        const date = new Date(dateTime);
        return date.toLocaleString('zh-CN');
    } catch (e) {
        return dateTime;
    }
}

/**
 * 通过ID显示规格详情模态框
 */
function showSpecModalById(specId) {
    if (!window.specDataStore || !window.specDataStore[specId]) {
        console.error('找不到规格数据:', specId);
        alert('找不到规格数据');
        return;
    }
    
    const data = window.specDataStore[specId];
    showSpecModal(data.viewId, data.viewName, data.spec);
}

/**
 * 显示规格详情模态框
 */
function showSpecModal(viewId, viewName, spec) {
    document.getElementById('specChartInfo').textContent = `${viewName} (${viewId})`;
    
    let formattedSpec = '';
    try {
        if (typeof spec === 'string') {
            formattedSpec = JSON.stringify(JSON.parse(spec), null, 2);
        } else {
            formattedSpec = JSON.stringify(spec, null, 2);
        }
    } catch (e) {
        formattedSpec = spec || '无规格数据';
    }
    
    document.getElementById('specContent').textContent = formattedSpec;
    
    const modal = new bootstrap.Modal(document.getElementById('specModal'));
    modal.show();
}

/**
 * 显示加载状态
 */
function showLoading() {
    Utils.loading.showGlobal();
}

/**
 * 隐藏加载状态
 */
function hideLoading() {
    Utils.loading.hideGlobal();
}