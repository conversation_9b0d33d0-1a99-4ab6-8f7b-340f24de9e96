$(document).ready(function() {
    // 表单提交事件
    $('#testForm').on('submit', function(e) {
        e.preventDefault();
        executeTest();
    });



    /**
     * 执行测试
     */
    function executeTest() {
        const baseUrl = $('#agentUrl').val().trim();
        const formData = {
            agentUrl: baseUrl + '/api/chatbi/chat',  // 传递完整的chat接口地址
            tenantId: $('#tenantId').val().trim(),
            userId: $('#userId').val().trim(),
            query: $('#testQuery').val().trim()
        };

        // 验证输入
        if (!formData.agentUrl) {
            showAlert('请输入Agent地址', 'warning');
            return;
        }
        if (!formData.tenantId) {
            showAlert('请输入租户ID', 'warning');
            return;
        }
        if (!formData.query) {
            showAlert('请输入测试问题', 'warning');
            return;
        }

        // 显示加载状态
        showLoading();
        
        const startTime = Date.now();

        // 第一步：调用chat接口获取requestId
        API.post('/test/executeTest', formData)
            .then(response => {
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                console.log('Chat接口响应:', response);
                
                if (response.success && response.data) {
                    // response.data现在直接是Agent服务的ApiResult
                    const agentResponse = response.data;
                    
                    // 显示初始reasoning结果
                    showInitialResult(agentResponse, duration, baseUrl);
                    
                    // 获取requestId - 适配Agent服务返回的ApiResult格式
                    let requestId = '';
                    if (agentResponse && agentResponse.data && agentResponse.data.requestId) {
                        requestId = agentResponse.data.requestId;
                    } else if (agentResponse && agentResponse.requestId) {
                        requestId = agentResponse.requestId;
                    }
                    
                    // 如果有requestId，开始轮询  
                    if (requestId) {
                        // 传递基础URL给轮询函数
                        startPolling(baseUrl, formData.tenantId, formData.userId, requestId, startTime);
                    } else {
                        console.log('未找到requestId，无法进行轮询');
                    }
                } else {
                    hideLoading();
                    showTestResult(response, duration);
                }
            })
            .catch(error => {
                hideLoading();
                let errorMsg = '测试执行失败';
                if (error.message) {
                    errorMsg += ': ' + error.message;
                } else {
                    errorMsg += ': ' + error;
                }
                showError(errorMsg);
            });
    }

    /**
     * 显示加载状态
     */
    function showLoading() {
        $('#executeBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>执行中...');
        $('#statusBadge').removeClass().addClass('badge bg-warning').text('执行中');
        $('#loadingArea').show();
        $('#resultArea').hide();
        $('#errorArea').hide();
    }

    /**
     * 隐藏加载状态
     */
    function hideLoading() {
        $('#executeBtn').prop('disabled', false).html('<i class="fas fa-play me-2"></i>执行测试');
        $('#loadingArea').hide();
    }

    /**
     * 显示初始reasoning结果
     */
    function showInitialResult(data, duration, baseUrl) {
        console.log('显示初始结果, 数据:', data);
        
        $('#statusBadge').removeClass().addClass('badge bg-warning').text('处理中');
        $('#timeBadge').show().text(duration + 'ms');
        
        // 显示API调用信息  
        showApiInfo(baseUrl);
        
        // 显示格式化后的JSON响应作为推理过程
        $('#reasoningContent').html(`<pre><code>${formatJsonResponse(data)}</code></pre>`);
        
        // 重置三个区域的状态
        resetSectionStatus('chartStatus', 'chartContent');
        resetSectionStatus('insightStatus', 'insightContent');
        resetSectionStatus('followUpStatus', 'followUpContent');
        
        $('#resultArea').show();
    }

    /**
     * 显示API调用信息
     */
    function showApiInfo(agentUrl) {
        $('#chatApiUrl').text(`${agentUrl}/api/chatbi/chat`);
        $('#loadDataUrl').text(`${agentUrl}/chatbi/loadData`);
        $('#loadInsightUrl').text(`${agentUrl}/chatbi/loadInsight`);
        $('#loadFollowUpUrl').text(`${agentUrl}/chatbi/loadFollowUp`);
        $('#apiInfoSection').show();
    }

    /**
     * 重置区域状态
     */
    function resetSectionStatus(statusId, contentId) {
        $(`#${statusId}`).removeClass().addClass('badge bg-secondary').text('待加载');
        $(`#${contentId}`).html(`
            <div class="text-center py-3">
                <div class="spinner-border spinner-border-sm text-primary me-2" role="status"></div>
                <span class="text-muted">正在获取数据...</span>
            </div>
        `);
    }

    /**
     * 显示测试结果
     */
    function showTestResult(result, duration) {
        $('#statusBadge').removeClass().addClass('badge bg-success').text('执行完成');
        $('#timeBadge').show().text(duration + 'ms');
        
        // 直接显示JSON数据
        renderJsonData(result.data);

        $('#resultArea').show();
    }

    /**
     * 开始轮询获取异步数据
     */
    function startPolling(agentUrl, tenantId, userId, requestId, startTime) {
        const pollInterval = 5000; // 5秒轮询一次
        const maxPollTime = 3 * 60 * 1000; // 最多3分钟
        let pollCount = 0;
        const maxPollCount = Math.floor(maxPollTime / pollInterval);
        
        // 跟踪各区域完成状态
        let chartCompleted = false;
        let insightCompleted = false;
        let followUpCompleted = false;
        
        hideLoading(); // 隐藏初始加载状态
        
        const pollTimer = setInterval(() => {
            pollCount++;
            
            if (pollCount > maxPollCount) {
                clearInterval(pollTimer);
                showPollingTimeout();
                return;
            }
            
            // 更新状态提示
            const elapsed = Math.floor((Date.now() - startTime) / 1000);
            $('#timeBadge').text(`${elapsed}s`);
            
            // 检查是否还有未完成的区域
            if (chartCompleted && insightCompleted && followUpCompleted) {
                clearInterval(pollTimer);
                $('#statusBadge').removeClass().addClass('badge bg-success').text('全部完成');
                const totalTime = Math.round((Date.now() - startTime) / 1000);
                $('#timeBadge').text(`${totalTime}s`);
                return;
            }
            
            console.log(`轮询状态 - 图表:${chartCompleted}, 解读:${insightCompleted}, 追问:${followUpCompleted}`);
            
            // 只轮询未完成的接口
            const pollPromises = [];
            
            if (!chartCompleted) {
                pollPromises.push(pollChartData(agentUrl, tenantId, userId, requestId));
            } else {
                pollPromises.push(Promise.resolve(null)); // 已完成的用null占位
            }
            
            if (!insightCompleted) {
                pollPromises.push(pollInsight(agentUrl, tenantId, userId, requestId));
            } else {
                pollPromises.push(Promise.resolve(null)); // 已完成的用null占位
            }
            
            if (!followUpCompleted) {
                pollPromises.push(pollFollowUp(agentUrl, tenantId, userId, requestId));
            } else {
                pollPromises.push(Promise.resolve(null)); // 已完成的用null占位
            }
            
            Promise.all(pollPromises).then(([chartData, insight, followUp]) => {
                // 只更新未完成的区域
                if (!chartCompleted && chartData) {
                    chartCompleted = updateChartSection(chartData);
                }
                if (!insightCompleted && insight) {
                    insightCompleted = updateInsightSection(insight);
                }
                if (!followUpCompleted && followUp) {
                    followUpCompleted = updateFollowUpSection(followUp);
                }
                
                // 完成状态会在下次轮询开始时检查
            }).catch(error => {
                console.error('轮询过程出错:', error);
                // 继续轮询，不中断
            });
            
        }, pollInterval);
    }

    /**
     * 轮询图表数据
     */
    function pollChartData(agentUrl, tenantId, userId, requestId) {
        return API.post('/test/pollData', {
            agentUrl: agentUrl + '/chatbi/loadData',
            tenantId: tenantId,
            userId: userId,
            requestId: requestId
        }).then(response => response.success ? response.data : null)
          .catch(() => null);
    }

    /**
     * 轮询解读数据
     */
    function pollInsight(agentUrl, tenantId, userId, requestId) {
        return API.post('/test/pollData', {
            agentUrl: agentUrl + '/chatbi/loadInsight',
            tenantId: tenantId,
            userId: userId,
            requestId: requestId
        }).then(response => response.success ? response.data : null)
          .catch(() => null);
    }

    /**
     * 轮询追问建议
     */
    function pollFollowUp(agentUrl, tenantId, userId, requestId) {
        return API.post('/test/pollData', {
            agentUrl: agentUrl + '/chatbi/loadFollowUp',
            tenantId: tenantId,
            userId: userId,
            requestId: requestId
        }).then(response => response.success ? response.data : null)
          .catch(() => null);
    }

    /**
     * 更新图表数据区域
     */
    function updateChartSection(chartData) {
        const statusBadge = $('#chartStatus');
        const contentDiv = $('#chartContent');
        
        if (!chartData) {
            return false; // 返回false表示未完成
        }
        
        // 检查LoadingStatus，只有不为LOADING时才显示结果
        if (chartData.loadingStatus && chartData.loadingStatus !== 'LOADING') {
            statusBadge.removeClass().addClass('badge bg-success').text('已完成');
            contentDiv.html(`<pre><code>${formatJsonResponse(chartData)}</code></pre>`);
            return true; // 返回true表示已完成
        } else {
            statusBadge.removeClass().addClass('badge bg-warning').text('加载中');
            return false; // 返回false表示未完成
        }
    }

    /**
     * 更新数据解读区域
     */
    function updateInsightSection(insight) {
        const statusBadge = $('#insightStatus');
        const contentDiv = $('#insightContent');
        
        if (!insight) {
            return false; // 返回false表示未完成
        }
        
        // 检查LoadingStatus，只有不为LOADING时才显示结果
        if (insight.loadingStatus && insight.loadingStatus !== 'LOADING') {
            statusBadge.removeClass().addClass('badge bg-success').text('已完成');
            contentDiv.html(`<pre><code>${formatJsonResponse(insight)}</code></pre>`);
            return true; // 返回true表示已完成
        } else {
            statusBadge.removeClass().addClass('badge bg-warning').text('加载中');
            return false; // 返回false表示未完成
        }
    }

    /**
     * 更新追问建议区域
     */
    function updateFollowUpSection(followUp) {
        const statusBadge = $('#followUpStatus');
        const contentDiv = $('#followUpContent');
        
        if (!followUp) {
            return false; // 返回false表示未完成
        }
        
        // 检查LoadingStatus，只有不为LOADING时才显示结果
        if (followUp.loadingStatus && followUp.loadingStatus !== 'LOADING') {
            statusBadge.removeClass().addClass('badge bg-success').text('已完成');
            contentDiv.html(`<pre><code>${formatJsonResponse(followUp)}</code></pre>`);
            return true; // 返回true表示已完成
        } else {
            statusBadge.removeClass().addClass('badge bg-warning').text('加载中');
            return false; // 返回false表示未完成
        }
    }

    /**
     * 显示轮询超时
     */
    function showPollingTimeout() {
        $('#statusBadge').removeClass().addClass('badge bg-warning').text('轮询超时');
        
        // 更新所有未完成的区域状态
        ['chartStatus', 'insightStatus', 'followUpStatus'].forEach(statusId => {
            const statusBadge = $(`#${statusId}`);
            if (!statusBadge.hasClass('bg-success')) { // 只更新未完成的区域
                statusBadge.removeClass().addClass('badge bg-warning').text('超时');
                const contentId = statusId.replace('Status', 'Content');
                $(`#${contentId}`).html('<div class="alert alert-warning mb-0"><i class="fas fa-exclamation-triangle me-2"></i>轮询超时，请检查Agent服务状态</div>');
            }
        });
    }



    /**
     * 渲染Agent响应数据
     */
    function renderJsonData(data) {
        const container = $('#agentResponse');
        container.empty();

        if (!data) {
            container.html('<div class="text-muted">暂无响应数据</div>');
            return;
        }

        // 展示格式化后的JSON响应
        container.html(`<pre><code>${formatJsonResponse(data)}</code></pre>`);
    }

    /**
     * 显示错误信息
     */
    function showError(message) {
        $('#statusBadge').removeClass().addClass('badge bg-danger').text('执行失败');
        $('#errorMessage').text(message);
        $('#errorArea').show();
        $('#resultArea').hide();
    }



    /**
     * 获取日志边框样式类
     */
    function getLogBorderClass(level) {
        switch (level) {
            case 'ERROR': return 'border-danger';
            case 'WARN': return 'border-warning';
            case 'INFO': return 'border-info';
            case 'DEBUG': return 'border-secondary';
            default: return 'border-primary';
        }
    }

    /**
     * 格式化时间戳
     */
    function formatTimestamp(timestamp) {
        if (!timestamp) return '-';
        const date = new Date(timestamp);
        return date.toLocaleString('zh-CN');
    }

    /**
     * HTML转义
     */
    function escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * HTML解码
     */
    function decodeHtml(html) {
        if (!html) return '';
        const div = document.createElement('div');
        div.innerHTML = html;
        return div.textContent || div.innerText || '';
    }

    /**
     * 格式化JSON响应，处理HTML标签和特殊字符
     */
    function formatJsonResponse(data) {
        if (!data) return '';
        
        // 深度复制数据，避免修改原始数据
        const cleanData = JSON.parse(JSON.stringify(data));
        
        // 递归处理对象中的HTML内容和特殊字符
        function cleanTextInObject(obj) {
            if (typeof obj === 'string') {
                let cleaned = obj;
                
                // 处理HTML标签
                if (cleaned.includes('<') && cleaned.includes('>')) {
                    cleaned = decodeHtml(cleaned);
                }
                
                // 处理换行符和其他转义字符
                cleaned = cleaned
                    .replace(/\\n/g, '\n')      // 将 \n 转换为实际换行符
                    .replace(/\\t/g, '\t')      // 将 \t 转换为制表符
                    .replace(/\\r/g, '\r')      // 将 \r 转换为回车符
                    .replace(/\\\\/g, '\\');    // 将 \\ 转换为单个反斜杠
                
                return cleaned;
            } else if (Array.isArray(obj)) {
                return obj.map(item => cleanTextInObject(item));
            } else if (obj && typeof obj === 'object') {
                const cleaned = {};
                for (const [key, value] of Object.entries(obj)) {
                    cleaned[key] = cleanTextInObject(value);
                }
                return cleaned;
            }
            return obj;
        }
        
        const cleanedData = cleanTextInObject(cleanData);
        return JSON.stringify(cleanedData, null, 2);
    }

    /**
     * 显示提示信息
     */
    function showAlert(message, type = 'info') {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${escapeHtml(message)}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // 在页面顶部显示提示
        if ($('.alert-container').length === 0) {
            $('main').prepend('<div class="alert-container"></div>');
        }
        
        $('.alert-container').html(alertHtml);
        
        // 3秒后自动消失
        setTimeout(function() {
            $('.alert').alert('close');
        }, 3000);
    }
}); 