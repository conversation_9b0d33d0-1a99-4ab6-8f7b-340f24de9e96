/**
 * 图表知识管理JavaScript - 精简版
 */

let chartTable;
let featureTable;
let currentSelectedChart = null;

// 简化的初始化函数
$(document).ready(function() {
    // 延迟初始化，确保所有资源加载完成
    setTimeout(function() {
        try {
            console.log('开始初始化图表知识管理页面...');
            
            // 检查基本依赖
            if (typeof $ === 'undefined') {
                console.error('jQuery未加载');
                showSimpleError('jQuery未加载，请刷新页面重试');
                return;
            }
            
            // 初始化页面
            initPage();
            
            // 绑定事件
            bindEvents();
            
            // 加载数据
            loadChartList().catch(error => {
                console.error('初始化时加载图表列表失败:', error);
            });
            loadChartCount().catch(error => {
                console.error('初始化时加载图表数量失败:', error);
            });
            
            // 初始化测试模式指示器
            updateTestModeIndicator(true);
            
            console.log('图表知识管理页面初始化完成');
        } catch (error) {
            console.error('页面初始化失败:', error);
            showSimpleError('页面初始化失败: ' + error.message);
        }
    }, 1500); // 延迟1.5秒确保资源加载完成
});

// 简单的错误提示函数
function showSimpleError(message) {
    if (typeof Utils !== 'undefined' && Utils.showError) {
        Utils.showError(message);
    } else {
        alert(message);
    }
}

/**
 * 初始化页面
 */
function initPage() {
    try {
        console.log('初始化DataTables...');
        
        // 检查DataTables是否可用
        const hasDataTables = typeof $.fn.DataTable !== 'undefined';
        console.log('DataTables可用:', hasDataTables);
        
        if (hasDataTables) {
            // 使用原生DataTables
            initDataTables();
        } else {
            // 使用简单表格
            initSimpleTables();
        }
        
        console.log('表格初始化完成');
    } catch (error) {
        console.error('表格初始化失败:', error);
        // 降级到简单表格
        initSimpleTables();
    }
}

/**
 * 初始化DataTables
 */
function initDataTables() {
    // 图表列表表格
    chartTable = $('#chartTable').DataTable({
        columns: [
            { data: 'viewId', title: 'ViewID' },
            { data: 'viewName', title: '图表名称' },
            { 
                data: 'spec', 
                title: '图表规格',
                orderable: false,
                render: function(data, type, row) {
                    if (!data) return '<span class="text-muted">无规格</span>';
                    
                    let preview = '';
                    try {
                        const jsonStr = typeof data === 'string' ? data : JSON.stringify(data);
                        preview = jsonStr.length > 50 ? jsonStr.substring(0, 50) + '...' : jsonStr;
                    } catch (e) {
                        preview = '无效JSON';
                    }
                    
                    const specData = JSON.stringify(data);
                    return `<div class="spec-preview" 
                                 data-view-id="${row.viewId}" 
                                 data-view-name="${row.viewName}" 
                                 data-spec='${specData}' 
                                 onclick="showSpecModalFromElement(this)">
                                ${preview}
                                <i class="fas fa-expand-alt ms-1"></i>
                            </div>`;
                }
            },
            { 
                data: null, 
                title: '操作',
                orderable: false,
                render: function(data, type, row) {
                    return `
                        <button class="btn btn-sm btn-primary select-chart-btn" 
                                data-view-id="${row.viewId}" data-view-name="${row.viewName}">
                            <i class="fas fa-hand-pointer me-1"></i>选择
                        </button>
                        <button class="btn btn-sm btn-danger delete-chart-btn ms-1" 
                                data-view-id="${row.viewId}">
                            <i class="fas fa-trash"></i>
                        </button>
                    `;
                }
            }
        ],
        pageLength: 10,
        order: [[0, 'asc']],
        language: {
            processing: "处理中...",
            emptyTable: "暂无图表数据",
            zeroRecords: "没有找到匹配的记录"
        },
        destroy: true // 允许重新初始化
    });
    
    // 特征表格
    featureTable = $('#featureTable').DataTable({
        columns: [
            { 
                data: 'feature', 
                title: '特征',
                render: function(data, type, row) {
                    return `<span class="text-break">${data || ''}</span>`;
                }
            },
            { 
                data: 'weight', 
                title: '权重',
                render: function(data, type, row) {
                    return `<span class="badge bg-primary">${data || 1.0}</span>`;
                }
            },
            { 
                data: null, 
                title: '操作',
                orderable: false,
                                    render: function(data, type, row) {
                        return `
                            <button class="btn btn-sm btn-warning edit-feature-btn" 
                                    data-feature-id="${row.id || ''}" 
                                    data-feature="${row.feature || ''}" 
                                    data-weight="${row.weight || 1.0}">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger delete-feature-btn ms-1" 
                                    data-feature-id="${row.id || ''}">
                                <i class="fas fa-trash"></i>
                            </button>
                        `;
                    }
            }
        ],
        pageLength: 5,
        searching: false,
        info: false,
        lengthChange: false,
        paging: false,
        language: {
            emptyTable: "暂无特征数据"
        },
        destroy: true
    });
}

/**
 * 初始化简单表格（备用方案）
 */
function initSimpleTables() {
    console.log('使用简单表格实现');
    
    // 创建简单的表格对象
    chartTable = {
        _data: [], // 存储数据
        clear: function() {
            $('#chartTable tbody').empty();
            this._data = [];
            return this;
        },
        rows: {
            add: function(data) {
                // 保存数据
                chartTable._data = data;
                
                const tbody = $('#chartTable tbody');
                data.forEach(row => {
                    // 处理spec列
                    let specHtml = '<span class="text-muted">无规格</span>';
                    if (row.spec) {
                        let preview = '';
                        try {
                            const jsonStr = typeof row.spec === 'string' ? row.spec : JSON.stringify(row.spec);
                            preview = jsonStr.length > 50 ? jsonStr.substring(0, 50) + '...' : jsonStr;
                        } catch (e) {
                            preview = '无效JSON';
                        }
                        
                        const specData = JSON.stringify(row.spec);
                        specHtml = `<div class="spec-preview" 
                                         data-view-id="${row.viewId}" 
                                         data-view-name="${row.viewName}" 
                                         data-spec='${specData}' 
                                         onclick="showSpecModalFromElement(this)">
                                        ${preview}
                                        <i class="fas fa-expand-alt ms-1"></i>
                                    </div>`;
                    }
                    
                    const tr = `
                        <tr>
                            <td>${row.viewId || ''}</td>
                            <td>${row.viewName || ''}</td>
                            <td>${specHtml}</td>
                            <td>
                                <button class="btn btn-sm btn-primary select-chart-btn" 
                                        data-view-id="${row.viewId}" data-view-name="${row.viewName}">
                                    <i class="fas fa-hand-pointer me-1"></i>选择
                                </button>
                                <button class="btn btn-sm btn-danger delete-chart-btn ms-1" 
                                        data-view-id="${row.viewId}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                    tbody.append(tr);
                });
                return chartTable; // 返回chartTable对象以支持链式调用
            }
        },
        data: function() {
            return {
                toArray: function() {
                    return chartTable._data;
                }
            };
        },
        draw: function() { return chartTable; } // 返回chartTable对象以支持链式调用
    };
    
    featureTable = {
        _data: [], // 存储数据
        clear: function() {
            $('#featureTable tbody').empty();
            this._data = [];
            return this;
        },
        rows: {
            add: function(data) {
                // 保存数据
                featureTable._data = data;
                
                const tbody = $('#featureTable tbody');
                data.forEach(row => {
                    const tr = `
                        <tr>
                            <td><span class="text-break">${row.feature || ''}</span></td>
                            <td><span class="badge bg-primary">${row.weight || 1.0}</span></td>
                            <td>
                                <button class="btn btn-sm btn-warning edit-feature-btn" 
                                        data-feature-id="${row.id || ''}" 
                                        data-feature="${row.feature || ''}" 
                                        data-weight="${row.weight || 1.0}">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-danger delete-feature-btn ms-1" 
                                        data-feature-id="${row.id || ''}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                    tbody.append(tr);
                });
                return featureTable; // 返回featureTable对象以支持链式调用
            }
        },
        data: function() {
            return {
                toArray: function() {
                    return featureTable._data;
                }
            };
        },
        draw: function() { return featureTable; } // 返回featureTable对象以支持链式调用
    };
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 选择图表事件
    $(document).on('click', '.select-chart-btn', function() {
        const viewId = $(this).data('view-id');
        const viewName = $(this).data('view-name');
        selectChart(viewId, viewName);
    });
    
    // 删除图表事件
    $(document).on('click', '.delete-chart-btn', function() {
        const viewId = $(this).data('view-id');
        deleteChart(viewId);
    });
    
    // 刷新数据按钮
    $('#refreshBtn').on('click', function() {
        refreshData();
    });
    
    // 添加特征按钮
    $('#addFeatureBtn').on('click', function() {
        showFeatureModal();
    });
    
    // 编辑特征事件
    $(document).on('click', '.edit-feature-btn', function() {
        const featureId = $(this).data('feature-id');
        const feature = $(this).data('feature');
        const weight = $(this).data('weight');
        showFeatureModal(featureId, feature, weight);
    });
    
    // 删除特征事件
    $(document).on('click', '.delete-feature-btn', function() {
        const featureId = $(this).data('feature-id');
        deleteFeature(featureId);
    });
    
    // 保存特征按钮
    $('#saveFeatureBtn').on('click', function() {
        saveFeature();
    });
    
    // 测试召回按钮
    $('#testRecallBtn').on('click', function() {
        testRecall();
    });
    
    // 测试问题输入框回车事件
    $('#testQuestion').on('keypress', function(e) {
        if (e.which === 13) { // Enter键
            $('#testRecallBtn').click();
        }
    });
    
    // 模态框重置事件
    $('#featureModal').on('hidden.bs.modal', function() {
        Utils.resetForm('#featureForm');
    });
}

/**
 * 加载图表列表
 */
function loadChartList() {
    return API.post('/getChartKnowledgeList', {})
        .then(response => {
            if (response.success) {
                const charts = response.data || [];
                chartTable.clear().rows.add(charts).draw();
                console.log(`加载了 ${charts.length} 个图表`);
                return charts;
            } else {
                const errorMsg = response.message || '加载图表列表失败';
                Utils.showError(errorMsg);
                throw new Error(errorMsg);
            }
        })
        .catch(error => {
            console.error('加载图表列表失败:', error);
            const errorMsg = '加载图表列表失败';
            Utils.showError(errorMsg);
            throw error;
        });
}

/**
 * 加载图表数量
 */
function loadChartCount() {
    return API.post('/getChartCount', {})
        .then(response => {
            if (response.success) {
                $('#chartCount').text(response.data || 0);
                return response.data || 0;
            } else {
                const errorMsg = response.message || '加载图表数量失败';
                console.error('加载图表数量失败:', errorMsg);
                throw new Error(errorMsg);
            }
        })
        .catch(error => {
            console.error('加载图表数量失败:', error);
            throw error;
        });
}

/**
 * 刷新数据
 */
function refreshData() {
    const $btn = $('#refreshBtn');
    Utils.showLoading($btn, '刷新中...');
    
    Promise.all([
        loadChartList(),
        loadChartCount()
    ]).then(() => {
        // 如果有选中的图表，重新加载特征
        if (currentSelectedChart) {
            loadFeatureList(currentSelectedChart.viewId);
        }
        Utils.showSuccess('数据刷新成功');
    }).catch(error => {
        console.error('刷新数据失败:', error);
        Utils.showError('刷新数据失败');
    }).finally(() => {
        Utils.hideLoading($btn);
    });
}

/**
 * 选择图表
 */
function selectChart(viewId, viewName) {
    currentSelectedChart = { viewId, viewName };
    
    // 更新UI
    $('#selectedChartInfo').text(`当前选择: ${viewName} (${viewId})`);
    $('#noChartSelected').hide();
    $('#featureManagement').show();
    $('#addFeatureBtn').prop('disabled', false);
    
    // 更新测试模式指示器
    updateTestModeIndicator(false);
    
    // 加载特征列表
    loadFeatureList(viewId);
    
    // 高亮选中的行
    $('#chartTable tbody tr').removeClass('table-active');
    $(`.select-chart-btn[data-view-id="${viewId}"]`).closest('tr').addClass('table-active');
    
    Utils.showSuccess(`已选择图表: ${viewName}`);
}

/**
 * 加载特征列表
 */
function loadFeatureList(viewId) {
    API.post('/getChartFeaturesByViewId', { viewId: viewId })
        .then(response => {
            if (response.success) {
                const features = response.data || [];
                featureTable.clear().rows.add(features).draw();
                $('#featureCount').text(features.length);
                console.log(`加载了 ${features.length} 个特征`);
            } else {
                Utils.showError(response.message || '加载特征列表失败');
            }
        })
        .catch(error => {
            console.error('加载特征列表失败:', error);
            Utils.showError('加载特征列表失败');
        });
}

/**
 * 删除图表
 */
function deleteChart(viewId) {
    Utils.confirm('确定要删除这个图表的所有特征吗？此操作不可恢复！', () => {
        API.post('/deleteChart', { viewId: viewId })
            .then(response => {
                if (response.success) {
                    Utils.showSuccess('图表特征删除成功');
                    loadChartList().catch(error => {
                        console.error('删除后重新加载图表列表失败:', error);
                    });
                    loadChartCount().catch(error => {
                        console.error('删除后重新加载图表数量失败:', error);
                    });
                    
                    // 如果删除的是当前选中的图表，清空选择
                    if (currentSelectedChart && currentSelectedChart.viewId === viewId) {
                        currentSelectedChart = null;
                        $('#noChartSelected').show();
                        $('#featureManagement').hide();
                        $('#addFeatureBtn').prop('disabled', true);
                        $('#testResults').hide();
                        
                        // 更新测试模式指示器
                        updateTestModeIndicator(true);
                    }
                } else {
                    Utils.showError(response.message || '删除失败');
                }
            })
            .catch(error => {
                console.error('删除图表失败:', error);
                Utils.showError('删除失败');
            });
    });
}

/**
 * 显示特征模态框
 */
function showFeatureModal(featureId = null, feature = '', weight = 1.0) {
    if (!currentSelectedChart) {
        Utils.showWarning('请先选择一个图表');
        return;
    }
    
    // 设置模态框标题
    $('#featureModalTitle').text(featureId ? '编辑特征' : '添加特征');
    
    // 填充表单数据
    $('#featureId').val(featureId || '');
    $('#featureViewId').val(currentSelectedChart.viewId);
    $('#featureName').val(feature);
    $('#featureWeight').val(weight);
    
    // 显示模态框
    $('#featureModal').modal('show');
}

/**
 * 保存特征
 */
function saveFeature() {
    if (!Utils.validateForm('#featureForm')) {
        return;
    }
    
    const data = {
        viewId: $('#featureViewId').val(),
        feature: {
            id: $('#featureId').val() || null,
            knowledgeId: $('#featureViewId').val(),
            feature: $('#featureName').val().trim(),
            weight: parseFloat($('#featureWeight').val()),
            knowledgeType: 'CHART'
        }
    };
    
    const $btn = $('#saveFeatureBtn');
    Utils.showLoading($btn, '保存中...');
    
    API.post('/saveChartFeature', data)
        .then(response => {
            if (response.success) {
                Utils.showSuccess('特征保存成功');
                $('#featureModal').modal('hide');
                loadFeatureList(currentSelectedChart.viewId);
            } else {
                Utils.showError(response.message || '保存失败');
            }
        })
        .catch(error => {
            console.error('保存特征失败:', error);
            Utils.showError('保存失败');
        })
        .finally(() => {
            Utils.hideLoading($btn);
        });
}

/**
 * 删除特征
 */
function deleteFeature(featureId) {
    Utils.confirm('确定要删除这个特征吗？', () => {
        API.post('/deleteChartFeature', { 
            viewId: currentSelectedChart.viewId,
            featureId: featureId 
        })
        .then(response => {
            if (response.success) {
                Utils.showSuccess('特征删除成功');
                loadFeatureList(currentSelectedChart.viewId);
            } else {
                Utils.showError(response.message || '删除失败');
            }
        })
        .catch(error => {
            console.error('删除特征失败:', error);
            Utils.showError('删除失败');
        });
    });
}

/**
 * 测试召回 - 支持全局测试和针对性测试两种模式
 */
function testRecall() {
    const question = $('#testQuestion').val().trim();
    if (!question) {
        Utils.showWarning('请输入测试问题');
        return;
    }
    
    const $btn = $('#testRecallBtn');
    Utils.showLoading($btn, '测试中...');
    
    // 判断测试模式
    const isGlobalTest = !currentSelectedChart;
    let requestData = {
        testQuery: question
    };
    
    if (!isGlobalTest) {
        // 针对性测试：包含目标图表信息和特征
        requestData.viewId = currentSelectedChart.viewId;
        
        // 获取当前图表的特征列表
        let features = [];
        try {
            features = featureTable.data().toArray().map(row => row.feature);
        } catch (error) {
            console.error('获取特征列表失败:', error);
            // 备用方案：直接从DOM获取
            $('#featureTable tbody tr').each(function() {
                const featureText = $(this).find('td:first-child').text().trim();
                if (featureText) {
                    features.push(featureText);
                }
            });
        }
        requestData.features = features;
        
        console.log('执行针对性召回测试, 目标图表:', currentSelectedChart.viewId);
    } else {
        console.log('执行全局召回测试');
    }
    
    API.post('/testChartFeature', requestData)
    .then(response => {
        if (response.success) {
            displayTestResults(response.data, question, isGlobalTest);
            Utils.showSuccess('测试完成');
        } else {
            Utils.showError(response.message || '测试失败');
        }
    })
    .catch(error => {
        console.error('测试召回失败:', error);
        Utils.showError('测试失败');
    })
    .finally(() => {
        Utils.hideLoading($btn);
    });
}

/**
 * 显示测试结果 - 支持全局和针对性两种测试模式
 */
function displayTestResults(result, question, isGlobalTest) {
    const $resultsDiv = $('#testResults');
    const $tbody = $('#recallResultTable tbody');
    
    // 清空现有结果
    $tbody.empty();
    
    // 根据测试模式显示不同的状态徽章
    if (isGlobalTest) {
        // 全局测试：隐藏匹配状态徽章
        $('#matchedBadge').hide();
        $('#notMatchedBadge').hide();
    } else {
        // 针对性测试：显示匹配状态
        if (result.matched) {
            $('#matchedBadge').show();
            $('#notMatchedBadge').hide();
        } else {
            $('#matchedBadge').hide();
            $('#notMatchedBadge').show();
        }
    }
    
    const recalledCharts = result.recalledCharts || [];
    
    if (recalledCharts.length === 0) {
        $tbody.append(`
            <tr>
                <td colspan="8" class="text-center text-muted py-4">
                    <i class="fas fa-search fa-2x mb-2"></i>
                    <p class="mb-0">未找到与问题"${question}"匹配的图表</p>
                    <small class="text-muted">请尝试调整问题描述或检查图表特征配置</small>
                </td>
            </tr>
        `);
    } else {
        recalledCharts.forEach((chart, index) => {
            const scoreClass = getScoreClass(chart.score);
            const rank = index + 1;
            
            // 排名徽章
            let rankBadgeClass = 'rank-other';
            if (rank === 1) rankBadgeClass = 'rank-1';
            else if (rank === 2) rankBadgeClass = 'rank-2';
            else if (rank === 3) rankBadgeClass = 'rank-3';
            
            const rankBadge = `<span class="rank-badge ${rankBadgeClass}">${rank}</span>`;
            
            // 状态显示：根据测试模式显示不同信息
            let statusDisplay = '';
            if (isGlobalTest) {
                // 全局测试：显示相关性状态
                if (chart.score >= 0.8) {
                    statusDisplay = '<span class="badge bg-success"><i class="fas fa-star me-1"></i>高度相关</span>';
                } else if (chart.score >= 0.5) {
                    statusDisplay = '<span class="badge bg-warning"><i class="fas fa-star-half-alt me-1"></i>中度相关</span>';
                } else {
                    statusDisplay = '<span class="badge bg-secondary"><i class="fas fa-star me-1"></i>低度相关</span>';
                }
            } else {
                // 针对性测试：显示是否为目标图表
                const isTarget = chart.viewId === currentSelectedChart.viewId;
                if (isTarget) {
                    statusDisplay = '<span class="badge bg-success"><i class="fas fa-bullseye me-1"></i>目标图表</span>';
                } else {
                    // 同时显示相关性
                    if (chart.score >= 0.8) {
                        statusDisplay = '<span class="badge bg-info"><i class="fas fa-star me-1"></i>高度相关</span>';
                    } else if (chart.score >= 0.5) {
                        statusDisplay = '<span class="badge bg-warning"><i class="fas fa-star-half-alt me-1"></i>中度相关</span>';
                    } else {
                        statusDisplay = '<span class="badge bg-secondary"><i class="fas fa-star me-1"></i>其他图表</span>';
                    }
                }
            }
            
            // 处理spec列
            let specHtml = '<span class="text-muted">无规格</span>';
            if (chart.spec) {
                let preview = '';
                try {
                    const jsonStr = typeof chart.spec === 'string' ? chart.spec : JSON.stringify(chart.spec);
                    preview = jsonStr.length > 30 ? jsonStr.substring(0, 30) + '...' : jsonStr;
                } catch (e) {
                    preview = '无效JSON';
                }
                
                const specData = JSON.stringify(chart.spec);
                specHtml = `<div class="spec-preview" 
                                 data-view-id="${chart.viewId}" 
                                 data-view-name="${chart.viewName}" 
                                 data-spec='${specData}' 
                                 onclick="showSpecModalFromElement(this)">
                                ${preview}
                                <i class="fas fa-expand-alt ms-1"></i>
                            </div>`;
            }
            
            // 处理命中特征
            let featuresHtml = '<span class="text-muted">无特征</span>';
            if (chart.matchedFeature) {
                featuresHtml = `<span class="feature-item">${chart.matchedFeature}</span>`;
            }
            
            // 处理特征权重
            let weightsHtml = '<span class="text-muted">-</span>';
            if (chart.featureWeight !== null && chart.featureWeight !== undefined) {
                weightsHtml = `<span class="weight-badge">${chart.featureWeight.toFixed(2)}</span>`;
            }
            
            // 针对性测试时高亮目标图表
            const isTarget = !isGlobalTest && currentSelectedChart && chart.viewId === currentSelectedChart.viewId;
            const rowClass = `recall-result-row ${scoreClass} ${isTarget ? 'table-warning' : ''}`;
            
            $tbody.append(`
                <tr class="${rowClass}">
                    <td class="text-center">${rankBadge}</td>
                    <td>${chart.viewId}</td>
                    <td>${chart.viewName}</td>
                    <td>${specHtml}</td>
                    <td>${featuresHtml}</td>
                    <td>${weightsHtml}</td>
                    <td class="test-result-score">${chart.score.toFixed(3)}</td>
                    <td>${statusDisplay}</td>
                </tr>
            `);
        });
    }
    
    // 显示结果区域
    $resultsDiv.show();
}

/**
 * 根据得分获取样式类
 */
function getScoreClass(score) {
    if (score >= 0.8) return 'test-result-high';
    if (score >= 0.5) return 'test-result-medium';
    return 'test-result-low';
}

/**
 * 更新测试模式指示器
 */
function updateTestModeIndicator(isGlobalMode) {
    const $indicator = $('#testModeIndicator');
    
    if (isGlobalMode) {
        $indicator.removeClass('bg-primary').addClass('bg-success');
        $indicator.html('<i class="fas fa-globe me-1"></i>全局测试模式');
    } else {
        $indicator.removeClass('bg-success').addClass('bg-primary');
        $indicator.html('<i class="fas fa-crosshairs me-1"></i>针对性测试模式');
    }
}