/**
 * 首页JavaScript
 */

$(document).ready(function() {
    // 初始化页面
    initPage();
    
    // 绑定事件
    bindEvents();
    
    // 加载统计数据
    loadStatistics();
});

/**
 * 初始化页面
 */
function initPage() {
    console.log('首页初始化完成');
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 快速测试按钮
    $('#quickTestBtn').on('click', function() {
        const question = $('#quickTestInput').val().trim();
        if (!question) {
            Utils.showWarning('请输入测试问题');
            return;
        }
        
        performQuickTest(question);
    });
    
    // 快速测试输入框回车事件
    $('#quickTestInput').on('keypress', function(e) {
        if (e.which === 13) { // Enter键
            $('#quickTestBtn').click();
        }
    });
}

/**
 * 加载统计数据
 */
function loadStatistics() {
    // 加载图表数量
    loadChartCount();
    
    // 加载测试用例数量
    loadTestCaseCount();
    
    // 加载标注记录数量
    loadAnnotationCount();
    
    // 加载准确率
    loadAccuracyRate();
}

/**
 * 加载图表数量
 */
function loadChartCount() {
    API.post('/getChartCount', {})
        .then(response => {
            if (response.success) {
                $('#chartCount').text(response.data || 0);
            } else {
                $('#chartCount').text('-');
            }
        })
        .catch(error => {
            console.error('加载图表数量失败:', error);
            $('#chartCount').text('-');
        });
}

/**
 * 加载测试用例数量
 */
function loadTestCaseCount() {
    API.post('/getTestCaseCount', {})
        .then(response => {
            if (response.success) {
                $('#testCaseCount').text(response.data || 0);
            } else {
                $('#testCaseCount').text('-');
            }
        })
        .catch(error => {
            console.error('加载测试用例数量失败:', error);
            $('#testCaseCount').text('-');
        });
}

/**
 * 加载标注记录数量
 */
function loadAnnotationCount() {
    API.post('/annotation/getAnnotationCount', {})
        .then(response => {
            if (response.success) {
                $('#annotationCount').text(response.data || 0);
            } else {
                $('#annotationCount').text('-');
            }
        })
        .catch(error => {
            console.error('加载标注记录数量失败:', error);
            $('#annotationCount').text('-');
        });
}

/**
 * 加载准确率
 */
function loadAccuracyRate() {
    API.post('/getAccuracyRate', {})
        .then(response => {
            if (response.success && response.data !== null) {
                $('#accuracyRate').text((response.data * 100).toFixed(1) + '%');
            } else {
                $('#accuracyRate').text('-');
            }
        })
        .catch(error => {
            console.error('加载准确率失败:', error);
            $('#accuracyRate').text('-');
        });
}

/**
 * 执行快速测试
 */
function performQuickTest(question) {
    const $btn = $('#quickTestBtn');
    
    Utils.showLoading($btn, '测试中...');
    
    API.post('/executeQuickTest', {
        question: question,
        tenantId: 'default'
    })
    .then(response => {
        if (response.success) {
            Utils.showSuccess('测试完成！');
            
            // 显示简单的测试结果
            if (response.data && response.data.charts && response.data.charts.length > 0) {
                const topChart = response.data.charts[0];
                Utils.showInfo(`最佳匹配图表: ${topChart.viewName} (得分: ${topChart.score.toFixed(2)})`);
            } else {
                Utils.showWarning('未找到匹配的图表');
            }
            
            // 清空输入框
            $('#quickTestInput').val('');
            
            // 跳转到测试页面查看详细结果
            setTimeout(() => {
                const contextPath = document.querySelector('meta[name="context-path"]')?.content || '';
                window.location.href = contextPath + '/test';
            }, 2000);
        } else {
            Utils.showError(response.message || '测试失败');
        }
    })
    .catch(error => {
        console.error('快速测试失败:', error);
        Utils.showError('测试失败，请重试');
    })
    .finally(() => {
        Utils.hideLoading($btn);
    });
}

/**
 * 刷新统计数据
 */
function refreshStatistics() {
    loadStatistics();
    Utils.showSuccess('统计数据已刷新');
}

// 定时刷新统计数据（每5分钟）
setInterval(function() {
    loadStatistics();
}, 5 * 60 * 1000);