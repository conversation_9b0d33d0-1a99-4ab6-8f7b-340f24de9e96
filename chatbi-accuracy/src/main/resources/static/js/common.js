/**
 * ChatBI 评估测试平台 - 通用JavaScript函数库
 * 提供统一的工具函数、API封装、错误处理等基础功能
 */

// ===== 全局配置 =====
const CONFIG = {
    API_BASE_URL: (function() {
        // 从 meta 标签获取上下文路径，如果没有则从当前URL推断
        const contextPath = document.querySelector('meta[name="context-path"]')?.content || 
                           (() => {
                               const path = window.location.pathname;
                               // 匹配 /agent-test/ 或 /agent-test 这样的模式
                               const match = path.match(/^(\/[^\/]+)/);
                               return match && match[1] !== '/' ? match[1] : '';
                           })();
        return contextPath + '/api';
    })(),
    TOAST_DURATION: 3000,
    TABLE_PAGE_SIZE: 10,
    DEBOUNCE_DELAY: 300,
    THROTTLE_DELAY: 1000,
    MAX_RETRY_COUNT: 3,
    RETRY_DELAY: 1000
};

// ===== 通用工具函数 =====
const Utils = {
    /**
     * 消息提示相关方法
     */
    message: {
        /**
         * 显示成功消息
         * @param {string} message - 消息内容
         * @param {number} duration - 显示时长（毫秒）
         */
        success(message, duration = CONFIG.TOAST_DURATION) {
            this._showToast(message, 'success', duration);
        },

        /**
         * 显示错误消息
         * @param {string} message - 消息内容
         * @param {number} duration - 显示时长（毫秒）
         */
        error(message, duration = CONFIG.TOAST_DURATION) {
            this._showToast(message, 'danger', duration);
        },

        /**
         * 显示警告消息
         * @param {string} message - 消息内容
         * @param {number} duration - 显示时长（毫秒）
         */
        warning(message, duration = CONFIG.TOAST_DURATION) {
            this._showToast(message, 'warning', duration);
        },

        /**
         * 显示信息消息
         * @param {string} message - 消息内容
         * @param {number} duration - 显示时长（毫秒）
         */
        info(message, duration = CONFIG.TOAST_DURATION) {
            this._showToast(message, 'info', duration);
        },

        /**
         * 内部方法：显示Toast消息
         * @private
         */
        _showToast(message, type = 'info', duration = CONFIG.TOAST_DURATION) {
            const toastId = 'toast-' + Date.now();
            const toastHtml = `
                <div id="${toastId}" class="toast align-items-center text-white bg-${type} border-0" 
                     role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="fas fa-${this._getIcon(type)} me-2"></i>
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" 
                                data-bs-dismiss="toast" aria-label="关闭"></button>
                    </div>
                </div>
            `;
            
            $('#toastContainer').append(toastHtml);
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, { delay: duration });
            
            // 自动移除DOM元素
            toastElement.addEventListener('hidden.bs.toast', () => {
                toastElement.remove();
            });
            
            toast.show();
        },

        /**
         * 获取消息类型对应的图标
         * @private
         */
        _getIcon(type) {
            const icons = {
                success: 'check-circle',
                danger: 'exclamation-triangle',
                warning: 'exclamation-circle',
                info: 'info-circle'
            };
            return icons[type] || 'info-circle';
        }
    },

    /**
     * 加载状态管理
     */
    loading: {
        /**
         * 显示元素加载状态
         * @param {string|Element} element - 元素选择器或DOM元素
         * @param {string} text - 加载文本
         */
        show(element, text = '加载中...') {
            const $element = $(element);
            if ($element.length === 0) return;
            
            $element.data('original-html', $element.html());
            $element.data('original-disabled', $element.prop('disabled'));
            
            $element.html(`
                <span class="loading-spinner me-2"></span>
                ${text}
            `).prop('disabled', true);
        },

        /**
         * 隐藏元素加载状态
         * @param {string|Element} element - 元素选择器或DOM元素
         */
        hide(element) {
            const $element = $(element);
            if ($element.length === 0) return;
            
            const originalHtml = $element.data('original-html');
            const originalDisabled = $element.data('original-disabled');
            
            if (originalHtml !== undefined) {
                $element.html(originalHtml);
            }
            if (originalDisabled !== undefined) {
                $element.prop('disabled', originalDisabled);
            }
        },

        /**
         * 显示全局加载遮罩
         */
        showGlobal() {
            $('#globalLoading').removeClass('d-none');
        },

        /**
         * 隐藏全局加载遮罩
         */
        hideGlobal() {
            $('#globalLoading').addClass('d-none');
        }
    },

    /**
     * 日期时间工具
     */
    datetime: {
        /**
         * 格式化日期
         * @param {Date|string|number} date - 日期对象、字符串或时间戳
         * @param {string} format - 格式字符串
         * @returns {string} 格式化后的日期字符串
         */
        format(date, format = 'YYYY-MM-DD HH:mm:ss') {
            if (!date) return '-';
            
            const d = new Date(date);
            if (isNaN(d.getTime())) return '-';
            
            const year = d.getFullYear();
            const month = String(d.getMonth() + 1).padStart(2, '0');
            const day = String(d.getDate()).padStart(2, '0');
            const hours = String(d.getHours()).padStart(2, '0');
            const minutes = String(d.getMinutes()).padStart(2, '0');
            const seconds = String(d.getSeconds()).padStart(2, '0');
            
            return format
                .replace('YYYY', year)
                .replace('MM', month)
                .replace('DD', day)
                .replace('HH', hours)
                .replace('mm', minutes)
                .replace('ss', seconds);
        },

        /**
         * 获取相对时间描述
         * @param {Date|string|number} date - 日期
         * @returns {string} 相对时间描述
         */
        relative(date) {
            if (!date) return '-';
            
            const now = new Date();
            const target = new Date(date);
            const diff = now - target;
            
            const minute = 60 * 1000;
            const hour = 60 * minute;
            const day = 24 * hour;
            
            if (diff < minute) return '刚刚';
            if (diff < hour) return Math.floor(diff / minute) + '分钟前';
            if (diff < day) return Math.floor(diff / hour) + '小时前';
            if (diff < 7 * day) return Math.floor(diff / day) + '天前';
            
            return this.format(date, 'YYYY-MM-DD');
        },

        /**
         * 获取今天的日期范围
         * @returns {Object} {start, end}
         */
        getToday() {
            const today = new Date();
            const start = new Date(today.getFullYear(), today.getMonth(), today.getDate());
            const end = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
            return { start, end };
        },

        /**
         * 获取指定天数前的日期范围
         * @param {number} days - 天数
         * @returns {Object} {start, end}
         */
        getLastDays(days) {
            const end = new Date();
            const start = new Date(end.getTime() - (days - 1) * 24 * 60 * 60 * 1000);
            start.setHours(0, 0, 0, 0);
            end.setHours(23, 59, 59, 999);
            return { start, end };
        }
    },

    /**
     * 数据格式化工具
     */
    format: {
        /**
         * 格式化文件大小
         * @param {number} bytes - 字节数
         * @returns {string} 格式化后的文件大小
         */
        fileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },

        /**
         * 格式化数字
         * @param {number} num - 数字
         * @param {number} decimals - 小数位数
         * @returns {string} 格式化后的数字
         */
        number(num, decimals = 2) {
            if (isNaN(num)) return '-';
            return Number(num).toLocaleString('zh-CN', {
                minimumFractionDigits: 0,
                maximumFractionDigits: decimals
            });
        },

        /**
         * 格式化百分比
         * @param {number} value - 数值（0-1）
         * @param {number} decimals - 小数位数
         * @returns {string} 百分比字符串
         */
        percentage(value, decimals = 1) {
            if (isNaN(value)) return '-';
            return (value * 100).toFixed(decimals) + '%';
        },

        /**
         * 截断文本
         * @param {string} text - 文本
         * @param {number} length - 最大长度
         * @param {string} suffix - 后缀
         * @returns {string} 截断后的文本
         */
        truncate(text, length = 50, suffix = '...') {
            if (!text || text.length <= length) return text || '';
            return text.substring(0, length) + suffix;
        }
    },

    /**
     * 函数工具
     */
    fn: {
        /**
         * 防抖函数
         * @param {Function} func - 要防抖的函数
         * @param {number} wait - 等待时间
         * @param {boolean} immediate - 是否立即执行
         * @returns {Function} 防抖后的函数
         */
        debounce(func, wait = CONFIG.DEBOUNCE_DELAY, immediate = false) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    timeout = null;
                    if (!immediate) func.apply(this, args);
                };
                const callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(this, args);
            };
        },

        /**
         * 节流函数
         * @param {Function} func - 要节流的函数
         * @param {number} limit - 限制时间
         * @returns {Function} 节流后的函数
         */
        throttle(func, limit = CONFIG.THROTTLE_DELAY) {
            let inThrottle;
            return function(...args) {
                if (!inThrottle) {
                    func.apply(this, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        },

        /**
         * 重试函数
         * @param {Function} fn - 要重试的函数
         * @param {number} maxRetries - 最大重试次数
         * @param {number} delay - 重试延迟
         * @returns {Promise} Promise对象
         */
        async retry(fn, maxRetries = CONFIG.MAX_RETRY_COUNT, delay = CONFIG.RETRY_DELAY) {
            let lastError;
            for (let i = 0; i <= maxRetries; i++) {
                try {
                    return await fn();
                } catch (error) {
                    lastError = error;
                    if (i < maxRetries) {
                        await new Promise(resolve => setTimeout(resolve, delay));
                    }
                }
            }
            throw lastError;
        }
    },

    /**
     * 对象工具
     */
    object: {
        /**
         * 深拷贝对象
         * @param {any} obj - 要拷贝的对象
         * @returns {any} 拷贝后的对象
         */
        deepClone(obj) {
            if (obj === null || typeof obj !== 'object') return obj;
            if (obj instanceof Date) return new Date(obj.getTime());
            if (obj instanceof Array) return obj.map(item => this.deepClone(item));
            if (typeof obj === 'object') {
                const clonedObj = {};
                for (const key in obj) {
                    if (obj.hasOwnProperty(key)) {
                        clonedObj[key] = this.deepClone(obj[key]);
                    }
                }
                return clonedObj;
            }
            return obj;
        },

        /**
         * 检查对象是否为空
         * @param {any} obj - 要检查的对象
         * @returns {boolean} 是否为空
         */
        isEmpty(obj) {
            if (obj == null) return true;
            if (Array.isArray(obj) || typeof obj === 'string') return obj.length === 0;
            if (typeof obj === 'object') return Object.keys(obj).length === 0;
            return false;
        },

        /**
         * 获取对象深层属性值
         * @param {Object} obj - 对象
         * @param {string} path - 属性路径，如 'a.b.c'
         * @param {any} defaultValue - 默认值
         * @returns {any} 属性值
         */
        get(obj, path, defaultValue = undefined) {
            const keys = path.split('.');
            let result = obj;
            for (const key of keys) {
                if (result == null || typeof result !== 'object') {
                    return defaultValue;
                }
                result = result[key];
            }
            return result !== undefined ? result : defaultValue;
        }
    },

    /**
     * 表单工具
     */
    form: {
        /**
         * 验证表单
         * @param {string|Element} formSelector - 表单选择器
         * @returns {boolean} 验证结果
         */
        validate(formSelector) {
            const form = $(formSelector)[0];
            if (!form) return false;
            
            form.classList.add('was-validated');
            return form.checkValidity();
        },

        /**
         * 重置表单
         * @param {string|Element} formSelector - 表单选择器
         */
        reset(formSelector) {
            const form = $(formSelector)[0];
            if (form) {
                form.reset();
                form.classList.remove('was-validated');
            }
        },

        /**
         * 获取表单数据
         * @param {string|Element} formSelector - 表单选择器
         * @returns {Object} 表单数据对象
         */
        getData(formSelector) {
            const form = $(formSelector);
            const data = {};
            form.find('input, select, textarea').each(function() {
                const $field = $(this);
                const name = $field.attr('name');
                if (name) {
                    if ($field.attr('type') === 'checkbox') {
                        data[name] = $field.is(':checked');
                    } else if ($field.attr('type') === 'radio') {
                        if ($field.is(':checked')) {
                            data[name] = $field.val();
                        }
                    } else {
                        data[name] = $field.val();
                    }
                }
            });
            return data;
        },

        /**
         * 设置表单数据
         * @param {string|Element} formSelector - 表单选择器
         * @param {Object} data - 数据对象
         */
        setData(formSelector, data) {
            const form = $(formSelector);
            Object.keys(data).forEach(name => {
                const $field = form.find(`[name="${name}"]`);
                if ($field.length > 0) {
                    const value = data[name];
                    if ($field.attr('type') === 'checkbox') {
                        $field.prop('checked', !!value);
                    } else if ($field.attr('type') === 'radio') {
                        $field.filter(`[value="${value}"]`).prop('checked', true);
                    } else {
                        $field.val(value);
                    }
                }
            });
        }
    },

    /**
     * URL工具
     */
    url: {
        /**
         * 获取URL参数
         * @param {string} name - 参数名
         * @param {string} url - URL字符串，默认为当前页面URL
         * @returns {string|null} 参数值
         */
        getParam(name, url = window.location.href) {
            const urlParams = new URLSearchParams(new URL(url).search);
            return urlParams.get(name);
        },

        /**
         * 设置URL参数
         * @param {string} name - 参数名
         * @param {string} value - 参数值
         * @param {boolean} pushState - 是否推入历史记录
         */
        setParam(name, value, pushState = true) {
            const url = new URL(window.location);
            url.searchParams.set(name, value);
            if (pushState) {
                window.history.pushState({}, '', url);
            } else {
                window.history.replaceState({}, '', url);
            }
        },

        /**
         * 删除URL参数
         * @param {string} name - 参数名
         * @param {boolean} pushState - 是否推入历史记录
         */
        removeParam(name, pushState = true) {
            const url = new URL(window.location);
            url.searchParams.delete(name);
            if (pushState) {
                window.history.pushState({}, '', url);
            } else {
                window.history.replaceState({}, '', url);
            }
        }
    },

    /**
     * 确认对话框
     * @param {string} message - 确认消息
     * @param {Function} callback - 确认后的回调函数
     * @param {Function} cancelCallback - 取消后的回调函数
     */
    confirm(message, callback, cancelCallback = null) {
        if (confirm(message)) {
            if (typeof callback === 'function') callback();
        } else {
            if (typeof cancelCallback === 'function') cancelCallback();
        }
    },

    // 向后兼容的方法
    showSuccess: function(message) { this.message.success(message); },
    showError: function(message) { this.message.error(message); },
    showWarning: function(message) { this.message.warning(message); },
    showInfo: function(message) { this.message.info(message); },
    showToast: function(message, type, duration) { this.message._showToast(message, type, duration); },
    showLoading: function(element, text) { this.loading.show(element, text); },
    hideLoading: function(element) { this.loading.hide(element); },
    formatDate: function(date, format) { return this.datetime.format(date, format); },
    formatFileSize: function(bytes) { return this.format.fileSize(bytes); },
    debounce: function(func, wait, immediate) { return this.fn.debounce(func, wait, immediate); },
    throttle: function(func, limit) { return this.fn.throttle(func, limit); },
    deepClone: function(obj) { return this.object.deepClone(obj); },
    validateForm: function(formSelector) { return this.form.validate(formSelector); },
    resetForm: function(formSelector) { this.form.reset(formSelector); },
    getUrlParam: function(name) { return this.url.getParam(name); },
    setUrlParam: function(name, value) { this.url.setParam(name, value); }
};

// ===== API请求封装 =====
const API = {
    /**
     * GET请求
     * @param {string} url - 请求URL
     * @param {Object} params - 查询参数
     * @param {Object} options - 请求选项
     * @returns {Promise} Promise对象
     */
    get(url, params = {}, options = {}) {
        return this.request('GET', url, null, params, options);
    },

    /**
     * POST请求
     * @param {string} url - 请求URL
     * @param {Object} data - 请求数据
     * @param {Object} options - 请求选项
     * @returns {Promise} Promise对象
     */
    post(url, data = {}, options = {}) {
        return this.request('POST', url, data, {}, options);
    },

    /**
     * PUT请求
     * @param {string} url - 请求URL
     * @param {Object} data - 请求数据
     * @param {Object} options - 请求选项
     * @returns {Promise} Promise对象
     */
    put(url, data = {}, options = {}) {
        return this.request('PUT', url, data, {}, options);
    },

    /**
     * DELETE请求
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise} Promise对象
     */
    delete(url, options = {}) {
        return this.request('DELETE', url, null, {}, options);
    },

    /**
     * 通用请求方法
     * @param {string} method - 请求方法
     * @param {string} url - 请求URL
     * @param {Object} data - 请求数据
     * @param {Object} params - 查询参数
     * @param {Object} options - 请求选项
     * @returns {Promise} Promise对象
     */
    async request(method, url, data = null, params = {}, options = {}) {
        try {
            // 构建完整URL
            const fullUrl = url.startsWith('http') ? url : CONFIG.API_BASE_URL + url;
            
            // 构建请求配置
            const config = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    ...options.headers
                },
                ...options
            };

            // 添加CSRF令牌
            if (window.APP_CONFIG?.csrfToken) {
                config.headers[window.APP_CONFIG.csrfHeader] = window.APP_CONFIG.csrfToken;
            }

            // 添加请求体
            if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
                config.body = JSON.stringify(data);
            }

            // 添加查询参数
            const urlWithParams = new URL(fullUrl, window.location.origin);
            Object.keys(params).forEach(key => {
                if (params[key] !== null && params[key] !== undefined) {
                    urlWithParams.searchParams.append(key, params[key]);
                }
            });

            // 发送请求
            const response = await fetch(urlWithParams.toString(), config);
            
            // 检查响应状态
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
            }

            // 解析响应
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            } else {
                return await response.text();
            }
        } catch (error) {
            console.error('API request failed:', error);
            
            // 显示用户友好的错误消息
            if (!options.silent) {
                Utils.message.error('请求失败: ' + error.message);
            }
            
            throw error;
        }
    },

    /**
     * 上传文件
     * @param {string} url - 上传URL
     * @param {FormData|File} fileData - 文件数据
     * @param {Object} options - 请求选项
     * @returns {Promise} Promise对象
     */
    upload(url, fileData, options = {}) {
        const formData = fileData instanceof FormData ? fileData : (() => {
            const fd = new FormData();
            fd.append('file', fileData);
            return fd;
        })();

        return this.request('POST', url, null, {}, {
            body: formData,
            headers: {
                // 不设置Content-Type，让浏览器自动设置
                'X-Requested-With': 'XMLHttpRequest'
            },
            ...options
        });
    }
};

// ===== DataTable默认配置 =====
const DataTableConfig = {
    /**
     * 初始化DataTable默认配置
     */
    init() {
        if (typeof $ !== 'undefined' && typeof $.fn.dataTable !== 'undefined') {
            $.extend(true, $.fn.dataTable.defaults, {
                language: {
                    "sProcessing": "处理中...",
                    "sLengthMenu": "显示 _MENU_ 项结果",
                    "sZeroRecords": "没有匹配结果",
                    "sInfo": "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
                    "sInfoEmpty": "显示第 0 至 0 项结果，共 0 项",
                    "sInfoFiltered": "(由 _MAX_ 项结果过滤)",
                    "sInfoPostFix": "",
                    "sSearch": "搜索:",
                    "sUrl": "",
                    "sEmptyTable": "表中数据为空",
                    "sLoadingRecords": "载入中...",
                    "sInfoThousands": ",",
                    "oPaginate": {
                        "sFirst": "首页",
                        "sPrevious": "上页",
                        "sNext": "下页",
                        "sLast": "末页"
                    },
                    "oAria": {
                        "sSortAscending": ": 以升序排列此列",
                        "sSortDescending": ": 以降序排列此列"
                    }
                },
                pageLength: CONFIG.TABLE_PAGE_SIZE,
                responsive: true,
                autoWidth: false,
                dom: '<"row"<"col-sm-6"l><"col-sm-6"f>>rtip',
                drawCallback: function() {
                    // 重新初始化工具提示
                    $('[data-bs-toggle="tooltip"]').tooltip();
                }
            });
            console.log('DataTable默认配置已设置');
        } else {
            console.warn('DataTable未加载，无法设置默认配置');
        }
    }
};

// ===== 页面初始化 =====
$(document).ready(function() {
    try {
        // 延迟初始化DataTable默认配置
        setTimeout(() => {
            DataTableConfig.init();
        }, 500);
        
        // 初始化Bootstrap组件
        if (typeof bootstrap !== 'undefined') {
            // 初始化所有工具提示
            $('[data-bs-toggle="tooltip"]').tooltip();
            // 初始化所有弹出框
            $('[data-bs-toggle="popover"]').popover();
        }
        
        console.log('通用组件初始化完成');
    } catch (error) {
        console.error('通用组件初始化失败:', error);
    }
});

// ===== 全局错误处理 =====
window.addEventListener('error', function(e) {
    console.error('Global error:', e.error);
    if (typeof Utils !== 'undefined' && Utils.message) {
        Utils.message.error('发生未知错误，请刷新页面重试');
    }
});

// 全局未处理的Promise拒绝
window.addEventListener('unhandledrejection', function(e) {
    console.error('Unhandled promise rejection:', e.reason);
    if (typeof Utils !== 'undefined' && Utils.message) {
        Utils.message.error('请求处理失败，请重试');
    }
});

// ===== 导出到全局 =====
window.Utils = Utils;
window.API = API;
window.CONFIG = CONFIG;
window.DataTableConfig = DataTableConfig;