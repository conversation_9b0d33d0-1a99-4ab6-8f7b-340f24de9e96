/**
 * ChatBI 对话标注平台 - 精简版
 * 支持Session级别的多轮对话标注
 */

// 全局变量
let currentSessionId = null;
let currentConversations = [];
let currentAnnotatingRound = 1;
let sessionList = [];
let currentPage = 1;
let pageSize = 20;

// 页面初始化
$(document).ready(function() {
    try {
        console.log('ChatBI对话标注平台初始化开始...');

        // 设置默认租户ID
        $('#tenantFilter').val('1');

        // 绑定搜索和筛选事件
        bindSearchAndFilterEvents();

        // 加载Session列表
        loadSessionList();

        console.log('ChatBI对话标注平台初始化完成');
    } catch (e) {
        console.error('页面初始化失败:', e);
        showAlert('页面初始化失败，请刷新重试', 'danger');
    }
});

// 绑定搜索和筛选事件
function bindSearchAndFilterEvents() {
    // 关键词搜索
    $('#keywordSearch').on('input', debounce(function() {
        currentPage = 1;
        loadSessionList();
    }, 500));

    // 搜索按钮点击
    $('#searchBtn').click(function() {
        currentPage = 1;
        loadSessionList();
    });

    // 筛选条件变化
    $('#tenantFilter, #userIdSearch, #timeRangeFilter, #annotationStatusFilter').change(function() {
        currentPage = 1;
        loadSessionList();
    });
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            try {
                func(...args);
            } catch (e) {
                console.error('防抖函数执行失败:', e);
            }
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 加载Session列表
function loadSessionList() {
    try {
        console.log('开始加载Session列表...');
        
        // 构建请求参数
        const request = {
            tenantId: $('#tenantFilter').val() || 'default',
            keyword: $('#keywordSearch').val(),
            userId: $('#userIdSearch').val(),
            annotationStatus: $('#annotationStatusFilter').val() ? parseInt($('#annotationStatusFilter').val()) : null,
            page: currentPage,
            size: pageSize
        };
        
        // 处理时间范围
        const timeRange = $('#timeRangeFilter').val();
        if (timeRange) {
            const timeRanges = getTimeRange(timeRange);
            request.startTime = timeRanges.start;
            request.endTime = timeRanges.end;
        }
        
        // 发送请求
        API.post('/chatAnnotation/getSessionList', request)
            .then(response => {
                console.log('API响应:', response);
                if (response.success) {
                    sessionList = response.data.content;
                    console.log('接收到的sessionList:', sessionList);
                    console.log('sessionList类型:', typeof sessionList, 'length:', sessionList ? sessionList.length : 'null');
                    renderSessionList(sessionList);
                    updateSessionCount(response.data.totalElements);
                    console.log('Session列表加载成功:', sessionList.length);
                } else {
                    console.error('Session列表加载失败:', response.message);
                    showAlert('Session列表加载失败: ' + response.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Session列表加载请求失败:', error);
                showAlert('Session列表加载失败，请重试', 'danger');
            });
    } catch (e) {
        console.error('加载Session列表失败:', e);
        showAlert('加载Session列表失败', 'danger');
    }
}

// 渲染Session列表
function renderSessionList(sessions) {
    console.log('开始渲染Session列表:', sessions);
    const container = $('#sessionList');
    container.empty();

    if (!sessions || sessions.length === 0) {
        console.log('Session列表为空，显示空状态');
        container.html(`
            <div class="text-center text-muted p-4">
                <i class="fas fa-inbox fa-2x mb-2"></i>
                <p>暂无Session数据</p>
            </div>
        `);
        return;
    }

    console.log('开始渲染', sessions.length, '个Session项');
    sessions.forEach((session, index) => {
        try {
            console.log(`渲染第${index + 1}个Session:`, session);
            const sessionItem = createSessionItem(session);
            container.append(sessionItem);
        } catch (e) {
            console.error(`渲染第${index + 1}个Session失败:`, e, session);
        }
    });
    console.log('Session列表渲染完成');
}

// 创建Session列表项
function createSessionItem(session) {
    try {
        // 安全检查session对象
        if (!session) {
            console.error('session对象为空');
            return $('<div class="text-danger p-2">Session数据错误</div>');
        }

        const isAnnotated = session.isAnnotated === 1;
        const annotationBadge = isAnnotated
            ? `<span class="badge bg-success">已标注</span>`
            : `<span class="badge bg-warning">待标注</span>`;

        const scoreDisplay = isAnnotated && session.sessionScore
            ? `<small class="text-muted">评分: ${session.sessionScore}/5</small>`
            : '';

        return $(`
            <div class="session-item p-3 border-bottom cursor-pointer" data-session-id="${session.sessionId || ''}">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div class="flex-grow-1">
                        <div class="fw-bold text-truncate" style="max-width: 200px;" title="${session.firstQuestion || ''}">
                            ${session.firstQuestion || '无问题内容'}
                        </div>
                        <small class="text-muted">用户: ${session.userId || '未知用户'}</small>
                    </div>
                    <div class="text-end">
                        ${annotationBadge}
                    </div>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">
                        <i class="fas fa-comments me-1"></i>${session.conversationCount || 0}轮对话
                    </small>
                    <small class="text-muted">
                        ${formatDateTime(session.lastQueryTime)}
                    </small>
                </div>
                ${scoreDisplay ? `<div class="mt-1">${scoreDisplay}</div>` : ''}
            </div>
        `).click(function() {
            if (session.sessionId) {
                selectSession(session.sessionId);
            } else {
                console.error('sessionId为空，无法选择Session');
            }
        });
    } catch (e) {
        console.error('创建Session列表项失败:', e, session);
        return $('<div class="text-danger p-2">Session渲染失败</div>');
    }
}

// 选择Session
function selectSession(sessionId) {
    try {
        console.log('选择Session:', sessionId);
        
        // 更新选中状态
        $('.session-item').removeClass('bg-light border-primary');
        $(`.session-item[data-session-id="${sessionId}"]`).addClass('bg-light border-primary');
        
        currentSessionId = sessionId;
        
        // 加载Session详情
        loadSessionDetail(sessionId);
    } catch (e) {
        console.error('选择Session失败:', e);
        showAlert('选择Session失败', 'danger');
    }
}

// 加载Session详情
function loadSessionDetail(sessionId) {
    try {
        console.log('开始加载Session详情:', sessionId);
        
        const request = {
            sessionId: sessionId,
            tenantId: $('#tenantFilter').val() || 'default'
        };
        
        API.post('/chatAnnotation/getSessionDetail', request)
            .then(response => {
                if (response.success && response.data) {
                    currentConversations = response.data.conversations || [];
                    renderConversationDisplay(response.data);

                    // 默认选择第一轮对话进行标注
                    if (currentConversations.length > 0) {
                        selectConversationRound(1);
                    }

                    console.log('Session详情加载成功:', currentConversations.length);
                } else {
                    console.error('Session详情加载失败:', response.message);
                    showAlert('Session详情加载失败: ' + response.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Session详情加载请求失败:', error);
                showAlert('Session详情加载失败，请重试', 'danger');
            });
    } catch (e) {
        console.error('加载Session详情失败:', e);
        showAlert('加载Session详情失败', 'danger');
    }
}

// 渲染对话展示区
function renderConversationDisplay(sessionDetail) {
    const container = $('#conversationDisplay');
    const titleContainer = $('#currentSessionTitle');
    const countContainer = $('#conversationCount');
    
    // 更新标题
    titleContainer.text(`Session: ${sessionDetail.sessionId.substring(0, 8)}...`);
    countContainer.text(`${sessionDetail.conversationCount}轮对话`);
    
    container.empty();
    
    if (!currentConversations || currentConversations.length === 0) {
        container.html(`
            <div class="text-center text-muted p-4">
                <i class="fas fa-comments fa-2x mb-2"></i>
                <p>该Session暂无对话记录</p>
            </div>
        `);
        return;
    }
    
    // 渲染每轮对话
    currentConversations.forEach((conversation, index) => {
        const conversationItem = createConversationItem(conversation, index + 1);
        container.append(conversationItem);
    });
}

// 创建对话项
function createConversationItem(conversation, round) {
    // 安全检查annotation对象
    const isAnnotated = conversation.annotation !== null && conversation.annotation !== undefined;
    const annotationBadge = isAnnotated
        ? `<span class="badge bg-success">已标注</span>`
        : `<span class="badge bg-warning">待标注</span>`;

    // 安全访问roundScore属性
    const scoreDisplay = isAnnotated && conversation.annotation && conversation.annotation.roundScore
        ? `<small class="text-success">评分: ${conversation.annotation.roundScore}/5</small>`
        : '';

    return $(`
        <div class="conversation-item border-bottom p-3 cursor-pointer" data-round="${round}">
            <div class="d-flex justify-content-between align-items-start mb-2">
                <h6 class="mb-0">第${round}轮对话</h6>
                <div>
                    ${annotationBadge}
                    ${scoreDisplay}
                </div>
            </div>

            <!-- 用户问题 -->
            <div class="mb-3">
                <div class="d-flex align-items-center mb-1">
                    <i class="fas fa-user text-primary me-2"></i>
                    <small class="text-muted">用户问题</small>
                </div>
                <div class="bg-light p-2 rounded">
                    ${conversation.userQuestion || '无问题内容'}
                </div>
            </div>

            <!-- AI回答 -->
            <div class="mb-2">
                <div class="d-flex align-items-center mb-1">
                    <i class="fas fa-robot text-success me-2"></i>
                    <small class="text-muted">AI回答</small>
                </div>
                <div class="bg-light p-2 rounded" style="max-height: 150px; overflow-y: auto;">
                    ${renderAIResponse(conversation.aiResponse)}
                </div>
            </div>

            <div class="text-end">
                <small class="text-muted">
                    ${formatDateTime(conversation.queryTime)} |
                    响应时间: ${conversation.responseTime || 0}ms
                </small>
            </div>
        </div>
    `).click(function() {
        selectConversationRound(round);
    });
}

// 渲染AI回答 - 统一使用ResponseComponent字段命名
function renderAIResponse(aiResponse) {
    if (!aiResponse) return '暂无回答内容';

    let html = '';

    // 推理分析组件
    if (aiResponse.reasoningResponse) {
        html += `<div class="mb-2">
            <strong>🧠 推理:</strong>
            <div class="ms-2 text-muted">${aiResponse.reasoningResponse}</div>
        </div>`;
    }

    // 图表数据组件
    if (aiResponse.chartDataResponse) {
        try {
            // 如果是JSON字符串，尝试解析并格式化显示
            const chartData = typeof aiResponse.chartDataResponse === 'string'
                ? JSON.parse(aiResponse.chartDataResponse)
                : aiResponse.chartDataResponse;

            if (chartData && Object.keys(chartData).length > 0) {
                html += `<div class="mb-2">
                    <strong>📊 图表数据:</strong>
                    <div class="ms-2 text-muted">${JSON.stringify(chartData, null, 2)}</div>
                </div>`;
            }
        } catch (e) {
            // 如果解析失败，直接显示原始内容
            html += `<div class="mb-2">
                <strong>📊 图表数据:</strong>
                <div class="ms-2 text-muted">${aiResponse.chartDataResponse}</div>
            </div>`;
        }
    }

    // 数据洞察组件
    if (aiResponse.insightResponse) {
        html += `<div class="mb-2">
            <strong>💡 洞察:</strong>
            <div class="ms-2 text-muted">${aiResponse.insightResponse}</div>
        </div>`;
    }

    // 追问建议组件
    if (aiResponse.followUpResponse) {
        try {
            const suggestions = typeof aiResponse.followUpResponse === 'string'
                ? JSON.parse(aiResponse.followUpResponse)
                : aiResponse.followUpResponse;

            if (Array.isArray(suggestions) && suggestions.length > 0) {
                html += `<div class="mb-2">
                    <strong>🔍 建议:</strong>
                    <div class="ms-2 text-muted">${suggestions.join(', ')}</div>
                </div>`;
            } else if (suggestions) {
                html += `<div class="mb-2">
                    <strong>🔍 建议:</strong>
                    <div class="ms-2 text-muted">${suggestions}</div>
                </div>`;
            }
        } catch (e) {
            html += `<div class="mb-2">
                <strong>🔍 建议:</strong>
                <div class="ms-2 text-muted">${aiResponse.followUpResponse}</div>
            </div>`;
        }
    }

    // 执行日志
    if (aiResponse.actionLogs) {
        html += `<div class="mb-2">
            <strong>📋 执行日志:</strong>
            <div class="ms-2 text-muted" style="font-size: 11px; max-height: 100px; overflow-y: auto;">
                ${aiResponse.actionLogs}
            </div>
        </div>`;
    }

    return html || '暂无回答内容';
}

// 选择对话轮次进行标注
function selectConversationRound(round) {
    try {
        console.log('选择第', round, '轮对话进行标注');

        // 清除所有选中状态
        $('.conversation-item').removeClass('bg-light border-primary selected-conversation bg-primary text-white');
        console.log('清除了所有选中状态');

        // 添加新的选中状态
        const targetElement = $(`.conversation-item[data-round="${round}"]`);
        targetElement.addClass('selected-conversation');
        console.log('为第', round, '轮对话添加了selected-conversation类');
        console.log('当前元素的类:', targetElement.attr('class'));

        currentAnnotatingRound = round;

        // 渲染标注面板
        renderAnnotationPanel(round);
    } catch (e) {
        console.error('选择对话轮次失败:', e);
        showAlert('选择对话轮次失败', 'danger');
    }
}

// 渲染标注面板
function renderAnnotationPanel(round) {
    const container = $('#annotationPanel');
    const titleContainer = $('#currentAnnotationTitle');

    // 更新标题
    titleContainer.text(`第${round}轮标注`);

    const conversation = currentConversations[round - 1];
    if (!conversation) {
        container.html('<div class="text-center text-muted p-4">无效的对话轮次</div>');
        return;
    }

    const existingAnnotation = conversation.annotation;

    container.html(`
        <form id="annotationForm">
            <!-- 4个组件标注 -->
            <div class="mb-2">
                <h6 class="mb-2" style="font-size: 14px;">
                    <i class="fas fa-puzzle-piece me-1"></i>组件标注
                </h6>

                <!-- 推理组件 -->
                <div class="component-annotation" data-component="reasoning">
                    <label class="form-label fw-bold">🧠 推理分析</label>
                    <div class="row">
                        <div class="col-6">
                            <select class="form-select form-select-sm component-score" name="reasoning_score">
                                <option value="">评分</option>
                                <option value="1">1分</option>
                                <option value="2">2分</option>
                                <option value="3">3分</option>
                                <option value="4">4分</option>
                                <option value="5">5分</option>
                            </select>
                        </div>
                        <div class="col-6">
                            <input type="text" class="form-control form-control-sm component-tags"
                                   name="reasoning_tags" placeholder="标签">
                        </div>
                    </div>
                    <textarea class="form-control form-control-sm mt-1 component-comments"
                              name="reasoning_comments" rows="1" placeholder="评价..."></textarea>
                </div>

                <!-- 图表数据组件 -->
                <div class="component-annotation" data-component="chartData">
                    <label class="form-label fw-bold">📊 图表数据</label>
                    <div class="row">
                        <div class="col-6">
                            <select class="form-select form-select-sm component-score" name="chartData_score">
                                <option value="">评分</option>
                                <option value="1">1分</option>
                                <option value="2">2分</option>
                                <option value="3">3分</option>
                                <option value="4">4分</option>
                                <option value="5">5分</option>
                            </select>
                        </div>
                        <div class="col-6">
                            <input type="text" class="form-control form-control-sm component-tags"
                                   name="chartData_tags" placeholder="标签">
                        </div>
                    </div>
                    <textarea class="form-control form-control-sm mt-1 component-comments"
                              name="chartData_comments" rows="1" placeholder="评价..."></textarea>
                </div>

                <!-- 洞察组件 -->
                <div class="component-annotation" data-component="insight">
                    <label class="form-label fw-bold">💡 数据洞察</label>
                    <div class="row">
                        <div class="col-6">
                            <select class="form-select form-select-sm component-score" name="insight_score">
                                <option value="">评分</option>
                                <option value="1">1分</option>
                                <option value="2">2分</option>
                                <option value="3">3分</option>
                                <option value="4">4分</option>
                                <option value="5">5分</option>
                            </select>
                        </div>
                        <div class="col-6">
                            <input type="text" class="form-control form-control-sm component-tags"
                                   name="insight_tags" placeholder="标签">
                        </div>
                    </div>
                    <textarea class="form-control form-control-sm mt-1 component-comments"
                              name="insight_comments" rows="1" placeholder="评价..."></textarea>
                </div>

                <!-- 追问建议组件 -->
                <div class="component-annotation" data-component="followUp">
                    <label class="form-label fw-bold">🔍 追问建议</label>
                    <div class="row">
                        <div class="col-6">
                            <select class="form-select form-select-sm component-score" name="followUp_score">
                                <option value="">评分</option>
                                <option value="1">1分</option>
                                <option value="2">2分</option>
                                <option value="3">3分</option>
                                <option value="4">4分</option>
                                <option value="5">5分</option>
                            </select>
                        </div>
                        <div class="col-6">
                            <input type="text" class="form-control form-control-sm component-tags"
                                   name="followUp_tags" placeholder="标签">
                        </div>
                    </div>
                    <textarea class="form-control form-control-sm mt-1 component-comments"
                              name="followUp_comments" rows="1" placeholder="评价..."></textarea>
                </div>
            </div>

            <!-- 单轮整体标注 -->
            <div class="mb-2">
                <h6 class="mb-2" style="font-size: 14px;">
                    <i class="fas fa-star me-1"></i>单轮整体评价
                </h6>

                <!-- 整体评分 -->
                <div class="mb-2">
                    <label class="form-label" style="font-size: 12px;">整体评分 <span class="text-danger">*</span></label>
                    <div class="btn-group w-100" role="group">
                        <input type="radio" class="btn-check" name="roundScore" id="roundScore1" value="1">
                        <label class="btn btn-outline-danger" style="font-size: 11px; padding: 2px 4px;" for="roundScore1">1</label>

                        <input type="radio" class="btn-check" name="roundScore" id="roundScore2" value="2">
                        <label class="btn btn-outline-warning" style="font-size: 11px; padding: 2px 4px;" for="roundScore2">2</label>

                        <input type="radio" class="btn-check" name="roundScore" id="roundScore3" value="3">
                        <label class="btn btn-outline-info" style="font-size: 11px; padding: 2px 4px;" for="roundScore3">3</label>

                        <input type="radio" class="btn-check" name="roundScore" id="roundScore4" value="4">
                        <label class="btn btn-outline-primary" style="font-size: 11px; padding: 2px 4px;" for="roundScore4">4</label>

                        <input type="radio" class="btn-check" name="roundScore" id="roundScore5" value="5">
                        <label class="btn btn-outline-success" style="font-size: 11px; padding: 2px 4px;" for="roundScore5">5</label>
                    </div>
                </div>

                <!-- 整体标签 -->
                <div class="mb-2">
                    <label class="form-label" style="font-size: 12px;">整体标签</label>
                    <input type="text" class="form-control form-control-sm" id="roundTags"
                           placeholder="输入标签，逗号分隔" style="font-size: 11px; padding: 2px 6px;">
                </div>

                <!-- 整体评价 -->
                <div class="mb-2">
                    <label class="form-label" style="font-size: 12px;">整体评价</label>
                    <textarea class="form-control form-control-sm" id="roundComments"
                              rows="2" placeholder="请输入对本轮回答的整体评价..." style="font-size: 11px; padding: 2px 6px; resize: none;"></textarea>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="d-grid gap-1">
                <button type="button" class="btn btn-success btn-sm" onclick="saveQueryAnnotation()" style="font-size: 11px; padding: 4px 8px;">
                    <i class="fas fa-save me-1"></i>保存问题级标注
                </button>
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="showSessionAnnotationModal()" style="font-size: 11px; padding: 4px 8px;">
                    <i class="fas fa-layer-group me-1"></i>Session整体标注
                </button>
            </div>
        </form>
    `);

    // 如果存在标注，填充数据
    if (existingAnnotation) {
        fillAnnotationForm(existingAnnotation);
    }
}

// 填充标注表单
function fillAnnotationForm(annotation) {
    try {
        // 安全检查annotation对象
        if (!annotation) {
            console.log('annotation为空，跳过表单填充');
            return;
        }

        // 填充组件标注
        if (annotation.componentAnnotations) {
            const components = annotation.componentAnnotations;

            ['reasoning', 'chartData', 'insight', 'followUp'].forEach(component => {
                const componentData = components[component];
                if (componentData) {
                    $(`[name="${component}_score"]`).val(componentData.score || '');
                    $(`[name="${component}_tags"]`).val(componentData.tags ? componentData.tags.join(', ') : '');
                    $(`[name="${component}_comments"]`).val(componentData.comments || '');
                }
            });
        }

        // 填充单轮标注
        if (annotation.roundScore) {
            $(`#roundScore${annotation.roundScore}`).prop('checked', true);
        }

        if (annotation.roundTags && Array.isArray(annotation.roundTags)) {
            $('#roundTags').val(annotation.roundTags.join(', '));
        }

        if (annotation.roundComments) {
            $('#roundComments').val(annotation.roundComments);
        }

        console.log('标注表单填充完成');
    } catch (e) {
        console.error('填充标注表单失败:', e);
    }
}

// 保存问题级标注
function saveQueryAnnotation() {
    try {
        console.log('开始保存问题级标注...');

        // 验证必填项
        const roundScore = $('input[name="roundScore"]:checked').val();
        if (!roundScore) {
            showAlert('请选择整体评分', 'warning');
            return;
        }

        const conversation = currentConversations[currentAnnotatingRound - 1];
        if (!conversation) {
            showAlert('无效的对话轮次', 'danger');
            return;
        }

        // 收集组件标注数据
        const componentAnnotations = {};
        ['reasoning', 'chartData', 'insight', 'followUp'].forEach(component => {
            const score = $(`[name="${component}_score"]`).val();
            const tags = $(`[name="${component}_tags"]`).val();
            const comments = $(`[name="${component}_comments"]`).val();

            if (score || tags || comments) {
                componentAnnotations[component] = {
                    score: score ? parseInt(score) : null,
                    tags: tags ? tags.split(',').map(t => t.trim()).filter(t => t) : [],
                    comments: comments || ''
                };
            }
        });

        // 收集单轮标注数据
        const roundTags = $('#roundTags').val();
        const roundComments = $('#roundComments').val();

        // 构建请求
        const request = {
            requestId: conversation.conversationId,
            sessionId: currentSessionId,
            tenantId: $('#tenantFilter').val() || 'default',
            componentAnnotations: JSON.stringify(componentAnnotations),
            roundScore: parseInt(roundScore),
            roundTags: roundTags ? roundTags.split(',').map(t => t.trim()).filter(t => t) : [],
            roundComments: roundComments || '',
            attachments: null // 暂不支持附件
        };

        // 发送请求
        API.post('/chatAnnotation/saveQueryAnnotation', request)
            .then(response => {
                if (response.success) {
                    showAlert('问题级标注保存成功', 'success');

                    // 更新本地数据
                    updateLocalAnnotation(conversation, request);

                    // 刷新显示 - 重新渲染对话列表
                    renderConversationDisplay({
                        sessionId: currentSessionId,
                        conversationCount: currentConversations.length
                    });

                    // 重新选择当前轮次
                    selectConversationRound(currentAnnotatingRound);

                    console.log('问题级标注保存成功');
                } else {
                    showAlert('问题级标注保存失败: ' + response.message, 'danger');
                }
            })
            .catch(error => {
                console.error('问题级标注保存请求失败:', error);
                showAlert('问题级标注保存失败，请重试', 'danger');
            });
    } catch (e) {
        console.error('保存问题级标注失败:', e);
        showAlert('保存问题级标注失败', 'danger');
    }
}

// 更新本地标注数据
function updateLocalAnnotation(conversation, request) {
    try {
        // 解析组件标注
        const componentAnnotations = JSON.parse(request.componentAnnotations);

        // 更新conversation的annotation
        conversation.annotation = {
            componentAnnotations: componentAnnotations,
            roundScore: request.roundScore,
            roundTags: request.roundTags,
            roundComments: request.roundComments,
            createTime: Date.now()
        };

        console.log('本地标注数据更新完成');
    } catch (e) {
        console.error('更新本地标注数据失败:', e);
    }
}

// 显示Session整体标注模态框
function showSessionAnnotationModal() {
    // 这里可以实现Session级标注的模态框
    // 暂时用简单的prompt代替
    const sessionScore = prompt('请输入Session整体评分(1-5):');
    if (sessionScore && sessionScore >= 1 && sessionScore <= 5) {
        const sessionComments = prompt('请输入Session整体评价:') || '';

        saveSessionAnnotation(parseInt(sessionScore), [], sessionComments);
    }
}

// 保存Session级标注
function saveSessionAnnotation(sessionScore, sessionTags, sessionComments) {
    try {
        console.log('开始保存Session级标注...');

        const request = {
            sessionId: currentSessionId,
            tenantId: $('#tenantFilter').val() || 'default',
            userId: 'current_user', // 这里应该从当前用户获取
            sessionScore: sessionScore,
            sessionTags: sessionTags,
            sessionComments: sessionComments
        };

        API.post('/chatAnnotation/saveSessionAnnotation', request)
            .then(response => {
                if (response.success) {
                    showAlert('Session级标注保存成功', 'success');

                    // 刷新Session列表
                    loadSessionList();

                    console.log('Session级标注保存成功');
                } else {
                    showAlert('Session级标注保存失败: ' + response.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Session级标注保存请求失败:', error);
                showAlert('Session级标注保存失败，请重试', 'danger');
            });
    } catch (e) {
        console.error('保存Session级标注失败:', e);
        showAlert('保存Session级标注失败', 'danger');
    }
}

// 工具函数：获取时间范围
function getTimeRange(range) {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    switch (range) {
        case 'today':
            return {
                start: today.getTime(),
                end: now.getTime()
            };
        case 'yesterday':
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            return {
                start: yesterday.getTime(),
                end: today.getTime()
            };
        case 'week':
            const weekStart = new Date(today);
            weekStart.setDate(weekStart.getDate() - weekStart.getDay());
            return {
                start: weekStart.getTime(),
                end: now.getTime()
            };
        case 'month':
            const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
            return {
                start: monthStart.getTime(),
                end: now.getTime()
            };
        default:
            return null;
    }
}

// 工具函数：格式化日期时间
function formatDateTime(timestamp) {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;

    // 如果是今天
    if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {
        return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    }

    // 如果是本年
    if (date.getFullYear() === now.getFullYear()) {
        return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }) + ' ' +
               date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    }

    // 完整日期
    return date.toLocaleDateString('zh-CN') + ' ' +
           date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
}

// 工具函数：更新Session数量
function updateSessionCount(count) {
    $('#sessionCount').text(count);
}

// 工具函数：显示提示信息
function showAlert(message, type = 'info') {
    // 使用common.js中的Utils.message方法
    switch (type) {
        case 'success':
            Utils.message.success(message);
            break;
        case 'danger':
        case 'error':
            Utils.message.error(message);
            break;
        case 'warning':
            Utils.message.warning(message);
            break;
        case 'info':
        default:
            Utils.message.info(message);
            break;
    }
}
