package com.facishare.bi.common.entities;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 数值字段
 */
@Data
public class MeasureFieldDto implements Serializable {
  private static final long serialVersionUID = -1L;
  private String whatFieldType;
  @JSONField(name = "fieldID")
  private String fieldID;

  @JSONField(name = "fieldName")
  private String fieldName;


  @JSONField(name = "fieldType")
  private String fieldType;


  @JSONField(name = "dbFieldName")
  private String dbFieldName;


  @JSONField(name = "dbObjName")
  private String dbObjName;


  @JSONField(name = "isVisible")
  private int isVisible = 1;//默认值为1，前端不传的时候默认构造为显示

  @JSONField(name = "orderType")
  private int orderType;  // 默认值0，不排序


  @JSONField(name = "aggrType")
  private String aggrType = "2"; // 给定默认值，计数 0无统计方式,1计数,2求和,3最大值,4最小值,5平均值,6唯一计数,7同比增长值,8同比增长率,9环比增长值,10环比增长率


  @JSONField(name = "yAxisIndex")
  private int yAxisIndex;     // y轴从左到右的顺序，默认值0，表示y轴


  @JSONField(name = "legendName")
  private String legendName;

  @JSONField(name = "isDetail")
  private int isDetail;


  @JSONField(name = "parentID")
  private String parentID;    // 层级字段ID


  @JSONField(name = "formula")
  private String formula;


  @JSONField(name = "formatStr")
  private String formatStr;


  @JSONField(name = "ratioType")//同环比类型
  private String ratioType = "0";


  @JSONField(name = "fieldLocation")
  private String fieldLocation; //指标字段槽位


  @JSONField(name = "subFieldType")
  private String subFieldType;


  @JSONField(name = "refObjName")
  private String refObjName;


  @JSONField(name = "isPredefined")//同环比类型
  private int isPredefined;


  @JSONField(name = "status")
  private int status;


  @JSONField(name = "calcFeature")
  private String calcFeature;

  /**
   * chenshy 20181106
   * 前端展示用的聚合方式
   * 除了主属性唯一计数为唯一计数之外，其它都是去重求和
   * 即61变成6
   * 6变成2
   */

  @JSONField(name = "aggShowType")
  private String aggShowType;

  private String udfFieldId;


  @JSONField(name = "isNullActionDate")
  private boolean isNullActionDate;


  @JSONField(name = "isShowLegend")
  private int isShowLegend = 1; //是否显示图例  默认显示


  @JSONField(name = "cellWidth")
  private int cellWidth;//宽度支持


  @JSONField(name = "objShowName")
  private String objShowName;


  @JSONField(name = "allowDetailExport")
  private int allowDetailExport;


  @JSONField(name = "type")
  private String type;


  @JSONField(name = "isShowValue")
  private int isShowValue = 1; //是否显示数值  默认显示


  @JSONField(name = "fixed")
  private int fixed;


  /**
   * chenshy 20181120
   * 数据权限重构需要用到
   */
  private String objectDescribeApiName;

  //当前字段可选的聚合方式
  private Map<String, String> aggrTypeMap;

  private FormatConfig formatConfig;

  private String ruleCreateTime;
  private String ruleLastModifiedTime;

  private boolean keyCountDistinctType;

  private List<String> operateMenu; //["order","filter","group","aggr"]字段可被做的操作。是否可作为排序、数据范围、分组、指标。

  private CanNotOperateDesc canNotOperateDesc;

  private MeasureConfig measureConfig;


  /**
   * 是否掩码显示
   */
  private boolean isShowMask;
  /**
   * 去掩码显示角色
   * 数据格式为 {"data_roles":["owner","data_owner_main_dept_leader"],"role":["00000000000000000000000000000006"]}
   */
  private String removeMaskRoles;

  /**
   * 来源于 stat_view_field表中的view_field_id,用于翻译legendName
   */
  private String viewFieldId;

  /**
   * 引用字段的原始字段类型
   */
  private String quoteType;
  /**
   * 引用字段的原始字段信息
   */
  private String refTargetField;

  private String analysisType;

  private int isCalc = 0;

}
