package com.facishare.bi.metadata.context.dto.ads;

import lombok.Data;

import java.sql.Date;

/**
 * Created by shaobr 2019-12-18
 * statviewfilter实体类
 */
@Data
public class StatViewFilter {
  private String filterId;
  private String viewId;
  private String fieldId;
  private String parentId;
  private Integer displayNum;
  private Integer operator;
  private String value1;
  private String value2;
  private int isDefault;
  private String dateRangeId;
  private Integer ei;
  private Integer filterType;
  private String optionId;
  private int isLock;
  private int creator;
  private Date createTime;
  private int updator;
  private Date updateTime;
  private int isDelete;
}
