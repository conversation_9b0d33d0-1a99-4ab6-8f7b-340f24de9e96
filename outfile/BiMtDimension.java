package com.facishare.bi.metadata.context.dto.dw;


import com.facishare.bi.metadata.context.dto.UdfObjFieldBoExt;
import lombok.Data;

import java.util.List;

/**
 * 维度；不嵌套ODS实体
 */
@Data
public class BiMtDimension {
  /**
   * 基础标识
   */
  private String tenantId;
  private String dimensionId;
  private String topologyDescribeId;

  /**
   * 维度基本属性
   */
  private String dimensionField;
  private String dimensionType;
  private String dimensionName;
  private String customType;
  private String customTypeName;
  private String description;
  private String dimensionConfig;

  /**
   * 对象关联信息
   */
  private String describeApiName;
  private List<String> describeApiNames;
  

  /**
   * 审计信息
   */
  private Long createTime;
  private Long lastModifiedTime;
  private String createdBy;
  private String lastModifiedBy;

  /**
   * 状态标记
   */
  private Integer status;
  private Integer isHide;
  private Integer isDeleted;
  private Integer isUsed;
  private String disableReason;


  /**
   * 引用关系
   */
  private String relationFieldId;
  private Integer relationLevel;

  /**
   * 源数据信息
   */
  private String sourceDimensionId;
  private String fieldId;
  private String sourceDescribeApiName;

  


  /**
   * 指向的字段描述
   */
  @Deprecated
  private UdfObjFieldBoExt udfObjFieldBoExt;
}
