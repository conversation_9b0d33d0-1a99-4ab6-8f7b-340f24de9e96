//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.facishare.bi.common.entities.filter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import lombok.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public enum FilterOperatorMapByTypeEnum {
  TEXT_LIKE(Arrays.asList("text", "long_text", "email", "phone_number", "url", "object_reference", "auto_number", "rich_text", "what_list_data"), "LIKE", 1, "包含", "text"),
  TEXT_NLIKE(Arrays.asList("text", "long_text", "email", "phone_number", "url", "object_reference", "auto_number", "rich_text", "what_list_data"), "NLIKE", 2, "不包含", "text"),
  TEXT_IN(Arrays.asList("text", "long_text", "email", "phone_number", "url", "object_reference", "auto_number", "rich_text", "what_list_data"), "IN", 26, "属于", "text"),
  TEXT_NIN(Arrays.asList("text", "long_text", "email", "phone_number", "url", "object_reference", "auto_number", "rich_text", "what_list_data"), "NIN", 27, "不属于", "text"),
  TEXT_EQ(Arrays.asList("text", "long_text", "email", "phone_number", "url", "object_reference", "auto_number", "rich_text", "what_list_data"), "EQ", 3, "等于", "text"),
  TEXT_N(Arrays.asList("text", "long_text", "email", "phone_number", "url", "object_reference", "auto_number", "rich_text", "what_list_data"), "N", 4, "不等于", "text"),
  TEXT_IS(Arrays.asList("text", "long_text", "email", "phone_number", "url", "object_reference", "auto_number", "rich_text", "what_list_data"), "IS", 7, "为空", "text"),
  TEXT_ISN(Arrays.asList("text", "long_text", "email", "phone_number", "url", "object_reference", "auto_number", "rich_text", "what_list_data"), "ISN", 8, "不为空", "text"),
  TEXT_STARTWITH(Arrays.asList("text", "long_text", "email", "phone_number", "url", "object_reference", "auto_number", "rich_text", "what_list_data"), "STARTWITH", 5, "开始于", "text"),
  TEXT_ENDWITH(Arrays.asList("text", "long_text", "email", "phone_number", "url", "object_reference", "auto_number", "rich_text", "what_list_data"), "ENDWITH", 6, "结束于", "text"),
  NUMBER_EQ(Arrays.asList("number", "currency", "percentile", "count"), "EQ", 9, "等于", "number"),
  NUMBER_N(Arrays.asList("number", "currency", "percentile", "count"), "N", 10, "不等于", "number"),
  NUMBER_GT(Arrays.asList("number", "currency", "percentile", "count"), "GT", 11, "大于", "number"),
  NUMBER_GTE(Arrays.asList("number", "currency", "percentile", "count"), "GTE", 13, "大于等于", "number"),
  NUMBER_LT(Arrays.asList("number", "currency", "percentile", "count"), "LT", 12, "小于", "number"),
  NUMBER_LTE(Arrays.asList("number", "currency", "percentile", "count"), "LTE", 14, "小于等于", "number"),
  NUMBER_BETWEEN(Arrays.asList("number", "currency", "percentile", "count"), "BETWEEN", 24, "介于", "number"),
  NUMBER_IS(Arrays.asList("number", "currency", "percentile", "count"), "IS", 15, "为空", "number"),
  NUMBER_ISN(Arrays.asList("number", "currency", "percentile", "count"), "ISN", 16, "不为空", "number"),
  REF_MANY_IN(Collections.singletonList("object_reference_many"), "IN", 26, "属于", "text"),
  REF_MANY_NIN(Collections.singletonList("object_reference_many"), "NIN", 27, "不属于", "text"),
  REF_MANY_LIKE(Collections.singletonList("object_reference_many"), "LIKE", 1, "包含", "text"),
  REF_MANY_NLIKE(Collections.singletonList("object_reference_many"), "NLIKE", 2, "不包含", "text"),
  REF_MANY_IS(Collections.singletonList("object_reference_many"), "IS", 7, "为空", "text"),
  REF_MANY_ISN(Collections.singletonList("object_reference_many"), "ISN", 8, "不为空", "text"),
  LOCATION_LIKE(Collections.singletonList("location"), "LIKE", 1, "包含", "location"),
  LOCATION_NLIKE(Collections.singletonList("location"), "NLIKE", 2, "不包含", "location"),
  LOCATION_EQ(Collections.singletonList("location"), "EQ", 3, "等于", "location"),
  LOCATION_N(Collections.singletonList("location"), "N", 4, "不等于", "location"),
  LOCATION_IS(Collections.singletonList("location"), "IS", 7, "为空", "location"),
  LOCATION_ISN(Collections.singletonList("location"), "ISN", 8, "不为空", "location"),
  LOCATION_STARTWITH(Collections.singletonList("location"), "STARTWITH", 5, "开始于", "location"),
  LOCATION_ENDWITH(Collections.singletonList("location"), "ENDWITH", 6, "结束于", "location"),
  BOOLEAN_EQ(Collections.singletonList("true_or_false"), "EQ", 3, "等于", "boolean"),
  BOOLEAN_N(Collections.singletonList("true_or_false"), "N", 4, "不等于", "boolean"),
  BOOLEAN_IS(Collections.singletonList("true_or_false"), "IS", 7, "为空", "boolean"),
  BOOLEAN_ISN(Collections.singletonList("true_or_false"), "ISN", 8, "不为空", "boolean"),
  BOOLEAN_IN(Collections.singletonList("true_or_false"), "IN", 26, "属于", "boolean"),
  BOOLEAN_NIN(Collections.singletonList("true_or_false"), "NIN", 27, "不属于", "boolean"),
  SELECT_ONE_EQ(Collections.singletonList("select_one"), "EQ", 3, "等于", "selectOne"),
  SELECT_ONE_N(Collections.singletonList("select_one"), "N", 4, "不等于", "selectOne"),
  SELECT_ONE_HASANYOF(Collections.singletonList("select_one"), "HASANYOF", 26, "属于", "selectOne"),
  SELECT_ONE_NHASANYOF(Collections.singletonList("select_one"), "NHASANYOF", 27, "不属于", "selectOne"),
  SELECT_ONE_IS(Collections.singletonList("select_one"), "IS", 7, "为空", "selectOne"),
  SELECT_ONE_ISN(Collections.singletonList("select_one"), "ISN", 8, "不为空", "selectOne"),
  SELECT_MANY_EQ(Collections.singletonList("select_many"), "EQ", 3, "等于", "selectMany"),
  SELECT_MANY_N(Collections.singletonList("select_many"), "N", 4, "不等于", "selectMany"),
  SELECT_MANY_LIKE(Collections.singletonList("select_many"), "LIKE", 2, "包含", "selectMany"),
  SELECT_MANY_NLIKE(Collections.singletonList("select_many"), "NLIKE", 3, "不包含", "selectMany"),
  SELECT_MANY_HASANYOF(Collections.singletonList("select_many"), "HASANYOF", 26, "属于", "selectMany"),
  SELECT_MANY_NHASANYOF(Collections.singletonList("select_many"), "NHASANYOF", 27, "不属于", "selectMany"),
  SELECT_MANY_IS(Collections.singletonList("select_many"), "IS", 7, "为空", "selectMany"),
  SELECT_MANY_ISN(Collections.singletonList("select_many"), "ISN", 8, "不为空", "selectMany"),
  CIRCLE_IN(Arrays.asList("employee", "department", "out_employee", "out_department"), "IN", 26, "属于", "circle"),
  CIRCLE_NIN(Arrays.asList("employee", "department", "out_employee", "out_department"), "NIN", 27, "不属于", "circle"),
  CIRCLE_IS(Arrays.asList("employee", "department", "out_employee", "out_department"), "IS", 7, "为空", "circle"),
  CIRCLE_ISN(Arrays.asList("employee", "department", "out_employee", "out_department"), "ISN", 8, "不为空", "circle"),
  CIRCLE_MANY_HASANYOF(Arrays.asList("dimension", "employee_many", "department_many", "data_visibility_range"), "HASANYOF", 26, "属于", "circle"),
  CIRCLE_MANY_NHASANYOF(Arrays.asList("dimension", "employee_many", "department_many", "data_visibility_range"), "NHASANYOF", 27, "不属于", "circle"),
  CIRCLE_MANY_IS(Arrays.asList("dimension", "employee_many", "department_many", "data_visibility_range"), "IS", 7, "为空", "circle"),
  CIRCLE_MANY_ISN(Arrays.asList("dimension", "employee_many", "department_many", "data_visibility_range"), "ISN", 8, "不为空", "circle"),
  TIME_BETWEEN(Collections.singletonList("time"), "BETWEEN", 23, "时间段", "time"),
  TIME_EQ(Collections.singletonList("time"), "EQ", 17, "等于", "time"),
  TIME_N(Collections.singletonList("time"), "N", 18, "不等于", "time"),
  TIME_LT(Collections.singletonList("time"), "LT", 19, "早于", "time"),
  TIME_GT(Collections.singletonList("time"), "GT", 39, "晚于", "time"),
  TIME_LTE(Collections.singletonList("time"), "LTE", 38, "早于等于", "time"),
  TIME_GTE(Collections.singletonList("time"), "GTE", 20, "晚于等于", "time"),
  TIME_IS(Collections.singletonList("time"), "IS", 21, "为空", "time"),
  TIME_ISN(Collections.singletonList("time"), "ISN", 22, "不为空", "time"),
  DATE_BETWEEN(Arrays.asList("date", "date_time"), "BETWEEN", 23, "时间段", "dateTime"),
  DATE_EQ(Arrays.asList("date", "date_time"), "EQ", 17, "等于", "dateTime"),
  DATE_N(Arrays.asList("date", "date_time"), "N", 18, "不等于", "dateTime"),
  DATE_LT(Arrays.asList("date", "date_time"), "LT", 19, "早于", "dateTime"),
  DATE_GT(Arrays.asList("date", "date_time"), "GT", 39, "晚于", "dateTime"),
  DATE_LTE(Arrays.asList("date", "date_time"), "LTE", 38, "早于等于", "dateTime"),
  DATE_GTE(Arrays.asList("date", "date_time"), "GTE", 20, "晚于等于", "dateTime"),
  DATE_IS(Arrays.asList("date", "date_time"), "IS", 21, "为空", "dateTime"),
  DATE_ISN(Arrays.asList("date", "date_time"), "ISN", 22, "不为空", "dateTime"),
  DAYBEFOR(Arrays.asList("date", "date_time"), "LTEO", 28, "过去N天内（含当天）", "dateTime"),
  DAYAFTER(Arrays.asList("date", "date_time"), "GTEO", 29, "未来N天内（含当天）", "dateTime"),
  MONTHBEFOR(Arrays.asList("date", "date_time"), "LTEO", 30, "过去N月内（含当月）", "dateTime"),
  MONTHAFTER(Arrays.asList("date", "date_time"), "GTEO", 31, "未来N月内（含当月）", "dateTime"),
  BEFORDAY(Arrays.asList("date", "date_time"), "LT", 32, "N天前", "dateTime"),
  AFTERDAY(Arrays.asList("date", "date_time"), "GT", 33, "N天后", "dateTime"),
  WEEKBEFOR(Arrays.asList("date", "date_time"), "LTEO", 34, "过去N周内（含当周）", "dateTime"),
  WEEKAFTER(Arrays.asList("date", "date_time"), "GTEO", 35, "未来N周内（含当周）", "dateTime"),
  BEFORWEEK(Arrays.asList("date", "date_time"), "LT", 36, "N周前", "dateTime"),
  AFTERWEEK(Arrays.asList("date", "date_time"), "GT", 37, "N周后", "dateTime"),
  DAYBEFOR2(Arrays.asList("date", "date_time"), "LTE", 40, "过去N天内（不含当天）", "dateTime"),
  DAYAFTER2(Arrays.asList("date", "date_time"), "GTE", 41, "未来N天内（不含当天）", "dateTime"),
  WEEKBEFOR2(Arrays.asList("date", "date_time"), "LTE", 42, "过去N周内（不含当周）", "dateTime"),
  WEEKAFTER2(Arrays.asList("date", "date_time"), "GTE", 43, "未来N周内（不含当周）", "dateTime"),
  MONTHBEFOR2(Arrays.asList("date", "date_time"), "LTE", 44, "过去N月内（不含当月）", "dateTime"),
  MONTHAFTER2(Arrays.asList("date", "date_time"), "GTE", 45, "未来N月内（不含当月）", "dateTime"),
  FILE_IS(Arrays.asList("image", "file_attachment", "big_file_attachment", "signature"), "IS", 7, "为空", "file"),
  FILE_ISN(Arrays.asList("image", "file_attachment", "big_file_attachment", "signature"), "ISN", 8, "不为空", "file"),
  CASCADE_HASANYOF(Collections.singletonList("casecade_select"), "HASANYOF", 26, "属于", "cascade"),
  CASCADE_NHASANYOF(Collections.singletonList("casecade_select"), "NHASANYOF", 27, "不属于", "cascade"),
  WHAT_EQ(Collections.singletonList("what_data"), "EQ", 3, "等于", "what");

  @Generated
  private static final Logger log = LoggerFactory.getLogger(FilterOperatorMapByTypeEnum.class);
  private List<String> types;
  private String paasOperator;
  private int biOperator;
  private String label;
  private String converterType;

  private FilterOperatorMapByTypeEnum(List types, String paasOperator, int biOperator, String label, String converterType) {
    this.types = types;
    this.paasOperator = paasOperator;
    this.biOperator = biOperator;
    this.label = label;
    this.converterType = converterType;
  }

  public static int getBiOperatorByPaas(String type, String paasOperator) {
    FilterOperatorMapByTypeEnum[] var2 = values();
    int var3 = var2.length;

    for(int var4 = 0; var4 < var3; ++var4) {
      FilterOperatorMapByTypeEnum fe = var2[var4];
      if (fe.getTypes().contains(type) && fe.getPaasOperator().equals(paasOperator)) {
        return fe.getBiOperator();
      }
    }

    log.error("un support filter operator type:{},paasOperator:{}", type, paasOperator);
    return TEXT_ISN.getBiOperator();
  }

  public static String getPaasOperatorByBi(String type, int biOperator) {
    FilterOperatorMapByTypeEnum[] var2 = values();
    int var3 = var2.length;

    for(int var4 = 0; var4 < var3; ++var4) {
      FilterOperatorMapByTypeEnum fe = var2[var4];
      if (fe.getTypes().contains(type) && fe.getBiOperator() == biOperator) {
        return fe.getPaasOperator();
      }
    }

    log.error("un support filter operator type:{},biOperator:{}", type, biOperator);
    return TEXT_ISN.getPaasOperator();
  }

  public static String getConverterType(String type) {
    FilterOperatorMapByTypeEnum[] var1 = values();
    int var2 = var1.length;

    for(int var3 = 0; var3 < var2; ++var3) {
      FilterOperatorMapByTypeEnum fe = var1[var3];
      if (fe.getTypes().contains(type)) {
        return fe.getConverterType();
      }
    }

    log.error("un support converter, type:{}", type);
    return null;
  }

  public static FilterOperatorMapByTypeEnum getFilterEnum(String type, String paasOperator) {
    FilterOperatorMapByTypeEnum[] var2 = values();
    int var3 = var2.length;

    for(int var4 = 0; var4 < var3; ++var4) {
      FilterOperatorMapByTypeEnum fe = var2[var4];
      if (fe.getTypes().contains(type) && fe.getPaasOperator().equals(paasOperator)) {
        return fe;
      }
    }

    log.error("un support filterEnum, type:{},paasOperator:{}", type, paasOperator);
    return null;
  }

  @Generated
  public List<String> getTypes() {
    return this.types;
  }

  @Generated
  public String getPaasOperator() {
    return this.paasOperator;
  }

  @Generated
  public int getBiOperator() {
    return this.biOperator;
  }

  @Generated
  public String getLabel() {
    return this.label;
  }

  @Generated
  public String getConverterType() {
    return this.converterType;
  }
}
