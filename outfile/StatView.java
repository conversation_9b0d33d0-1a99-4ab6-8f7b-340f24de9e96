package com.facishare.bi.metadata.context.dto.ads;

import lombok.Data;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 图表视图定义实体类，对应数据库表 stat_view
 * 用于存储BI系统中统计图表的基本信息和配置
 */
@Data
public class StatView {
  /** 视图唯一标识 */
  private String viewId;

  /** 视图名称 */
  private String viewName;

  /** 视图名称国际化key */
  private String viewNameI18NKey;

  /** 所属文件夹编号 */
  private String categoryId;

  /** 关联的模板ID */
  private String templateId;

  /** 自定义筛选器逻辑 */
  private String filtersLogic;

  /** 企业编号 */
  private int ei;

  /** 报表描述 */
  private String description;

  /** 是否自选chart类型：0-否，1-是 */
  private int chartUserDefined;

  /** 统计图主题ID */
  private String schemaId;

  /** 权限类型 */
  private int permissionType;

  /** 创建人ID */
  private int creator;

  /** 创建时间 */
  private Date createTime;

  /** 最近修改人ID */
  private int updator;

  /** 最近修改时间 */
  private Date updateTime;

  /** 最后缓存时间 */
  private Date lastCacheTime;

  /** 是否删除：0-否，1-是 */
  private int isDelete;

  /** 显示前n条数据 */
  private int topNum;

  /** 是否显示维度：0-否，1-是 */
  private int isShowDimension;

  /** 统计图类型 */
  private int source;

  /** 平均值字段ID */
  private String averageFieldId;

  /** 平均值字段比率类型 */
  private String averageFieldRatioType;

  /** 转化率表达式 */
  private String cvrExp;

  /** 目标规则的主题对象：客户规则-biz_account，员工规则-org_employee_user，产品规则-biz_product */
  private String goalThemeApiName;

  /** 下钻路径配置 */
  private String drillDownPath;

  /** 目标标签 */
  private String goalTag;

  /** 数据源类型：0-标准对象数据源，1-原生SQL数据源，2-复合图表数据源 */
  private int dataSource;

  /** 时区，默认Asia/Shanghai */
  private String timeZone;

  /** 视图配置JSON */
  private String viewConfig;

  /** 外部租户ID */
  private String outTenantId;

  /** 应用ID */
  private String appId;

  /** 外部所有者 */
  private String outOwner;

  /** 视图中使用的维度字段配置列表 */
  private List<StatViewField> viewFields;

  /** 视图中使用的过滤条件字段配置列表 */
  private List<StatViewFilter> viewFilters;

  /** 视图中使用的属性字段配置列表 */
  private List<StatViewProperty> viewProperties;



  /** 获取普通维度字段列表 */
  public List<StatViewField> normalDimensionFields() {
    return viewFields.stream()
        .filter(field -> field.getAxisType() == 0)
        .collect(Collectors.toList());
  }

  /** 获取属性维度字段列表 */
  public List<StatViewField> propertyDimensionFields() {
    return viewFields.stream()
        .filter(field -> field.getAxisType() == 3)
        .collect(Collectors.toList());
  }

  /** 获取指标字段列表 */
  public List<StatViewField> measureFields() {
    return viewFields.stream()
        .filter(field -> field.getAxisType() != 0 && field.getAxisType() != 3)
        .collect(Collectors.toList());
  }

}
