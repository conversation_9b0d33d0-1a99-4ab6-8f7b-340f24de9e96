package com.fxiaoke.ai.exception;

import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

@Getter
public enum ModelErrorCodeEnum {
    AI_MODEL_TIMEOUT("s301990064", "调用模型超时，请您重试", "paas.ai.AI_MODEL_TIMEOUT"),
    AI_MODEL_INVALID_REQUEST_ERROR("s301990065", "模型请求参数有误，请仔细检查", "paas.ai.AI_MODEL_INVALID_REQUEST_ERROR"),
    AI_MODEL_ARREARS_ERROR("s301990066", "拒绝访问，请确保您的帐户处于良好状态。", "paas.ai.AI_MODEL_ARREARS_ERROR"),
    AI_MODEL_SYSTEM_ERROR("s301990067", "调用AI接口出现系统报错，请稍后再试。", "paas.ai.AI_MODEL_SYSTEM_ERROR"),
    AI_MODEL_ACCESS_DENIED_ERROR("s301990068", "模型访问被拒绝，请您检查模型配置", "paas.ai.AI_MODEL_ACCESS_DENIED_ERROR"),
    AI_MODEL_UNAVAILABLE_ERROR("s301990069", "模型不可用，请您检查模型配置", "paas.ai.AI_MODEL_UNAVAILABLE_ERROR"),
    AI_MODEL_QUOTA_EXCEEDED_ERROR("s301990070", "模型被限流或配额不足，请您检查模型配置", "paas.ai.AI_MODEL_QUOTA_EXCEEDED_ERROR"),

    ;


    ModelErrorCodeEnum(String errorCode, String errorMessage, String i18nKey) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
        this.i18nKey = i18nKey;
    }

    final String errorCode;
    final String errorMessage;
    final String i18nKey;

    public static String getI18nKeyByErrorCode(String errorCode) {
        Optional<ModelErrorCodeEnum> first = Arrays.stream(ModelErrorCodeEnum.values())
                .filter(x -> x.errorCode.equals(errorCode))
                .findFirst();
        return first.map(ModelErrorCodeEnum::getI18nKey).orElse("");
    }
}
