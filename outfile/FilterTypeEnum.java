package com.facishare.bi.easy.stat.core.pojo.enums;

import com.facishare.bi.common.Constants;
import com.facishare.bi.common.GrayManager;
import com.facishare.bi.common.dto.filter.Filter;
import com.facishare.bi.easy.stat.Infrastruct.constant.EasyStatQueryConstant;
import com.facishare.bi.easy.stat.core.common.util.CommonPredicateUtil;
import com.facishare.bi.easy.stat.core.common.util.EasyStatThreadLocal;
import com.facishare.bi.metadata.context.dto.dw.MultiDimGoalDescribeManager;
import com.facishare.bi.privilege.org.RequestContextManager;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.facishare.bi.easy.stat.Infrastruct.constant.EasyStatQueryConstant.DB.USER_ID;

/**
 * <AUTHOR>
 * @date 2022-11-08
 * @desc 筛选器类型
 *
 */
@Getter
@Slf4j
public enum FilterTypeEnum {

    /**
     * 无
     */
    NULL,

    /**
     * 日期
     */
    DATE("Date", "Date", ""),

    /**
     * 日期时间
     */
    DATE_TIME("DateTime", "Date", "DateTime"),

    /**
     * 日期时间
     */
    TIME("Time", "Date", "Time"),

    /**
     * 文本(详细地址)
     */
    STRING("String", "String", ""),

    /**
     * 链接
     */
    URL("String", "String", null, null,null,"url"),

    /**
     * 主属性
     */
    MAIN("Main", "String", "", "name"),

    /**
     * 负责人
     */
    OWNER("Owner", "Circle,Number", "Circle", "owner,user_id", "org_employee_user"),

    /**
     * 负责人主属部门
     */
    OWNER_DEPARTMENT("Owner_Department", "Circle,String", null, "owner_department,main_department"),

    /**
     * 外部负责人
     */
    OUT_OWNER("Out_Owner", "Circle,Number", "Circle", "out_owner", "org_employee_user"),

    /**
     * 维度权限 负责人
     */
    DIM_AUTH_OWNER("Dim_Auth_Owner", "Number", "Circle", "owner", "org_employee_user"),

    /**
     * 维度权限 负责人主属部门
     */
    DIM_AUTH_OWNER_DEPARTMENT("Dim_Auth_Owner_Department", "String", "Circle", "owner_department", "org_employee_user"),

    /**
     * 归属部门
     */
    DATA_OWN_DEPARTMENT("Data_Own_Department", "String,Circle", "Circle", "data_own_department,dept_id,data_own_organization", "org_dept", "department"),

    /**
     * 自定义部门
     */
    DEPARTMENT("Department", "String,Circle", "Circle", null, "org_dept", "department"),

    /**
     * 自定义多值部门
     */
    DEPARTMENT_MANY("Department_Many", "String,Circle", "Circle", null, null, "department_many"),

    /**
     * 外部部门
     */
    OUT_DEPARTMENT("Out_Department", "String,Circle", "Circle", null, "org_dept", "out_department"),

    /**
     * 单选
     */
    SELECT_ONE("Select_One", "MultiSelectEnum,SingleSelectEnum,String,Number,RefEnum,LevelUncertainty", "MultiSelectEnum,SingleSelectEnum,RefEnum,LevelUncertainty", null, null, "select_one,record_type,ref_enum,text"),

    /**
     * 多选
     */
    SELECT_MANY("Select_Many", "MultiSelectEnum,String", "MultiSelectEnum", null, null, "select_many"),

    /**
     * 维度类型
     */
    DIMENSION("Select_Many", "String", "MultiSelectEnum", null, null, "dimension"),

    /**
     * 布尔
     */
    BOOLEAN("Boolean", "MultiSelectEnum,SingleSelectEnum", "MultiSelectEnum,SingleSelectEnum", null, null, "true_or_false"),

    /**
     * 自定义人员
     */
    EMPLOYEE("Employee", "Circle,Number", "Circle", null, "org_employee_user", "employee"),

    /**
     * 自定义多值人员
     */
    EMPLOYEE_MANY("Employee_Many", "Circle,Number", "Circle", null, null, "employee_many"),

    /**
     * 老 - 产品分类
     */
    NORMAL_PRODUCT_CATEGORY("Normal_Product_Category", "LevelUncertainty,MultiSelectEnum,SingleSelectEnum,String", "LevelUncertainty,MultiSelectEnum,SingleSelectEnum", "category,enum_code", "product_category,biz_product", null, "category"),

    /**
     * 新 - 产品分类
     */
    REF_PRODUCT_CATEGORY("Ref_Product_Category", "String,LTree", "LTree", null, "product_category", "object_reference", "category_path"),

    /**
     * 查找关联
     */
    REF("Ref", "String", null, null, null, "object_reference,master_detail"),

    /**
     * 查找关联多选
     */
    REF_MANY("Ref_Many", "String", null, null, null, "object_reference_many"),

    /**
     * 国家
     */
    COUNTRY("country", "String,LevelEnum", "LevelEnum", null, null, "country"),

    /**
     * 省
     */
    PROVINCE( "province", "String,LevelEnum", "LevelEnum", null, null, "province"),

    /**
     * 市
     */
    CITY("city", "String,LevelEnum", "LevelEnum", null, null, "city"),

    /**
     * 区
     */
    DISTRICT("district", "String,LevelEnum", "LevelEnum", null, null, "district"),

    /**
     * 乡镇
     */
    TOWN("town", "String,LevelEnum", "LevelEnum", null, null, "town"),

    /**
     * 客户层级
     */
    ACCOUNT_PATH("account_path", "String,MultiSelectEnum", "MultiSelectEnum", "account_path", null, "tree_path"),

    /**
     * 市场活动层级
     */
    MARKETING_EVENT_PATH("marketing_event_path", "String,MultiSelectEnum", "MultiSelectEnum", "marketing_event_path", null, "tree_path"),

    /**
     * 落地页层级
     */
    LANDING_PAGE_PATH("landing_page_path", "String,MultiSelectEnum", "MultiSelectEnum", "landing_page_path", null, "tree_path"),

    /**
     * BOM路径
     */
    BOM_PATH("bom_path", "String,MultiSelectEnum", "MultiSelectEnum", "bom_path", null, "tree_path"),

    /**
     * 产品分类层级
     */
    PRODUCT_CATEGORY_PATH( "product_category_path", "String,MultiSelectEnum", "LTree", "product_category_path", null, "tree_path"),

    /**
     * 指标
     */
    AGG("Agg", null, null, null, null, null, null, "agg_data"),

    /**
     * 标签组
     */
    TAG_GROUP("Tag_Group", "String,MultiSelectEnum", "MultiSelectEnum",null, null,  "tag_group"),

    /**
     * 标签
     */
    SUB_TAG("Sub_Group", "String,MultiSelectEnum", "MultiSelectEnum", null, null,  "sub_tag"),

    /**
     * whatList
     */
    WHAT_LIST("what_list", null, null, null, null, "group"),

    /**
     * whatList
     */
    WHAT_API("what", null, "Bpm", "objectApiName,object_api_name,entity_id,relate_object_api_name,api_name,flow_type", null, "text"),

    /**
     * whatList
     */
    WHAT_DATA("what", null, "Bpm", null, null, "text"),

    /**
     * 部门类字段指定层级
     */
    DEPARTMENT_APPOINT_LEVEL,

    /**
     * 产品分类类字段指定层级
     */
    PRODUCT_CATEGORY_APPOINT_LEVEL,

    /**
     * 目标规则筛选
     */
    GOAL_RULE,

    /**
     * 指标
     */
    NUMBER("Number", "Number", ""),

    /**
     * 图片
     */
    IMAGE("Number", "Number", null, null, null, "image"),

    /**
     * tag
     */
    TAG("String", "String", "Tag", null, null, "tag");

    private String configName;
    private String fieldTypeName;

    private String subFieldTypeName;

    private String dbFieldName;

    private String refObjName;

    private String type;

    private String refTargetField;

    private String dbObjName;

    private static List<String> userIdCrmObjNames = Lists.newArrayList("PersonnelObj", "GoalValueBIObj");

    FilterTypeEnum() {
    }

    FilterTypeEnum(String configName, String fieldTypeName) {
        this.configName = configName;
        this.fieldTypeName = fieldTypeName;
    }

    FilterTypeEnum(String configName, String fieldTypeName, String subFieldTypeName) {
        this.configName = configName;
        this.fieldTypeName = fieldTypeName;
        this.subFieldTypeName = subFieldTypeName;
    }

    FilterTypeEnum(String configName, String fieldTypeName, String subFieldTypeName, String dbFieldName) {
        this.configName = configName;
        this.fieldTypeName = fieldTypeName;
        this.subFieldTypeName = subFieldTypeName;
        this.dbFieldName = dbFieldName;
    }

    FilterTypeEnum(String configName, String fieldTypeName, String subFieldTypeName, String dbFieldName, String refObjName) {
        this.configName = configName;
        this.fieldTypeName = fieldTypeName;
        this.subFieldTypeName = subFieldTypeName;
        this.dbFieldName = dbFieldName;
        this.refObjName = refObjName;
    }

    FilterTypeEnum(String configName, String fieldTypeName, String subFieldTypeName, String dbFieldName, String refObjName, String type) {
        this.configName = configName;
        this.fieldTypeName = fieldTypeName;
        this.subFieldTypeName = subFieldTypeName;
        this.dbFieldName = dbFieldName;
        this.refObjName = refObjName;
        this.type = type;
    }

    FilterTypeEnum(String configName, String fieldTypeName, String subFieldTypeName, String dbFieldName, String refObjName, String type, String refTargetField) {
        this.configName = configName;
        this.fieldTypeName = fieldTypeName;
        this.subFieldTypeName = subFieldTypeName;
        this.dbFieldName = dbFieldName;
        this.refObjName = refObjName;
        this.type = type;
        this.refTargetField = refTargetField;
    }

    FilterTypeEnum(String configName, String fieldTypeName, String subFieldTypeName, String dbFieldName, String refObjName, String type, String refTargetField, String dbObjName) {
        this.configName = configName;
        this.fieldTypeName = fieldTypeName;
        this.subFieldTypeName = subFieldTypeName;
        this.dbFieldName = dbFieldName;
        this.refObjName = refObjName;
        this.type = type;
        this.refTargetField = refTargetField;
        this.dbObjName = dbObjName;
    }

    /**
     * 获取字段名称
     */
    public String getFieldTypeName(){
        return fieldTypeName;
    }

    /**
     * 属于null
     */
    public boolean isNull(){
        return Objects.equals(NULL, this);
    }

    /**
     * 属于日期
     */
    public boolean isDate(){
        return Objects.equals(DATE, this);
    }

    /**
     * 属于日期时间
     */
    public boolean isDateTime(){
        return Objects.equals(DATE_TIME, this);
    }

    /**
     * 属于日期/日期时间
     */
    public boolean isDateLike(){
        return isDate() || isDateTime();
    }

    /**
     * 属于文本
     */
    public boolean isString(){
        return Objects.equals(STRING, this);
    }

    /**
     * 属于单选
     */
    public boolean isSelectOne(){
        return Objects.equals(SELECT_ONE, this);
    }

    /**
     * 属于多选
     */
    public boolean isSelectMany(){
        return Objects.equals(SELECT_MANY, this);
    }

    /**
     * 属于布尔
     */
    public boolean isBoolean(){
        return Objects.equals(BOOLEAN, this);
    }

    /**
     * 属于自定义人员
     */
    public boolean isEmployee(){
        return Objects.equals(EMPLOYEE, this);
    }

    /**
     * 属于负责人
     */
    public boolean isOwner(){
        return Objects.equals(OWNER, this);
    }

    /**
     * 属于外部负责人
     */
    public boolean isOutOwner(){
        return Objects.equals(OUT_OWNER, this);
    }

    /**
     * 属于外部部门
     */
    public boolean isOutDept(){
        return Objects.equals(OUT_DEPARTMENT, this);
    }

    /**
     * 属于枚举
     */
    public boolean isSelectOption() {
        return isSelectOne() || isSelectMany() || isBoolean();
    }

    /**
     * 是自定义部门
     */
    public boolean isDepartment(){
        return Objects.equals(DEPARTMENT, this);
    }

    /**
     * 是归属部门
     */
    public boolean isDataOwnDepartment(){
        return Objects.equals(DATA_OWN_DEPARTMENT, this);
    }


    /**
     * 是负责人主属部门
     */
    public boolean isOwnDepartment(){
        return Objects.equals(OWNER_DEPARTMENT, this);
    }

    /**
     * 是负责人主属部门维度权限
     */
    public boolean isDimAuthOwnDepartment(){
        return Objects.equals(DIM_AUTH_OWNER_DEPARTMENT, this);
    }

    /**
     * 是负责人
     */
    public boolean isDimAuthOwner(){
        return Objects.equals(DIM_AUTH_OWNER, this);
    }

    /**
     * 是老 - 产品分类
     */
    public boolean isNormalProductCategory() {
        return Objects.equals(NORMAL_PRODUCT_CATEGORY, this);
    }

    /**
     * 是新 - 产品分类
     */
    public boolean isRefProductCategory() {
        return Objects.equals(REF_PRODUCT_CATEGORY, this);
    }

    /**
     * 是新 - 产品分类
     */
    public boolean isAllProductCategory() {
        return isNormalProductCategory() || isRefProductCategory();
    }

    /**
     * 是主属性
     */
    public boolean isMain() {
        return Objects.equals(MAIN, this);
    }

    /**
     * 是查找关联
     */
    public boolean isRef() {
        return Objects.equals(REF, this);
    }

    /**
     * 是查找关联多选
     */
    public boolean isRefMany() {
        return Objects.equals(REF_MANY, this);
    }

    /**
     * 查找关联类筛选
     */
    public boolean isRefIdFilter(){
        return isRef() || isRefMany();
    }

    /**
     * 需要查询业务表类筛选
     */
    public boolean isRefIdOrMainFilter(){
        return isRef() || isRefMany() || isMain();
    }

    /**
     * 是id to name 类型的
     */
    public boolean isIdToName(){
        return isMain() || isRef();
    }

    /**
     * 是层级字段
     */
    public boolean isLevel(){
        return isDepartment() || isDataOwnDepartment() || isOwnDepartment() || isAllProductCategory();
    }

    public boolean isTreePath() {
        return isMarketingEventPath() || isAccountPath() || isLandingPagePath() || isBomPath() || isProductCategoryPath();
    }

    /**
     * 是国家
     */
    public boolean isCountry(){
        return COUNTRY.equals(this);
    }

    /**
     * 是省
     */
    public boolean isProvince(){
        return PROVINCE.equals(this);
    }

    /**
     * 是市
     */
    public boolean isCity(){
        return CITY.equals(this);
    }

    /**
     * 是区
     */
    public boolean isDistrict(){
        return DISTRICT.equals(this);
    }

    /**
     * 是乡镇
     */
    public boolean isTown() {
        return TOWN.equals(this);
    }

    /**
     * 地图类型
     */
    public boolean isMap(){
        return isCountry() || isProvince() || isCity() || isDistrict() || isTown();
    }

    /**
     * 省市区
     */
    public boolean isProvinceCityDistrict(){
        return isProvince() || isCity() || isDistrict();
    }

    /**
     * 指标
     */
    public boolean isAgg(){
        return AGG.equals(this);
    }

    /**
     * 是标签组
     */
    public boolean isTagGroup(){
        return TAG_GROUP.equals(this);
    }

    /**
     * 是标签
     */
    public boolean isSubTag(){
        return SUB_TAG.equals(this);
    }

    /**
     * 是标签组/标签
     */
    public boolean isAllTag(){
        return isTagGroup() || isSubTag();
    }

    /**
     * 是客户层级
     */
    public boolean isAccountPath() {
        return Objects.equals(this, ACCOUNT_PATH);
    }

    /**
     * 是市场活动层级
     */
    public boolean isMarketingEventPath() {
        return Objects.equals(this, MARKETING_EVENT_PATH);
    }

    /**
     * 是落地页层级
     */
    public boolean isLandingPagePath() {
        return Objects.equals(this, LANDING_PAGE_PATH);
    }

    /**
     * 是BOM路径
     */
    public boolean isBomPath() {
        return Objects.equals(this, BOM_PATH);
    }

    /**
     * 是产品分类路径
     */
    public boolean isProductCategoryPath() {
        return Objects.equals(this, PRODUCT_CATEGORY_PATH);
    }

    /**
     * 是what api字段
     */
    public boolean isWhatApi() {
        return Objects.equals(this, WHAT_API);
    }

    /**
     * 是what data字段
     */
    public boolean isWhatData() {
        return Objects.equals(this, WHAT_DATA);
    }

    /**
     * 是部门类指定层级
     */
    public boolean isDepartmentAppointLevel(){
        return Objects.equals(DEPARTMENT_APPOINT_LEVEL, this);
    }

    /**
     * 是产品分类指定层级
     */
    public boolean isProductCategoryAppointLevel(){
        return Objects.equals(PRODUCT_CATEGORY_APPOINT_LEVEL, this);
    }

    /**
     * 是what_list
     */
    public boolean isWhatList(){
        return Objects.equals(WHAT_LIST, this);
    }

    /**
     * 是否部门多值
     */
    public boolean isDepartmentMany() {
        return Objects.equals(DEPARTMENT_MANY, this);
    }

    /**
     * 是否人员多值
     */
    public boolean isEmployeeMany() {
        return Objects.equals(EMPLOYEE_MANY, this);
    }

    /**
     * 获得对应的filter类型
     */
    public static FilterTypeEnum getFilterTypeEnum(Filter filter){
        /** 控制打印数据范围类型 */
        boolean needLogFilterType = isNeedLogFilterType(filter);
        /** 获取数据范围类型 */
        FilterTypeEnum filterTypeEnum = getFilterTypeEnumInner(filter, needLogFilterType);
        /** 打印数据范围判定日志 */
        logFilterType(filter, filterTypeEnum, needLogFilterType);
        return filterTypeEnum;
    }

    /**
     * 是否需要打印数据范围类型
     */
    private static boolean isNeedLogFilterType(Filter filter){
        /** 只有第一次需要打印 */
        if (EasyStatThreadLocal.addFilterTypeLogSet(filter.getFieldId())) {
            return true;
        }

        return false;
    }

    /**
     * 判定数据范围类型
     */
    private static FilterTypeEnum getFilterTypeEnumInner(Filter filter, boolean needLogFilterType){
        String fieldId = filter.getFieldId();
        String fieldName = filter.getFieldName();
        String fieldType = filter.getFieldType();
        String subFieldType = filter.getSubFieldType();
        String dbFieldName = filter.getDbFieldName();
        String refObjName = filter.getRefObjName();
        String type = filter.getType();
        String quoteType = filter.getQuoteType();
        String refTargetField = filter.getRefTargetField();
        String dbObjName = filter.getDbObjName();
        String crmObjName = filter.getCrmObjName();
        String returnType = filter.getReturnType();
        int isSingle = Objects.isNull(filter.getIsSingle()) ? 1 : filter.getIsSingle();
        if (needLogFilterType) {
            log.info(EasyStatQueryConstant.LOG.getLogPre() + "filter类型, 判定入口 fieldId: {}, fieldName: {}, fieldType: {}, subFieldType: {}, dbFieldName: {}, refObjName: {}, type: {}, quoteType: {}, refTargetField: {}, dbObjName: {}, crmObjName: {}", fieldId, fieldName, fieldType , subFieldType, dbFieldName, refObjName, type, quoteType, refTargetField, dbObjName, crmObjName);
        }

        String realType = CommonPredicateUtil.isQuote(type) ? quoteType : type;
        realType = CommonPredicateUtil.isFormula(type) || CommonPredicateUtil.isCount(type) ? returnType : realType;
        FilterTypeEnum filterTypeEnum = getFilterTypeEnumInner(filter, fieldType, subFieldType, dbFieldName, refObjName, realType, refTargetField, dbObjName, crmObjName, isSingle);
        filterTypeEnum = getFilterAppointLevelType(filter, filterTypeEnum);
        if (!filterTypeEnum.isNull()) {
            return filterTypeEnum;
        }

        if (needLogFilterType) {
            log.info(EasyStatQueryConstant.LOG.getLogPre() + "filter类型, 判定失败 主要字段判定 fieldId: {}, fieldName: {}, fieldType: {}, subFieldType: {}, dbFieldName: {}, refObjName: {}, type: {}, quoteType: {}, refTargetField: {}, dbObjName: {}, crmObjName: {}", fieldId, fieldName, fieldType, subFieldType, dbFieldName, refObjName, type, quoteType, refTargetField, dbObjName, crmObjName);
            log.info(EasyStatQueryConstant.LOG.getLogPre() + "filter类型, 判定失败 filter: {}", filter);
        }
        return filterTypeEnum;
    }

    /**
     * 获取数据范围类型
     */
    private static FilterTypeEnum getFilterTypeEnumInner(Filter filter,
                                                         String fieldType,
                                                         String subFieldType,
                                                         String dbFieldName,
                                                         String refObjName,
                                                         String type,
                                                         String refTargetField,
                                                         String dbObjName,
                                                         String crmObjName,
                                                         int isSingle) {
        /** 特殊: 老商机的 售前阶段 字段, 判定成查找关联 */
        if (CommonPredicateUtil.isOldBizOppoStageIdFieldId(filter.getFieldId())) {
            return REF;
        }

        /** 特殊的自定义人员（FlowTaskHandleTimeDetailObj） */
        if (GrayManager.isAllow("specialEmpField", RequestContextManager.getEi())
          && arrContains(EMPLOYEE.getFieldTypeName(), fieldType)
          && EMPLOYEE.getSubFieldTypeName().equals(subFieldType)
          && USER_ID.equals(dbFieldName)
          && !userIdCrmObjNames.contains(crmObjName)
          && !arrContains(OUT_OWNER.getDbFieldName(), dbFieldName)
          && !(OWNER_DEPARTMENT.getDbFieldName().equals(dbFieldName))
          && EMPLOYEE.getType().equals(type)) {
            return EMPLOYEE;
        }

        /** 多维目标规则 */
        if (MultiDimGoalDescribeManager.checkIsRuleId(filter.getFieldId())) {
            return GOAL_RULE;
        }

        /** whatList */
        if (Constants.WHAT_LIST_FIELDS.contains(filter.getUdfFieldId()) || Objects.equals(WHAT_LIST.getType(), filter.getType())) {
            return WHAT_LIST;
        }

        /**
         * 指标
         */
        if (AGG.getDbObjName().equals(dbObjName) && !CommonPredicateUtil.isActionDate(dbFieldName)) {
            return AGG;
        }

        /** 日期类型 */
        if (DATE.getFieldTypeName().equals(fieldType) && StringUtils.isBlank(subFieldType)) {
            return DATE;
        }

        /** 日期时间类型 */
        if (DATE_TIME.getFieldTypeName().equals(fieldType) && DATE_TIME.getSubFieldTypeName().equals(subFieldType)) {
            return DATE_TIME;
        }

        /** 时间类型,筛选走日期时间类型 */
        if (TIME.getFieldTypeName().equals(fieldType) && TIME.getSubFieldTypeName().equals(subFieldType)) {
            return DATE_TIME;
        }

        /** 文本类型 */
        if (STRING.getFieldTypeName().equals(fieldType)
                && StringUtils.isBlank(subFieldType)
                && !Objects.equals(EasyStatQueryConstant.DB.NAME, dbFieldName)
                && !Objects.equals(EasyStatQueryConstant.DB.NAME, refTargetField)
                && !arrContains(OWNER_DEPARTMENT.getDbFieldName(), dbFieldName)
                && !arrContains(REF.getType(), type)
                && !arrContains(REF_MANY.getType(), type)) {
            return STRING;
        }

        /** 链接类型=>文本类型 */
        if(URL.getType().equals(type)){
            return STRING;
        }

        /** 主属性 */
        if (MAIN.getFieldTypeName().equals(fieldType) &&
                StringUtils.isBlank(subFieldType) &&
                (Objects.equals(EasyStatQueryConstant.DB.NAME, dbFieldName) || Objects.equals(EasyStatQueryConstant.DB.NAME, refTargetField)) &&
                !arrContains(REF.getType(), type) &&
                !arrContains(REF_MANY.getType(), type)) {
            return MAIN;
        }

        /** 负责人 */
        if (arrContains(OWNER.getFieldTypeName(), fieldType)
                && OWNER.getSubFieldTypeName().equals(subFieldType)
                && arrContains(OWNER.getDbFieldName(), dbFieldName)
                && OWNER.getRefObjName().equals(refObjName)) {
            return OWNER;
        }

        /** 负责人主属部门 */
        if (arrContains(OWNER_DEPARTMENT.getFieldTypeName(), fieldType)
                && arrContains(OWNER_DEPARTMENT.getDbFieldName(), dbFieldName)) {
            return OWNER_DEPARTMENT;
        }

        /** 外部负责人 */
        if (arrContains(OUT_OWNER.getFieldTypeName(), fieldType)
                && OUT_OWNER.getSubFieldTypeName().equals(subFieldType)
                && arrContains(OUT_OWNER.getDbFieldName(), dbFieldName)
                && OUT_OWNER.getRefObjName().equals(refObjName)) {
            return OUT_OWNER;
        }

        /** 归属部门 */
        if (arrContains(DATA_OWN_DEPARTMENT.getFieldTypeName(), fieldType)
                && DATA_OWN_DEPARTMENT.getSubFieldTypeName().equals(subFieldType)
                && arrContains(DATA_OWN_DEPARTMENT.getDbFieldName(), dbFieldName)
                && (DATA_OWN_DEPARTMENT.getRefObjName().equals(refObjName) || Objects.equals(DATA_OWN_DEPARTMENT.getType(), type))) {
            return DATA_OWN_DEPARTMENT;
        }

        /** 外部部门 */
        if (arrContains(OUT_DEPARTMENT.getFieldTypeName(), fieldType)
          && OUT_DEPARTMENT.getSubFieldTypeName().equals(subFieldType)
          && !arrContains(DATA_OWN_DEPARTMENT.getDbFieldName(), dbFieldName)
          && Objects.equals(OUT_DEPARTMENT.getType(), type)) {
            return OUT_DEPARTMENT;
        }

        // 特殊多值字段
        if (DEPARTMENT.getType().equals(type) && isSingle == 0) {
            return DEPARTMENT_MANY;
        }

        /** 自定义部门 */
        if (arrContains(DEPARTMENT.getFieldTypeName(), fieldType)
                && DEPARTMENT.getSubFieldTypeName().equals(subFieldType)
                && !arrContains(DATA_OWN_DEPARTMENT.getDbFieldName(), dbFieldName)
                && !Objects.equals(DEPARTMENT_MANY.getType(), type)
                && !"vice_departments".equals(dbFieldName)
                && (DEPARTMENT.getRefObjName().equals(refObjName) || Objects.equals(DEPARTMENT.getType(), type))) {
            return DEPARTMENT;
        }

        /** 自定义多值部门 */
        if (arrContains(DEPARTMENT_MANY.getFieldTypeName(), fieldType)
                && DEPARTMENT_MANY.getSubFieldTypeName().equals(subFieldType)
                && Objects.equals(DEPARTMENT_MANY.getType(), type)
                //人员对象的附属部门
                || "vice_departments".equals(dbFieldName)) {
            return DEPARTMENT_MANY;
        }

        /** 单选 */
        if (Stream.of(SELECT_ONE.getFieldTypeName().split(EasyStatQueryConstant.SPLIT.SPLIT_STR_COMMA)).collect(Collectors.toList()).contains(fieldType)
                && Stream.of(SELECT_ONE.getSubFieldTypeName().split(EasyStatQueryConstant.SPLIT.SPLIT_STR_COMMA)).collect(Collectors.toList()).contains(subFieldType)
                && Stream.of(SELECT_ONE.getType().split(EasyStatQueryConstant.SPLIT.SPLIT_STR_COMMA)).collect(Collectors.toList()).contains(type)
                && !(arrContains(NORMAL_PRODUCT_CATEGORY.getDbFieldName(), dbFieldName) && arrContains(NORMAL_PRODUCT_CATEGORY.getRefObjName(), refObjName))
                && !NORMAL_PRODUCT_CATEGORY.getRefTargetField().equals(refTargetField)) {
            return SELECT_ONE;
        }

        /** 多选 */
        if (Stream.of(SELECT_MANY.getFieldTypeName().split(EasyStatQueryConstant.SPLIT.SPLIT_STR_COMMA)).collect(Collectors.toList()).contains(fieldType)
                && Stream.of(SELECT_MANY.getSubFieldTypeName().split(EasyStatQueryConstant.SPLIT.SPLIT_STR_COMMA)).collect(Collectors.toList()).contains(subFieldType)
                && SELECT_MANY.getType().equals(type)) {
            return SELECT_MANY;
        }
        /** 维度类型=>按照多选处理 */
        if(DIMENSION.getType().equals(type)){
            return SELECT_MANY;
        }

        /** 布尔 */
        if (
//                arrContains(BOOLEAN.getFieldTypeName(), fieldType) &&
//                 arrContains(BOOLEAN.getSubFieldTypeName(), subFieldType) &&
                        BOOLEAN.getType().equals(type)) {
            return BOOLEAN;
        }
        // 特殊的多值人员
        if (EasyStatQueryConstant.COMMON.SPECAL_EMP_MANY_LIST.contains(dbObjName + "|" + dbFieldName) ||
                EasyStatQueryConstant.COMMON.SPECAL_EMP_MANY_LIST.contains(refObjName + "|" + refTargetField) ||
                (isSingle == 0 && Constants.EMPLOYEE.equals(type))) {
            return EMPLOYEE_MANY;
        }

        /** 自定义人员 */
        if (arrContains(EMPLOYEE.getFieldTypeName(), fieldType)
                && EMPLOYEE.getSubFieldTypeName().equals(subFieldType)
                && !arrContains(OWNER.getDbFieldName(), dbFieldName)
                && !arrContains(OUT_OWNER.getDbFieldName(), dbFieldName)
                && !(OWNER_DEPARTMENT.getDbFieldName().equals(dbFieldName))
                && EMPLOYEE.getType().equals(type)) {
            return EMPLOYEE;
        }

        /** 自定义多值人员 */
        if (arrContains(EMPLOYEE_MANY.getFieldTypeName(), fieldType)
                && EMPLOYEE_MANY.getSubFieldTypeName().equals(subFieldType)
                && EMPLOYEE_MANY.getType().equals(type)) {
            return EMPLOYEE_MANY;
        }

        /** 老 - 产品分类 */
        if (arrContains(NORMAL_PRODUCT_CATEGORY.getFieldTypeName(), fieldType)
          && arrContains(NORMAL_PRODUCT_CATEGORY.getSubFieldTypeName(), subFieldType)
          && (arrContains(NORMAL_PRODUCT_CATEGORY.getDbFieldName(), dbFieldName) && arrContains(NORMAL_PRODUCT_CATEGORY.getRefObjName(), refObjName)) || NORMAL_PRODUCT_CATEGORY.getRefTargetField().equals(refTargetField)) {
            return NORMAL_PRODUCT_CATEGORY;
        }

        /** 新 - 产品分类 */
        if (Stream.of(REF_PRODUCT_CATEGORY.getFieldTypeName().split(EasyStatQueryConstant.SPLIT.SPLIT_STR_COMMA)).collect(Collectors.toList()).contains(fieldType)
                && REF_PRODUCT_CATEGORY.getSubFieldTypeName().equals(subFieldType) && !arrContains(PRODUCT_CATEGORY_PATH.getType(), type)) {
            return REF_PRODUCT_CATEGORY;
        }

        /** 查找关联 */
        if (Stream.of(REF.getFieldTypeName().split(EasyStatQueryConstant.SPLIT.SPLIT_STR_COMMA)).collect(Collectors.toList()).contains(fieldType)
                && arrContains(REF.getType(), type)) {
            return REF;
        }

        /** 查找关联多选 */
        if (Stream.of(REF_MANY.getFieldTypeName().split(EasyStatQueryConstant.SPLIT.SPLIT_STR_COMMA)).collect(Collectors.toList()).contains(fieldType)
                && REF_MANY.getType().equals(type)) {
            return REF_MANY;
        }

        /**
         * 国家
         */
        if (arrContains(COUNTRY.getFieldTypeName(), fieldType)
                && COUNTRY.getSubFieldTypeName().equals(subFieldType)
                && COUNTRY.getType().equals(type)) {
            return COUNTRY;
        }

        /**
         * 省
         */
        if (arrContains(PROVINCE.getFieldTypeName(), fieldType)
                && PROVINCE.getSubFieldTypeName().equals(subFieldType)
                && PROVINCE.getType().equals(type)) {
            return PROVINCE;
        }

        /**
         * 市
         */
        if (arrContains(CITY.getFieldTypeName(), fieldType)
                && CITY.getSubFieldTypeName().equals(subFieldType)
                && CITY.getType().equals(type)) {
            return CITY;
        }

        /**
         * 区
         */
        if (arrContains(DISTRICT.getFieldTypeName(), fieldType)
                && DISTRICT.getSubFieldTypeName().equals(subFieldType)
                && DISTRICT.getType().equals(type)) {
            return DISTRICT;
        }

        /**
         * 乡镇
         */
        if (arrContains(TOWN.getFieldTypeName(), fieldType)
          && TOWN.getSubFieldTypeName().equals(subFieldType)
          && TOWN.getType().equals(type)) {
            return TOWN;
        }

        /**
         * 客户层级
         */
        if (arrContains(ACCOUNT_PATH.getFieldTypeName(), fieldType)
                && ACCOUNT_PATH.getSubFieldTypeName().equals(subFieldType)
                && ACCOUNT_PATH.getDbFieldName().equals(dbFieldName)
                && ACCOUNT_PATH.getType().equals(type)) {
            return ACCOUNT_PATH;
        }

        /**
         * 市场活动层级
         */
        if (arrContains(MARKETING_EVENT_PATH.getFieldTypeName(), fieldType)
                && MARKETING_EVENT_PATH.getSubFieldTypeName().equals(subFieldType)
                && MARKETING_EVENT_PATH.getDbFieldName().equals(dbFieldName)
                && MARKETING_EVENT_PATH.getType().equals(type)) {
            return MARKETING_EVENT_PATH;
        }

        /**
         * 落地页层级
         */
        if (arrContains(LANDING_PAGE_PATH.getFieldTypeName(), fieldType)
              && LANDING_PAGE_PATH.getSubFieldTypeName().equals(subFieldType)
              && LANDING_PAGE_PATH.getDbFieldName().equals(dbFieldName)
              && LANDING_PAGE_PATH.getType().equals(type)) {
            return LANDING_PAGE_PATH;
        }

        /**
         * BOM路径
         */
        if (arrContains(BOM_PATH.getFieldTypeName(), fieldType)
              && BOM_PATH.getSubFieldTypeName().equals(subFieldType)
              && BOM_PATH.getDbFieldName().equals(dbFieldName)
              && BOM_PATH.getType().equals(type)) {
            return BOM_PATH;
        }

        /**
         * 产品分类层级
         */
        if (arrContains(PRODUCT_CATEGORY_PATH.getFieldTypeName(), fieldType)
                && PRODUCT_CATEGORY_PATH.getSubFieldTypeName().equals(subFieldType)
                && PRODUCT_CATEGORY_PATH.getType().equals(type)
                && PRODUCT_CATEGORY_PATH.getDbFieldName().equals(dbFieldName)) {
            return PRODUCT_CATEGORY_PATH;
        }

        /**
         * what_api字段
         */
        if (WHAT_API.getSubFieldTypeName().equals(subFieldType) && WHAT_API.getType().equals(type) &&
          arrContains(WHAT_API.getDbFieldName(), dbFieldName)) {
            return WHAT_API;
        }

        /**
         * what_data字段
         */
        if (WHAT_DATA.getSubFieldTypeName().equals(subFieldType) && WHAT_DATA.getType().equals(type)) {
            return WHAT_DATA;
        }

        /**
         * 标签组
         */
        if (arrContains(TAG_GROUP.getFieldTypeName(), fieldType)
                && TAG_GROUP.getSubFieldTypeName().equals(subFieldType)
                && TAG_GROUP.getType().equals(type)) {
            return TAG_GROUP;
        }

        /**
         * 标签
         */
        if (arrContains(SUB_TAG.getFieldTypeName(), fieldType)
                && SUB_TAG.getSubFieldTypeName().equals(subFieldType)
                && SUB_TAG.getType().equals(type)) {
            return SUB_TAG;
        }

        if (NUMBER.getFieldTypeName().equals(fieldType) || IMAGE.getType().equals(type)) {
            return NUMBER;
        }

        if (TAG.getFieldTypeName().equals(fieldType) || TAG.getType().equals(type)) {
            return STRING;
        }

        return NULL;
    }

    /**
     * 获取筛选字段的指定层级类型
     */
    private static FilterTypeEnum getFilterAppointLevelType(Filter filter, FilterTypeEnum filterTypeEnum) {
        /** 部门类指定层级 */
        if (filter.checkFilterIsAppointLevel() &&
          (filterTypeEnum.isDepartment() || filterTypeEnum.isOwnDepartment() || filterTypeEnum.isDataOwnDepartment())) {
            return DEPARTMENT_APPOINT_LEVEL;
        }
        /** 产品分类类指定层级 */
        if (filter.checkFilterIsAppointLevel() && filterTypeEnum.isAllProductCategory()) {
            return PRODUCT_CATEGORY_APPOINT_LEVEL;
        }
        return filterTypeEnum;
    }

    /**
     * 包含
     */
    private static boolean arrContains(String str, String containsStr){
        if (StringUtils.isBlank(str)) {
            return false;
        }

        return Stream.of(str.split(EasyStatQueryConstant.SPLIT.SPLIT_STR_COMMA)).collect(Collectors.toList()).contains(containsStr);
    }

    /**
     * 统一打印数据范围
     */
    private static void logFilterType(Filter filter, FilterTypeEnum filterTypeEnum, boolean needLogFilterType){
        String fieldId = filter.getFieldId();
        String fieldName = filter.getFieldName();
        if (needLogFilterType) {
            log.info(EasyStatQueryConstant.LOG.getLogPre() + "filter类型, 判断成功 fieldId: {}, fieldName: {}, filterTypeEnum: {}", fieldId, fieldName, filterTypeEnum);
        }
    }
}
