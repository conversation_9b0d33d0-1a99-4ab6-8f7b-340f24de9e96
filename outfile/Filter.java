package com.facishare.bi.common.dto.filter;

import com.alibaba.fastjson.annotation.JSONField;
import com.dyuproject.protostuff.Tag;
import com.facishare.bi.common.Constants;
import com.facishare.bi.common.dto.*;
import com.facishare.bi.common.entities.FieldFilter;
import com.facishare.bi.common.entities.FieldInfo;
import com.facishare.bi.common.entities.UI;
import com.facishare.bi.common.model.EmployeeAndCircle;
import com.google.common.base.Strings;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

import static com.facishare.bi.common.Constants.DIMENSION;

/**
 * 过滤器参数类
 * Created by liupeng on 2016/6/2.
 */
public class Filter implements Comparable<Filter>, Cloneable, ILocationField {
  @Tag(1)
  @JSONField(name = "displayNumber")
  private int displayNumber;

  @Tag(2)
  @JSONField(name = "fieldId")
  private String fieldId;

  @Tag(3)
  @JSONField(name = "isLock")
  private int isLock;

  @Tag(4)
  @JSONField(name = "operator")
  private int operator;

  @Tag(5)
  @JSONField(name = "value1")
  private String value1;

  @Tag(6)
  @JSONField(name = "dbFieldName")
  private String dbFieldName;

  @Tag(7)
  @JSONField(name = "dbObjName")
  private String dbObjName;

  @Tag(8)
  @JSONField(name = "fieldName")
  private String fieldName;

  @Tag(9)
  @JSONField(name = "operatorLabel")
  private String operatorLabel;

  @Tag(10)
  @JSONField(name = "dateRangeID")
  private String dateRangeID;

  @Tag(11)
  @JSONField(name = "fieldType")
  private String fieldType;

  @Tag(12)
  @JSONField(name = "value2")
  private String value2;

  @Tag(13)
  @JSONField(name = "enumName")
  private String enumName;

  @Tag(14)
  @JSONField(name = "enumOptionList")
  private List<EnumOption> enumOptionList;

  @Tag(15)
  @JSONField(name = "enumCodeList")
  private List<String> enumCodeList;

  @Tag(16)
  @JSONField(name = "enumValueList")
  private List<String> enumValueList;

  @Tag(17)
  @JSONField(name = "subFieldType")
  private String subFieldType;

  @Tag(18)
  @JSONField(name = "parentID")
  private String parentID;

  @Tag(19)
  @JSONField(name = "enumItemList")
  private List<EnumItem> enumItemList;

  @Tag(20)
  @JSONField(name = "selectedItemList")
  private List<EnumItem> selectedItemList;

  @Tag(21)
  @JSONField(name = "refObjName")
  private String refObjName;

  @Tag(22)
  @JSONField(name = "allSelectableCirAndEmp")
  private GetAllSelectableCirclesAndEmployeesResult allSelectableCirAndEmp;

  @Tag(23)
  @JSONField(name = "cascadeFieldID")
  private String cascadeFieldID;

  @Tag(24)
  @JSONField(name = "ui")
  private FieldTypeInfo ui;

  @Tag(25)
  @JSONField(name = "isPre")
  private int isPre = 0;

  @Tag(26)
  @JSONField(name = "objName")
  private String objName;

  @Tag(27)
  @JSONField(name = "type")
  private String type;   // uiType

  @Tag(28)
  @JSONField(name = "parentFieldId")
  private String parentFieldId;

  @Tag(29)
  @JSONField(name = "parentDbFieldName")
  private String parentDbFieldName;

  @Tag(30)
  @JSONField(name = "parentDbObjName")
  private String parentDbObjName;

  @Tag(31)
  @JSONField(name = "parentFieldName")
  private String parentFieldName;

  @Tag(32)
  @JSONField(name = "fieldLocation")
  private String fieldLocation;

  @Tag(33)
  @JSONField(name = "fieldID")
  private String fieldID=this.fieldId;

  @Tag(34)
  @JSONField(name = "isOffLine")
  private boolean isOffLine = false; //是否下线该字段

  @Tag(35)
  @JSONField(name = "offLineMsg")
  private String offLineMsg;  //下线提示

  /**
   * 对象节点的Sequence
   */
  @Tag(36)
  @JSONField(name = "objectSequence")
  private Integer objectSequence;


  public Integer getObjectSequence() {
    return objectSequence;
  }

  public void setObjectSequence(Integer objectSequence) {
    this.objectSequence = objectSequence;
  }

  @Tag(37)
  @JSONField(name = "showParentInfo")
  private int showParentInfo = 0;  //在指标管理界面是否回显parent信息 0 显示 ， 1 不显示

  @Tag(37)
  @JSONField(name="objectDescribeApiName")
  private String objectDescribeApiName;

  @Tag(38)
  @JSONField(name="tableLocation")
  private String tableLocation;

  @Tag(39)
  @JSONField(name="fromExpandCells")
  private boolean fromExpandCells;

  @Tag(40)
  @JSONField(name="fieldRelationType")
  private String fieldRelationType;

  //用于存储全局的筛选器中 使用查找关联对象的id进行查找数据的目标字段 "type": "object_reference" 才有这个值
  @Tag(41)
  @JSONField(name="globalRefTargetField")
  private String globalRefTargetField;

  @Tag(42) //用于覆盖value1 展示给用户
  @JSONField(name="value1Code")
  private String value1Code;

  @Tag(43)
  @JSONField(name="refTargetField")
  private String refTargetField;

  @Tag(44)
  @JSONField(name="isSupportStopEmployeeOption")
  private int isSupportStopEmployeeOption = 0;

  @Tag(45)
  @JSONField(name="objId")
  private String objId;


  @Tag(46)
  @JSONField(name="formatStr")
  private String formatStr;

  @Tag(47)
  @JSONField(name="isApplyCircleFilter")
  private boolean isApplyCircleFilter = false;

  @Tag(48)
  @JSONField(name="isOutDetailFilter")
  private int isOutDetailFilter ;

  @Tag(49)
  @JSONField(name="originalType")
  private String originalType;

  @Tag(50)
  @JSONField(name = "crmFieldName")
  private String crmFieldName;

  @Tag(51)
  @JSONField(name = "filterId")
  private String filterId;

  @Tag(52)
  @JSONField(name = "isLeakage")
  private int isLeakage;
  @Tag(53)
  @JSONField(name = "linkObjName")
  private String linkObjName;

  /**
   * 筛选器来源 goalRule-根据目标规则生成的筛选器
   */
  @Tag(54)
  @JSONField(name = "filterSource")
  private String filterSource;

  @Tag(55)
  @JSONField(name = "crmObjName")
  private String crmObjName;

  //字段对应的udf的field_id
  @Tag(56)
  @JSONField(name = "udfFieldId")
  private String udfFieldId;


  @Tag(57)
  @JSONField(name = "selectedEmployeeAndCircleList")
  private List<EmployeeAndCircle> selectedEmployeeAndCircleList;

  @Tag(58)
  @JSONField(name="refKeyField")
  private String refKeyField;

  @Tag(59)
  @JSONField(name="status")
  private int status = -1;

  @Tag(60)
  @JSONField(name="objShowName")
  private String objShowName;

  @Tag(61)
  @JSONField(name="isContainStopEmployeeOption")
  private int isContainStopEmployeeOption;

  /**
   * 引用类型
   */
  private String quoteType;

  /**
   * 计算字段返回类型
   */
  private String returnType;


  /**
   * 时区无关属性
   */
  private Boolean notUseMultitimeZone = false;

  /**
   * 是否包含停用部门
   */
  private boolean containStopDept;
  /**
   * value值是否是被其它筛选影响。0没影响，1受全局筛选人员，2受全局筛选日期，3受自定义筛选
   */
  private int valueEffectBySomeFilter;

  /**
   * 该字段为查看明细，报表解析层级类型数据范围的标识——以产品分类为例：
   * 产品分类平铺时查看明细，用0表示
   * 产品分类汇总时查看明细，用1表示
   * 该字段由维度和下钻字段中dimHierarchyType中构造而来
   */
  private Integer filterHierarchyType = 1;

  /**
   * 报表字段解析，本身节点用1表示
   * 报表字段解析，本身节点用2表示
   */
  private Integer isGroup = 2;

  /**
   * 枚举项场景id
   */
  private String option;
  /**
   * 指定层级可选项
   */
  private List<Integer> specifyLevelOptions;

  /**
   *筛选器的配置
   */
  private FilterConfig filterConfig;

  /**
   * 区分是维度字段还是指标字段
   */
  private String aggDimType;

  private String uiType;

  private String extendFieldApiName;

  /**
   * dw描述id
   */
  private String dwFieldId;

  /**
   * 筛选器字段是否开启了数据多语
   */
  private Boolean enableMultiLang;

  /**
   *
   */
  private Integer isSingle;

  public boolean getEnableMultiLang() {
    return enableMultiLang != null && enableMultiLang;
  }

  public void setEnableMultiLang(Boolean enableMultiLang) {
    this.enableMultiLang = enableMultiLang;
  }

  public String getDwFieldId() {
    return dwFieldId;
  }

  public void setDwFieldId(String dwFieldId) {
    this.dwFieldId = dwFieldId;
  }

  private String dimensionId;

  private List<String> operateMenu;

  private String customType;


  public String getCustomType() {
    return customType;
  }

  public void setCustomType(String customType) {
    this.customType = customType;
  }

  public String getDimensionId() {
    return dimensionId;
  }

  public void setDimensionId(String dimensionId) {
    this.dimensionId = dimensionId;
  }

  public String getUiType() {
    return uiType;
  }

  public void setUiType(String uiType) {
    this.uiType = uiType;
  }

  public int getValueEffectBySomeFilter() {
    return valueEffectBySomeFilter;
  }

  public void setValueEffectBySomeFilter(int valueEffectBySomeFilter) {
    this.valueEffectBySomeFilter = valueEffectBySomeFilter;
  }
  public String getAggDimType() {
    return aggDimType;
  }

  public void setAggDimType(String aggDimType) {
    this.aggDimType = aggDimType;
  }

  public String getQuoteType() {
    return quoteType;
  }

  public void setQuoteType(String quoteType) {
    this.quoteType = quoteType;
  }

  public void setFilterId(String filterId){
    this.filterId = filterId;
  }

  public String getFilterId(){
    return filterId;
  }

  public String getCrmFieldName() {
    return crmFieldName;
  }

  public void setCrmFieldName(String crmFieldName) {
    this.crmFieldName = crmFieldName;
  }

  public String getOriginalType() {
    return originalType;
  }

  public void setOriginalType(String originalType) {
    this.originalType = originalType;
  }

  public boolean isApplyCircleFilter() {
    return isApplyCircleFilter;
  }

  public void setApplyCircleFilter(boolean applyCircleFilter) {
    isApplyCircleFilter = applyCircleFilter;
  }

  public String getFormatStr() {
    return formatStr;
  }

  public void setFormatStr(String formatStr) {
    this.formatStr = formatStr;
  }

  public String getObjId() {
    return objId;
  }

  public void setObjId(String objId) {
    this.objId = objId;
  }

  public int getIsSupportStopEmployeeOption() {
    return isSupportStopEmployeeOption;
  }

  public void setIsSupportStopEmployeeOption(int isSupportStopEmployeeOption) {
    this.isSupportStopEmployeeOption = isSupportStopEmployeeOption;
  }

  public String getRefTargetField() {
    return refTargetField;
  }

  public void setRefTargetField(String refTargetField) {
    this.refTargetField = refTargetField;
  }

  public String getValue1Code() {
    return value1Code;
  }

  public void setValue1Code(String value1Code) {
    this.value1Code = value1Code;
  }

  public String getGlobalRefTargetField() {
    return globalRefTargetField;
  }

  public void setGlobalRefTargetField(String globalRefTargetField) {
    this.globalRefTargetField = globalRefTargetField;
  }

  public String getFieldRelationType() {
    return fieldRelationType;
  }

  public void setFieldRelationType(String fieldRelationType) {
    this.fieldRelationType = fieldRelationType;
  }

  public boolean isFromExpandCells() {
    return fromExpandCells;
  }

  public void setFromExpandCells(boolean fromExpandCells) {
    this.fromExpandCells = fromExpandCells;
  }

  public String getTableLocation() {
    return tableLocation;
  }

  public void setTableLocation(String tableLocation) {
    this.tableLocation = tableLocation;
  }

  public int getShowParentInfo() {
    return showParentInfo;
  }
  public void setShowParentInfo(int showParentInfo) {
    this.showParentInfo = showParentInfo;
  }
  public boolean isOffLine() {
    return isOffLine;
  }

  public void setOffLine(boolean offLine) {
    isOffLine = offLine;
  }

  public String getOffLineMsg() {
    return offLineMsg;
  }

  public void setOffLineMsg(String offLineMsg) {
    this.offLineMsg = offLineMsg;
  }

  public int getDisplayNumber() {
    return displayNumber;
  }

  public void setDisplayNumber(int displayNumber) {
    this.displayNumber = displayNumber;
  }

  public String getFieldId() {
    return Strings.isNullOrEmpty(fieldId) ? fieldID : fieldId;
  }

  public void setFieldId(String fieldId) {
    this.fieldId = fieldId;
  }

  public int getIsLock() {
    return isLock;
  }

  public void setIsLock(int isLock) {
    this.isLock = isLock;
  }

  public int getOperator() {
    return operator;
  }

  public void setOperator(int operator) {
    this.operator = operator;
  }

  public String getValue1() {
    return value1;
  }

  public void setValue1(String value1) {
    this.value1 = value1;
  }

  public String getDbFieldName() {
    return dbFieldName;
  }

  public void setDbFieldName(String dbFieldName) {
    this.dbFieldName = dbFieldName;
  }

  public String getDbObjName() {
    return dbObjName;
  }

  public void setDbObjName(String dbObjName) {
    this.dbObjName = dbObjName;
  }

  public String getFieldName() {
    return fieldName;
  }

  public void setFieldName(String fieldName) {
    this.fieldName = fieldName;
  }

  public String getOperatorLabel() {
    return operatorLabel;
  }

  public void setOperatorLabel(String operatorLabel) {
    this.operatorLabel = operatorLabel;
  }

  public String getDateRangeID() {
    return dateRangeID;
  }

  public void setDateRangeID(String dateRangeID) {
    this.dateRangeID = dateRangeID;
  }

  public String getFieldType() {
    return fieldType;
  }

  public void setFieldType(String fieldType) {
    this.fieldType = fieldType;
  }

  public String getValue2() {
    return value2;
  }

  public void setValue2(String value2) {
    this.value2 = value2;
  }

  public String getEnumName() {
    return enumName;
  }

  public void setEnumName(String enumName) {
    this.enumName = enumName;
  }

  public List<EnumOption> getEnumOptionList() {
    return enumOptionList;
  }

  public void setEnumOptionList(List<EnumOption> enumOptionList) {
    this.enumOptionList = enumOptionList;
  }

  public List<String> getEnumCodeList() {
    return enumCodeList;
  }

  public void setEnumCodeList(List<String> enumCodeList) {
    this.enumCodeList = enumCodeList;
  }

  public List<String> getEnumValueList() {
    return enumValueList;
  }

  public void setEnumValueList(List<String> enumValueList) {
    this.enumValueList = enumValueList;
  }

  public String getSubFieldType() {
    return subFieldType;
  }

  public void setSubFieldType(String subFieldType) {
    this.subFieldType = subFieldType;
  }

  public String getParentID() {
    return parentID;
  }

  public void setParentID(String parentID) {
    this.parentID = parentID;
  }

  public List<EnumItem> getEnumItemList() {
    return enumItemList;
  }

  public void setEnumItemList(List<EnumItem> enumItemList) {
    this.enumItemList = enumItemList;
  }

  public List<EnumItem> getSelectedItemList() {
    return selectedItemList;
  }

  public void setSelectedItemList(List<EnumItem> selectedItemList) {
    this.selectedItemList = selectedItemList;
  }

  public String getRefObjName() {
    return refObjName;
  }

  public void setRefObjName(String refObjName) {
    this.refObjName = refObjName;
  }

  public GetAllSelectableCirclesAndEmployeesResult getAllSelectableCirAndEmp() {
    return allSelectableCirAndEmp;
  }

  public void setAllSelectableCirAndEmp(GetAllSelectableCirclesAndEmployeesResult allSelectableCirAndEmp) {
    this.allSelectableCirAndEmp = allSelectableCirAndEmp;
  }

  public String getCascadeFieldID() {
    return cascadeFieldID;
  }

  public void setCascadeFieldID(String cascadeFieldID) {
    this.cascadeFieldID = cascadeFieldID;
  }

  public FieldTypeInfo getUi() {
    return ui;
  }

  public void setUi(FieldTypeInfo ui) {
    this.ui = ui;
  }

  public int getIsPre() {
    return isPre;
  }

  public void setIsPre(int isPre) {
    this.isPre = isPre;
  }

  public String getObjName() {
    return objName;
  }

  public void setObjName(String objName) {
    this.objName = objName;
  }

  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }

  public String getParentFieldId() {
    return parentFieldId;
  }

  public void setParentFieldId(String parentFieldId) {
    this.parentFieldId = parentFieldId;
  }

  public String getParentDbFieldName() {
    return parentDbFieldName;
  }

  public void setParentDbFieldName(String parentDbFieldName) {
    this.parentDbFieldName = parentDbFieldName;
  }

  public String getParentDbObjName() {
    return parentDbObjName;
  }

  public void setParentDbObjName(String parentDbObjName) {
    this.parentDbObjName = parentDbObjName;
  }

  public String getParentFieldName() {
    return parentFieldName;
  }

  public void setParentFieldName(String parentFieldName) {
    this.parentFieldName = parentFieldName;
  }

  public String getFieldLocation() {
    return fieldLocation;
  }

  public void setFieldLocation(String fieldLocation) {
    this.fieldLocation = fieldLocation;
  }

  public String getFieldID() {
    return Strings.isNullOrEmpty(fieldID) ? fieldId : fieldID;
  }

  public void setFieldID(String fieldID) {
    this.fieldID = fieldID;
  }
  public void setFieldID() {
    this.fieldID = this.fieldId;
  }

  public void setIsLeakage(int isLeakage){
    this.isLeakage = isLeakage;
  }
  public int getIsLeakage(){
    return isLeakage;
  }

  public String getObjectDescribeApiName() {
    return objectDescribeApiName;
  }

  public void setObjectDescribeApiName(String objectDescribeApiName) {
    this.objectDescribeApiName = objectDescribeApiName;
  }

  public boolean isDimensionType(){
    return StringUtils.equals(this.type, DIMENSION);
  }

  public int getIsOutDetailFilter(){
    return isOutDetailFilter;
  }
  public void setIsOutDetailFilter(int isOutDetailFilter){
    this.isOutDetailFilter = isOutDetailFilter;
  }

  public String getLinkObjName() {
    return linkObjName;
  }

  public void setLinkObjName(String linkObjName) {
    this.linkObjName = linkObjName;
  }

  public String getFilterSource() {
    return filterSource;
  }

  public void setFilterSource(String filterSource) {
    this.filterSource = filterSource;
  }

  public String getCrmObjName() {
    return crmObjName;
  }

  public void setCrmObjName(String crmObjName) {
    this.crmObjName = crmObjName;
  }

  public String getUdfFieldId() {
    return udfFieldId;
  }

  public void setUdfFieldId(String udfFieldId) {
    this.udfFieldId = udfFieldId;
  }

  public Integer getFilterHierarchyType() {
    return filterHierarchyType;
  }

  public void setFilterHierarchyType(Integer filterHierarchyType) {
    this.filterHierarchyType = filterHierarchyType;
  }

  public Integer getIsGroup() {
    return isGroup;
  }

  public void setIsGroup(Integer isGroup) {
    this.isGroup = isGroup;
  }

  @Override
  public String toString() {
    return "Filter{" + "displayNumber=" + displayNumber + ", fieldId='" + fieldId + '\'' + ", isLock=" + isLock +
      ", operator=" + operator + ", value1='" + value1 + '\'' + ", dbFieldName='" + dbFieldName + '\'' +
      ", dbObjName='" + dbObjName + '\'' + ", fieldName='" + fieldName + '\'' + ", operatorLabel='" + operatorLabel +
      '\'' + ", dateRangeID='" + dateRangeID + '\'' + ", fieldType='" + fieldType + '\'' + ", value2='" + value2 +
      '\'' + ", enumName='" + enumName + '\'' + ", enumOptionList=" + enumOptionList + ", enumCodeList=" +
      enumCodeList + ", enumValueList=" + enumValueList + ", subFieldType='" + subFieldType + '\'' + ", parentID='" +
      parentID + '\'' + ", enumItemList=" + enumItemList + ", selectedItemList=" + selectedItemList + ", refObjName='" +
      refObjName + '\'' + ", allSelectableCirAndEmp=" + allSelectableCirAndEmp + ", cascadeFieldID='" + cascadeFieldID +
      '\'' + ", ui=" + ui + ", is_pre=" + isPre + ", objName='" + objName + '\'' + ", type='" + type + '\'' +
      ", parentFieldId='" + parentFieldId + '\'' + ", parentDbFieldName='" + parentDbFieldName + '\'' +
      ", parentDbObjName='" + parentDbObjName + '\'' + ", parentFieldName='" + parentFieldName + '\'' +
      ", fieldLocation='" + fieldLocation + '\'' + ", fieldID='" + fieldID + '\'' + ", isOffLine=" + isOffLine +
      ", offLineMsg='" + offLineMsg + '\'' + ", showParentInfo=" + showParentInfo + ", objectDescribeApiName='" +
      objectDescribeApiName + '\'' + '}';
  }

  @Override
  public int compareTo(Filter other) {
    int t = this.getDisplayNumber();
    int o = other.getDisplayNumber();
    return t < o ? -1 : (t == o) ? 0 : 1;
  }

  @Override
  // 目前浅拷贝即可
  public Filter clone() {
    Filter cloned = null;
    try {
      cloned = (Filter) super.clone();
    } catch (CloneNotSupportedException e) {
      throw new RuntimeException(e);
    }
    return cloned;
  }

  /**
   *  判断是否为主属部门
   * @return
   */
  public boolean isMainDepartment(){
    return Objects.equals(this.dbFieldName, Constants.MAIN_DEPARTMENT)
           &&Objects.equals(this.fieldType,Constants.STRING)&&Objects.equals(this.subFieldType,Constants.CIRCLE);
  }

  public List<EmployeeAndCircle> getSelectedEmployeeAndCircleList() {
    return selectedEmployeeAndCircleList;
  }

  public void setSelectedEmployeeAndCircleList(List<EmployeeAndCircle> selectedEmployeeAndCircleList) {
    this.selectedEmployeeAndCircleList = selectedEmployeeAndCircleList;
  }

  public String getRefKeyField() {
    return refKeyField;
  }

  public void setRefKeyField(String refKeyField) {
    this.refKeyField = refKeyField;
  }

  public int getStatus() {
    return status;
  }

  public void setStatus(int status) {
    this.status = status;
  }

  public String getObjShowName() {
    return objShowName;
  }

  public void setObjShowName(String objShowName) {
    this.objShowName = objShowName;
  }

  public int getIsContainStopEmployeeOption() {
    return isContainStopEmployeeOption;
  }

  public void setIsContainStopEmployeeOption(int isContainStopEmployeeOption) {
    this.isContainStopEmployeeOption = isContainStopEmployeeOption;
  }

  public Boolean isNotUseMultitimeZone() {
    return notUseMultitimeZone;
  }

  public void setNotUseMultitimeZone(Boolean notUseMultitimeZone) {
    this.notUseMultitimeZone = notUseMultitimeZone;
  }

  public boolean isContainStopDept() {
    return containStopDept;
  }

  public void setContainStopDept(boolean containStopDept) {
    this.containStopDept = containStopDept;
  }

  public String getReturnType() {
    return returnType;
  }

  public void setReturnType(String returnType) {
    this.returnType = returnType;
  }

  public String getOption() {
    return option;
  }

  public void setOption(String option) {
    this.option = option;
  }

  public FilterConfig getFilterConfig() {
    return filterConfig;
  }

  public void setFilterConfig(FilterConfig filterConfig) {
    this.filterConfig = filterConfig;
  }

  public List<String> getOperateMenu() {
    return operateMenu;
  }

  public void setOperateMenu(List<String> operateMenu) {
    this.operateMenu = operateMenu;
  }

  /**
   * 判断筛选器是否为指定层级筛选
   */
  public boolean checkFilterIsAppointLevel() {
    return Objects.nonNull(filterConfig) && Objects.equals(1, filterConfig.getShowAppointLevelSwitch()) &&
      Objects.equals(1, filterConfig.getSwitchStatus());
  }

  public List<Integer> getSpecifyLevelOptions() {
    return specifyLevelOptions;
  }

  public void setSpecifyLevelOptions(List<Integer> specifyLevelOptions) {
    this.specifyLevelOptions = specifyLevelOptions;
  }

  public String getExtendFieldApiName() {
    return extendFieldApiName;
  }

  public void setExtendFieldApiName(String extendFieldApiName) {
    this.extendFieldApiName = extendFieldApiName;
  }

  public FieldFilter toFieldFilter() {
    FieldFilter fieldFilter = new FieldFilter();
    fieldFilter.setFieldInfo(toFieldInfo());
    fieldFilter.setContainStopDept(containStopDept);
    fieldFilter.setDateRangeId(dateRangeID);
    fieldFilter.setDisplayNumber(displayNumber);
    fieldFilter.setOperator(operator);
    fieldFilter.setValue1(value1);
    fieldFilter.setValue2(value2);
    return fieldFilter;
  }

  public FieldInfo toFieldInfo() {
    FieldInfo fieldInfo = new FieldInfo();
    fieldInfo.setFieldId(StringUtils.isNotBlank(udfFieldId) ? udfFieldId : fieldId);
    fieldInfo.setFieldName(fieldName);
    fieldInfo.setObjectId(objId);
    fieldInfo.setDbFieldName(dbFieldName);
    fieldInfo.setDbObjName(dbObjName);
    fieldInfo.setFieldType(fieldType);
    fieldInfo.setSubFieldType(subFieldType);
    fieldInfo.setRefObjName(refObjName);
    fieldInfo.setRefKeyField(refKeyField);
    fieldInfo.setRefTargetField(refTargetField);
    fieldInfo.setType(type);
    fieldInfo.setCrmObjName(crmObjName);
    fieldInfo.setQuoteType(quoteType);
    fieldInfo.setReturnType(returnType);
    fieldInfo.setIsSupportStopEmployeeOption(isSupportStopEmployeeOption);
    fieldInfo.setUi(fieldTypeInfoToCommonEntitiesUI(ui));
    return fieldInfo;
  }

  public UI fieldTypeInfoToCommonEntitiesUI(FieldTypeInfo fieldTypeInfo) {
    if (fieldTypeInfo == null) {
      return null;
    }
    UI uI = new UI();
    uI.setType(fieldTypeInfo.getType());
    uI.setGroup(fieldTypeInfo.getGroup());
    uI.setFormat(fieldTypeInfo.getFormat());
    uI.setJustLeafNodeSelect(fieldTypeInfo.getJustLeafNodeSelect());
    uI.setOptionApiName(fieldTypeInfo.getOptionApiName());
    uI.setOutEmpData(fieldTypeInfo.getOutEmpData());
    uI.setOutDeptData(fieldTypeInfo.getOutDeptData());
    return uI;
  }

  public Integer getIsSingle() {
    return isSingle;
  }

  public void setIsSingle(Integer isSingle) {
    this.isSingle = isSingle;
  }
}
