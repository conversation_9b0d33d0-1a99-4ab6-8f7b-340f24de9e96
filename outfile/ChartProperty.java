package com.facishare.bi.metadata.context.infrastructure.dao.entity;

import com.facishare.bi.common.entities.TerminalEnum;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2016/12/26.
 */
@Data
public class ChartProperty {
  @Column(name = "chart_property_id")
  private String chartPropertyId;
  @Column(name = "chart_type")
  private String chartType;
  @Column(name = "property_name")
  private String propertyName;
  @Column(name = "default_value")
  private String defaultValue;
  @Column(name = "parent_property_id")
  private String parentPropertyId;
  @Enumerated(EnumType.STRING)
  private TerminalEnum terminal;
}
