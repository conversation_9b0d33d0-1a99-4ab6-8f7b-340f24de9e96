package com.facishare.bi.metadata.context.dto.ads;

import lombok.Data;

import java.util.Date;

/**
 * 图表视图字段配置实体类，对应数据库表 stat_view_field
 * 用于存储图表中维度、指标、过滤条件等字段的具体配置信息
 */
@Data
public class StatViewField {

  /** 视图字段配置ID */
  private String viewFieldId;

  /** 关联的视图ID */
  private String viewId;

  /** 关联的字段ID */
  private String fieldId;

  /** 排序类型：0-不排序，1-升序，2-降序，3-自定义排序 */
  private int orderType;

  /** 聚合类型，3表示属性维度，其它取指标的聚合类型 见AggrTypeEnum*/
  private String aggrType;

  /** 轴类型：0-维度，3-属性维度，1-第一行指标，2-第二行指标 */
  private int axisType;

  /** 企业编号 */
  private int ei;

  /** 是否可见 */
  private int isVisible;

  /** 字段顺序 */
  private int seq;

  /** 图例名称 */
  private String legendName;

  /** 创建人ID */
  private int creator;

  /** 创建时间 */
  private Date createTime;

  /** 最近修改人ID */
  private int updator;

  /** 最近修改时间 */
  private Date updateTime;

  /** 是否删除：0-否，1-是 */
  private int isDelete;

  /** 父级字段ID，用于日期等层级字段 */
  private String parentId;

  /** 同环比类型，默认"0" */
  private String ratioType = "0";

  /** 是否显示图例：0-否，1-是，默认1 */
  private int isShowLegend = 1; //是否显示图例  默认显示

  /** 列宽 */
  private int cellWidth;//字段宽度

  /** 数值格式化配置JSON */
  private String formatConfig;

  /** 转化率漏斗图阶段配置 */
  private String stageInfo;

  /** 层级分组标记：0-否，1-是 */
  private int hierarchyGroupFlag;

  /** 是否显示数值：0-否，1-是，默认1 */
  private int isShowValue = 1; //是否显示数值  默认显示

  /** 冻结列：0-否，1-是，默认0 */
  private int fixed;

  /** 指标配置JSON */
  private String measureConfig;

  /** 维度配置JSON */
  private String dimensionConfig;

  /** 分析类型，用于累计计算、占比等 见AggAnalysisTypeEnum*/
  private String analysisType;

  /** 移动端维度配置JSON */
  private String mobileDimensionConfig;
}
