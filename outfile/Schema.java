package com.facishare.bi.metadata.context.dto.dw;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.Date;
import java.util.List;

/**
 * 主题
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
public class Schema extends DwBiRelatedMetadata {
  private String schemaId;
  /**
   * 企业ID
   */
  private String tenantId;
  /**
   * 主题名称
   */
  private String schemaName;

  /**
   * 对象名称
   */
  private String schemaEnName;

  /**
   * 主题对象展示名称
   */
  private String schemaCnName;

  /**
   * 创建人
   */
  private String creator;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 描述
   */
  private String description;

  /**
   * 主题状态：0:启用 1:初始化 2:停用
   */
  private int status = -1;

  /**
   * 最近修改人
   */
  private String lastModifier;

  /**
   * 最近修改时间
   */
  private Date lastModifiedTime;

  /**
   * 是否删除
   */
  private int isDeleted = -1;

  /**
   * 是否是预置主题
   */
  private int isPre = 0;

  /**
   * 是否管控
   */
  private Integer limitControlTag;

  /**
   * 出栈企业
   */
  private String outboundTenantId;

  /**
   * 维度
   */
  private List<BiMtDimension> dimensions;

  /**
   * 指标
   */
  private List<BiMtMeasure> measures;
}
