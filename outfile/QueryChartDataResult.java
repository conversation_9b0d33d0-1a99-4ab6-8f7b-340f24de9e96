package com.facishare.bi.common.entities;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.bi.common.entities.stat.CellData;
import com.facishare.bi.common.entities.stat.Field;
import com.facishare.bi.common.entities.stat.StatQueryInfo;
import com.facishare.bi.common.entities.stat.FilterList;
import com.facishare.bi.common.entities.union.DetailQueryExtInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class QueryChartDataResult implements Serializable {
  private static final long serialVersionUID = -1L;
  @JSONField(name = "dataSet")
  private List<StatColumnDataDto> dataSet;
  @JSONField(name = "stages")
  private List<Stage> stages;
  @JSONField(name = "drillRouteFieldLists")
  private List<DrillFieldDto> drillRouteFieldLists;

  @JSONField(name = "dimensionFields")
  private List<DimensionFieldDto> dimensionFields;

  private Integer isDrilledDept;

  private List<FilterList> filterLists;

  /**
   * 统计图查询信息
   */
  private StatQueryInfo statQueryInfo;
  /**
   * 转化率漏斗图配置
   */
  private ViewConfig viewConfig;
  /**
   * 表头
   */
  private List<List<Field>> fields;
  /**
   * 表体
   */
  private List<List<CellData>> data;

  private transient Status status;

  /**
   * 分页信息
   */
  private Page page;

  /**
   * 系统时间
   */
  private String currentTime;

  /**
   * 数据更新时间
   */
  private String statUpdateTime;

  /**
   * 所有筛选条件
   */
  private List<List<FieldFilter>> fieldFilters;

  /**
   * 查看明细信息
   */
  private DetailQueryExtInfo detailQueryExtInfo;
}
