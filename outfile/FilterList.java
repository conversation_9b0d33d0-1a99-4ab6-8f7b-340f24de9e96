package com.facishare.bi.common.entities.stat;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.base.MoreObjects;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON> on 8/11/16.
 */
public class FilterList implements Serializable {
  private static final long serialVersionUID = -1L;
  @JSONField(name = "filters")
  private List<Filter> filters;

  public FilterList(List<Filter> filters) {
    this.filters = filters;
  }

  public FilterList() {
  }

  /**
   * 合并查看明细时的预置筛选条件和用户选择的筛选条件
   * leftFilterLists :  (A1*A2) | (B1*B2)
   * rightFilterLists : (C1*C2) | (D1*D2)
   * mergeFilterLists : ( (A1*A2) | (B1*B2)) * ( (C1*C2) | (D1*D2))
   * result ( (A1*A2*C1*C2) | (A1*A2*D1*D2) | (B1*B2*C1*C2) | (B1*B2*D1*D2) )
   */
  static public List<FilterList> mergeFilterLists(List<FilterList> leftFilterLists, List<FilterList> rightFilterLists) {
    if (CollectionUtils.isEmpty(leftFilterLists)) {
      return rightFilterLists;
    }
    if (CollectionUtils.isEmpty(rightFilterLists)) {
      return leftFilterLists;
    }
    List<FilterList> resultFilterList = Lists.newArrayList();
    for (FilterList preFilterList : rightFilterLists) {
      for (FilterList userFilterList : leftFilterLists) {
        List<Filter> filters = userFilterList.getFilters();
        List<Filter> resultFilter = Lists.newArrayList();
        resultFilter.addAll(filters);
        resultFilter.addAll(preFilterList.getFilters());
        FilterList filterList = new FilterList();
        filterList.setFilters(resultFilter);
        resultFilterList.add(filterList);
      }
    }
    return resultFilterList;
  }

  public List<Filter> getFilters() {
    return filters;
  }

  public void setFilters(List<Filter> filters) {
    this.filters = filters;
  }

  @Override
  public String toString() {
    return MoreObjects.toStringHelper(this).omitNullValues().add("filters", filters).toString();
  }

}
