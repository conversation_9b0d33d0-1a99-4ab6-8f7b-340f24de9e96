package com.facishare.bi.stat.dto.arg;

import com.alibaba.fastjson.annotation.JSONField;
import com.dyuproject.protostuff.Tag;
import com.facishare.bi.common.Constants;
import com.facishare.bi.common.dto.DrillCell;
import com.facishare.bi.common.dto.MeasureFieldDto;
import com.facishare.bi.common.dto.QuerySourceEnum;
import com.facishare.bi.common.dto.WarningRangeFormat;
import com.facishare.bi.common.dto.filter.*;
import com.facishare.bi.common.entities.ExportConfig;
import com.facishare.bi.common.entities.StatLayoutInfo;
import com.facishare.bi.easy.stat.core.common.pojo.EasyStatQueryInfo;
import com.facishare.bi.metadata.context.dto.dw.MultiDimGoalRule;
import com.facishare.bi.stat.common.QueryActionEnum;
import com.facishare.bi.stat.dto.DimensionFieldDto;
import com.facishare.bi.stat.dto.DrillFieldDto;
import com.facishare.bi.stat.dto.ViewConfig;
import com.facishare.bi.stat.dto.obj.StatFieldInfoDto;
import com.facishare.bi.stat.query.goal.bo.GoalRuleFilter;
import com.facishare.bi.stat.query.goal.bo.GoalRuleGroupKey;
import com.google.gson.Gson;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import java.util.List;

/**
 * 类说明:
 * 统计图数据查询参数类，
 * 作者: 于少博(yusb)
 * 创建日期: 2018/9/14
 * 参数:
 * 返回值:
**/
@Data
@Slf4j
public class QueryChartDataArg implements Cloneable{
  @Tag(1)
  @JSONField(name = "id")
  private String id;      // 统计图模板或者视图id

  @Tag(2)
  @JSONField(name = "isView")
  private int isView;      // 0,模板；1,视图；2,空白模板    对应StatCategoryEnum的枚举

  @Tag(3)
  @JSONField(name = "schemaId")
  private String schemaId;      // 统计图主题id

  //todo @于少博 这里为什么是List<String>?
  @Tag(4)
  @JSONField(name = "defaultFilterOptionIDs")
  private List<String> defaultFilterOptionIDs;    // 场景筛选器

  @Tag(5)
  @JSONField(name = "filterLists")
  private List<FilterList> filterLists;        // 数据范围筛选器

  @Tag(6)
  @JSONField(name = "dimensionFields")
  private List<DimensionFieldDto> dimensionFields;

  @Tag(7)
  @JSONField(name = "measureFields")
  private List<MeasureFieldDto> measureFields;

  @Tag(8)
  @JSONField(name = "drillDimValues")
  private List<DrillCell> drillDimValues;    // 钻取参数

  @Tag(9)
  @JSONField(name = "topNum")
  private Integer topNum;    // top数目

  @Tag(10)
  @JSONField(name = "dimDataStructure")
  private int dimDataStructure = 1; // 参照StatExecModel.dimDataStructure

  @Tag(11)
  @JSONField(name = "isDepFinish")
  private int isDepFinish; // 是否来自部门完成情况 1:是 0:否

  @Tag(12)
  @JSONField(name = "globalFilter")
  private GlobalFilter globalFilter;

  @Tag(13)
  @JSONField(name="isShowDimension")
  private int isShowDimension = 1;//是否显示指标为0的维度  1:显示 0:不显示

  @Tag(14)
  @JSONField(name = "source")
  private int source;

  @Tag(15)
  @JSONField(name = "averageFieldId")
  private String averageFieldId;//显示平均值的字段id  为空则不显示平均线

  @Tag(16)
  @JSONField(name = "dashboardId")
  private String dashboardId;//驾驶舱id

  @Tag(17)
  @JSONField(name = "querySource")
  private String querySource = QuerySourceEnum.UNKNOWN.getSourceMarkers();//查询请求的来源

  @Tag(18)
  @JSONField(name = "tips")
  private String tips;//临时保存tips

  @Tag(19)
  @JSONField(name = "averageFieldRatioType")
  private String averageFieldRatioType;//显示平均值的字段类型

  private int isContainStopEmployee; //是否包含离职员工去缓存标识

  @Tag(20)
  @JSONField(name = "schemaEnName")
  private String schemaEnName;

  @Tag(21)
  @JSONField(name = "stages")
  private List <Stage> stages;

  @Tag(22)
  @JSONField(name = "cvrExp")
  private String cvrExp;//转化率漏斗转化率计算方式 totalCvr(总转化率) /  stageCvr(阶段转化率)

  @Tag(23)
  @JSONField(name = "refresh")
  private int refresh;

  @Tag(24)
  @JSONField(name = "cacheTime")
  private int cacheTime;

  @Tag(25)
  @JSONField(name = "drillRouteFieldLists")
  private  List<DrillFieldDto> drillRouteFieldLists;

  @Tag(26)
  @JSONField(name = "dimensionValue")
  private String dimensionValue;

  @Tag(27)
  @JSONField(name = "dimensionValueCode")
  private String dimensionValueCode;

  @Tag(28)
  @JSONField(name = "queryAction")
  private String queryAction = QueryActionEnum.Common.getType();

  @Tag(29)
  @JSONField(name = "goalRuleGroupKey")
  private GoalRuleGroupKey goalRuleGroupKey;

  @Tag(30)
  @JSONField(name = "ratioDateFieldId")
  private String ratioDateFieldId;


  @Tag(31)
  private int isTryDrillDept;//是否尝试下钻一级部门


  @Tag(32)
  private String goalRuleId;//是否尝试下钻一级部门

  @Tag(33)
  @JSONField(name = "dimensionAttrFields")
  private List<MeasureFieldDto> dimensionAttrFields;

  /**
   * 统计图原始的维度字段
   * 新目标主题统计图员工姓名做维度时，需要根据原始的维度信息是否是人员维度
   */
  @Tag(34)
  @JSONField(name = "originalDimensionFields")
  private List<StatFieldInfoDto> originalDimensionFields;


  private GoalRuleFilter goalRuleFilter;

  List<BaseLocalFilter> dateFiters;

  List<BaseLocalFilter> empFilters;

  private LocalFilter localFilter;

  private String goalTag;

  /**
   * 自定义筛选器配置数据
   */
  private List<CustomizeFilter> customizeFilterConfig;
  /**
   * 查询请求来源 board-从驾驶舱来的请求查询
   */
  private String from;
  /**
   * 查询请求来源id
   */
  private String fromID;
  /**
   *拼表调源表时拼表的来源，board说明是在驾驶舱里的拼表，用于判断离职员工是否走驾驶舱配置使用
   */
  private String lwtFrom;

  /**
   * 拼表的来源id，board时传的驾驶舱id
   */
  private String lwtFromId;
  //拼表id
  private String lwtId;

  /**
   * 图表联动筛选器
   */
  private List<LinkFilter> linkFilters;

  /**
   * 时区
   */
  private String timeZone = Constants.DEFAULT_TIME_ZONE;

  /**
   * 用于识别调用方
   */
  private String version;

  /**
   * 预警
   */
  private List<WarningRangeFormat> warningRangeInfoList;

  /**
   * 统计图配置信息
   */
  private StatLayoutInfo statLayoutInfo;


  /**
   * 下钻到下级部门的标识
   */
  private boolean drillToSubDeptFlag;

  /**
   * 是否走重构
   *
   * 1-走老逻辑 2-走重构(PG) 3-走重构(CH)
   */
  private Integer easyStatQueryTypeForTest;

  /**
   * 转化率漏斗图配置
   */
  private ViewConfig viewConfig;

  /**
   * 图表类型
   */
  private String chartType;

  /**
   * 统计图查询模式(部门类字段下钻，解析维度值时使用)
   *
   * 0-老查询 1-重构查询
   */
  private Integer easyStatQueryType;

  /**
   * 重构查询限制条数(限制明细数据)
   *
   * {@link com.facishare.bi.easy.stat.core.common.enums.EasyStatLimitTypeEnum}
   */
  private Integer queryLimit = 0;


  /**
   * 数据查询来源
   *
   * {@link com.facishare.bi.common.dto.StatDataQuerySourceEnum}
   */
  private Integer statDataQuerySource = 0;

  /**
   * 终端来源 web端或移动端
   *
   * {@link com.facishare.bi.common.dto.ClientSourceEnum}
   */
  private String clientSource;

  /**
   * 分页数 从1开始，null/0不分页
   */
  private Integer pageNumber;

  /**
   * 分页大小
   */
  private Integer pageSize;

  /**
   * 分页偏移量
   */
  private Integer offset;

  /**
   * 分页类型
   *
   * {@link com.facishare.bi.common.dto.PageUpTypeEnum}
   */
  private String pageUpType;

  /**
   * 订阅任务名称
   */
  private String schName;

  /**
   * 是否需要全量的字段信息（拼表请求源表时需要将计算字段中用到的指标字段下发的结果集中）
   * 0-不需要  1-需要
   */
  private int needAllFields;

  /**
   * 统计图重构 查询参数
   */
  private EasyStatQueryInfo easyStatQueryInfo = new EasyStatQueryInfo();

  /**
   * 多维目标规则信息
   */
  private MultiDimGoalRule multiDimGoalRule;

  private String commonFilterList;

  /**
   * 导出参数
   */
  private ExportConfig exportConfig;

  private List<FilterList> secondaryFilterLists;

  @Override
  public String toString() {
    return new Gson().toJson(this);
  }

  public List<DrillCell> getDrillDimValues() {
    return drillDimValues;
  }

  @Override
  public QueryChartDataArg clone(){
    QueryChartDataArg obj = null;
    try {
      obj = (QueryChartDataArg) super.clone();
      String s = new Gson().toJson(this);
      obj = new Gson().fromJson(s,this.getClass());
    } catch (CloneNotSupportedException e) {
      log.error("error in clone",e);
    }
    return obj;
  }

}
