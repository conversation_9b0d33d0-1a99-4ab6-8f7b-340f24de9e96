package com.facishare.bi.common.entities;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 转化率漏斗图配置
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ViewConfig {
  /**
   * 转化率公式
   * 1：总转化率公式
   * 2：阶段转化率公式
   */
  private String conversionFormula;
  /**
   * 阶段信息
   */
  private List<StageInfo> stageInfoList;
  /**
   * 统计表复合表头配置
   */
  private CompositeHeader compositeHeader;

  /**
   * 统计图下钻类型,-1查看明细（默认），1自由下钻 ，2固定下钻
   */
  private int drillType;

  /**
   * 函数配置
   */
  private FuncConfig funcConfig;
}
