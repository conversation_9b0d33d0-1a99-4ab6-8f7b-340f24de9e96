package com.facishare.bi.common.entities;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.bi.common.entities.stat.Cell;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

@Data
public class StatColumnDataDto implements Serializable {
  private static final long serialVersionUID = -1L;
  @JSONField(name = "fieldID")
  private String fieldID;
  @JSONField(name = "fieldName")
  private String fieldName;
  @JSONField(name = "fieldType")
  private String fieldType;
  @JSONField(name = "aggrType")
  private String aggrType;
  @JSONField(name = "value")
  private List<Cell> value;
  @JSONField(name = "ratioType")
  private String ratioType;
  @JSONField(name = "dataType")
  private String dataType;
  @JSONField(name = "total")
  private String total;
  @J<PERSON><PERSON>ield(name = "totalValue")
  private String totalValue;
  @JSONField(name = "average")
  private String average;
  @JSONField(name = "averageValue")
  private String averageValue;
  @JSONField(name = "isShowCrmDetail")
  private int isShowCrmDetail;
  private String formatStr;
  private int isDim;
  private String legendName;
  private int isAttributeDim;
  private boolean isUserFormattedShowValue;
  private String totalTextColor;
  private String totalBorderColor;

  @JSONField(name="totalFormattedShowValue")
  private String totalFormattedShowValue;

  @JSONField(name="isLink")
  private int isLink = 0;
  @JSONField(name="cellWidth")
  private int cellWidth = 0;
  @JSONField(name="maxValue")
  private String maxValue;
  @JSONField(name="maxFmtValue")
  private String maxFmtValue;
  @JSONField(name="minValue")
  private String minValue;
  @JSONField(name="minFmtValue")
  private String minFmtValue;
  @JSONField(name="avgValue")
  private String avgValue;
  @JSONField(name="avgFmtValue")
  private String avgFmtValue;
  /**
   * 是否掩码显示
   */
  private boolean isShowMask;
  /**
   * 去掩码显示角色
   * 数据格式为 {"data_roles":["owner","data_owner_main_dept_leader"],"role":["00000000000000000000000000000006"]}
   */
  private String removeMaskRoles;

  /**
   * 是否为主属性
   */
  private boolean isMainDim;

  /**
   * 占比类型 默认为0 1-总计占比
   */
  private int proportionType;
  /**
   * 分析类型
   * 累计：301
   */
  @JSONField(name = "analysisType")
  private String analysisType;

  private String parentId;

  /**
   * 维度配置
   */
  private DimensionConfig dimensionConfig;
  /**
   * 交叉表列维度维度值排序结果
   */
  private List<String> valueSortedList;
  /**
   * 上期值
   */
  private String preValue;
  /**
   * 本期值
   */
  private String curValue;
  /**
   * 上期范围
   */
  private String preDate;
  /**
   * 本期范围
   */
  private String curDate;
  /**
   * 同环比差值
   */
  private String ratioCompareValue;

  /**
   * 空数据格子的颜色配置
   */
  @JsonProperty("virtualZero")
  private Cell virtualZero;
  /**
   * 表头字段id
   */
  private String viewFieldId;
  /**
   * 字段唯一key生成
   * 规则详见：https://wiki.firstshare.cn/pages/viewpage.action?pageId=291045636
   * fieldKey  ===>   fieldId#fieldParentId#aggrType#fieldTypeKey
   */
  public String getStatColumnKey() {
    String fieldTypeKey = getColumnFieldTypeKey();
    if (StringUtils.equals("61", aggrType)) {
      aggrType = "6";
    }
    return String.join("#", fieldID, Objects.toString(parentId, ""), Objects.toString(aggrType, ""), fieldTypeKey);
  }

  private String getColumnFieldTypeKey() {
    String fieldTypeKey = "";
    //同环比字段
    if (StringUtils.isNotEmpty(ratioType) && !StringUtils.equals("0", ratioType)) {
      fieldTypeKey = Integer.parseInt(ratioType) < 10 ?
        AnalysisTypeEnum.RATIO_PREFIX.getTypeCode() + "0" + ratioType :
        AnalysisTypeEnum.RATIO_PREFIX.getTypeCode() + ratioType;
    }
    //占比字段
    if (1 == proportionType) {
      fieldTypeKey = AnalysisTypeEnum.PROPORTION.getTypeCode();
    }
    //累计字段
    if (StringUtils.equals(AnalysisTypeEnum.ACCUMULATION.getTypeCode(), analysisType)) {
      fieldTypeKey = analysisType;
    }
    //指定层级维度字段
    if (checkIsAppointDim()) {
      fieldTypeKey = dimensionConfig.getDimensionConfigFieldTypeKey();
    }

    return fieldTypeKey;
  }

  /**
   * 是否为指定层级维度
   */
  private boolean checkIsAppointDim() {
    if (Objects.isNull(dimensionConfig) || Objects.isNull(dimensionConfig.getDimLevel())) {
      return false;
    }
    return dimensionConfig.getDimLevel() > 0;
  }
  public boolean isAttrDim() {
    return 1 == isAttributeDim;
  }

  public boolean checkIsColDim() {
    if (Objects.isNull(dimensionConfig) || Objects.isNull(dimensionConfig.getGroupType())) {
      return false;
    }
    return Objects.equals(dimensionConfig.getGroupType(), DimGroupTypeEnum.COLUMN_GROUP.getGroupType());
  }
}
