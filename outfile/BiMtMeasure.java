package com.facishare.bi.metadata.context.dto.dw;

import com.facishare.bi.metadata.context.dto.UdfObjFieldBoExt;
import com.facishare.bi.metadata.context.dto.ods.BiMtField;
import lombok.Data;

/**
 * 指标；不嵌套ODS实体
 */
@Data
public class BiMtMeasure {

  /**
   * 指标ID
   */
  public String measureId;

  /**
   * tenantId
   */
  public String tenantId;

  /**
   * 拓扑图ID
   */
  public String topologyDescribeId;

  /**
   * 指标名称
   */
  public String name;

  /**
   * 指标对象所在拓扑节点
   */
  public String nodeId;


  /**
   * 指标表达式：SUM($field$)/COUNT($field$)
   */
  public String aggExpression;

  /**
   * SUM/AGG
   */
  public String aggType;

  /**
   * 摘要
   */
  public String description;

  /**
   * 槽位-value10
   */
  public String fieldLocation;

  /**
   * 筛选器
   */
  public String filters;

  /**
   * 0正常  1禁用
   */
  public int status;

  /**
   * 禁用原因
   */
  public String disableReason;

  /**
   * 指标聚合日期
   * node_id.order_time
   */
  public String actionDateField;

  /**
   * 原始字段-所属对象名
   */
  public String crmObjName;

  /**
   * 原始字段名
   */
  public String crmFieldName;

  /**
   * 原始字段ID
   */
  public String fieldId;

  /**
   * 目标规则考核指标格式化配置
   */
  private String formatConfig;

  /**
   * 指向的描述信息
   */
  @Deprecated
  private UdfObjFieldBoExt udfObjFieldBoExt;

  @Deprecated
  public BiMtField biMtField;


}
