<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <!-- 使用纷享的父POM -->
  <parent>
    <groupId>com.fxiaoke.cloud</groupId>
    <artifactId>fxiaoke-spring-cloud-parent</artifactId>
    <version>2.7.0-SNAPSHOT</version>
    <relativePath/>
  </parent>

  <groupId>com.fxiaoke</groupId>
  <artifactId>fs-bi-agent</artifactId>
  <version>9.5.0-SNAPSHOT</version>
  <packaging>pom</packaging>
  <name>fs-bi-agent</name>
  <description>CRM领域数据分析助手</description>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <maven-checkstyle-plugin.version>3.2.1</maven-checkstyle-plugin.version>
    <maven-surefire-plugin.version>2.22.2</maven-surefire-plugin.version>
    <maven-failsafe-plugin.version>2.22.2</maven-failsafe-plugin.version>
    <java.version>17</java.version>
  </properties>

  <!-- 子模块列表 -->
  <modules>
    <module>chatbi-common</module>
    <module>chatbi-prompts</module>
    <module>chatbi-planning</module>
    <module>chatbi-knowledge</module>
    <module>chatbi-memory</module>
    <module>chatbi-action</module>
    <module>chatbi-monitoring</module>
    <module>chatbi-integration</module>
    <module>chatbi-bootstrap</module>
    <module>chatbi-tests</module>
    <module>chatbi-accuracy</module>
  </modules>

  <!-- 依赖管理，用于子模块引用 -->
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.mapstruct</groupId>
        <artifactId>mapstruct</artifactId>
        <version>1.5.2.Final</version> <!-- 使用最新稳定版本 -->
      </dependency>
      <dependency>
        <groupId>org.mapstruct</groupId>
        <artifactId>mapstruct-processor</artifactId>
        <version>1.5.2.Final</version>
      </dependency>
      <!-- 模块间依赖 -->
      <dependency>
        <groupId>com.fxiaoke</groupId>
        <artifactId>chatbi-common</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fxiaoke</groupId>
        <artifactId>chatbi-prompts</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fxiaoke</groupId>
        <artifactId>chatbi-planning</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fxiaoke</groupId>
        <artifactId>chatbi-knowledge</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fxiaoke</groupId>
        <artifactId>chatbi-memory</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fxiaoke</groupId>
        <artifactId>chatbi-action</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fxiaoke</groupId>
        <artifactId>chatbi-monitoring</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fxiaoke</groupId>
        <artifactId>chatbi-integration</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fxiaoke</groupId>
        <artifactId>chatbi-bootstrap</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fxiaoke</groupId>
        <artifactId>chatbi-bootstrap</artifactId>
        <version>${project.version}</version>
        <type>war</type>
      </dependency>
      <dependency>
        <groupId>com.fxiaoke</groupId>
        <artifactId>chatbi-tests</artifactId>
        <version>${project.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.fxiaoke</groupId>
        <artifactId>chatbi-accuracy</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- 第三方依赖 -->
      <dependency>
        <groupId>com.facishare</groupId>
        <artifactId>fs-bi-common-entities</artifactId>
        <version>${project.version}</version>
        <exclusions>
          <exclusion>
            <groupId>javax.persistence</groupId>
            <artifactId>persistence-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>com.facishare</groupId>
        <artifactId>fs-bi-metadata-context</artifactId>
        <version>9.6.0-SNAPSHOT</version>
        <exclusions>
          <exclusion>
            <groupId>javax.persistence</groupId>
            <artifactId>persistence-api</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.apache.tomcat</groupId>
            <artifactId>annotations-api</artifactId>
          </exclusion>
          <exclusion>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>com.facishare</groupId>
        <artifactId>fs-rest-client-core</artifactId>
        <version>6.6.5-SNAPSHOT</version>
      </dependency>

      <dependency>
        <groupId>com.fxiaoke</groupId>
        <artifactId>fs-paas-ai-api</artifactId>
        <version>1.0.1-SNAPSHOT</version>
      </dependency>

      <!-- ClickHouse相关依赖 -->
      <dependency>
        <groupId>com.clickhouse</groupId>
        <artifactId>clickhouse-jdbc</artifactId>
        <version>0.4.4</version>
      </dependency>
      <dependency>
        <groupId>com.clickhouse</groupId>
        <artifactId>clickhouse-data</artifactId>
        <version>0.4.4</version>
      </dependency>
      <dependency>
        <groupId>com.clickhouse</groupId>
        <artifactId>clickhouse-client</artifactId>
        <version>0.4.4</version>
      </dependency>

      <dependency>
        <groupId>com.vladsch.flexmark</groupId>
        <artifactId>flexmark-all</artifactId>
        <version>0.62.2</version>
      </dependency>

      <dependency>
        <groupId>com.h2database</groupId>
        <artifactId>h2</artifactId>
        <version>2.1.214</version>
        <scope>runtime</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <!-- 所有子模块共享的依赖 -->
  <dependencies>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context</artifactId>
      <version>5.3.31</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context-support</artifactId>
      <version>5.3.31</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.github.ben-manes.caffeine</groupId>
      <artifactId>caffeine</artifactId>
      <version>3.1.8</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>

  <build>
    <pluginManagement>
      <plugins>
        <!-- 代码质量检查插件 -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-checkstyle-plugin</artifactId>
          <version>${maven-checkstyle-plugin.version}</version>
        </plugin>

        <!-- 单元测试插件 -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${maven-surefire-plugin.version}</version>
          <configuration>
            <includes>
              <include>**/*Test.java</include>
              <include>**/Test*.java</include>
              <include>**/*Tests.java</include>
              <include>**/*TestCase.java</include>
            </includes>
            <excludes>
              <exclude>**/*IT.java</exclude>
              <exclude>**/IT*.java</exclude>
              <exclude>**/*ITCase.java</exclude>
            </excludes>
            <compilerArgs>
              <arg>--add-modules</arg>
              <arg>ALL-MODULE-PATH</arg>
            </compilerArgs>
          </configuration>
        </plugin>

        <!-- 集成测试插件 -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-failsafe-plugin</artifactId>
          <version>${maven-failsafe-plugin.version}</version>
          <configuration>
            <includes>
              <include>**/*IT.java</include>
              <include>**/IT*.java</include>
              <include>**/*ITCase.java</include>
            </includes>
          </configuration>
        </plugin>

        <!-- 编译插件 -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <configuration>
            <source>${java.version}</source>
            <target>${java.version}</target>
            <encoding>${project.build.sourceEncoding}</encoding>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>
</project>