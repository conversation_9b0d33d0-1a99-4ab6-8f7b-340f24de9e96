{"version": "1.0", "ruleGroups": [{"name": "产品理解", "description": "ChatBI产品理解相关规则", "rules": [{"name": "产品概述", "path": "prd_summary.mdc", "priority": 1}, {"name": "用户场景", "path": "use_case_highlight.mdc", "priority": 2}, {"name": "核心功能", "path": "core_features.mdc", "priority": 3}]}, {"name": "架构设计", "description": "系统架构和设计相关规则", "rules": [{"name": "架构概述", "path": "architecture_overview.mdc", "priority": 1}, {"name": "数据模型", "path": "data_model_guidelines.mdc", "priority": 2}, {"name": "工作流程", "path": "workflow_guidelines.mdc", "priority": 3}]}, {"name": "开发规范", "description": "开发和编码相关规则", "rules": [{"name": "编码规范", "path": "style_guide_pointer.mdc", "priority": 1}, {"name": "开发流程", "path": "development_guidelines.mdc", "priority": 2}, {"name": "集成规范", "path": "integration_guidelines.mdc", "priority": 3}, {"name": "提示词工程", "path": "prompt_engineering.mdc", "priority": 4}]}, {"name": "质量与性能", "description": "质量保证和性能优化相关规则", "rules": [{"name": "性能指南", "path": "performance_guidelines.mdc", "priority": 1}, {"name": "最佳实践", "path": "best_practices.mdc", "priority": 2}]}], "globalRules": [{"name": "产品概述", "path": "prd_summary.mdc", "applyToAllFiles": true}, {"name": "编码规范", "path": "style_guide_pointer.mdc", "applyToAllFiles": true}, {"name": "用户场景", "path": "use_case_highlight.mdc", "applyToAllFiles": true}], "fileTypeRules": [{"filePattern": "*.java", "rules": ["style_guide_pointer.mdc", "best_practices.mdc"]}, {"filePattern": "*Controller.java", "rules": ["workflow_guidelines.mdc"]}, {"filePattern": "*Service.java", "rules": ["core_features.mdc", "performance_guidelines.mdc"]}, {"filePattern": "*Repository.java", "rules": ["data_model_guidelines.mdc"]}, {"filePattern": "*LlmService.java", "rules": ["prompt_engineering.mdc", "integration_guidelines.mdc"]}, {"filePattern": "pom.xml", "rules": ["development_guidelines.mdc"]}]}