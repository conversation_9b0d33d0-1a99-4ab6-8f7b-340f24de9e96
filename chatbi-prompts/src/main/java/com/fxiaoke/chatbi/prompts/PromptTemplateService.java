package com.fxiaoke.chatbi.prompts;

import com.fxiaoke.chatbi.common.model.prompts.PromptTemplateType;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.StreamUtils;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
@EnableCaching
@Configuration
public class PromptTemplateService {

  private final Map<String, String> templateCache = new ConcurrentHashMap<>();
  private final Map<String, String> demoContentMap = new ConcurrentHashMap<>();
  private final Map<String, List<TemplateVariable>> templateVariablesMap = new ConcurrentHashMap<>();
  
  // 文件存储根目录
  private final String storageDir;

  public PromptTemplateService() {
    // 确定存储目录
    String userDir = System.getProperty("user.dir");
    this.storageDir = userDir + "/chatbi-prompts/src/main/resources/prompts";
    
    // 确保目录存在
    ensureDirectoryExists(this.storageDir);
    
    // 初始化示例内容
    initDemoContent();
    
    // 初始化模板变量定义
    initTemplateVariables();
  }
  
  /**
   * 提示词模板的缓存管理器
   */
  @Bean("promptCacheManager")
  public CacheManager promptCacheManager() {
    CaffeineCacheManager cacheManager = new CaffeineCacheManager();
    cacheManager.setCaffeine(Caffeine.newBuilder()
                                    .maximumSize(100)
                                    .expireAfterWrite(1, TimeUnit.HOURS)
                                    .initialCapacity(10));
    return cacheManager;
  }
  
  /**
   * 确保目录存在
   */
  private void ensureDirectoryExists(String dirPath) {
    File dir = new File(dirPath);
    if (!dir.exists()) {
      boolean created = dir.mkdirs();
      if (created) {
        log.info("创建目录成功: {}", dirPath);
      } else {
        log.warn("创建目录失败: {}", dirPath);
      }
    }
  }

  /**
   * 获取模板文件路径
   */
  private String getTemplateFilePath(PromptTemplateType type) {
    return storageDir + "/" + type.getTemplateName() + ".ftl";
  }

  /**
   * 初始化示例内容
   */
  private void initDemoContent() {
    // 图表问题生成的示例内容
    demoContentMap.put(PromptTemplateType.CHART_QUESTION_GEN.getTemplateName(),
        "关键词：{\n" +
        "    \"d\": [\n" +
        "        \"员工姓名\"\n" +
        "    ],\n" +
        "    \"f\": [\n" +
        "        \"0\",\n" +
        "        \"日期\"\n" +
        "    ],\n" +
        "    \"m\": [\n" +
        "        \"BI_5bcee8b2b8e0e70001016199_base\"\n" +
        "    ]\n" +
        "}\n" +
        "\n" +
        "图表定义：" +
        "{\n" +
        "    \"chartType\": \"bar\",\n" +
        "    \"description\": \"已修改\",\n" +
        "    \"dimensions\": [\n" +
        "        {\n" +
        "            \"fieldId\": \"BI_5bcec12156fc11160c10043d\",\n" +
        "            \"orderType\": \"不排序\"\n" +
        "        }\n" +
        "    ],\n" +
        "    \"filters\": [\n" +
        "        {\n" +
        "            \"dateRangeId\": \"0\",\n" +
        "            \"fieldId\": \"0\",\n" +
        "            \"operator\": \"0\"\n" +
        "        },\n" +
        "        {\n" +
        "            \"dateRangeId\": \"4\",\n" +
        "            \"fieldId\": \"BI_5bcec12156fc11160c100443\",\n" +
        "            \"operator\": \"23\"\n" +
        "        }\n" +
        "    ],\n" +
        "    \"measures\": [\n" +
        "        {\n" +
        "            \"aggregator\": \"2\",\n" +
        "            \"fieldId\": \"BI_5bcee8b2b8e0e70001016199_base\",\n" +
        "            \"orderType\": \"降序\"\n" +
        "        }\n" +
        "    ],\n" +
        "    \"name\": \"本月销售精英榜_base\",\n" +
        "    \"schemaId\": \"BI_5bcec11f56fc11160c8c8271\",\n" +
        "    \"viewId\": \"BI_5cd8d8e469a7e500012be9b4_base\"\n" +
        "}"
    );
  }

  /**
   * 初始化模板变量定义
   */
  private void initTemplateVariables() {
    // 意图识别模板变量
    List<TemplateVariable> intentVars = new ArrayList<>();
    intentVars.add(new TemplateVariable("instructions", "用户当前指令", "STRING", "必填", "用户当前输入的指令文本", "分析上个月的销售额"));
    intentVars.add(new TemplateVariable("history", "历史对话记录", "ARRAY", "可选", "包含历史对话的数组，每项有role和content字段", 
        "[{\"role\":\"user\",\"content\":\"销售额怎么样？\"},{\"role\":\"assistant\",\"content\":\"非常好\"}]"));
    intentVars.add(new TemplateVariable("previousIntent", "上一次查询", "STRING", "可选", "用户上一次的查询内容", "上个季度的销售额"));
    intentVars.add(new TemplateVariable("workingMemory", "工作记忆", "OBJECT", "可选", "当前会话的上下文信息", 
        "{\"lastChart\":\"销售趋势图\",\"lastMeasures\":[\"销售额\"]}"));
    templateVariablesMap.put(PromptTemplateType.INTENT_RECOGNITION.getTemplateName(), intentVars);
    
    // 图表洞察模板变量
    List<TemplateVariable> insightVars = new ArrayList<>();
    insightVars.add(new TemplateVariable("chartData", "图表数据", "JSON", "必填", "需要分析的图表数据，包含图表类型、维度、指标等信息", 
        "{\"chartType\":\"line\",\"data\":[{\"x\":\"1月\",\"y\":100},{\"x\":\"2月\",\"y\":150}],\"title\":\"销售趋势\"}"));
    templateVariablesMap.put(PromptTemplateType.CHART_INSIGHT.getTemplateName(), insightVars);
    
    // 查询计划模板变量
    List<TemplateVariable> queryVars = new ArrayList<>();
    queryVars.add(new TemplateVariable("userQuery", "用户查询", "STRING", "必填", "用户的原始查询文本", "分析上月各区域的销售额"));
    queryVars.add(new TemplateVariable("extractedInfo", "提取的信息", "JSON", "可选", "从用户查询中提取的结构化信息", 
        "{\"analysisType\":\"TREND\",\"measures\":[\"销售额\"],\"dimensions\":[\"区域\",\"日期\"],\"timeRange\":\"上个月\"}"));
    queryVars.add(new TemplateVariable("chartKnowledge", "图表知识", "STRING", "可选", "相关图表的元数据信息", 
        "图表1: 销售趋势图(折线图,按月展示销售趋势)\n图表2: 区域分布图(饼图,展示各区域销售占比)"));
    queryVars.add(new TemplateVariable("schemaKnowledge", "主题知识", "STRING", "可选", "主题模型的元数据信息", 
        "主题: 销售分析\n维度: 日期、区域、产品\n指标: 销售额、客单价、订单量"));
    queryVars.add(new TemplateVariable("dictionaryKnowledge", "数据字典", "STRING", "可选", "数据字段的定义信息", 
        "销售额: 订单金额之和\n区域: 华北、华东、华南、西南"));
    templateVariablesMap.put(PromptTemplateType.QUERY_DSL.getTemplateName(), queryVars);
    
    // 推荐模板变量
    List<TemplateVariable> recVars = new ArrayList<>();
    recVars.add(new TemplateVariable("metadataDesc", "元数据知识", "STRING", "可选", "系统元数据描述", 
        "指标：销售额(m_sales)、利润(m_profit)、订单量(m_orders)\n维度：日期(d_date)、区域(d_region)、产品(d_product)"));
    recVars.add(new TemplateVariable("chartsDesc", "图表库知识", "STRING", "可选", "可用图表库描述", 
        "chart_12345: 月度销售趋势图(line)\nchart_67890: 区域销售分布(pie)"));
    recVars.add(new TemplateVariable("queryResult", "当前图表数据", "STRING", "可选", "当前查询的图表数据", 
        "{\"chartType\":\"bar\",\"data\":[{\"区域\":\"华北\",\"销售额\":2500},{\"区域\":\"华东\",\"销售额\":3200}]}"));
    recVars.add(new TemplateVariable("history", "对话历史", "ARRAY", "可选", "历史对话记录", 
        "[{\"role\":\"user\",\"content\":\"分析华北区域的销售\"},{\"role\":\"assistant\",\"content\":\"华北区域销售额为2500万\"}]"));
    recVars.add(new TemplateVariable("instructions", "用户指令", "STRING", "可选", "特定的用户指令", "给我推荐相关的分析"));
    templateVariablesMap.put(PromptTemplateType.FOLLOW_UP_QUESTION.getTemplateName(), recVars);
    
    // 图表问题生成模板变量
    List<TemplateVariable> chartQuesVars = new ArrayList<>();
    chartQuesVars.add(new TemplateVariable("spec", "图表定义", "JSON", "必填", "图表的详细定义，包含图表类型、维度、指标等", 
        "{\"chartType\":\"bar\",\"dimensions\":[\"区域\"],\"measures\":[\"销售额\"],\"name\":\"区域销售分析\"}"));
    chartQuesVars.add(new TemplateVariable("limit", "问题数量限制", "NUMBER", "可选", "需要生成的问题数量，默认为50", "50"));
    templateVariablesMap.put(PromptTemplateType.CHART_QUESTION_GEN.getTemplateName(), chartQuesVars);
  }

  /**
   * 获取提示词模板
   */
  @Cacheable(value = "promptTemplates", key = "#type.templateName", cacheManager = "promptCacheManager")
  public String getTemplate(PromptTemplateType type) {
    // 首先检查缓存
    String cachedTemplate = templateCache.get(type.getTemplateName());
    if (cachedTemplate != null) {
      return cachedTemplate;
    }

    // 检查文件系统中是否存在自定义模板文件
    String filePath = getTemplateFilePath(type);
    File file = new File(filePath);
    
    try {
      // 如果文件存在，从文件系统读取
      if (file.exists()) {
        String template = Files.readString(file.toPath(), StandardCharsets.UTF_8);
        // 放入缓存
        templateCache.put(type.getTemplateName(), template);
        return template;
      }
      
      // 如果文件不存在，从原始资源加载
      String template = loadTemplate(type.getResource());
      // 加载后放入缓存
      templateCache.put(type.getTemplateName(), template);
      // 保存到文件系统
      saveTemplateToFile(type, template);
      
      return template;
    } catch (IOException e) {
      log.error("加载提示词模板失败: {}", type, e);
      throw new RuntimeException("加载提示词模板失败: " + type, e);
    }
  }

  /**
   * 保存模板到文件
   */
  private void saveTemplateToFile(PromptTemplateType type, String content) {
    try {
      String filePath = getTemplateFilePath(type);
      File file = new File(filePath);
      
      // 确保父目录存在
      File parentDir = file.getParentFile();
      if (!parentDir.exists()) {
        boolean created = parentDir.mkdirs();
        if (!created) {
          log.warn("创建父目录失败: {}", parentDir.getAbsolutePath());
        }
      }
      
      // 写入内容
      Files.writeString(file.toPath(), content, StandardCharsets.UTF_8);
      log.info("已保存模板到文件: {}", type.getTemplateName());
    } catch (IOException e) {
      log.error("保存模板到文件失败: {}", type.getTemplateName(), e);
    }
  }

  /**
   * 刷新指定类型的提示词模板
   */
  @CacheEvict(value = "promptTemplates", key = "#type.templateName", cacheManager = "promptCacheManager")
  public void refreshTemplate(PromptTemplateType type, String content) {
    try {
      if (content == null || content.trim().isEmpty()) {
        throw new IllegalArgumentException("模板内容不能为空");
      }

      // 更新内存缓存
      templateCache.put(type.getTemplateName(), content);
      
      // 保存模板内容到文件
      saveTemplateToFile(type, content);

      log.info("已更新提示词模板: {}", type);
    } catch (Exception e) {
      log.error("更新提示词模板失败: {}", type, e);
      throw new RuntimeException("更新提示词模板失败: " + e.getMessage(), e);
    }
  }

  private String loadTemplate(Resource resource) throws IOException {
    return StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
  }

  /**
   * 获取示例内容
   */
  public String getDemoContent(PromptTemplateType type) {
    return demoContentMap.getOrDefault(type.getTemplateName(), "");
  }
  
  /**
   * 获取模板变量列表
   */
  public List<TemplateVariable> getTemplateVariables(PromptTemplateType type) {
    return templateVariablesMap.getOrDefault(type.getTemplateName(), Collections.emptyList());
  }


  /**
   * 使用Lambda表达式将"key:value1,value2,..."格式的字符串转换为Map
   * 值部分按逗号分割为列表，不限制值的个数
   *
   * @param input 输入的字符串，每行一个键值对
   * @return 转换后的Map，值为字符串列表
   */
  public Map<String, List<String>> convertToMap(String input) {
    return Arrays.stream(input.split("\n"))
            .filter(line -> !line.trim().isEmpty())       // 过滤空行
            .map(line -> line.split(":", 2))             // 按冒号分割，最多分割2部分
            .filter(parts -> parts.length == 2)           // 确保有键和值
            .collect(Collectors.toMap(
                    parts -> parts[0].trim(),                 // 键（去除前后空格）
                    parts -> {
                      String values = parts[1].trim();      // 值部分（去除前后空格）
                      if (values.isEmpty()) {
                        return List.of();                 // 空值返回空列表
                      } else {
                        return Arrays.stream(values.split(","))
                                .map(String::trim)        // 对每个值去除前后空格
                                .collect(Collectors.toList());
                      }
                    },
                    (existing, replacement) -> existing       // 处理重复键（保留第一个）
            ));
  }
  /**
   * 将复杂格式的字符串转换为嵌套Map结构
   * 格式: key:groupId1#value1,value2;groupId2#value3,value4
   *
   * @param input 输入的字符串，每行一个复杂键值对
   * @return 转换后的嵌套Map结构
   */
  public Map<String, Map<String, List<String>>> convertToComplexMap(String input) {
    return Arrays.stream(input.split("\n"))
            .filter(line -> !line.trim().isEmpty())       // 过滤空行
            .map(line -> line.split(":", 2))             // 按冒号分割，最多分割2部分
            .filter(parts -> parts.length == 2)           // 确保有键和值
            .collect(Collectors.toMap(
                    parts -> parts[0].trim(),                 // 主键
                    parts -> parseGroups(parts[1].trim()),    // 解析分组值
                    (existing, replacement) -> existing       // 处理重复键（保留第一个）
            ));
  }

  /**
   * 解析分组值字符串为Map<组ID, 值列表>
   * 格式: groupId1#value1,value2;groupId2#value3,value4
   *
   * @param groupsString 分组值字符串
   * @return 解析后的分组Map
   */
  private static Map<String, List<String>> parseGroups(String groupsString) {
    if (groupsString.isEmpty()) {
      return Collections.emptyMap();
    }

    Map<String, List<String>> groupsMap = new LinkedHashMap<>(); // 保持插入顺序

    // 按分号分割不同组
    String[] groups = groupsString.split(";");

    for (String group : groups) {
      // 按#分割组ID和值列表
      String[] groupParts = group.split("#", 2);
      if (groupParts.length == 2) {
        String groupId = groupParts[0].trim();
        String valuesString = groupParts[1].trim();

        // 解析值列表
        List<String> values;
        if (valuesString.isEmpty()) {
          values = Collections.emptyList();
        } else {
          values = Arrays.stream(valuesString.split(","))
                  .map(String::trim)
                  .collect(Collectors.toList());
        }

        groupsMap.put(groupId, values);
      }
    }

    return groupsMap;
  }
}