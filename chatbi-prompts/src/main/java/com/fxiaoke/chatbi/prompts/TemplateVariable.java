package com.fxiaoke.chatbi.prompts;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 模板变量定义类，用于描述FreeMarker模板中使用的变量
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TemplateVariable {
    /**
     * 变量名称
     */
    private String name;
    
    /**
     * 变量显示名称
     */
    private String displayName;
    
    /**
     * 变量类型 (STRING, NUMBER, BOOLEAN, ARRAY, OBJECT, JSON等)
     */
    private String type;
    
    /**
     * 必填/可选
     */
    private String required;
    
    /**
     * 变量描述
     */
    private String description;
    
    /**
     * 示例值
     */
    private String example;
    
    /**
     * 构造函数(没有示例值的版本，兼容现有代码)
     */
    public TemplateVariable(String name, String displayName, String type, String required, String description) {
        this.name = name;
        this.displayName = displayName;
        this.type = type;
        this.required = required;
        this.description = description;
        this.example = "";
    }
} 