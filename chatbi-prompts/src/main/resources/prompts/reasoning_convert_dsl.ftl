【查询DSL生成阶段】
1. 基础配置：
- 分析类型：${analysisType!'' }
- 图表类型：${chartType!'' }

2. 维度设置：
- 主维度：${mainDim!'' }
- 排序方式：${orderType!'' }
- 时间粒度：${timeGranularity!'' }

3. 过滤条件：
- 数据范围:
<#if filters?? && filters?size gt 0>
    <#list filters as filter>
        * ${filter!''}<#if filter_has_next>
    </#if>
    </#list>
<#else>
    无过滤条件
</#if>

4. 指标计算：
- 聚合方式：${aggType!'' }
- 目标字段：${mainMeasure!'' }

5. 结果限制：
- 最大返回条目：${maxPageSize!'' }

6. 输出配置：
- 图表名称：${chartName!'' }

7. 综上：
${reasoning!'' }
