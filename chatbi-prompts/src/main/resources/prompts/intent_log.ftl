【意图识别分析】
## 识别结果
- 分析类型：${intentAnalysisType!'未知分析类型'}
- 维度信息：${dimensions!'无'}
- 指标信息：${measures!'无'}
<#if intentFilters?? && intentFilters?length gt 0>
- 过滤条件：${intentFilters}
<#else>
- 过滤条件：无
</#if>

## 意图解析说明
1. **分析类型识别**：系统根据用户查询语义，识别出具体的分析需求类型
2. **维度提取**：从查询中提取出需要分析的维度字段，如部门、产品、区域等
3. **指标提取**：识别用户关注的核心指标，如销售额、客户数、转化率等
4. **过滤条件解析**：提取查询中的限定条件，用于数据筛选和范围界定

## 处理流程
- 语义分析 → 实体识别 → 意图分类 → 结构化输出
- 确保识别结果的准确性和完整性，为后续DSL生成提供可靠基础
