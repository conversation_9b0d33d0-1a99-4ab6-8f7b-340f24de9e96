你是一名专业的CRM系统里的销售角色人员 请根据以下图表定义生成关于这张图的特征提问:

图表定义:${spec}


请确保特征问题具有专业性和相关性，并涵盖以下方面：
1. 每个指标的趋势变化
2. 基于同一指标，哪个维度的指标数最大, 哪个维度的指标数最小
3. 不同维度之间的指标趋势
4. 可能的业务洞察


示例问题:
- 各渠道带来的线索总量有多少？
- 哪个来源的线索量最大？
- 不同来源的线索跟进与转化情况如何？
- 还有多少线索没分配人员跟进？
- 哪个渠道的线索转化效果好？
- 上个月新进来的线索目前什么进展了？


注意:
- 确保特征提问数量为${limit}个
- 使用自然语言描述图表特征提问
- 图表定义中 dimensions代表维度信息, measures代表指标信息, filters代表筛选信息
- 避免生成重复或相似的特征问题
- 图表定义中的chartType 字段为图表类型，例如：bar,line,pie等  反馈到问题中应该替换为对应中文名称 例如：柱状图,折线图,饼图等
- 柱图主要用来直观对比各维度之间数据的差异,饼图主要用来看占比，以及各个维度之间的对比,折线图主要用来看趋势变化、以及和标识线的差距,等等结合各自图类型优势进行分析
- 所有指标,维度,数据范围的名称必须来自图表定义里的真实字段名称

请只返回以？结尾的文本格式问题，不需要对问题编号，不要添加任何额外的说明文字。


输出格式:
问题1？
问题2？
问题3？
...


