【推理润色分析】
## 润色结果
<#if processTime??>
- 处理时间：${processTime?string("yyyy-MM-dd HH:mm:ss")}
<#else>
- 处理时间：未记录
</#if>
- 原始长度：${originalLength!0} 字符
- 润色后长度：${polishedLength!0} 字符
<#if compressionRatio??>
- 压缩比例：${(compressionRatio * 100)?string("0.0")}%
<#else>
- 压缩比例：无法计算
</#if>

## 润色内容
<#if polishedReasoning?? && polishedReasoning?length gt 0>
${polishedReasoning}
<#else>
润色内容为空
</#if>

## 润色效果分析
<#if originalLength gt 0 && polishedLength gt 0>
<#assign ratio = polishedLength / originalLength>
<#if ratio lt 0.8>
- 内容精简：显著压缩冗余信息，提升阅读效率
<#elseif ratio lt 1.2>
- 内容优化：保持信息完整性的同时改善表达质量
<#else>
- 内容扩展：增加必要说明，提升内容的完整性和可理解性
</#if>
<#else>
- 效果评估：无法进行定量分析
</#if>

## 润色策略
1. **语言优化**：改善表达方式，提升可读性和专业性
2. **结构调整**：优化内容组织，增强逻辑性和条理性
3. **冗余消除**：去除不必要的重复信息，提高信息密度
4. **准确性保证**：确保润色过程中不改变原始含义和关键信息

## 质量标准
- 保持原始语义准确性
- 提升表达清晰度
- 增强专业性和可读性
- 优化信息传达效率
