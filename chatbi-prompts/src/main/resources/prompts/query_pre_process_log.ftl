【查询预处理分析】
## 处理结果
- 处理状态：${description!'未知状态'}

## 查询内容
- 原始查询：${originalQuery!'无'}
- 处理后查询：${processedQuery!'无'}

## 术语替换详情
<#if termReplacements?? && termReplacements?length gt 0>
<#assign replacements = termReplacements?eval>
<#if replacements?? && replacements?size gt 0>
<#list replacements?keys as key>
- "${key}" → "${replacements[key]}"
</#list>
<#else>
- 无术语替换
</#if>
<#else>
- 无术语替换
</#if>

## 预处理说明
1. **术语标准化**：将用户使用的业务术语转换为系统标准术语
2. **查询优化**：对用户输入进行语法和语义层面的优化处理
3. **歧义消除**：解决可能存在的语义歧义，提高后续处理的准确性
4. **格式规范**：统一查询格式，便于后续模块的标准化处理

## 处理效果
<#if originalQuery?? && processedQuery?? && originalQuery != processedQuery>
- 查询内容已优化，提升了语义清晰度和处理效率
<#else>
- 查询内容无需调整，原始表达已足够清晰准确
</#if>

## 质量保证
- 保持原始语义不变
- 提升表达准确性
- 增强系统理解能力
