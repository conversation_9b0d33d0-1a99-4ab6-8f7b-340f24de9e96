【查询DSL生成分析】
## 基础信息
- 视图ID：${viewId!'未知'}
- 图表名称：${chartName!'未命名'}
- 主题ID：${schemaId!'未知'}

## 分析配置
- 分析类型：${analysisType!'未知'}
- 图表类型：${chartType!'未知'}
<#if maxPageSize??>
- 最大返回条目：${maxPageSize} 条
<#else>
- 最大返回条目：默认
</#if>

## 过滤条件
<#if filters?? && filters?size gt 0>
<#list filters as filter>
- 匹配筛选字段信息：
  - 名称：${filter.fieldName!'未知'}
  - ID：${filter.fieldId!'未知'}
  - 操作符：${filter.operator!'='}
  - 筛选值：${filter.value1!'未知'}
  - dateRange：${filter.dateRange!'无'}
</#list>
<#else>
- 无匹配过滤条件
</#if>


## DSL生成说明
1. **结构化转换**：将用户意图转换为标准化的查询DSL结构
2. **图表适配**：根据数据特征和用户需求选择最适合的图表类型
3. **过滤优化**：合理设置过滤条件，确保查询结果的相关性和准确性
4. **性能考虑**：通过限制返回条目数量，平衡查询性能和数据完整性

## 质量保证
- 语法正确性检查
- 逻辑一致性验证
- 性能影响评估
