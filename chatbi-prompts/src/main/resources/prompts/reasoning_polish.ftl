<#-- 
推理润色模板
参数说明:
- originalReasoning: 原始推理内容
- reasoningData: 推理过程收集的数据 (Map<ActionType, ReasoningData>)
-->

<#-- 简化输出，保留关键信息 -->
<#assign MAX_LENGTH = 800>
<#assign reasoning = originalReasoning?trim>

<#-- 如果原始内容太长，进行简化处理 -->
<#if reasoning?length gt MAX_LENGTH>
  <#-- 提取开头部分 -->
  <#assign introduction = reasoning?substring(0, 200)?trim>
  
  <#-- 提取关键部分 (基于关键词) -->
  <#assign keyPhrases = ["主要发现", "关键点", "分析结果", "数据显示", "总结", "建议"]>
  <#assign keyContent = "">
  
  <#list keyPhrases as phrase>
    <#if reasoning?contains(phrase)>
      <#assign startPos = reasoning?index_of(phrase)>
      <#if startPos gt 0>
        <#assign endPos = reasoning?index_of("。", startPos + phrase?length)>
        <#if endPos == -1>
          <#assign endPos = reasoning?length>
        </#if>
        <#assign keyContent = keyContent + reasoning?substring(startPos, endPos + 1) + "\n">
      </#if>
    </#if>
  </#list>
  
  <#-- 提取结尾部分 -->
  <#assign conclusion = "">
  <#if reasoning?contains("总结") || reasoning?contains("结论") || reasoning?contains("建议")>
    <#assign lastPos = reasoning?last_index_of("总结")>
    <#if lastPos == -1>
      <#assign lastPos = reasoning?last_index_of("结论")>
    </#if>
    <#if lastPos == -1>
      <#assign lastPos = reasoning?last_index_of("建议")>
    </#if>
    <#if lastPos gt 0>
      <#assign conclusion = reasoning?substring(lastPos)>
    </#if>
  <#else>
    <#assign conclusion = reasoning?substring(reasoning?length - 200)?trim>
  </#if>
  
  <#-- 组合结果 -->
  ${introduction}
  
  <#if keyContent?has_content>
  ${keyContent}
  </#if>
  
  ${conclusion}
<#else>
  ${reasoning}
</#if> 