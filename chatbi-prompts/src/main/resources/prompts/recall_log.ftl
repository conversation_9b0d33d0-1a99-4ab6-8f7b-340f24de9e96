【多路召回策略分析】
## 基本信息
- 总召回渠道数：${totalChannels!'' } 个
- 召回总图表数：${totalCharts!'' } 个
- 总耗时：${timeUsed!'' } 毫秒

## 召回渠道详情
<#if channels?? && channels?size gt 0>
<#list channels as channel>
### ${channel.channelName!'未知渠道'}
- 渠道类型：${channel.channelType!'未知'} <#if channel.channelType == 'TENANT'>（租户库）<#elseif channel.channelType == 'SYSTEM'>（系统库）</#if>
- 召回方法：${channel.recallMethod!'未知'} <#if channel.recallMethod == 'FIELD_ID'>（字段ID匹配）<#elseif channel.recallMethod == 'KEYWORD'>（关键词匹配）<#elseif channel.recallMethod == 'VECTOR'>（向量检索）</#if>
- 权重系数：${channel.weight!''}
- 召回结果数：${channel.resultCount!''} 个

<#if channel.topResults?? && channel.topResults?size gt 0>
#### 前${channel.topResults?size}个最佳匹配结果：
<#list channel.topResults as result>
- 图表ID: ${result.chartId!'未知ID'}
- 图表名称: ${result.chartName!'未知名称'}
<#if channel.recallMethod == 'FIELD_ID'>
- 字段匹配率: ${result.fieldIdMatchRate!'0.0'}
<#elseif channel.recallMethod == 'KEYWORD'>
- 关键词总得分: ${result.keyWordScore!'0.0'}
<#if result.categoryMatchRates??>
  - 维度匹配率: ${(result.categoryMatchRates.dimensionMatchRate!0.0)?string("0.00")}
  - 指标匹配率: ${(result.categoryMatchRates.measureMatchRate!0.0)?string("0.00")}
  - 筛选条件匹配率: ${(result.categoryMatchRates.filterMatchRate!0.0)?string("0.00")}
</#if>
<#if result.matchedKeywords??>
  <#if result.matchedKeywords.matchedDimensions?? && result.matchedKeywords.matchedDimensions?size gt 0>
  - 匹配的维度: ${result.matchedKeywords.matchedDimensions?join(", ")}
  </#if>
  <#if result.matchedKeywords.matchedMeasures?? && result.matchedKeywords.matchedMeasures?size gt 0>
  - 匹配的指标: ${result.matchedKeywords.matchedMeasures?join(", ")}
  </#if>
  <#if result.matchedKeywords.matchedfilters?? && result.matchedKeywords.matchedfilters?size gt 0>
  - 匹配的筛选条件: ${result.matchedKeywords.matchedfilters?join(", ")}
  </#if>
</#if>
<#elseif channel.recallMethod == 'VECTOR'>
- 向量相似度得分: ${result.vectorScore!'0.0'}
</#if>
<#if !result_has_next>---</#if>
</#list>
</#if>

</#list>
</#if>

## 召回策略说明
1. **多渠道并行召回**：系统同时从多个渠道获取相关图表，提高召回覆盖率
2. **权重排序机制**：
   - 渠道类型权重：租户库 > 系统库
   - 召回方法权重：字段ID匹配 > 关键词匹配 ≈ 向量检索
3. **匹配分数计算**：
   - 字段ID匹配：根据维度、指标、过滤条件的匹配率计算
   - 关键词匹配：基于关键词匹配度评分，并细分为：
     * 维度匹配率：用户查询中的维度关键词与图表维度的匹配程度
     * 指标匹配率：用户查询中的指标关键词与图表指标的匹配程度
     * 筛选条件匹配率：用户查询中的筛选条件与图表筛选条件的匹配程度
   - 向量检索：基于语义相似度评分
