【查询预处理阶段】
**1. 接收原始查询**：${originalQuery!'' }
**2. 进行术语规范化处理**：
- 保持原始查询不变（无术语替换）
- 验证查询有效性：通过有效性校验
3. 输出预处理结果：
- 处理后查询：${processedQuery!'' }
- 术语替换映射：${termReplacements!'' }

【知识检索阶段】
1. 图表知识检索：
- 发现预定义图表：销售漏斗(预测金额) [C1]
- 图表类型：漏斗图
- 维度：商机阶段(F2 升序)
- 指标：预测金额(F3 SUM)
- 过滤条件：F4(27), F5(23,6), F6(26), F7(26)

2. 主题知识检索：
- 匹配主题：商机2.0 [S8]
- 可用维度：
- F5: 日期
- F6: 负责人所在部门
- F2: 商机阶段
- F7: 销售流程
- F4: 生命状态
- 核心指标：
- F3: 预测金额(SUM)

3. 字典知识加载：
- 时间范围类型：CURRENTYEAR(本年度)
- 图表类型：bar(柱状图)
- 操作符：EQ(等于)

【意图识别阶段】
1. 意图类型分析：
- 识别为分析型查询(ANALYSIS)
- 置信度：${confidence!'' }

2. 核心要素提取：
- 分析类型：${intentAnalysisType!'' }
- 目标维度：${dimension!'' }
- 时间范围：${timeRange!'' }
- 指标需求：${measure!'' }

3. 过滤条件构建：
- 数据过滤条件：${intentFilter!'' }

4. 可视化建议：
- 推荐图表类型：${intendChartType!'' }

【查询DSL生成阶段】
1. 基础配置：
- 分析类型：${analysisType!'' }
- 图表类型：${chartType!'' }

2. 维度设置：
- 主维度：${mainDim!'' }
- 排序方式：${orderType!'' }
- 时间粒度：${timeGranularity!'' }

3. 过滤条件：
- 数据范围: 
<#if filters?? && filters?size gt 0>
  <#list filters as filter>
  * ${filter!''}<#if filter_has_next>
  </#if>
  </#list>
<#else>
  无过滤条件
</#if>

4. 指标计算：
- 聚合方式：${aggType!'' }
- 目标字段：${mainMeasure!'' }

5. 结果限制：
- 最大返回条目：${maxPageSize!'' }

6. 输出配置：
- 图表名称：${chartName!'' }

7. 综上：
${reasoning!'' }
