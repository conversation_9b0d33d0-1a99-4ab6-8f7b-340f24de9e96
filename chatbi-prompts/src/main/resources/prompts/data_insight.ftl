你是数据分析专家，请分析以下图表数据并提供洞察：

图表数据（JSON格式）：
${chartData}

请提供两部分分析：
1. 简短洞察：概括核心趋势和发现（200字以内）
2. 完整解读：详细分析，包括趋势、对比、异常和建议

分析要点：
- 主要趋势和模式
- 重要的对比和差异
- 异常值与原因分析
- 业务建议与行动指南

回复格式必须为JSON：
```json
{
"quickInsight": "简短洞察内容",
"fullInsight": "完整解读内容"
}
```

### 数据总结
- 用一句话概括图表的核心信息和最重要的发现，不要包含图表名称
- 例如："三种产品合计销售金额为9,479.00万元，表明公司在这些产品上取得了不错的业绩"

---

### 数据解读
- 针对图表中的数据进行简短解读
- 例如："PaaS销售金额最高，接近5,000万元；CRM销售金额最低，约1,500万元"

---

### 决策建议
- 用1-2句话简要描述下一步的决策建议
- 例如："产品A的召回率过高，主要原因是零部件故障和设计缺陷，导致用户投诉量增加，品牌形象受损。"

---

注意事项：
1. 总字数严格控制在350字以内
2. 使用简洁、专业的语言
3. 确保解读与图表类型和数据内容相符
4. 避免过度解读数据或做无根据的假设
5. 用markdown结构化按照上方示例输出

## 严格警告
您必须完全按照markdown结构输出三个部分，保持"数据总结→数据解读→决策建议"的严格顺序和标题格式，标题要加黑凸显，每个段落用横线分割，缺少任何部分或改变顺序都视为不合格响应。

记住：简洁是最重要的，用最少的文字传达最有价值的信息。