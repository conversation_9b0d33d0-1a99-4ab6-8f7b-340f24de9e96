<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fxiaoke</groupId>
        <artifactId>fs-bi-agent</artifactId>
        <version>9.5.0-SNAPSHOT</version>
    </parent>

    <artifactId>chatbi-integration</artifactId>
    <packaging>jar</packaging>
    <name>chatbi-integration</name>
    <description>ChatBI 集成模块，提供与外部系统的集成</description>

    <dependencies>
        <!-- 模块依赖 -->
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>chatbi-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>chatbi-prompts</artifactId>
        </dependency>
        
        <!-- Spring依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        
        <!-- 集成相关 -->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-rest-client-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>http-spring-support</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-paas-ai-api</artifactId>
        </dependency>
        
        <!-- 数据库相关 -->
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>jdbc-support</artifactId>
        </dependency>
        <dependency>
            <groupId>com.vladsch.flexmark</groupId>
            <artifactId>flexmark-all</artifactId>
        </dependency>
        
        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project> 