package com.fxiaoke.chatbi.integration.model.ch;


import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class DimensionValueFull {
    /**
     * 维度id
     */
    private String dimensionId;
    /**
     * 字段id
     */
    private String fieldId;
    /**
     * 原始值
     */
    private String value;
    /**
     * 展示值
     */
    private String displayValue;
    /**
     * 租户ID
     */
    private String tenantId;
    /**
     * 权重
     */
    private Float weight;
    /**
     * 是否删除
     */
    private int isDeleted;


}
