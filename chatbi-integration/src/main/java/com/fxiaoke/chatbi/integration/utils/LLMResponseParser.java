package com.fxiaoke.chatbi.integration.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * LLM响应解析工具
 * 用于从LLM返回的文本中提取有效JSON信息
 */
public class LLMResponseParser {
    private static final Logger log = LoggerFactory.getLogger(LLMResponseParser.class);
    
    // JSON提取正则表达式
    private static final Pattern JSON_BLOCK_PATTERN = Pattern.compile("```(?:json)?\\s*(.+?)\\s*```", Pattern.DOTALL);
    private static final Pattern JSON_PATTERN = Pattern.compile("\\{[^{}]*(?:\\{[^{}]*}[^{}]*)*}", Pattern.DOTALL);
    

    private static String removeComments(String json) {
        if (json == null) return null;
        
        // 移除行内注释 (//...)
        String result = json.replaceAll("//.*?($|\\n)", "");
        
        // 如果需要，也可以移除块注释 (/* ... */)
        result = result.replaceAll("/\\*.*?\\*/", "");
        
        return result;
    }

    /**
     * 从LLM响应中提取JSON数据并解析为指定类型的对象
     * 
     * @param llmResponse LLM返回的响应文本
     * @param clazz 目标类型
     * @return 解析后的对象，如果无法提取或解析则返回null
     */
    public static <T> T parseJson(String llmResponse, Class<T> clazz) {
        if (StringUtils.isBlank(llmResponse)) {
            log.warn("LLM response is empty");
            return null;
        }

        String preprocessed = removeComments(llmResponse);
        
        String jsonStr = extractJson(preprocessed);
        if (StringUtils.isBlank(jsonStr)) {
            log.error("Failed to extract JSON from LLM response: {}", llmResponse);
            return null;
        }
        
        try {
            return JSON.parseObject(jsonStr, clazz);
        } catch (Exception e) {
            log.error("Failed to parse JSON to {}: {}", clazz.getSimpleName(), jsonStr, e);
            return null;
        }
    }
    
    /**
     * 从LLM响应中提取JSON字符串
     */
    private static String extractJson(String llmResponse) {
        // 1. 尝试从代码块中提取
        Matcher blockMatcher = JSON_BLOCK_PATTERN.matcher(llmResponse);
        if (blockMatcher.find()) {
            String json = blockMatcher.group(1).trim();
            if (isValidJson(json)) {
                return json;
            }
        }
        
        // 2. 尝试直接解析为JSON
        if (isValidJson(llmResponse)) {
            return llmResponse;
        }
        
        // 3. 尝试提取花括号包围的内容
        Matcher jsonMatcher = JSON_PATTERN.matcher(llmResponse);
        if (jsonMatcher.find()) {
            String json = jsonMatcher.group(0).trim();
            if (isValidJson(json)) {
                return json;
            }
        }
        
        return null;
    }
    
    /**
     * 检查字符串是否为有效的JSON
     */
    private static boolean isValidJson(String jsonStr) {
        if (StringUtils.isBlank(jsonStr)) {
            return false;
        }
        
        try {
            JSON.parse(jsonStr);
            return true;
        } catch (JSONException e) {
            return false;
        }
    }
} 