package com.fxiaoke.chatbi.integration.model.ch;

import com.fxiaoke.chatbi.common.model.enums.KnowledgeType;
import com.fxiaoke.chatbi.common.model.knowledge.Knowledge;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.util.Objects;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "stat_field")
public class StatField implements Knowledge {
    private String tenantId;

    private String fieldId;

    private String fieldName;

    private String aggDimType;

    private String udfFieldId;

    @Override
    public String getKnowledgeId() {
        return getFieldId();
    }

    @Override
    public KnowledgeType getKnowledgeTypeEnum() {
        return Objects.equals(getAggDimType(), "dim") ?
                KnowledgeType.DIMENSION :
                KnowledgeType.MEASURE;
    }
}
