package com.fxiaoke.chatbi.integration.repository.ch;

import com.fxiaoke.chatbi.integration.model.ch.DimensionValueFull;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 维度值仓库接口
 * 负责维度值的查询和字段映射
 */
public interface DimensionValueRepository {

    /**
     * 根据多个关键词直接搜索相关的字段ID集合（去重）
     *
     * @param keywords 关键词列表
     * @param tenantId 租户ID
     * @return 唯一的字段ID集合
     */
    Set<String> searchFieldIdsByKeywords(List<String> keywords, String tenantId);

    /**
     * 批量根据值查找字段ID
     *
     * @param values   维度值列表
     * @param tenantId 租户ID
     * @return 值到字段ID的映射
     */
    Map<String, String> batchSearchFieldIdsByValues(List<String> values, String tenantId);

    /**
     * 根据值列表精确匹配维度值信息
     * 与搜索不同，此方法要求值完全匹配
     *
     * @param values   值列表
     * @param tenantId 租户ID
     * @param limit    每个值返回的最大结果数，默认不限制
     * @return 匹配的维度值完整信息列表
     */
    List<DimensionValueFull> exactMatchByValues(List<String> values, String tenantId, int limit);

    /**
     * 根据值列表精确匹配维度值信息
     * 重载方法，不限制返回结果数量
     *
     * @param values   值列表
     * @param tenantId 租户ID
     * @return 匹配的维度值完整信息列表
     */
    default List<DimensionValueFull> exactMatchByValues(List<String> values, String tenantId) {
        return exactMatchByValues(values, tenantId, 0);
    }
    /**
     * 根据值列表模糊匹配维度值信息
     *
     * @param dimensionId 维度ID
     * @param displayValues 维度值
     * @param tenantId      租户ID
     * @return 匹配的维度值完整信息列表
     */
    List<DimensionValueFull> vagueMatchByDisplayValues(String dimensionId, List<String> displayValues, String tenantId, int limit);

}