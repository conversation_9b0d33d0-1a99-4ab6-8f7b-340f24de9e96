package com.fxiaoke.chatbi.integration.dao.pg;

import com.fxiaoke.chatbi.integration.model.pg.EnterpriseKnowledge;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 企业知识库Mapper
 */
@Mapper
public interface EnterpriseKnowledgeMapper extends ITenant<EnterpriseKnowledgeMapper> {

  /**
   * 根据术语精确查询（支持源术语和同义词）
   *
   * @param term     查询术语
   * @param tenantId 租户ID
   * @return 企业知识
   */
  EnterpriseKnowledge findByTerm(@Param("term") String term, @Param("tenantId") String tenantId);
  
  /**
   * 根据租户ID和删除状态查询所有企业知识
   *
   * @param tenantId  租户ID
   * @param isDeleted 删除状态(0-未删除, 1-已删除)
   * @return 企业知识列表
   */
  List<EnterpriseKnowledge> findAllByTenantIdAndIsDeleted(@Param("tenantId") String tenantId, @Param("isDeleted") Integer isDeleted);
} 