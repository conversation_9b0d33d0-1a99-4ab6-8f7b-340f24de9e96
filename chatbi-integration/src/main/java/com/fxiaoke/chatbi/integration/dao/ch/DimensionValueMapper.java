package com.fxiaoke.chatbi.integration.dao.ch;

import com.fxiaoke.chatbi.integration.model.ch.DimensionValueFull;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 维度值映射Mapper
 * 用于在ClickHouse中查询维度值映射信息
 */
@Mapper
public interface DimensionValueMapper extends ITenant<DimensionValueMapper> {

  /**
   * 根据多个关键词模糊匹配维度值
   *
   * @param dimensionId 维度ID
   * @param keywords    关键词列表
   * @param tenantId    租户ID
   * @param limit       返回数量限制
   * @return 维度值列表
   */
  List<DimensionValueFull> searchByKeywords(@Param("dimensionId") String dimensionId,
                                            @Param("keywords") List<String> keywords,
                                            @Param("tenantId") String tenantId,
                                            @Param("limit") int limit);

  /**
   * 根据多个关键词直接查询不重复的字段ID集合
   *
   * @param keywords 关键词列表
   * @param tenantId 租户ID
   * @return 字段ID集合
   */
  Set<String> searchFieldIdsByKeywords(@Param("keywords") List<String> keywords, @Param("tenantId") String tenantId);
  
  /**
   * 根据值列表精确匹配维度值
   *
   * @param values 值列表
   * @param tenantId 租户ID
   * @param limit 返回数量限制，0表示不限制
   * @return 维度值列表
   */
  List<DimensionValueFull> exactMatchByValues(@Param("values") List<String> values, 
                                             @Param("tenantId") String tenantId,
                                             @Param("limit") int limit);
}