package com.fxiaoke.chatbi.integration.model.ch.accuracy;

import com.github.mybatis.annotation.DynamicTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.util.List;

/**
 * 测试用例表
 * 对应ClickHouse的bi_test_case表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "bi_test_case")
public class TestCase {
    /**
     * 测试用例唯一标识
     */
    private String id;
    
    /**
     * 租户ID
     */
    private String tenantId;
    
    /**
     * 所属数据集ID
     */
    private String datasetId;
    
    /**
     * 测试问题文本
     */
    private String query;
    
    /**
     * 预期意图类型：CHART/TABLE/TEXT等
     */
    private String expectedIntent;
    
    /**
     * 预期维度，JSON格式
     */
    private String expectedDimensions;
    
    /**
     * 预期指标，JSON格式
     */
    private String expectedMeasures;
    
    /**
     * 预期过滤条件，JSON格式
     */
    private String expectedFilters;
    
    /**
     * 预期图表类型：柱状图/折线图/饼图等
     */
    private String expectedChartType;
    
    /**
     * 预期应该召回的图表ID列表
     */
    @DynamicTypeHandler("com.github.mybatis.handler.list.ListTypeHandler")
    private List<String> expectedChartIds;
    
    /**
     * 预期的图表排序，JSON格式
     */
    private String expectedChartRanking;
    
    /**
     * 测试用例来源：MANUAL/AUTO_GEN/USER_FEEDBACK等
     */
    private String source;
    
    /**
     * 是否启用上下文：1-启用，0-不启用
     */
    private Integer contextEnabled;
    
    /**
     * 难度级别：1-简单，2-中等，3-复杂
     */
    private Integer difficultyLevel;
    
    /**
     * 标签列表，用于分类和筛选
     */
    @DynamicTypeHandler("com.github.mybatis.handler.list.ListTypeHandler")
    private List<String> tags;
    
    /**
     * 创建人ID
     */
    private String createdBy;
    
    /**
     * 创建时间戳(毫秒)
     */
    private Long createTime;
    
    /**
     * 最后修改人ID
     */
    private String lastModifiedBy;
    
    /**
     * 最后修改时间戳(毫秒)
     */
    private Long lastModifiedTime;
    
    /**
     * 扩展信息，JSON格式
     */
    private String extraInfo;
    
    /**
     * 删除状态：0-正常，-1/-2-已删除
     */
    private Integer isDeleted;
}
