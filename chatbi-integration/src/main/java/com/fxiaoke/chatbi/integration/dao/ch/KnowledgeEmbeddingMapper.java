package com.fxiaoke.chatbi.integration.dao.ch;

import com.fxiaoke.chatbi.integration.model.ch.KnowledgeEmbedding;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 知识向量Mapper接口
 * 对应ClickHouse的bi_knowledge_embedding表的数据访问操作
 */
@Mapper
public interface KnowledgeEmbeddingMapper extends ITenant<KnowledgeEmbeddingMapper> {

    /**
     * 批量保存知识向量
     *
     * @param knowledgeEmbeddings 知识向量列表
     * @return 影响行数
     */
    int batchSaveKnowledgeEmbeddings(@Param("knowledgeEmbeddings") List<KnowledgeEmbedding> knowledgeEmbeddings);

    /**
     * 搜索相似向量
     *
     * @param tenantId        租户ID
     * @param embeddingString 查询向量字符串
     * @param knowledgeType   知识类型
     * @param threshold       相似度阈值
     * @param limit           结果数量限制
     * @return 相似知识向量唯一标识列表
     */
    List<String> searchSimilarEmbeddingKnowledgeId(
            @Param("tenantId") String tenantId,
            @Param("embeddingString") String embeddingString,
            @Param("knowledgeType") String knowledgeType,
            @Param("threshold") double threshold,
            @Param("limit") int limit);

    /**
     * 搜索相似向量，并限定只在指定的知识标识范围内搜索
     *
     * @param tenantId        租户ID
     * @param embeddingString 查询向量字符串
     * @param knowledgeType   知识类型
     * @param threshold       相似度阈值
     * @param limit           结果数量限制
     * @param allowedIds      允许的知识标识列表，用于限定搜索范围
     * @return 相似知识向量唯一标识列表
     */
    List<String> searchSimilarEmbeddingKnowledgeIdWithFilter(
            @Param("tenantId") String tenantId,
            @Param("embeddingString") String embeddingString,
            @Param("knowledgeType") String knowledgeType,
            @Param("threshold") double threshold,
            @Param("limit") int limit,
            @Param("allowedIds") List<String> allowedIds);

    /**
     * 搜索相似向量，并返回完整的知识向量对象（包含相似度分数）
     *
     * @param tenantId        租户ID
     * @param embeddingString 查询向量字符串
     * @param knowledgeType   知识类型
     * @param threshold       相似度阈值
     * @param limit           结果数量限制
     * @return 知识向量对象列表（包含相似度分数）
     */
    List<KnowledgeEmbedding> searchEmbeddingWithScores(
            @Param("tenantId") String tenantId,
            @Param("embeddingString") String embeddingString,
            @Param("knowledgeType") String knowledgeType,
            @Param("threshold") double threshold,
            @Param("limit") int limit);

    /**
     * 根据知识标识获取所有特征向量
     *
     * @param tenantId      租户ID
     * @param knowledgeType 知识类型
     * @param knowledgeId   知识标识
     * @return 知识向量列表
     */
    List<KnowledgeEmbedding> findByKnowledgeId(
            @Param("tenantId") String tenantId,
            @Param("knowledgeType") String knowledgeType,
            @Param("knowledgeId") String knowledgeId);

    /**
     * 批量查询多个知识标识的特征向量
     *
     * @param tenantId      租户ID
     * @param knowledgeType 知识类型
     * @param knowledgeIds  知识标识列表
     * @return 知识向量列表
     */
    List<KnowledgeEmbedding> findByKnowledgeIdList(
            @Param("tenantId") String tenantId,
            @Param("knowledgeType") String knowledgeType,
            @Param("knowledgeIds") List<String> knowledgeIds);

  /**
   * 搜索向量的相似度,限定在指定的知识ID范围内,只返回最相似的结果
   *
   * @param tenantId        租户ID
   * @param embeddingString 查询向量字符串
   * @param knowledgeType   知识类型
   * @param knowledgeIds    知识ID列表(可选过滤条件)
   * @param threshold       相似度阈值
   * @return 最相似的知识向量对象
   */
  KnowledgeEmbedding searchEmbeddingsWithScores(
      @Param("tenantId") String tenantId,
      @Param("embeddingString") String embeddingString,
      @Param("knowledgeType") String knowledgeType,
      @Param("knowledgeIds") List<String> knowledgeIds,
      @Param("threshold") double threshold);

  /**
   * 根据ID删除知识向量（软删除）
   *
   * @param tenantId 租户ID
   * @param id       知识向量ID
   * @return 影响行数
   */
  int deleteById(@Param("tenantId") String tenantId, @Param("id") String id);

  /**
   * 批量删除知识向量（以增代删）
   *
   * @param tenantId 租户ID
   * @param ids      知识向量ID列表
   * @return 影响行数
   */
  int batchDeleteByIds(@Param("tenantId") String tenantId, @Param("ids") List<String> ids);

    /**
     * 删除知识向量数据
     *
     * @param tenantId      租户ID
     * @param knowledgeType 知识类型（可选）
     * @param knowledgeIds  知识ID列表（可选）
     * @return 影响行数
     */
    int deleteKnowledgeEmbeddings(
            @Param("tenantId") String tenantId,
            @Param("knowledgeType") String knowledgeType,
            @Param("knowledgeIds") List<String> knowledgeIds);
}
