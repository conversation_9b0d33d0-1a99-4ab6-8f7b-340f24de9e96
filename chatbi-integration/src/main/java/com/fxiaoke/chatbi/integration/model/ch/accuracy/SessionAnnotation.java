package com.fxiaoke.chatbi.integration.model.ch.accuracy;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.util.List;

/**
 * Session级标注记录表
 * 对应ClickHouse的bi_session_annotation表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "bi_session_annotation")
public class SessionAnnotation {
    /**
     * Session标注记录唯一标识
     */
    private String id;
    
    /**
     * 关联的会话ID
     */
    private String sessionId;
    
    /**
     * 租户ID
     */
    private String tenantId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * Session整体质量评分：1-5分
     */
    private Integer sessionScore;
    
    /**
     * Session标签：["逻辑连贯", "用户满意", "完整解答", "需改进", "体验良好"]
     */
    private List<String> sessionTags;
    
    /**
     * Session的综合文字评价
     */
    private String sessionComments;
    
    /**
     * 标注人ID
     */
    private String createdBy;
    
    /**
     * 创建时间戳(毫秒)
     */
    private Long createTime;
    
    /**
     * 最后修改人ID
     */
    private String lastModifiedBy;
    
    /**
     * 最后修改时间戳(毫秒)
     */
    private Long lastModifiedTime;
    
    /**
     * 删除状态：0-正常，1-已删除
     */
    private Integer isDeleted;
}
