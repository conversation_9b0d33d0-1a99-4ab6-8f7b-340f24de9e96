package com.fxiaoke.chatbi.integration.repository.impl.ch;

import com.fxiaoke.chatbi.common.config.EmbeddingProperties;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.knowledge.ChannelType;
import com.fxiaoke.chatbi.integration.dao.ch.ChartKnowledgeMapper;
import com.fxiaoke.chatbi.integration.model.ch.ChartKnowledge;
import com.fxiaoke.chatbi.integration.repository.ch.ChartKnowledgeRepository;
import com.fxiaoke.chatbi.integration.repository.ch.KnowledgeEmbeddingRepository;
import com.fxiaoke.chatbi.integration.util.VectorUtil;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 图表知识存储库实现
 * 基于ClickHouse的图表知识存储和检索实现
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ChartKnowledgeRepositoryImpl implements ChartKnowledgeRepository {
    private final EmbeddingProperties embeddingProperties;
    private final ChartKnowledgeMapper chartKnowledgeMapper;
    private final KnowledgeEmbeddingRepository knowledgeEmbeddingRepository;

    @Override
    public void saveChartKnowledgeList(List<ChartKnowledge> chartKnowledgeList, String tenantId) {
        if (chartKnowledgeList == null || chartKnowledgeList.isEmpty()) {
            return;
        }

        try {
            // 直接使用批量插入
            Lists.partition(chartKnowledgeList, 200).forEach(
                    batch -> chartKnowledgeMapper.setTenantId(tenantId).batchSaveChartKnowledges(chartKnowledgeList));
            log.debug("成功批量保存图表知识: 数量={}", chartKnowledgeList.size());
        } catch (Exception e) {
            log.error("批量保存图表知识失败", e);
            throw e;
        }
    }

    /**
     * 根据图表ID列表批量获取图表知识
     */
    @Override
    public List<ChartKnowledge> batchGetByViewIds(List<String> viewIds,
            UserIdentity userIdentity,
            ChannelType channelType) {
        String tenantId = VectorUtil.getTenantId(userIdentity, channelType);
        if (CollectionUtils.isEmpty(viewIds) || StringUtils.isBlank(tenantId)) {
            log.warn("批量获取图表知识参数无效: viewIds={}, tenantId={}", CollectionUtils.isEmpty(viewIds) ? "空" : viewIds.size(),
                    tenantId);
            return Collections.emptyList();
        }

        log.info("开始批量获取图表知识, viewIds数量={}", viewIds.size());

        try {
            // 使用MyBatis Mapper进行批量查询
            List<ChartKnowledge> result = chartKnowledgeMapper.setTenantId(tenantId)
                    .batchGetByViewIds(tenantId, viewIds);

            if (CollectionUtils.isEmpty(result)) {
                log.warn("批量查询图表知识为空，使用临时实现创建基本对象");
                return createTempChartKnowledge(viewIds, tenantId);
            }

            log.info("成功获取图表知识: {}/{}", result.size(), viewIds.size());
            return result;
        } catch (Exception e) {
            log.error("批量获取图表知识异常，使用临时实现", e);
            // 异常情况下使用临时实现
            return createTempChartKnowledge(viewIds, tenantId);
        }
    }

    /**
     * 创建临时ChartKnowledge对象列表
     * 当数据库查询失败或结果为空时使用
     */
    private List<ChartKnowledge> createTempChartKnowledge(List<String> viewIds, String tenantId) {
        if (CollectionUtils.isEmpty(viewIds) || StringUtils.isBlank(tenantId)) {
            return Collections.emptyList();
        }

        List<ChartKnowledge> result = new ArrayList<>();
        for (String viewId : viewIds) {
            ChartKnowledge knowledge = new ChartKnowledge();
            knowledge.setViewId(viewId);
            knowledge.setTenantId(tenantId);
            knowledge.setUsageCount(0);
            result.add(knowledge);
        }
        return result;
    }

    /**
     * 直接根据字段ID列表检索图表
     *
     * @param fieldIds 字段ID列表
     * @param tenantId 租户ID
     * @return 图表ID列表
     */
    @Override
    public List<String> searchChartsByFieldIds(List<String> fieldIds, String tenantId, ChannelType channelType) {
        tenantId = VectorUtil.getTenantId(tenantId, channelType);
        if (CollectionUtils.isEmpty(fieldIds) || StringUtils.isBlank(tenantId)) {
            log.warn("字段ID列表或租户ID为空，无法执行检索");
            return Collections.emptyList();
        }

        try {
            log.info("直接根据字段ID列表检索图表, fieldIds={}, tenantId={}", fieldIds, tenantId);

            // 构建字段ID的JSON数组格式，用于ClickHouse查询
            String fieldIdsJson = "[" + fieldIds.stream().map(id -> "'" + id + "'").collect(Collectors.joining(","))
                    + "]";

            // 使用Mapper执行查询
            List<String> viewIds = chartKnowledgeMapper.setTenantId(tenantId).searchChartsByFieldIds(tenantId,
                    fieldIdsJson);

            log.info("根据字段ID检索到图表数量: {}", viewIds.size());
            return viewIds;
        } catch (Exception e) {
            log.error("根据字段ID检索图表失败: fieldIds={}, tenantId={}", fieldIds, tenantId, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<ChartKnowledge> findAllByTenant(String tenantId) {
        try {
            return chartKnowledgeMapper.setTenantId(tenantId).findAllByTenantId(tenantId);
        } catch (Exception e) {
            log.error("查询所有图表知识失败: tenantId={}", tenantId, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<String> searchChartsByKeyword(String keyword, String tenantId, ChannelType channelType) {
        String effectiveTenantId = VectorUtil.getTenantId(tenantId, channelType);
        if (StringUtils.isBlank(keyword) || StringUtils.isBlank(effectiveTenantId)) {
            log.warn("关键词或租户ID为空，无法执行关键词检索: keyword={}, tenantId={}", keyword, effectiveTenantId);
            return Collections.emptyList();
        }

        try {
            log.info("开始根据关键词检索图表: keyword={}, tenantId={}", keyword, effectiveTenantId);

            // 直接使用关键词，不构建模式
            // 使用Mapper执行查询
            List<String> viewIds = chartKnowledgeMapper.setTenantId(effectiveTenantId)
                    .searchChartsByKeyword(effectiveTenantId, keyword);

            log.info("根据关键词检索到图表数量: {}", viewIds.size());
            return viewIds;
        } catch (Exception e) {
            log.error("根据关键词检索图表失败: keyword={}, tenantId={}", keyword, effectiveTenantId, e);
            return Collections.emptyList();
        }
    }

    /**
     * 根据分类关键词搜索图表
     * 分别在三个不同列中搜索对应的关键词
     */
    @Override
    public List<String> searchChartsByCategorizedKeyword(
            String dimensionKeyword,
            String measureKeyword,
            String filterKeyword,
            String tenantId,
            ChannelType channelType) {
        String effectiveTenantId = VectorUtil.getTenantId(tenantId, channelType);

        // 检查参数有效性，至少需要一个有效的关键词
        boolean hasValidKeyword = StringUtils.isNotBlank(dimensionKeyword) ||
                StringUtils.isNotBlank(measureKeyword) ||
                StringUtils.isNotBlank(filterKeyword);

        if (!hasValidKeyword || StringUtils.isBlank(effectiveTenantId)) {
            log.warn("分类关键词和租户ID无效，无法执行检索: dimensionKeyword={}, measureKeyword={}, filterKeyword={}, tenantId={}",
                    dimensionKeyword, measureKeyword, filterKeyword, effectiveTenantId);
            return Collections.emptyList();
        }

        try {
            log.info("开始根据分类关键词检索图表: dimensionKeyword={}, measureKeyword={}, filterKeyword={}, tenantId={}",
                    dimensionKeyword, measureKeyword, filterKeyword, effectiveTenantId);

            // 准备关键词参数，对于空值使用空字符串
            String dimension = StringUtils.defaultString(dimensionKeyword, "");
            String measure = StringUtils.defaultString(measureKeyword, "");
            String filter = StringUtils.defaultString(filterKeyword, "");

            // 使用Mapper执行查询
            List<String> viewIds = chartKnowledgeMapper.setTenantId(effectiveTenantId)
                    .searchChartsByCategorizedKeyword(effectiveTenantId, dimension, measure, filter);

            log.info("根据分类关键词检索到图表数量: {}", viewIds.size());
            return viewIds;
        } catch (Exception e) {
            log.error("根据分类关键词检索图表失败: dimensionKeyword={}, measureKeyword={}, filterKeyword={}, tenantId={}",
                    dimensionKeyword, measureKeyword, filterKeyword, effectiveTenantId, e);
            return Collections.emptyList();
        }
    }

    /**
     * 根据分类关键词搜索图表 同时打分
     * 分别在三个不同列中搜索对应的关键词
     */
    @Override
    public List<ChartKnowledge> selectChartsByKeywordWithScores(
            List<String> dimensionKeyword,
            List<String> measureKeyword,
            List<String> filterKeyword,
            String tenantId,
            ChannelType channelType) {
        String effectiveTenantId = VectorUtil.getTenantId(tenantId, channelType);
        if (StringUtils.isBlank(effectiveTenantId)) {
            log.warn("分类关键词和租户ID无效，无法执行检索: dimensionKeyword={}, measureKeyword={}, filterKeyword={}, tenantId={}",
                    dimensionKeyword, measureKeyword, filterKeyword, effectiveTenantId);
            return Collections.emptyList();
        }

        try {
            log.info("开始根据分类关键词检索图表: dimensionKeyword={}, measureKeyword={}, filterKeyword={}, tenantId={}",
                    dimensionKeyword, measureKeyword, filterKeyword, effectiveTenantId);

            // 使用Mapper执行查询
            List<ChartKnowledge> chartKnowledges = chartKnowledgeMapper.setTenantId(effectiveTenantId)
                    .selectChartsByKeywordWithScores(dimensionKeyword, measureKeyword, filterKeyword,
                            effectiveTenantId);

            log.info("根据分类关键词检索到图表数量: {}", chartKnowledges.size());
            return chartKnowledges;
        } catch (Exception e) {
            log.error("根据分类关键词检索图表失败: dimensionKeyword={}, measureKeyword={}, filterKeyword={}, tenantId={}",
                    dimensionKeyword, measureKeyword, filterKeyword, effectiveTenantId, e);
            return Collections.emptyList();
        }
    }

    @Override
    public void deleteByTenantIdAndViewIds(String tenantId, List<String> viewIds) {
        if (StringUtils.isBlank(tenantId)) {
            log.warn("tenantId is blank, skip delete operation");
            return;
        }
        try {
            int affectedRows = chartKnowledgeMapper.setTenantId(tenantId).deleteByTenantIdAndViewIds(tenantId, viewIds);
            log.info("Successfully deleted chart knowledge for tenant: {}, viewIds: {}, affected rows: {}",
                    tenantId, viewIds, affectedRows);
        } catch (Exception e) {
            log.error("Failed to delete chart knowledge for tenant: {}, viewIds: {}", tenantId, viewIds, e);
            throw e;
        }
    }
}