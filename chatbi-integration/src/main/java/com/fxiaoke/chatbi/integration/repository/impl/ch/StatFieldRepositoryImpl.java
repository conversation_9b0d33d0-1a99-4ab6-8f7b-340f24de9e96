package com.fxiaoke.chatbi.integration.repository.impl.ch;

import com.fxiaoke.chatbi.integration.dao.ch.StatFieldMapper;
import com.fxiaoke.chatbi.integration.model.ch.StatField;
import com.fxiaoke.chatbi.integration.repository.ch.StatFieldRepository;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Repository
@RequiredArgsConstructor
public class StatFieldRepositoryImpl implements StatFieldRepository {

  private final StatFieldMapper statFieldMapper;

  @Override
  public List<StatField> getKnowledgeStatFields(String tenantId) {
    try {
      return statFieldMapper.setTenantId(tenantId).getKnowledgeStatFields(tenantId);
    } catch (Exception e) {
      log.error("getStatFields error!", e);
    }
    return Lists.newArrayList();
  }

  @Override
  public Map<String, String> getFieldIdToNameMapBySchemaId(String schemaId, List<String> candidateIds, String tenantId) {
    if (StringUtils.isBlank(schemaId)) {
      log.warn("schemaId为空，无法查询字段");
      return Maps.newHashMap();
    }
    
    try {
      // 调用Mapper查询字段列表，由于SQL中已添加LIMIT 1，最多返回一条记录
      List<StatField> fields = statFieldMapper.setTenantId(tenantId)
              .getFieldsBySchemaIdAndCandidateIds(schemaId, candidateIds, tenantId);
      
      if (CollectionUtils.isEmpty(fields)) {
        log.info("未找到主题[{}]下的字段", schemaId);
        return Maps.newHashMap();
      }
      
      // 由于最多只有一条记录，直接获取第一个元素
      StatField field = fields.get(0);
      Map<String, String> result = Maps.newHashMap();
      result.put(field.getFieldId(), field.getFieldName());
      
      log.info("为主题[{}]找到匹配字段: {} -> {}", schemaId, field.getFieldId(), field.getFieldName());
      return result;
    } catch (Exception e) {
      log.error("查询主题[{}]下的字段时发生错误", schemaId, e);
      return Maps.newHashMap();
    }
  }
}
