package com.fxiaoke.chatbi.integration.config;

import com.github.mybatis.spring.DynamicDataSource;
import com.github.mybatis.spring.ScannerConfigurer;
import com.github.mybatis.tenant.TenantPolicy;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ResourceLoaderAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * @Author:jief
 * @Date:2024/4/2
 */
@Configuration
public class ChBiDBConfig implements ResourceLoaderAware {

  private ResourceLoader resourceLoader;

  @Bean("biCHDataSource")
  @DependsOn("springUtil")
  public DataSource tenantDataSource(@Qualifier("cHRouterPolicy") TenantPolicy tenantPolicy) {
    DynamicDataSource dataSource = new DynamicDataSource();
    dataSource.setConfigName("fs-bi-warehouse");
    dataSource.setSectionNames("chDataSource");
    dataSource.setTenantPolicy(tenantPolicy);
    dataSource.setConnectionPoolDriver("hikari");
    return dataSource;
  }

  @Bean("biCHSqlSessionFactoryBean")
  public SqlSessionFactoryBean sqlSessionFactoryBean(@Qualifier("biCHDataSource") DataSource dataSource) throws Exception {
    SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
    sqlSessionFactoryBean.setDataSource(dataSource);
    sqlSessionFactoryBean.setTypeAliasesPackage("com.fxiaoke.chatbi.integration.model.ch");
    sqlSessionFactoryBean.setConfigLocation(resourceLoader.getResource("classpath:mybatis/ch-mybatis-config.xml"));
    ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
    sqlSessionFactoryBean.setMapperLocations(resolver.getResources("classpath*:mybatis/mapper/ch/*.xml"));
    return sqlSessionFactoryBean;
  }

  @Bean("chScannerConfigurer")
  public ScannerConfigurer scannerConfigurer() {
    ScannerConfigurer scannerConfigurer = new ScannerConfigurer();
    scannerConfigurer.setBasePackage("com.fxiaoke.chatbi.integration.dao.ch");
    scannerConfigurer.setSqlSessionFactoryBeanName("biCHSqlSessionFactoryBean");
    return scannerConfigurer;
  }

  @Bean("biCHTxManager")
  public DataSourceTransactionManager dataSourceTransactionManager(@Qualifier("biCHDataSource") DataSource dataSource) {
    DataSourceTransactionManager txManager = new DataSourceTransactionManager();
    txManager.setDataSource(dataSource);
    return txManager;
  }

  @Override
  public void setResourceLoader(ResourceLoader resourceLoader) {
    this.resourceLoader = resourceLoader;
  }
}
