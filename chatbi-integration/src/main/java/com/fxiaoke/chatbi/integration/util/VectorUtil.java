package com.fxiaoke.chatbi.integration.util;

import org.apache.commons.lang3.StringUtils;

/**
 * 向量处理工具类
 * 提供向量数据格式转换和处理的通用方法
 */
public class VectorUtil {
    
    /**
     * 将float[]转换为ClickHouse数组格式的字符串
     *
     * @param array 向量数组
     * @return ClickHouse数组格式字符串，例如 [0.1,0.2,0.3]
     */
    public static String floatArrayToString(float[] array) {
        if (array == null || array.length == 0) {
            return "[]";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("[");
        for (int i = 0; i < array.length; i++) {
            if (i > 0) {
                sb.append(",");
            }
            sb.append(array[i]);
        }
        sb.append("]");
        return sb.toString();
    }
    
    /**
     * 根据数据源类型获取租户ID
     * 针对SYSTEM类型返回系统租户ID，其他情况返回用户租户ID
     *
     * @param tenantId    租户ID
     * @param channelType 数据源类型
     * @return 处理后的租户ID
     */
    public static String getTenantId(String tenantId, com.fxiaoke.chatbi.common.model.knowledge.ChannelType channelType) {
        return channelType == com.fxiaoke.chatbi.common.model.knowledge.ChannelType.SYSTEM ? "-1" : tenantId;
    }
    
    /**
     * 根据用户身份信息和数据源类型获取租户ID
     *
     * @param userIdentity 用户身份信息
     * @param channelType  数据源类型
     * @return 处理后的租户ID
     */
    public static String getTenantId(com.fxiaoke.chatbi.common.model.UserIdentity userIdentity, 
                                     com.fxiaoke.chatbi.common.model.knowledge.ChannelType channelType) {
        if (userIdentity == null || StringUtils.isBlank(userIdentity.getTenantId())) {
            return null;
        }
        return getTenantId(userIdentity.getTenantId(), channelType);
    }
} 