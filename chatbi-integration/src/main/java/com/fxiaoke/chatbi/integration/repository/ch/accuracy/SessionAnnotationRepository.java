package com.fxiaoke.chatbi.integration.repository.ch.accuracy;

import com.fxiaoke.chatbi.integration.model.ch.accuracy.SessionAnnotation;

import java.util.List;
import java.util.Optional;

/**
 * Session级标注数据访问接口
 * 提供Session级标注记录的CRUD操作
 */
public interface SessionAnnotationRepository {
    
    /**
     * 保存Session级标注记录
     * 注意：数据存储在CH系统库，无需传递tenantId
     *
     * @param annotation Session标注对象
     * @return 保存成功返回true
     */
    boolean save(SessionAnnotation annotation);

    /**
     * 更新Session级标注记录
     * 注意：数据存储在CH系统库，无需传递tenantId
     *
     * @param annotation Session标注对象
     * @return 更新成功返回true
     */
    boolean update(SessionAnnotation annotation);
    
    /**
     * 根据Session ID查询标注记录
     *
     * @param sessionId 会话ID
     * @return Session标注记录
     */
    Optional<SessionAnnotation> findBySessionId(String sessionId);
    
    /**
     * 根据Session ID和租户ID查询标注记录
     * 注意：数据存储在CH系统库，tenantId仅作为查询条件
     *
     * @param sessionId 会话ID
     * @param tenantId 租户ID（查询条件）
     * @return Session标注记录
     */
    Optional<SessionAnnotation> findBySessionIdAndTenantId(String sessionId, String tenantId);

    /**
     * 根据租户ID查询所有Session标注记录
     * 注意：数据存储在CH系统库，tenantId仅作为查询条件
     *
     * @param tenantId 租户ID（查询条件）
     * @param offset 偏移量
     * @param limit 限制数量
     * @return Session标注记录列表
     */
    List<SessionAnnotation> findByTenantId(String tenantId, int offset, int limit);

    /**
     * 统计租户下的Session标注数量
     * 注意：数据存储在CH系统库，tenantId仅作为查询条件
     *
     * @param tenantId 租户ID（查询条件）
     * @return 标注数量
     */
    long countByTenantId(String tenantId);
    
    /**
     * 删除Session标注记录（逻辑删除）
     *
     * @param id Session标注记录ID
     * @return 删除成功返回true
     */
    boolean deleteById(String id);
}
