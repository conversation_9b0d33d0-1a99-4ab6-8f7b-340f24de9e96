package com.fxiaoke.chatbi.integration.client.fsbi;

import com.alibaba.fastjson.JSONObject;
import com.facishare.bi.common.dto.MyOrg;
import com.facishare.bi.metadata.context.dto.UIEntity;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

@RestResource(value = "BI-UITYPE", desc = "调用fs-bi-uitype服务", contentType = "application/json")
public interface FSBiUiClient {

  @POST(value = "/bi/dimension/getUIType", desc = "UITYPE")
  FieldUIType.SingleFieldResult getUIType(@Body JSONObject arg, @HeaderMap Map<String, String> headers);
}
