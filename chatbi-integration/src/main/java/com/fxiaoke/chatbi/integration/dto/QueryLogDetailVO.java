package com.fxiaoke.chatbi.integration.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 问答记录详情响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryLogDetailVO {
    
    /**
     * 记录ID
     */
    private String id;
    
    /**
     * 链路追踪ID
     */
    private String traceId;
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 租户ID
     */
    private String tenantId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 用户问题
     */
    private String query;
    
    /**
     * 系统回答
     */
    private String answer;
    
    /**
     * 响应组件信息
     */
    private String responseComponents;
    
    /**
     * 响应时间
     */
    private Integer responseTime;
    
    /**
     * 是否启用上下文
     */
    private Integer isContextEnabled;
    
    /**
     * 来源
     */
    private String source;
    
    /**
     * 用户反馈
     */
    private Integer feedback;
    
    /**
     * 反馈备注
     */
    private String feedbackComment;
    
    /**
     * 是否已标注
     */
    private Integer isAnnotated;
    
    /**
     * 问询时间
     */
    private Long queryTime;
    
    /**
     * 创建时间
     */
    private Long createTime;
    
    /**
     * 执行日志
     */
    private String executionLogs;
    
    /**
     * 标签列表
     */
    private List<String> tags;
    
    /**
     * 创建人ID
     */
    private String createdBy;
    
    /**
     * 标注信息（如果存在）
     */
    private AnnotationVO annotation;
}