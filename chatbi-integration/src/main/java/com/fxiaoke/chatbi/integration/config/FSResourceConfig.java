package com.fxiaoke.chatbi.integration.config;

import com.facishare.bi.api.UdfObjFieldService;
import com.facishare.dubbo.plugin.client.DubboRestFactoryBean;
import com.facishare.dubbo.plugin.client.ServerHostProfile;
import com.facishare.rest.core.RestServiceProxyFactory;
import com.facishare.rest.core.RestServiceProxyFactoryBean;
import com.fxiaoke.chatbi.integration.client.fsbi.FSBIUDFClient;
import com.fxiaoke.chatbi.integration.client.fsbi.FSBiUiClient;
import com.fxiaoke.chatbi.integration.client.llm.PaasAiClient;
import com.fxiaoke.common.http.spring.HttpSupportFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.fxiaoke.chatbi.integration.client.fsbi.FSBIClient;
import org.springframework.context.annotation.Lazy;

/**
 * FS-BI资源代理配置
 * 用于创建FSResource的RPC代理实例
 */
@Configuration
public class FSResourceConfig {

    /**
     * 创建RestServiceProxyFactory bean
     * 对应XML中的restServiceProxyFactory bean
     */
    @Bean(initMethod = "init")
    public RestServiceProxyFactory restServiceProxyFactory() {
        RestServiceProxyFactory factory = new RestServiceProxyFactory();
        factory.setConfigName("fs-bi-rest-proxy");
        return factory;
    }

    /**
     * 创建FSBIClient代理bean
     * 对应XML中的fsBiProxy bean
     */
    @Bean
    public FSBIClient fsBiProxy()throws Exception  {
        RestServiceProxyFactoryBean<FSBIClient> factoryBean = new RestServiceProxyFactoryBean<>();
        factoryBean.setType(FSBIClient.class);
        factoryBean.setFactory(restServiceProxyFactory());
        return factoryBean.getObject();
    }

    /**
     * 创建FSBIUDFClient代理bean
     * 对应XML中的fsBiProxy bean
     */
    @Bean
    public FSBIUDFClient fsUdfBiProxy()throws Exception  {
        RestServiceProxyFactoryBean<FSBIUDFClient> factoryBean = new RestServiceProxyFactoryBean<>();
        factoryBean.setType(FSBIUDFClient.class);
        factoryBean.setFactory(restServiceProxyFactory());
        return factoryBean.getObject();
    }

    /**
     * 创建FSBIClient代理bean
     * 对应XML中的fsBiProxy bean
     */
    @Bean
    public FSBiUiClient fsBiUIProxy()throws Exception  {
        RestServiceProxyFactoryBean<FSBiUiClient> factoryBean = new RestServiceProxyFactoryBean<>();
        factoryBean.setType(FSBiUiClient.class);
        factoryBean.setFactory(restServiceProxyFactory());
        return factoryBean.getObject();
    }

    /**
     * 创建PaasAiClient代理bean
     * 对应XML中的paasAiClient bean
     */
    @Bean
    public PaasAiClient paasAiClient()throws Exception  {
        RestServiceProxyFactoryBean<PaasAiClient> factoryBean = new RestServiceProxyFactoryBean<>();
        factoryBean.setType(PaasAiClient.class);
        factoryBean.setFactory(restServiceProxyFactory());
        return factoryBean.getObject();
    }

    /**
     * 配置dubboRestHttpClient
     * 对应XML: <bean name="dubboRestHttpClient" id="dubboRestHttpClient" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean"/>
     */
    @Bean(name = "dubboRestHttpClient")
    public HttpSupportFactoryBean dubboRestHttpClient() {
        return new HttpSupportFactoryBean();
    }

    /**
     * 配置biMetaDataApiHostProfile
     * 对应XML: <bean id="biMetaDataApiHostProfile" class="com.facishare.dubbo.plugin.client.ServerHostProfile">
     *            <property name="configName" value="fs-bi-metadata-rest-client"/>
     *          </bean>
     */
    @Bean
    public ServerHostProfile biMetaDataApiHostProfile() {
        ServerHostProfile profile = new ServerHostProfile();
        profile.setConfigName("fs-bi-metadata-rest-client");
        return profile;
    }

    /**
     * 配置UdfObjFieldService的DubboRest工厂
     * 对应XML: <bean class="com.facishare.dubbo.plugin.client.DubboRestFactoryBean" lazy-init="true">
     *            <property name="objectType" value="com.facishare.bi.api.UdfObjFieldService"/>
     *            <property name="serverHostProfile" ref="biMetaDataApiHostProfile"/>
     *          </bean>
     */
    @Bean
    @Lazy
    public DubboRestFactoryBean<UdfObjFieldService> udfObjFieldServiceFactoryBean() {
        DubboRestFactoryBean<UdfObjFieldService> factoryBean = new DubboRestFactoryBean<>();
        factoryBean.setObjectType(UdfObjFieldService.class);
        factoryBean.setServerHostProfile(biMetaDataApiHostProfile());
        return factoryBean;
    }

    /**
     * 直接暴露UdfObjFieldService实例
     * 这个额外的Bean定义确保UdfObjFieldService可以直接被注入
     */
    @Bean
    @Lazy
    public UdfObjFieldService udfObjFieldService() throws Exception {
        return udfObjFieldServiceFactoryBean().getObject();
    }
} 