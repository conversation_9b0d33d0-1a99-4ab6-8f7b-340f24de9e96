package com.fxiaoke.chatbi.integration.repository.ch;

import com.fxiaoke.chatbi.integration.model.ch.StatField;

import java.util.List;
import java.util.Map;

/**
 * 统计图 维度 指标元数据
 */
public interface StatFieldRepository {

  List<StatField> getKnowledgeStatFields(String tenantId);
  
  /**
   * 根据字段名称和主题ID获取字段ID到字段名的映射
   * 用于精确匹配字段名到字段ID，最多返回一个最匹配的结果
   *
   * @param schemaId     主题ID
   * @param candidateIds 候选字段ID列表，用于限定查询范围
   * @param tenantId     租户ID
   * @return 字段ID到字段名的映射（最多包含一个元素）
   */
  Map<String, String> getFieldIdToNameMapBySchemaId(String schemaId, List<String> candidateIds, String tenantId);

}
