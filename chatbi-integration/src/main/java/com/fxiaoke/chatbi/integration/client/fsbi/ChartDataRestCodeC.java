package com.fxiaoke.chatbi.integration.client.fsbi;

import com.facishare.bi.common.entities.QueryChartDataResult;
import com.facishare.rest.core.codec.DefaultRestCodec;
import com.facishare.rest.core.codec.IRestCodeC;
import com.facishare.rest.core.exception.RestProxyRuntimeException;
import com.facishare.rest.core.util.JsonUtil;
import com.google.gson.reflect.TypeToken;
import org.apache.http.HttpStatus;

import java.util.List;
import java.util.Map;

import static java.nio.charset.StandardCharsets.UTF_8;


/**
 * 实现结果集解析接口
 */
public class ChartDataRestCodeC implements IRestCodeC {

    @Override
    public <T> byte[] encodeArg(T obj) {
        // 可以选择重用 DefaultRestCodec 的实现
        return DefaultRestCodec.instance.encodeArg(obj);
    }

    @Override
    public <T> T decodeResult(int statusCode, Map<String, List<String>> headers, byte[] bytes, Class<T> clazz) {
        try {
            String bodyString = new String(bytes, UTF_8);

            // 检查状态码
            if (statusCode >= HttpStatus.SC_MULTIPLE_CHOICES) {
                throw new RestProxyRuntimeException(statusCode, bodyString);
            }

            // 处理正常的解码逻辑
            if (clazz == String.class) {
                return (T) bodyString;
            }
            if (clazz == void.class) {
                return null;
            }

            // 使用 JsonUtil 进行解码
            ApiResult<QueryChartDataResult> apiResult = JsonUtil.fromJson(bodyString, new TypeToken<ApiResult<QueryChartDataResult>>() {}.getType());

            return (T) apiResult.getResult();
        } catch (Exception e) {
            // 处理异常并调用默认解码逻辑
            return DefaultRestCodec.instance.decodeResult(statusCode, headers, bytes, clazz);
        }
    }
}