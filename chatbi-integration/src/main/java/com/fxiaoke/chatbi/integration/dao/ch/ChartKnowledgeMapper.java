package com.fxiaoke.chatbi.integration.dao.ch;

import com.fxiaoke.chatbi.integration.model.ch.ChartKnowledge;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 图表知识Mapper接口
 * 对应bi_chart_knowledge表的数据访问操作
 */
@Mapper
public interface ChartKnowledgeMapper extends ITenant<ChartKnowledgeMapper> {
    /**
     * 批量保存图表知识
     *
     * @param chartKnowledgeList 图表知识列表
     * @return 影响行数
     */
    int batchSaveChartKnowledges(@Param("chartKnowledges") List<ChartKnowledge> chartKnowledgeList);

    /**
     * 根据租户ID查询所有图表知识
     *
     * @param tenantId 租户ID
     * @return 图表知识列表
     */
    List<ChartKnowledge> findAllByTenantId(@Param("tenantId") String tenantId);

    /**
     * 批量获取图表知识
     *
     * @param tenantId 租户ID
     * @param viewIds  图表ID列表
     * @return 图表知识列表
     */
    List<ChartKnowledge> batchGetByViewIds(
            @Param("tenantId") String tenantId,
            @Param("viewIds") List<String> viewIds);

    /**
     * 直接根据字段ID列表检索图表ID
     *
     * @param tenantId     租户ID
     * @param fieldIdsJson 字段ID列表JSON字符串
     * @return 匹配的图表ID列表
     */
    List<String> searchChartsByFieldIds(
            @Param("tenantId") String tenantId,
            @Param("fieldIdsJson") String fieldIdsJson);

    /**
     * 根据关键词模式搜索图表
     *
     * @param tenantId 租户ID
     * @param keyword  关键词
     * @return 匹配的图表ID列表
     */
    List<String> searchChartsByKeyword(
            @Param("tenantId") String tenantId,
            @Param("keyword") String keyword);

    /**
     * 根据分类关键词搜索图表
     * 分别在三个不同列中搜索对应的关键词
     *
     * @param tenantId         租户ID
     * @param dimensionKeyword 维度关键词
     * @param measureKeyword   指标关键词
     * @param filterKeyword    筛选条件关键词
     * @return 匹配的图表ID列表
     */
    List<String> searchChartsByCategorizedKeyword(
            @Param("tenantId") String tenantId,
            @Param("dimensionKeyword") String dimensionKeyword,
            @Param("measureKeyword") String measureKeyword,
            @Param("filterKeyword") String filterKeyword);

    /**
     * 根据关键字和评分选择图表
     * 本函数旨在通过维度关键字、指标关键字和过滤器关键字来选取图表，并考虑租户ID以确保数据隔离
     *
     * @param dimKeys    维度关键字列表，用于匹配图表的维度
     * @param meaKeys    度量关键字列表，用于匹配图表的度量
     * @param filterKeys 过滤器关键字列表，用于匹配图表的过滤条件
     * @param tenantId   租户ID，用于区分不同租户的数据
     * @return 这些图表与给定的关键字匹配度高，并可能根据评分进行排序或过滤
     */
    List<ChartKnowledge> selectChartsByKeywordWithScores(@Param("dimKeys") List<String> dimKeys,
            @Param("meaKeys") List<String> meaKeys,
            @Param("filterKeys") List<String> filterKeys,
            @Param("tenantId") String tenantId);

    /**
     * 根据租户ID和视图ID列表删除图表知识数据
     *
     * @param tenantId 租户ID
     * @param viewIds  视图ID列表
     * @return 影响行数
     */
    int deleteByTenantIdAndViewIds(@Param("tenantId") String tenantId, @Param("viewIds") List<String> viewIds);
}