package com.fxiaoke.chatbi.integration.model.ch;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Transient;

/**
 * 知识特征向量表
 * bi_knowledge_embedding
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KnowledgeEmbedding {
  /**
   * 唯一标识ID
   * 使用UUID生成
   */
  private String id;

  /**
   * 租户ID
   */
  private String tenantId;

  /**
   * 知识类型
   */
  private String knowledgeType;

  /**
   * 知识标识
   */
  private String knowledgeId;

  /**
   * 嵌入向量数据
   */
  private float[] embedding;

  /**
   * 图表特征问题
   */
  private String feature;

  /**
   * 问题权重
   */
  private float weight;
  
  /**
   * 向量搜索得分
   * 仅在向量搜索结果中使用，表示与查询向量的相似度
   */
  @Transient
  private Double vectorScore;
}