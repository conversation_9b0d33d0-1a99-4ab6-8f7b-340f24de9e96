package com.fxiaoke.chatbi.integration.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 标注信息VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnnotationVO {
    
    /**
     * 标注ID
     */
    private String id;
    
    /**
     * 整体标注评分：1-5分
     */
    private Integer resultScore;
    
    /**
     * 标签列表
     */
    private List<String> tags;
    
    /**
     * 错误类型列表
     */
    private List<String> errorTypes;
    
    /**
     * 期望结果信息
     */
    private String expectedResult;
    
    /**
     * 标注人ID
     */
    private String createdBy;
    
    /**
     * 标注时间
     */
    private Long createTime;
} 