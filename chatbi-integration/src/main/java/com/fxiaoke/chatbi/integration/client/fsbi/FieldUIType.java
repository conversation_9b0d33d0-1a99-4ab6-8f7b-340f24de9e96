package com.fxiaoke.chatbi.integration.client.fsbi;

import com.facishare.bi.common.entities.UI;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

public interface FieldUIType {
  @Data
  @Builder
  class SingleFieldArg implements Serializable {
    private String dimensionId;
    private String fieldId;
    private String themeApiName;
    private String schemaId;
    private String tag;
    private String queryValue;
  }
  @Data
  class SingleFieldResult implements Serializable {
    private UI ui;
  }

  @Data
  class GetUIByApiNameArg implements Serializable {
    private String crmObjName;
    private String apiName;
  }


  @Data
  @Builder
  class GetUIByApiNamesArg implements Serializable {
    private String crmObjName;
    private List<String> apiNames;
  }
}
