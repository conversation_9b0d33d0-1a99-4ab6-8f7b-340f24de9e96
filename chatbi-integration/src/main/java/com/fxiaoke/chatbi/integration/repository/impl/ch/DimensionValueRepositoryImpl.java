package com.fxiaoke.chatbi.integration.repository.impl.ch;

import com.fxiaoke.chatbi.integration.dao.ch.DimensionValueMapper;
import com.fxiaoke.chatbi.integration.model.ch.DimensionValueFull;
import com.fxiaoke.chatbi.integration.repository.ch.DimensionValueRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * 维度值仓库实现类
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class DimensionValueRepositoryImpl implements DimensionValueRepository {

  private final DimensionValueMapper dimensionValueMapper;

  // 默认查询限制
  private static final int DEFAULT_LIMIT = 50;

  @Override
  public Set<String> searchFieldIdsByKeywords(List<String> keywords, String tenantId) {
    if (keywords == null || keywords.isEmpty() || StringUtils.isBlank(tenantId)) {
      return Collections.emptySet();
    }

    try {
      // 直接查询字段ID集合（SQL层面做去重）
      log.info("直接查询字段ID集合: keywords={}, tenantId={}", keywords, tenantId);
      Set<String> fieldIds = dimensionValueMapper.setTenantId(tenantId).searchFieldIdsByKeywords(keywords, tenantId);
      log.info("查询到字段ID数量: {}", fieldIds.size());
      return fieldIds;
    } catch (Exception e) {
      log.error("直接查询字段ID集合失败", e);
      return Collections.emptySet();
    }
  }

  @Override
  public Map<String, String> batchSearchFieldIdsByValues(List<String> values, String tenantId) {
    if (CollectionUtils.isEmpty(values)) {
      return Collections.emptyMap();
    }

    Map<String, String> valueFieldMap = new HashMap<>();
    // TODO: 从全量维度表返查

    return valueFieldMap;
  }
  
  @Override
  public List<DimensionValueFull> exactMatchByValues(List<String> values, String tenantId, int limit) {
    if (CollectionUtils.isEmpty(values) || StringUtils.isBlank(tenantId)) {
      return Collections.emptyList();
    }
    
    try {
      return dimensionValueMapper.setTenantId(tenantId).exactMatchByValues(values, tenantId, limit);
    } catch (Exception e) {
      log.error("根据值列表精确匹配维度值失败", e);
      return Collections.emptyList();
    }
  }

  @Override
  public List<DimensionValueFull> vagueMatchByDisplayValues(String dimensionId, List<String> displayValues, String tenantId, int limit) {
    if (CollectionUtils.isEmpty(displayValues) || StringUtils.isBlank(tenantId)) {
      return Collections.emptyList();
    }

    try {
      return dimensionValueMapper.setTenantId(tenantId).searchByKeywords(dimensionId, displayValues, tenantId, limit);
    } catch (Exception e) {
      log.error("根据值列表模糊匹配维度值失败", e);
      return Collections.emptyList();
    }

  }

}