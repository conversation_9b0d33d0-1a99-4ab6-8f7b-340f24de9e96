package com.fxiaoke.chatbi.integration.model.ch.accuracy;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Session汇总信息Bean
 * 用于替代Map<String, Object>，避免类型转换问题
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SessionSummary {
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 租户ID
     */
    private String tenantId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 对话轮次数量
     */
    private Integer conversationCount;
    
    /**
     * 第一个问题（用于展示）
     */
    private String firstQuestion;
    
    /**
     * 最后一次对话时间
     */
    private Long lastQueryTime;
    
    /**
     * 是否已标注：0-未标注，1-已标注
     */
    private Integer isAnnotated;
    
    /**
     * 创建时间
     */
    private Long createTime;
}
