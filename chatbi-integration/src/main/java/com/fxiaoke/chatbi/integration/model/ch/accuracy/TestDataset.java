package com.fxiaoke.chatbi.integration.model.ch.accuracy;

import com.github.mybatis.annotation.DynamicTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.util.List;

/**
 * 测试数据集表
 * 对应ClickHouse的bi_test_dataset表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "bi_test_dataset")
public class TestDataset {
    /**
     * 数据集唯一标识
     */
    private String id;
    
    /**
     * 租户ID
     */
    private String tenantId;
    
    /**
     * 数据集名称
     */
    private String name;
    
    /**
     * 数据集详细描述
     */
    private String description;
    
    /**
     * 创建人ID
     */
    private String createdBy;
    
    /**
     * 创建时间戳(毫秒)
     */
    private Long createTime;
    
    /**
     * 最后修改人ID
     */
    private String lastModifiedBy;
    
    /**
     * 最后修改时间戳(毫秒)
     */
    private Long lastModifiedTime;
    
    /**
     * 标签列表，用于分类和筛选
     */
    @DynamicTypeHandler("com.github.mybatis.handler.list.ListTypeHandler")
    private List<String> tags;
    
    /**
     * 版本号
     */
    private String version;
    
    /**
     * 是否激活：1-激活，0-未激活
     */
    private Integer isActive;
    
    /**
     * 扩展信息，JSON格式
     */
    private String extraInfo;
    
    /**
     * 删除状态：0-正常，-1/-2-已删除
     */
    private Integer isDeleted;
}
