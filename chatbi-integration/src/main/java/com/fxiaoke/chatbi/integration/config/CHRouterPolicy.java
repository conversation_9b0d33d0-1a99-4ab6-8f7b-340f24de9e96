package com.fxiaoke.chatbi.integration.config;

import com.facishare.paas.pod.client.DbRouterClient;
import com.facishare.paas.pod.dto.RouterInfo;
import com.fxiaoke.common.PasswordUtil;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IniChangeListener;
import com.github.autoconf.base.IniConfig;
import com.github.mybatis.tenant.TenantContext;
import com.github.mybatis.tenant.TenantPolicy;
import com.google.common.base.Preconditions;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Objects;

@Slf4j
@Service("cHRouterPolicy")
public class CHRouterPolicy implements TenantPolicy {
  private static final String BIZ = "BI";
  private static final String APPLICATION = "fs-bi-stat-calculate";
  private static final String DIALECT = "clickhouse";
  private static final String SYS_TENANT = "-1";
  @Getter
  private String chProxyQueryUserNamePre;
  @Getter
  private String chProxyQueryPassword;

  @Getter
  private String chSysProxyQueryUserName;
  @Getter
  private String chSysProxyQueryPassword;

  @Getter
  private String sysDbName = "bi_ch_system";

  @Resource
  private DbRouterClient dbRouterClient;

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig("fs-bi-warehouse", new IniChangeListener("common") {
      @Override
      public void iniChanged(IniConfig config) {
        chProxyQueryUserNamePre = config.get("chproxy.cal.user.name.pre");
        chProxyQueryPassword = PasswordUtil.decode(config.get("chproxy.cal.paasword"));
      }
    });

    ConfigFactory.getConfig("fs-bi-warehouse", new IniChangeListener("sys") {
      @Override
      public void iniChanged(IniConfig config) {
        chSysProxyQueryUserName = config.get("chproxy.sys.user.name", "");
        chSysProxyQueryPassword = PasswordUtil.decode(config.get("chproxy.sys.paasword", ""));
        sysDbName = config.get("sysDbName", "");
      }
    });
  }

  @Override
  public TenantContext get(String tenantId, boolean readOnly) {
    RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantId, BIZ, APPLICATION, DIALECT, false);
    boolean checkSystem = Objects.equals(SYS_TENANT, tenantId);
    String chDBName = checkSystem ? sysDbName : routerInfo.getDbName();
    String userName = checkSystem ? chSysProxyQueryUserName : chProxyQueryUserNamePre + chDBName.substring(0, chDBName.length() - 3);
    String paasWord = checkSystem ? chSysProxyQueryPassword : chProxyQueryPassword;
    String schemaName = "public";
    return TenantContext.builder()
                        .id(tenantId)
                        .schema(schemaName)
                        .url("jdbc:clickhouse://" + routerInfo.getMasterProxyUrl() + "/" + chDBName)
                        .username(userName)
                        .password(paasWord)
                        .build();
  }

  public RouterInfo getRouterInfo(String tenantId) {
    Preconditions.checkArgument(StringUtils.isNotBlank(tenantId), "tenantId is blank");
    try {
      RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantId, BIZ, APPLICATION, DIALECT);
      boolean isBIStandalone = routerInfo.getStandalone();
      routerInfo.setStandalone(isBIStandalone);
      return routerInfo;
    } catch (Throwable e) {
      log.warn("getRouterInfo error tenantID:{},error msg:{}", tenantId, e);
    }
    return null;
  }

  public String getCHJdbcURL(String tenantId) {
    RouterInfo routerInfo = this.getRouterInfo(tenantId);
    if (routerInfo != null) {
      return "jdbc:clickhouse://" + routerInfo.getMasterProxyUrl() + "/" + routerInfo.getDbName();
    }
    return null;
  }

  public String getChJdbcURL(RouterInfo routerInfo, boolean IsMaster) {
    if (IsMaster) {
      return "jdbc:clickhouse://" + routerInfo.getMasterProxyUrl() + "/" + routerInfo.getDbName();
    }
    return "jdbc:clickhouse://" + routerInfo.getSlaveProxyUrl() + "/" + routerInfo.getDbName();
  }
}
