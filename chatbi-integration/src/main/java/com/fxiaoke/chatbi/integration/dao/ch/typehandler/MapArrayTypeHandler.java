package com.fxiaoke.chatbi.integration.dao.ch.typehandler;

// 自定义类型处理器
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.*;
import java.util.List;
import java.util.Map;

public class MapArrayTypeHandler extends BaseTypeHandler<Map<String, List<String>>> {

  private static final ObjectMapper objectMapper = new ObjectMapper();

  @Override
  public void setNonNullParameter(PreparedStatement ps, int i, Map<String, List<String>> parameter, JdbcType jdbcType) throws SQLException {
    // 将 Map 转换为 JSON 字符串
    String json = convertMapToJson(parameter);
    json = json.replace("\"", "'"); // 将双引号替换为单引号
    ps.setString(i, json);
  }

  @Override
  public Map<String, List<String>> getNullableResult(ResultSet rs, String columnName) throws SQLException {
    Object object = rs.getObject(columnName);
    return (Map<String, List<String>>) object;
  }

  @Override
  public Map<String, List<String>> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
    String json = rs.getString(columnIndex);
    return convertJsonToMap(json);
  }

  @Override
  public Map<String, List<String>> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
    String json = cs.getString(columnIndex);
    return convertJsonToMap(json);
  }

  private String convertMapToJson(Map<String, List<String>> map) {
    try {
      return objectMapper.writeValueAsString(map);
    } catch (Exception e) {
      throw new RuntimeException("Failed to convert Map to JSON", e);
    }
  }

  private Map<String, List<String>> convertJsonToMap(String json) {
    if (json == null) {
      return null;
    }
    try {
      json = json.replace("'", "\""); // 将单引号替换为双引号
      return objectMapper.readValue(json, new TypeReference<Map<String, List<String>>>() {});
    } catch (Exception e) {
      throw new RuntimeException("Failed to convert JSON to Map", e);
    }
  }
}