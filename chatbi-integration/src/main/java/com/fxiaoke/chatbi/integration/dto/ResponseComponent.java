package com.fxiaoke.chatbi.integration.dto;


import com.fxiaoke.chatbi.common.model.response.ChartDataResponse;
import com.fxiaoke.chatbi.common.model.response.FollowUpResponse;
import com.fxiaoke.chatbi.common.model.response.InsightResponse;
import com.fxiaoke.chatbi.common.model.response.ReasoningResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 系统响应数据
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResponseComponent {

    /**
     * 推理分析组件
     */
    private ReasoningResponse reasoningResponse;

    /**
     * 图表数据组件
     */
    private ChartDataResponse chartDataResponse;

    /**
     * 数据洞察组件
     */
    private InsightResponse insightResponse;

    /**
     * 追问建议组件
     */
    private FollowUpResponse followUpResponse;
}
