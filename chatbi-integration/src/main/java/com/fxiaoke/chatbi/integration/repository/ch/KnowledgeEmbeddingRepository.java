package com.fxiaoke.chatbi.integration.repository.ch;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.knowledge.ChannelType;
import com.fxiaoke.chatbi.integration.model.ch.KnowledgeEmbedding;

import java.util.List;
import java.util.Map;

/**
 * 知识向量存储库接口
 * 定义知识向量存储和检索的核心方法
 */
public interface KnowledgeEmbeddingRepository {
    /**
     * 批量保存知识向量
     *
     * @param embeddings 知识向量对象列表
     * @param tenantId   租户ID
     */
    void saveKnowledgeEmbeddings(List<KnowledgeEmbedding> embeddings, String tenantId);

    /**
     * 搜索相似向量的知识标识
     *
     * @param queryEmbedding 查询向量
     * @param userIdentity   用户身份信息
     * @param channelType    数据源类型（租户库/系统库）
     * @param knowledgeType  知识类型
     * @param threshold      相似度阈值，可选
     * @param limit          返回结果数量限制，可选
     * @return 相似向量的知识标识列表
     */
    List<String> searchSimilarEmbeddingKnowledgeId(float[] queryEmbedding,
            UserIdentity userIdentity,
            ChannelType channelType,
            String knowledgeType,
            Double threshold,
            Integer limit);

    /**
     * 搜索相似向量的知识，并返回带有相似度分数的结果
     *
     * @param queryEmbedding 查询向量
     * @param userIdentity   用户身份信息
     * @param channelType    数据源类型（租户库/系统库）
     * @param knowledgeType  知识类型
     * @param threshold      相似度阈值，可选
     * @param limit          返回结果数量限制，可选
     * @return 带有相似度分数的知识向量对象列表
     */
    List<KnowledgeEmbedding> searchEmbeddingWithScores(float[] queryEmbedding,
            UserIdentity userIdentity,
            ChannelType channelType,
            String knowledgeType,
            Double threshold,
            Integer limit);

    /**
     * 根据知识标识获取所有特征向量
     *
     * @param userIdentity  用户身份信息
     * @param channelType   数据源类型
     * @param knowledgeType 知识类型
     * @param knowledgeId   知识标识
     * @return 知识向量列表
     */
    List<KnowledgeEmbedding> findByKnowledgeId(UserIdentity userIdentity,
            ChannelType channelType,
            String knowledgeType,
            String knowledgeId);

    /**
     * 批量查询多个知识标识的特征向量
     *
     * @param userIdentity  用户身份信息
     * @param channelType   数据源类型
     * @param knowledgeType 知识类型
     * @param knowledgeIds  知识标识列表
     * @return 知识标识到对应特征向量列表的映射
     */
    Map<String, List<KnowledgeEmbedding>> findByKnowledgeIdList(UserIdentity userIdentity,
            ChannelType channelType,
            String knowledgeType,
            List<String> knowledgeIds);

    /**
     * 根据租户ID和知识类型删除知识向量数据
     *
     * @param tenantId      租户ID
     * @param knowledgeType 知识类型
     */
    void deleteKnowledgeEmbedding(String tenantId, String knowledgeType, List<String> knowledgeIds);
  /**
   * 搜索单个向量的最相似知识对象，限定在指定的知识标识列表中
   *
   * @param queryEmbedding 查询向量
   * @param userIdentity   用户身份信息
   * @param channelType    数据源类型
   * @param knowledgeType  知识类型
   * @param knowledgeIds   知识ID列表(可选过滤条件)
   * @param threshold      相似度阈值
   * @return 最佳匹配的知识向量对象
   */
  KnowledgeEmbedding searchSimilarEmbeddingWithFilter(
      float[] queryEmbedding,
      UserIdentity userIdentity,
      ChannelType channelType,
      String knowledgeType,
      List<String> knowledgeIds,
      Double threshold);

  /**
   * 根据ID删除知识向量
   *
   * @param id       知识向量ID
   * @param tenantId 租户ID
   */
  void deleteById(String id, String tenantId);

  /**
   * 批量删除知识向量
   *
   * @param ids      知识向量ID列表
   * @param tenantId 租户ID
   */
  void batchDeleteByIds(List<String> ids, String tenantId);
}
