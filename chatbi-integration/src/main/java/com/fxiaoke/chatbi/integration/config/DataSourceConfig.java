package com.fxiaoke.chatbi.integration.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration;
import org.springframework.boot.actuate.autoconfigure.metrics.mongo.MongoMetricsAutoConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchDataAutoConfiguration;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportResource;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.DriverManagerDataSource;

import javax.sql.DataSource;

/**
 * 数据源配置类
 * 同时支持H2数据库和BI元数据数据源
 */
@Configuration
@ImportResource({"classpath:metadata-context/fs-bi-metadata-context.xml", "classpath:spring/applicationContext-datasource-clickhouse.xml"})
@EnableAutoConfiguration(exclude = {MongoAutoConfiguration.class, MongoDataAutoConfiguration.class, MongoMetricsAutoConfiguration.class, ElasticsearchRestClientAutoConfiguration.class, ElasticsearchDataAutoConfiguration.class, DataSourceHealthContributorAutoConfiguration.class})
public class DataSourceConfig {
  /**
   * 配置H2数据源为主数据源
   */
  @Primary
  @Bean(name = "h2DataSource")
  @ConfigurationProperties(prefix = "spring.datasource")
  public DataSource h2DataSource() {
    return new DriverManagerDataSource();
  }

  /**
   * 注入BI租户PG数据源（已通过ImportResource引入）
   * 便于在其他配置类中使用
   */
  @Autowired
  @Qualifier("biTenantDataSource")
  private DataSource biTenantDataSource;

  /**
   * 注入BI系统PG数据源（已通过ImportResource引入）
   */
  @Autowired
  @Qualifier("biSystemDataSource")
  private DataSource biSystemDataSource;
}