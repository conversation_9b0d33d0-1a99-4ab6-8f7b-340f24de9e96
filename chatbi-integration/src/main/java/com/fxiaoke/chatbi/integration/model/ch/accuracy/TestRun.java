package com.fxiaoke.chatbi.integration.model.ch.accuracy;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;

/**
 * 测试运行表
 * 对应ClickHouse的bi_test_run表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "bi_test_run")
public class TestRun {
    /**
     * 测试运行唯一标识
     */
    private String id;
    
    /**
     * 租户ID
     */
    private String tenantId;
    
    /**
     * 数据集ID
     */
    private String datasetId;
    
    /**
     * 数据集名称
     */
    private String datasetName;
    
    /**
     * BI Agent版本号
     */
    private String agentVersion;
    
    /**
     * 运行开始时间戳(毫秒)
     */
    private Long runTime;
    
    /**
     * 总测试用例数
     */
    private Integer totalCount;
    
    /**
     * 成功测试用例数
     */
    private Integer successCount;
    
    /**
     * 运行状态：RUNNING/COMPLETED/FAILED
     */
    private String runStatus;
    
    /**
     * 运行发起人ID
     */
    private String runBy;
    
    /**
     * 运行时长(秒)
     */
    private Integer runDuration;
    
    /**
     * 创建人ID
     */
    private String createdBy;
    
    /**
     * 创建时间戳(毫秒)
     */
    private Long createTime;
    
    /**
     * 扩展信息，JSON格式
     */
    private String extraInfo;
    
    /**
     * 删除状态：0-正常，-1/-2-已删除
     */
    private Integer isDeleted;
} 