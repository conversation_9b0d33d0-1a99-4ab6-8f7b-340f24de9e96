package com.fxiaoke.chatbi.integration.model.ch;

import com.fxiaoke.chatbi.common.model.enums.KnowledgeType;
import com.fxiaoke.chatbi.common.model.knowledge.Knowledge;
import com.github.mybatis.annotation.DynamicTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;
import java.util.List;

/**
 * 图表知识表
 * 对应ClickHouse的bi_chart_knowledge表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "bi_chart_knowledge")
public class ChartKnowledge implements Knowledge {
  /**
   * 租户ID
   */
  private String tenantId;

  /**
   * 图表ID
   */
  private String viewId;
  
  /**
   * 图表名称
   */
  private String viewName;

  /**
   * 图表类型
   */
  private String chartType;

  /**
   * 图表主题ID
   */
  private String schemaId;

  /**
   * 图表详细定义
   */
  private String spec;
  
  /**
   * 维度名称列表
   */
  @DynamicTypeHandler("com.github.mybatis.handler.list.ListTypeHandler")
  private List<String> dimensionNames;
  
  /**
   * 指标名称列表
   */
  @DynamicTypeHandler("com.github.mybatis.handler.list.ListTypeHandler")
  private List<String> measureNames;
  
  /**
   * 筛选条件名称列表
   */
  @DynamicTypeHandler("com.github.mybatis.handler.list.ListTypeHandler")
  private List<String> filterNames;
  
  /**
   * 字段ID列表
   * 存储图表涉及的所有字段ID
   */
  @DynamicTypeHandler("com.github.mybatis.handler.list.ListTypeHandler")
  private List<String> fieldIds;

  /**
   * 使用频率
   * 记录图表被使用的次数
   */
  private Integer usageCount;
  
  /**
   * 向量得分
   * 仅在搜索结果中有效，表示与查询向量的相似度得分
   * 非持久化字段，不存储到数据库
   */
  @Transient
  private Double vectorScore;

  /**
   * 关键词得分
   * 仅在搜索结果中有效，标识关键词匹配得分
   * 非持久化字段，不存储到数据库
   */
  @Transient
  private Double keyWordScore;

  /**
   * 最后修改时间
   * 记录图表最后一次被业务修改的时间
   */
  private Long lastModifiedTime;

  @Override
  public String getKnowledgeId() {
    return getViewId();
  }

  @Override
  public KnowledgeType getKnowledgeTypeEnum() {
    return KnowledgeType.CHART;
  }
}