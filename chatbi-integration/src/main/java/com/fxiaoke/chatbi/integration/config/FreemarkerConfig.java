package com.fxiaoke.chatbi.integration.config;

import freemarker.template.Configuration;
import freemarker.template.TemplateExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

@Component
public class FreemarkerConfig {

  @Bean
  @Primary
  public Configuration freemarkerConfiguration() {
    Configuration configuration = new Configuration(Configuration.VERSION_2_3_32);

    // 设置默认编码
    configuration.setDefaultEncoding("UTF-8");

    // 设置模板异常处理器
    configuration.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);

    // 设置数字格式
    configuration.setNumberFormat("0.##");

    return configuration;
  }
}