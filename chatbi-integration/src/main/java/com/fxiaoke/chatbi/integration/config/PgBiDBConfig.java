package com.fxiaoke.chatbi.integration.config;

import com.github.mybatis.spring.ScannerConfigurer;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ResourceLoaderAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * PostgreSQL数据库配置类
 * 用于配置PG数据源的SqlSessionFactory和Mapper扫描器
 */
@Configuration
public class PgBiDBConfig implements ResourceLoaderAware {

  private ResourceLoader resourceLoader;

  @Override
  public void setResourceLoader(ResourceLoader resourceLoader) {
    this.resourceLoader = resourceLoader;
  }

  /**
   * 配置PG数据源的SqlSessionFactory
   * 使用通过ImportResource注入的biTenantDataSource
   */
  @Bean("biPgSqlSessionFactoryBean")
  public SqlSessionFactoryBean sqlSessionFactoryBean(@Qualifier("biTenantDataSource") DataSource dataSource) throws Exception {
    SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
    sqlSessionFactoryBean.setDataSource(dataSource);
    sqlSessionFactoryBean.setTypeAliasesPackage("com.fxiaoke.chatbi.integration.model.pg");
    
    // 创建与CH相似的MyBatis配置，或者复用已有配置
    // 这里可以设置自定义的MyBatis配置文件，也可以直接使用CH的配置
    sqlSessionFactoryBean.setConfigLocation(resourceLoader.getResource("classpath:/mybatis/mybatis-config.xml"));
    
    // 设置PG的Mapper XML文件位置
    ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
    sqlSessionFactoryBean.setMapperLocations(resolver.getResources("classpath*:mybatis/mapper/pg/*.xml"));
    
    return sqlSessionFactoryBean;
  }

  /**
   * 配置PG数据源的Mapper扫描器
   */
  @Bean("pgScannerConfigurer")
  public ScannerConfigurer scannerConfigurer() {
    ScannerConfigurer scannerConfigurer = new ScannerConfigurer();
    // 设置PG Mapper接口所在的包
    scannerConfigurer.setBasePackage("com.fxiaoke.chatbi.integration.dao.pg");
    scannerConfigurer.setSqlSessionFactoryBeanName("biPgSqlSessionFactoryBean");
    return scannerConfigurer;
  }

  /**
   * 配置PG数据源的事务管理器
   */
  @Bean("biPgTxManager")
  public DataSourceTransactionManager dataSourceTransactionManager(@Qualifier("biTenantDataSource") DataSource dataSource) {
    DataSourceTransactionManager txManager = new DataSourceTransactionManager();
    txManager.setDataSource(dataSource);
    return txManager;
  }
} 