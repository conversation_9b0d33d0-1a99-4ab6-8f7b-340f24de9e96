package com.fxiaoke.chatbi.integration.model.ch.accuracy;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.util.List;

/**
 * 问答记录表
 * 对应ClickHouse的bi_query_log表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "bi_query_log")
public class QueryLog {
    /**
     * 问答记录唯一标识
     */
    private String id;
    
    /**
     * 链路追踪ID，用于跟踪整个请求处理流程
     */
    private String traceId;
    
    /**
     * 会话ID，用于关联同一会话中的多次交互
     */
    private String sessionId;
    
    /**
     * 租户ID
     */
    private String tenantId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 标签列表，包含响应类型标记、业务场景、质量标记等
     */
    private List<String> tags;
    
    /**
     * 用户问题文本
     */
    private String query;
    
    /**
     * 系统响应内容
     */
    private String response;
    
    /**
     * 响应组件信息，JSON格式，包含reasoning/insight/followup/charts等各部分内容
     */
    private String responseComponents;
    
    /**
     * 响应时间(毫秒)
     */
    private Long responseTime;
    
    /**
     * 是否启用上下文：1-启用，0-不启用
     */
    private Integer isContextEnabled = 1;
    
    /**
     * 来源：WEB/MOBILE/API/TEST等
     */
    private String source;
    
    /**
     * 用户反馈：1-点赞，-1-点踩，0-无反馈
     */
    private Integer feedback = 0;
    
    /**
     * 反馈备注
     */
    private String feedbackComment;
    
    /**
     * 是否已标注：1-已标注，0-未标注
     */
    private Integer isAnnotated = 0;
    
    /**
     * 问询时间戳(毫秒)
     */
    private Long queryTime;
    
    /**
     * 创建人ID
     */
    private String createdBy;
    
    /**
     * 创建时间戳(毫秒)
     */
    private Long createTime;
    
    /**
     * 问答过程中各个Action的日志信息，JSON格式，包含意图识别、知识检索、数据查询等步骤的日志
     */
    private String actionLogs;
    
    /**
     * 删除状态：0-正常，-1/-2-已删除
     */
    private Integer isDeleted;

    /**
     * 最后更新时间
     */
    private Long lastModifiedTime;
}
