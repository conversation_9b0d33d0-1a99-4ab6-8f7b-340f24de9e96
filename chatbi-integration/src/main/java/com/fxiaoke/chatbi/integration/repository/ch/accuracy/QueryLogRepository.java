package com.fxiaoke.chatbi.integration.repository.ch.accuracy;

import com.fxiaoke.chatbi.integration.dto.QueryLogDetailVO;
import com.fxiaoke.chatbi.integration.dto.QueryLogFilter;
import com.fxiaoke.chatbi.integration.dto.QueryLogListVO;
import com.fxiaoke.chatbi.integration.model.ch.accuracy.QueryLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Optional;

/**
 * 问答记录数据访问接口
 * 提供问答记录的查询和统计操作
 */
public interface QueryLogRepository {
    
    /**
     * 根据筛选条件分页查询问答记录
     *
     * @param filter 筛选条件Bean
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<QueryLog> findByConditions(QueryLogFilter filter, Pageable pageable);
    
    /**
     * 根据筛选条件分页查询问答记录（联表查询，直接返回VO）
     * 优化版本：避免Map转换和N+1查询，直接映射到VO对象
     *
     * @param filter 筛选条件Bean
     * @param pageable 分页参数
     * @return 分页结果（包含标注信息的VO）
     */
    Page<QueryLogListVO> findByConditionsWithAnnotationVO(QueryLogFilter filter, Pageable pageable);
    
    /**
     * 根据ID查询问答记录
     *
     * @param id 记录ID
     * @return 问答记录
     */
    Optional<QueryLog> findById(String id);
    
    /**
     * 根据ID查询问答记录详情（联表查询，直接返回VO）
     * 优化版本：避免N+1查询，直接映射到详情VO对象
     *
     * @param id 记录ID
     * @return 问答记录详情VO（包含标注信息）
     */
    Optional<QueryLogDetailVO> findDetailWithAnnotationById(String id);

    /**
     * 更新问答记录
     * 注意：数据存储在CH系统库，无需传递tenantId
     *
     * @param queryLog 问答记录对象
     * @return 更新成功返回true
     */
    boolean update(QueryLog queryLog);
    /**
     * 统计所有问答记录总数
     * 注意：数据存储在CH系统库，无需传递tenantId
     *
     * @return 记录总数
     */
    long countAll();

    /**
     * 统计所有已标注的问答记录数量
     * 注意：数据存储在CH系统库，无需传递tenantId
     *
     * @return 已标注记录数量
     */
    long countAnnotated();

    /**
     * 保存问答记录
     * chatbi-accuracy模块数据存储在CH系统库，无需传递tenantId
     *
     * @param queryLog 问答记录对象
     * @return 保存成功返回true
     */
    boolean save(QueryLog queryLog);

    /**
     * 根据Session ID查询所有问答记录
     *
     * @param sessionId 会话ID
     * @return 问答记录列表
     */
    java.util.List<QueryLog> findBySessionId(String sessionId);

    /**
     * 根据Session ID和租户ID查询所有问答记录
     * 注意：数据存储在CH系统库，tenantId仅作为查询条件
     *
     * @param sessionId 会话ID
     * @param tenantId 租户ID（查询条件）
     * @return 问答记录列表
     */
    java.util.List<QueryLog> findBySessionIdAndTenantId(String sessionId, String tenantId);

    /**
     * 根据租户ID分组查询Session列表
     * 注意：数据存储在CH系统库，tenantId仅作为查询条件
     *
     * @param tenantId 租户ID（查询条件）
     * @param offset 偏移量
     * @param limit 限制数量
     * @return Session信息列表
     */
    java.util.List<com.fxiaoke.chatbi.integration.model.ch.accuracy.SessionSummary> findSessionsByTenantId(String tenantId, int offset, int limit);

    /**
     * 统计租户下的Session数量
     * 注意：数据存储在CH系统库，tenantId仅作为查询条件
     *
     * @param tenantId 租户ID（查询条件）
     * @return Session数量
     */
    long countSessionsByTenantId(String tenantId);
}