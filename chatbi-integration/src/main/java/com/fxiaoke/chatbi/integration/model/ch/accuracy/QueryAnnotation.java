package com.fxiaoke.chatbi.integration.model.ch.accuracy;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.util.List;

/**
 * 问题级标注记录表
 * 对应ClickHouse的bi_query_annotation表
 * 重构后支持4个组件标注+轮次标注+附件
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "bi_query_annotation")
public class QueryAnnotation {
    /**
     * 标注记录唯一标识
     */
    private String id;

    /**
     * 关联的问答记录ID
     */
    private String requestId;

    /**
     * 关联的会话ID
     */
    private String sessionId;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 4个组件标注详情，JSON格式
     * {
     *   "reasoning": {"score": 3, "tags": ["推理准确"], "comments": "逻辑清晰"},
     *   "chartData": {"score": 4, "tags": ["数据准确"], "comments": "图表效果好"},
     *   "insight": {"score": 2, "tags": ["解读浅显"], "comments": "需更深入"},
     *   "followUp": {"score": 5, "tags": ["建议实用"], "comments": "很有价值"}
     * }
     */
    private String componentAnnotations;

    /**
     * 单轮回答整体评分：1-5分
     */
    private Integer roundScore;

    /**
     * 单轮标签：["回答质量好", "用户体验佳", "解决程度高"]
     */
    private List<String> roundTags;

    /**
     * 单轮回答的综合评价
     */
    private String roundComments;

    /**
     * 附件信息JSON
     * [{"id":"att_001", "filename":"screenshot.png", "url":"/uploads/xxx.png",
     *   "type":"screenshot", "description":"问题截图", "size":1024000, "hash":"md5hash"}]
     */
    private String attachments;

    /**
     * 标注人ID
     */
    private String createdBy;
    
    /**
     * 标注时间戳(毫秒)
     */
    private Long createTime;
    
    /**
     * 最后修改时间戳(毫秒)
     */
    private Long lastModifiedTime;
    
    /**
     * 删除状态：0-正常，1-已删除
     */
    private Integer isDeleted;
}
