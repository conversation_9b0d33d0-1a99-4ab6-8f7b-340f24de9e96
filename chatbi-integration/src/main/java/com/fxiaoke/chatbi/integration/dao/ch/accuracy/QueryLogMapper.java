package com.fxiaoke.chatbi.integration.dao.ch.accuracy;

import com.fxiaoke.chatbi.integration.dto.QueryLogDetailVO;
import com.fxiaoke.chatbi.integration.dto.QueryLogFilter;
import com.fxiaoke.chatbi.integration.dto.QueryLogListVO;
import com.fxiaoke.chatbi.integration.model.ch.accuracy.QueryLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.github.mybatis.mapper.ITenant;

import java.util.List;

/**
 * 问答记录数据访问Mapper
 * 基于MyBatis的ClickHouse数据访问
 */
@Mapper
public interface QueryLogMapper extends ITenant<QueryLogMapper> {

    /**
     * 根据筛选条件分页查询问答记录
     * 
     * @param filter 筛选条件Bean
     * @param offset 偏移量
     * @param limit  限制数量
     * @return 问答记录列表
     */
    List<QueryLog> findByConditions(@Param("filter") QueryLogFilter filter,
            @Param("offset") long offset,
            @Param("limit") int limit);

    /**
     * 根据筛选条件分页查询问答记录（联表查询，直接返回VO）
     * 优化版本：避免Map转换，直接映射到VO对象
     * 
     * @param filter 筛选条件Bean
     * @param offset 偏移量
     * @param limit  限制数量
     * @return 问答记录VO列表（包含标注信息）
     */
    List<QueryLogListVO> findByConditionsWithAnnotationVO(@Param("filter") QueryLogFilter filter,
            @Param("offset") long offset,
            @Param("limit") int limit);

    /**
     * 统计符合筛选条件的记录总数
     * 
     * @param filter 筛选条件Bean
     * @return 记录总数
     */
    long countByConditions(@Param("filter") QueryLogFilter filter);

    /**
     * 根据ID查询问答记录（ID唯一）
     */
    QueryLog findById(@Param("id") String id);

    /**
     * 根据ID查询问答记录详情（联表查询，直接返回VO）
     * 优化版本：避免N+1查询，直接映射到详情VO对象
     */
    QueryLogDetailVO findDetailWithAnnotationById(@Param("id") String id);

    /**
     * 更新问答记录（从对象中获取租户ID）
     */
    int updateQueryLog(@Param("queryLog") QueryLog queryLog);

    /**
     * 统计问答记录总数
     */
    long countAll();

    /**
     * 统计已标注的问答记录数量
     */
    long countAnnotated();

    /**
     * 保存问答记录
     *
     * @param queryLog 问答记录对象
     * @return 影响行数
     */
    int save(@Param("queryLog") QueryLog queryLog);

    /**
     * 根据Session ID查询所有问答记录
     */
    List<QueryLog> findBySessionId(@Param("sessionId") String sessionId);

    /**
     * 根据Session ID和租户ID查询所有问答记录
     */
    List<QueryLog> findBySessionIdAndTenantId(@Param("sessionId") String sessionId, @Param("tenantId") String tenantId);

    /**
     * 根据租户ID分组查询Session列表
     */
    List<com.fxiaoke.chatbi.integration.model.ch.accuracy.SessionSummary> findSessionsByTenantId(@Param("tenantId") String tenantId, @Param("offset") int offset, @Param("limit") int limit);

    /**
     * 统计租户下的Session数量
     */
    long countSessionsByTenantId(@Param("tenantId") String tenantId);
}