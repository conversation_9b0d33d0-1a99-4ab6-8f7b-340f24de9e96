package com.fxiaoke.chatbi.integration.dao.pg;

import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface BiMTTopologyTableMapper extends ITenant<BiMTTopologyTableMapper> {

  @Select("select source_id from bi_mt_topology_table where tenant_id= #{tenantId} and source=0 and is_deleted=0 and status in(0,1,2)")
  List<String> getNormalStatusSourceId(@Param("tenantId") String tenantId);
}
