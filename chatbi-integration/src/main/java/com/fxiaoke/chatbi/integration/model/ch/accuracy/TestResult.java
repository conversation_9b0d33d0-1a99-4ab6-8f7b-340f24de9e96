package com.fxiaoke.chatbi.integration.model.ch.accuracy;

import com.github.mybatis.annotation.DynamicTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.util.List;

/**
 * 测试结果表
 * 对应ClickHouse的bi_test_result表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "bi_test_result")
public class TestResult {
    /**
     * 测试结果唯一标识
     */
    private String id;
    
    /**
     * 租户ID
     */
    private String tenantId;
    
    /**
     * 测试用例ID
     */
    private String testCaseId;
    
    /**
     * 数据集ID
     */
    private String datasetId;
    
    /**
     * 运行ID
     */
    private String runId;
    
    /**
     * 运行时间戳(毫秒)
     */
    private Long runTime;
    
    /**
     * 代理版本
     */
    private String agentVersion;
    
    /**
     * 是否成功：1-成功，0-失败
     */
    private Integer isSuccess;
    
    /**
     * 实际识别的意图类型
     */
    private String actualIntent;
    
    /**
     * 实际提取的维度，JSON格式
     */
    private String actualDimensions;
    
    /**
     * 实际提取的指标，JSON格式
     */
    private String actualMeasures;
    
    /**
     * 实际提取的过滤条件，JSON格式
     */
    private String actualFilters;
    
    /**
     * 实际推荐的图表类型
     */
    private String actualChartType;
    
    /**
     * 实际召回的图表ID列表
     */
    @DynamicTypeHandler("com.github.mybatis.handler.list.ListTypeHandler")
    private List<String> recalledChartIds;
    
    /**
     * 各图表的评分卡详情，JSON格式
     */
    private String chartRankingScores;
    
    /**
     * 使用的召回渠道信息，JSON格式
     */
    private String recallChannels;
    
    /**
     * 响应时间(毫秒)
     */
    private Integer responseTime;
    
    /**
     * 错误类型：INTENT_ERROR/DIMENSION_ERROR/MEASURE_ERROR/CHART_MATCH_ERROR等
     */
    private String errorType;
    
    /**
     * 错误详情
     */
    private String errorDetails;
    
    /**
     * 创建人ID
     */
    private String createdBy;
    
    /**
     * 创建时间戳(毫秒)
     */
    private Long createTime;
    
    /**
     * 扩展信息，JSON格式
     */
    private String extraInfo;
    
    /**
     * 删除状态：0-正常，-1/-2-已删除
     */
    private Integer isDeleted;
}
