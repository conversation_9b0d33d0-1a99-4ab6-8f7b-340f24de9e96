package com.fxiaoke.chatbi.integration.utils;

import com.facishare.ai.api.model.Message;
import com.fxiaoke.chatbi.common.model.ChatMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 消息构建工具类
 * 集中管理消息构建相关的逻辑，避免代码重复
 */
@Slf4j
public class MessageBuilderUtil {

    /**
     * 创建系统消息
     * 
     * @param content 消息内容
     * @return 系统消息对象
     */
    public static Message createSystemMessage(String content) {
        Message message = new Message();
        message.setRole("system");
        message.setContent(content);
        return message;
    }
    
    /**
     * 创建用户消息
     * 
     * @param content 消息内容
     * @return 用户消息对象
     */
    public static Message createUserMessage(String content) {
        Message message = new Message();
        message.setRole("user");
        message.setContent(content);
        return message;
    }
    
    /**
     * 创建带有Question前缀的用户消息
     * 
     * @param content 消息内容
     * @return 用户消息对象
     */
    public static Message createUserQuestionMessage(String content) {
        Message message = new Message();
        message.setRole("user");
        message.setContent("Question:" + content);
        return message;
    }
    
    /**
     * 创建助手消息
     * 
     * @param content 消息内容
     * @return 助手消息对象
     */
    public static Message createAssistantMessage(String content) {
        Message message = new Message();
        message.setRole("assistant");
        message.setContent(content);
        return message;
    }
    
    /**
     * 格式化历史对话上下文
     * 
     * @param history 历史对话消息列表
     * @param maxMessages 最大历史消息数量
     * @return 格式化后的历史对话上下文
     */
    public static String formatHistoryContext(List<ChatMessage> history, int maxMessages) {
        if (history == null || history.isEmpty()) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        
        // 限制历史消息数量，只取最近的几条
        int startIndex = Math.max(0, history.size() - maxMessages);
        List<ChatMessage> recentHistory = history.subList(startIndex, history.size());
        
        for (ChatMessage msg : recentHistory) {
            String role = "system".equals(msg.getRole()) ? "系统" : 
                         "user".equals(msg.getRole()) ? "用户" : 
                         "assistant".equals(msg.getRole()) ? "助手" : msg.getRole();
            
            sb.append(role).append(": ").append(msg.getContent()).append("\n\n");
        }
        
        return sb.toString();
    }
    
    /**
     * 添加前缀消息
     * 
     * @param messages 消息列表
     * @param prefix 前缀
     * @param content 内容
     */
    public static void addPrefixedSystemMessage(List<Message> messages, String prefix, String content) {
        if (StringUtils.hasText(content)) {
            messages.add(createSystemMessage(prefix + content));
        }
    }
} 