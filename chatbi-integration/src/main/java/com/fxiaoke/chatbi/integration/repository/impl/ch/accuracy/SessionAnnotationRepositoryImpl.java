package com.fxiaoke.chatbi.integration.repository.impl.ch.accuracy;

import com.fxiaoke.chatbi.integration.dao.ch.accuracy.SessionAnnotationMapper;
import com.fxiaoke.chatbi.integration.model.ch.accuracy.SessionAnnotation;
import com.fxiaoke.chatbi.integration.repository.ch.accuracy.SessionAnnotationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * Session级标注数据访问实现类
 * 基于ClickHouse的Session级标注记录CRUD操作
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class SessionAnnotationRepositoryImpl implements SessionAnnotationRepository {

    private final SessionAnnotationMapper sessionAnnotationMapper;

    @Override
    public boolean save(SessionAnnotation annotation) {
        if (annotation == null || !StringUtils.hasText(annotation.getId())) {
            log.warn("保存参数无效: annotation={}", annotation);
            return false;
        }

        try {
            int result = sessionAnnotationMapper.setTenantId("-1").saveSessionAnnotation(annotation);
            log.info("保存Session级标注记录成功: id={}, sessionId={}, result={}",
                    annotation.getId(), annotation.getSessionId(), result);
            return result > 0;
        } catch (Exception e) {
            log.error("保存Session级标注记录失败: id={}, sessionId={}", 
                    annotation.getId(), annotation.getSessionId(), e);
            return false;
        }
    }

    @Override
    public boolean update(SessionAnnotation annotation) {
        if (annotation == null || !StringUtils.hasText(annotation.getId())) {
            log.warn("更新参数无效: annotation={}", annotation);
            return false;
        }

        try {
            int result = sessionAnnotationMapper.setTenantId("-1").updateSessionAnnotation(annotation);
            log.info("更新Session级标注记录成功: id={}, sessionId={}, result={}",
                    annotation.getId(), annotation.getSessionId(), result);
            return result > 0;
        } catch (Exception e) {
            log.error("更新Session级标注记录失败: id={}, sessionId={}", 
                    annotation.getId(), annotation.getSessionId(), e);
            return false;
        }
    }

    @Override
    public Optional<SessionAnnotation> findBySessionId(String sessionId) {
        if (!StringUtils.hasText(sessionId)) {
            log.warn("会话ID为空，无法执行查询: sessionId={}", sessionId);
            return Optional.empty();
        }

        try {
            SessionAnnotation annotation = sessionAnnotationMapper.setTenantId("-1").findBySessionId(sessionId);
            return Optional.ofNullable(annotation);
        } catch (Exception e) {
            log.error("根据sessionId查询Session级标注记录失败: sessionId={}", sessionId, e);
            return Optional.empty();
        }
    }

    @Override
    public Optional<SessionAnnotation> findBySessionIdAndTenantId(String sessionId, String tenantId) {
        if (!StringUtils.hasText(sessionId) || !StringUtils.hasText(tenantId)) {
            log.warn("参数为空，无法执行查询: sessionId={}, tenantId={}", sessionId, tenantId);
            return Optional.empty();
        }

        try {
            // chatbi-accuracy模块数据存储在CH系统库，统一使用tenantId="-1"
            SessionAnnotation annotation = sessionAnnotationMapper.setTenantId("-1")
                    .findBySessionIdAndTenantId(sessionId, tenantId);
            return Optional.ofNullable(annotation);
        } catch (Exception e) {
            log.error("根据sessionId和tenantId查询Session级标注记录失败: sessionId={}, tenantId={}", sessionId, tenantId, e);
            return Optional.empty();
        }
    }

    @Override
    public List<SessionAnnotation> findByTenantId(String tenantId, int offset, int limit) {
        if (!StringUtils.hasText(tenantId)) {
            log.warn("租户ID为空，无法执行查询: tenantId={}", tenantId);
            return Collections.emptyList();
        }

        try {
            // chatbi-accuracy模块数据存储在CH系统库，统一使用tenantId="-1"
            List<SessionAnnotation> annotations = sessionAnnotationMapper.setTenantId("-1")
                    .findByTenantId(tenantId, offset, limit);
            log.info("根据tenantId分页查询Session级标注记录成功: tenantId={}, offset={}, limit={}, count={}",
                    tenantId, offset, limit, annotations.size());
            return annotations != null ? annotations : Collections.emptyList();
        } catch (Exception e) {
            log.error("根据tenantId分页查询Session级标注记录失败: tenantId={}, offset={}, limit={}", tenantId, offset, limit, e);
            return Collections.emptyList();
        }
    }

    @Override
    public long countByTenantId(String tenantId) {
        if (!StringUtils.hasText(tenantId)) {
            log.warn("租户ID为空，无法执行统计: tenantId={}", tenantId);
            return 0;
        }

        try {
            // chatbi-accuracy模块数据存储在CH系统库，统一使用tenantId="-1"
            long count = sessionAnnotationMapper.setTenantId("-1").countByTenantId(tenantId);
            log.info("统计租户下Session级标注记录数量成功: tenantId={}, count={}", tenantId, count);
            return count;
        } catch (Exception e) {
            log.error("统计租户下Session级标注记录数量失败: tenantId={}", tenantId, e);
            return 0;
        }
    }

    @Override
    public boolean deleteById(String id) {
        if (!StringUtils.hasText(id)) {
            log.warn("ID为空，无法执行删除: id={}", id);
            return false;
        }

        try {
            int result = sessionAnnotationMapper.setTenantId("-1").deleteById(id);
            log.info("逻辑删除Session级标注记录成功: id={}, result={}", id, result);
            return result > 0;
        } catch (Exception e) {
            log.error("逻辑删除Session级标注记录失败: id={}", id, e);
            return false;
        }
    }
}
