package com.fxiaoke.chatbi.integration.model.ch;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 检索查询参数
 * 用于图表检索的查询条件封装
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RetrievalQuery {

    /**
     * 查询文本
     */
    private String queryText;

    /**
     * 结果数量限制
     */
    private int limit;

    /**
     * 相似度阈值
     * 低于此阈值的结果将被过滤
     */
    private double threshold;

    /**
     * 是否启用语义搜索
     */
    private boolean semanticSearchEnabled;

    /**
     * 图表类型过滤器
     * 限定返回指定类型的图表
     */
    private List<String> chartTypeFilters;
} 