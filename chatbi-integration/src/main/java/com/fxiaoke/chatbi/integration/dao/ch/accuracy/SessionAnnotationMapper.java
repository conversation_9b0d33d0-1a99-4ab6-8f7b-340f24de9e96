package com.fxiaoke.chatbi.integration.dao.ch.accuracy;

import com.fxiaoke.chatbi.integration.model.ch.accuracy.SessionAnnotation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.github.mybatis.mapper.ITenant;

import java.util.List;

/**
 * Session级标注记录数据访问Mapper
 * 基于MyBatis的ClickHouse数据访问
 */
@Mapper
public interface SessionAnnotationMapper extends ITenant<SessionAnnotationMapper> {

    /**
     * 保存Session级标注记录
     */
    int saveSessionAnnotation(@Param("annotation") SessionAnnotation annotation);

    /**
     * 更新Session级标注记录
     */
    int updateSessionAnnotation(@Param("annotation") SessionAnnotation annotation);

    /**
     * 根据Session ID查询标注记录
     */
    SessionAnnotation findBySessionId(@Param("sessionId") String sessionId);
    
    /**
     * 根据Session ID和租户ID查询标注记录
     */
    SessionAnnotation findBySessionIdAndTenantId(@Param("sessionId") String sessionId, @Param("tenantId") String tenantId);
    
    /**
     * 根据租户ID分页查询Session标注记录
     */
    List<SessionAnnotation> findByTenantId(@Param("tenantId") String tenantId, @Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 统计租户下的Session标注数量
     */
    long countByTenantId(@Param("tenantId") String tenantId);
    
    /**
     * 逻辑删除Session标注记录
     */
    int deleteById(@Param("id") String id);
}
