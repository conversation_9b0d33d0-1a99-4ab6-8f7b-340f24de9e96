package com.fxiaoke.chatbi.integration.utils;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.experimental.UtilityClass;

import java.util.Map;
import java.util.Objects;

@UtilityClass
public class HttpHeaderBuilder {

  public Map<String, String> constructHttpHeader(UserIdentity userIdentity) {
    Map<String, String> headerMap = Maps.newHashMap();
    headerMap.put("X-fs-Enterprise-Account", Objects.toString(userIdentity.getEa(), ""));
    headerMap.put("X-fs-Enterprise-Id", Objects.toString(userIdentity.getTenantId(), ""));
    headerMap.put("X-fs-Employee-Id", Objects.toString(userIdentity.getUserId(), ""));
    headerMap.put("X-fs-PermissionUser-Id", Objects.toString(userIdentity.getPermissionUserId(), ""));
    headerMap.put("x-fs-ei", Objects.toString(userIdentity.getTenantId(), ""));
    headerMap.put("x-fs-locale", userIdentity.getLocale());
    headerMap.put("x-fs-trace-id", "trace00001-test");

    if (userIdentity.hasOutEI()) {
      headerMap.put("x-out-link-type", "1");
      headerMap.put("x-out-tenant-id", Objects.toString(userIdentity.getOutEI(), ""));
      headerMap.put("x-out-user-id", Objects.toString(userIdentity.getOutUserId(), ""));
      headerMap.put("x-out-app-id", Objects.toString(userIdentity.getAppId(), ""));
      headerMap.put("X-fs-upstream-owner-id", Objects.toString(userIdentity.getUserId(), ""));
      headerMap.put("x-app-id", Objects.toString(userIdentity.getAppId(), ""));
      if (!Strings.isNullOrEmpty(userIdentity.getOutIdentifyType())) {
        headerMap.put("x-out-identity-type", userIdentity.getOutIdentifyType());
      }
    }
    return headerMap;
  }
}
