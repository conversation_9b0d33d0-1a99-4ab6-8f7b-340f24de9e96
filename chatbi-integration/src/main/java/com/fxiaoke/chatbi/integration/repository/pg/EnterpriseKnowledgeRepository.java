package com.fxiaoke.chatbi.integration.repository.pg;

import com.fxiaoke.chatbi.integration.model.pg.EnterpriseKnowledge;

import java.util.List;

/**
 * 企业知识库仓储接口
 * 定义企业知识库的核心操作方法
 */
public interface EnterpriseKnowledgeRepository {
    
    /**
     * 根据术语查询企业知识（支持源术语、目标术语和同义词）
     *
     * @param term 查询术语
     * @param tenantId 租户ID
     * @return 企业知识
     */
    EnterpriseKnowledge findByTerm(String term, String tenantId);
    
    /**
     * 查询租户所有未删除的知识条目
     *
     * @param tenantId 租户ID
     * @param isDeleted 删除状态（0表示未删除）
     * @return 知识条目列表
     */
    List<EnterpriseKnowledge> findAllByTenantIdAndIsDeleted(String tenantId, Integer isDeleted);
} 