package com.fxiaoke.chatbi.integration.repository.ch.accuracy;

import com.fxiaoke.chatbi.integration.model.ch.accuracy.QueryAnnotation;

import java.util.List;
import java.util.Optional;

/**
 * 问题级标注数据访问接口
 * 提供问题级标注记录的CRUD操作
 */
public interface QueryAnnotationRepository {
    
    /**
     * 保存问题级标注记录
     * 注意：数据存储在CH系统库，无需传递tenantId
     *
     * @param annotation 标注对象
     * @return 保存成功返回true
     */
    boolean save(QueryAnnotation annotation);

    /**
     * 更新问题级标注记录
     * 注意：数据存储在CH系统库，无需传递tenantId
     *
     * @param annotation 标注对象
     * @return 更新成功返回true
     */
    boolean update(QueryAnnotation annotation);
    
    /**
     * 根据请求ID查询标注记录
     *
     * @param requestId 请求ID
     * @return 标注记录
     */
    Optional<QueryAnnotation> findByRequestId(String requestId);
    
    /**
     * 根据Session ID查询所有标注记录
     *
     * @param sessionId 会话ID
     * @return 标注记录列表
     */
    List<QueryAnnotation> findBySessionId(String sessionId);
    
    /**
     * 根据Session ID和租户ID查询所有标注记录
     * 注意：数据存储在CH系统库，tenantId仅作为查询条件
     *
     * @param sessionId 会话ID
     * @param tenantId 租户ID（查询条件）
     * @return 标注记录列表
     */
    List<QueryAnnotation> findBySessionIdAndTenantId(String sessionId, String tenantId);
    
    /**
     * 删除标注记录（逻辑删除）
     *
     * @param id 标注记录ID
     * @return 删除成功返回true
     */
    boolean deleteById(String id);
}
