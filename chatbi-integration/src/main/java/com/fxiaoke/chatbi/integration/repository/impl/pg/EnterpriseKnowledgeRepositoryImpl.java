package com.fxiaoke.chatbi.integration.repository.impl.pg;

import com.fxiaoke.chatbi.integration.dao.pg.EnterpriseKnowledgeMapper;
import com.fxiaoke.chatbi.integration.model.pg.EnterpriseKnowledge;
import com.fxiaoke.chatbi.integration.repository.pg.EnterpriseKnowledgeRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

/**
 * 企业知识库仓储实现类
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class EnterpriseKnowledgeRepositoryImpl implements EnterpriseKnowledgeRepository {

  private final EnterpriseKnowledgeMapper enterpriseKnowledgeMapper;

  @Override
  public EnterpriseKnowledge findByTerm(String term, String tenantId) {
    if (StringUtils.isBlank(term) || StringUtils.isBlank(tenantId)) {
      return null;
    }

    try {
      return enterpriseKnowledgeMapper.setTenantId(tenantId).findByTerm(term, tenantId);
    } catch (Exception e) {
      log.error("查询企业知识失败: term={}", term, e);
      return null;
    }
  }

  @Override
  public List<EnterpriseKnowledge> findAllByTenantIdAndIsDeleted(String tenantId, Integer isDeleted) {
    if (StringUtils.isBlank(tenantId)) {
      return Collections.emptyList();
    }

    try {
      return enterpriseKnowledgeMapper.setTenantId(tenantId).findAllByTenantIdAndIsDeleted(tenantId, isDeleted);
    } catch (Exception e) {
      log.error("查询企业知识列表失败: tenantId={}, isDeleted={}", tenantId, isDeleted, e);
      return Collections.emptyList();
    }
  }
} 