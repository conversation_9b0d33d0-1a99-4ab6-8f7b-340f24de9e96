package com.fxiaoke.chatbi.integration.exception;

import lombok.Getter;

/**
 * LLM服务异常
 * 用于处理LLM服务调用过程中的异常情况
 */
public class LlmServiceException extends RuntimeException {

    /**
     * 通用错误码
     */
    public static final String ERROR_CODE_GENERAL = "LLM-0001";

    /**
     * 超时错误码
     */
    public static final String ERROR_CODE_TIMEOUT = "LLM-0002";

    /**
     * 配额超限错误码
     */
    public static final String ERROR_CODE_QUOTA_EXCEEDED = "LLM-0003";

    /**
     * 无效请求错误码
     */
    public static final String ERROR_CODE_INVALID_REQUEST = "LLM-0004";

    /**
     * 错误码
     */
    @Getter
    private final String errorCode;

    /**
     * 创建一个带有错误消息的异常
     */
    public LlmServiceException(String message) {
        this(message, ERROR_CODE_GENERAL);
    }

    /**
     * 创建一个带有错误消息和错误码的异常
     */
    public LlmServiceException(String message, String errorCode) {
        super(message);
        this.errorCode = errorCode;
    }

    /**
     * 创建一个带有错误消息、错误码和原因的异常
     */
    public LlmServiceException(String message, String errorCode, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    /**
     * 创建一个带有错误消息和原因的异常
     */
    public LlmServiceException(String message, Throwable cause) {
        this(message, ERROR_CODE_GENERAL, cause);
    }

    /**
     * 创建一个超时异常
     */
    public static LlmServiceException timeout() {
        return new LlmServiceException("LLM调用超时", ERROR_CODE_TIMEOUT);
    }

    /**
     * 创建一个配额超限异常
     */
    public static LlmServiceException quotaExceeded() {
        return new LlmServiceException("LLM调用配额超限", ERROR_CODE_QUOTA_EXCEEDED);
    }

    /**
     * 创建一个无效请求异常
     */
    public static LlmServiceException invalidRequest(String reason) {
        return new LlmServiceException("无效的LLM请求: " + reason, ERROR_CODE_INVALID_REQUEST);
    }
} 