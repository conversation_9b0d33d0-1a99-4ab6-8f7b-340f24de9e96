package com.fxiaoke.chatbi.integration.dao.ch.typehandler;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.Array;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * 浮点数数组类型处理器
 * 用于处理ClickHouse中的Array(Float32)类型与Java float[]之间的转换
 */
public class FloatArrayTypeHandler extends BaseTypeHandler<float[]> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, float[] parameter, JdbcType jdbcType) throws SQLException {
        // 通常不会直接使用，因为在ClickHouse中我们是直接构造SQL字符串
        throw new UnsupportedOperationException("直接设置参数不支持，请使用字符串表示的向量数据");
    }

    @Override
    public float[] getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return convertToFloatArray(rs.getArray(columnName));
    }

    @Override
    public float[] getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return convertToFloatArray(rs.getArray(columnIndex));
    }

    @Override
    public float[] getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return convertToFloatArray(cs.getArray(columnIndex));
    }

    /**
     * 将SQL Array转换为float[]
     */
    private float[] convertToFloatArray(Array array) throws SQLException {
        if (array == null) {
            return new float[0];
        }
        
//        Object[] objArray = (Object[]) array.getArray();
//        float[] result = new float[objArray.length];
//
//        for (int i = 0; i < objArray.length; i++) {
//            if (objArray[i] instanceof Number) {
//                result[i] = ((Number) objArray[i]).floatValue();
//            }
//        }
        float[] result = (float[]) array.getArray(); // 直接转换为 float[]


        return result;
    }
} 