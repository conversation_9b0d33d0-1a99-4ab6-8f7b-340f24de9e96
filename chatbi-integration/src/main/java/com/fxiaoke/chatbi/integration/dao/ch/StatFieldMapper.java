package com.fxiaoke.chatbi.integration.dao.ch;

import com.fxiaoke.chatbi.integration.model.ch.StatField;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface StatFieldMapper extends ITenant<StatFieldMapper> {

  @Select("select field_id, field_name, agg_dim_type, tenant_id from stat_field where tenant_id = #{tenantId} and status in (1, 2, 6) and agg_dim_type <> '' and is_deleted = 0 and bi_sys_is_deleted = 0 and db_field_name not in ('data_auth_code','area_full_name','out_data_auth_code','out_tenant_id','out_data_auth_id', 'dimension_d1','dimension_d2','dimension_d3', 'id')")
  List<StatField> getKnowledgeStatFields(@Param("tenantId") String tenantId);


  @Select("select field_id, field_name, agg_dim_type, tenant_id, udf_field_id from stat_field where tenant_id = #{tenantId} and field_id = #{fieldId} and is_deleted = 0")
  List<StatField> getStatFieldByFieldId(@Param("tenantId") String tenantId, @Param("fieldId") String fieldId);

  /**
   * 根据主题ID和候选字段ID列表查询字段ID到字段名的映射
   * 如果candidateIds不为空，则只返回candidateIds中包含的字段
   * 最多返回一个结果
   * 
   * @param schemaId 主题ID
   * @param candidateIds 候选字段ID列表
   * @param tenantId 租户ID
   * @return 字段列表（最多一条记录）
   */
  @Select("<script>"
      + "SELECT field_id, field_name, agg_dim_type, tenant_id "
      + "FROM stat_field "
      + "WHERE tenant_id = #{tenantId} "
      + "AND status IN (1, 2, 6) "
      + "AND agg_dim_type &lt;&gt; '' "
      + "AND is_deleted = 0 "
      + "AND bi_sys_is_deleted = 0 "
      + "AND schema_id = #{schemaId} "
      + "<if test='candidateIds != null and candidateIds.size() > 0'>"
      + "  AND field_id IN "
      + "  <foreach item='item' collection='candidateIds' open='(' separator=',' close=')'>"
      + "    #{item}"
      + "  </foreach>"
      + "</if>"
      + "</script>")
  List<StatField> getFieldsBySchemaIdAndCandidateIds(
          @Param("schemaId") String schemaId,
          @Param("candidateIds") List<String> candidateIds,
          @Param("tenantId") String tenantId);
}
