package com.fxiaoke.chatbi.integration.client.fsbi;
 
import com.alibaba.fastjson.JSONObject;
import com.facishare.rest.core.codec.DefaultRestCodec;
import com.facishare.rest.core.codec.IRestCodeC;
import com.facishare.rest.core.exception.RestProxyRuntimeException;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.chatbi.common.model.dto.BaseResult;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.HttpStatus;

import java.net.URLDecoder;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.nio.charset.StandardCharsets.UTF_8;


/**
 * 实现结果集解析接口，将异常信息补充到config中下发给前端展示
 */
public class ChartConfigRestCodeC implements IRestCodeC {

    private final String X_FS_FAIL_MESSAGE = "x-fs-fail-message";

    @Override
    public <T> byte[] encodeArg(T obj) {
        // 可以选择重用 DefaultRestCodec 的实现
        return DefaultRestCodec.instance.encodeArg(obj);
    }

    @Override
    public <T> T decodeResult(int statusCode, Map<String, List<String>> headers, byte[] bytes, Class<T> clazz) {
        try {
            String bodyString = new String(bytes, UTF_8);

            // 检查状态码
            if (statusCode >= HttpStatus.SC_MULTIPLE_CHOICES) {
                throw new RestProxyRuntimeException(statusCode, bodyString);
            }

            // 处理 x-fs-fail-message 头
            List<String> failMessages = headers.get(X_FS_FAIL_MESSAGE);
            if (CollectionUtils.isNotEmpty(failMessages)) {
                String message = failMessages.stream()
                        .map(s -> URLDecoder.decode(s, UTF_8))
                        .collect(Collectors.joining(","));
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("tips", message);
                BaseResult baseResult = new BaseResult();
                baseResult.setResult(jsonObject);
                return (T) baseResult;
            }

            // 处理正常的解码逻辑
            if (clazz == String.class) {
                return (T) bodyString;
            }
            if (clazz == void.class) {
                return null;
            }

            return JsonUtil.fromJson(new String(bytes, UTF_8), clazz);
        } catch (Exception e) {
            // 处理异常并调用默认解码逻辑
            return DefaultRestCodec.instance.decodeResult(statusCode, headers, bytes, clazz);
        }
    }
}