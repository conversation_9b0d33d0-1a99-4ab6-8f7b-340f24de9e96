package com.fxiaoke.chatbi.integration.utils;
import com.vladsch.flexmark.ext.tables.TablesExtension;
import com.vladsch.flexmark.ext.toc.TocExtension;
import com.vladsch.flexmark.html.HtmlRenderer;
import com.vladsch.flexmark.parser.Parser;
import com.vladsch.flexmark.util.ast.Document;
import com.vladsch.flexmark.util.data.MutableDataSet;
import com.vladsch.flexmark.util.misc.Extension;
import org.elasticsearch.common.Strings;

import java.util.Arrays;

public class MarkdownUtil {
    private static MutableDataSet options = new MutableDataSet();
    private static Parser parser;
    private static HtmlRenderer renderer;
    static {
        options.set(Parser.EXTENSIONS, Arrays.<Extension>asList(TocExtension.create(), TablesExtension.create()));
        parser = Parser.builder(options).build();
        renderer = HtmlRenderer.builder(options).build();
    }
 
    public static String markdownToHtml(String md) {
        if(Strings.isEmpty(md)){
            return null;
        }
        Document document = parser.parse(md);
        return renderer.render(document);
    }
}
