package com.fxiaoke.chatbi.integration.repository.impl.ch;

import com.fxiaoke.chatbi.common.config.EmbeddingProperties;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.knowledge.ChannelType;
import com.fxiaoke.chatbi.integration.dao.ch.KnowledgeEmbeddingMapper;
import com.fxiaoke.chatbi.integration.model.ch.KnowledgeEmbedding;
import com.fxiaoke.chatbi.integration.repository.ch.KnowledgeEmbeddingRepository;
import com.fxiaoke.chatbi.integration.util.VectorUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 知识向量存储库实现
 * 基于ClickHouse的知识向量存储和检索实现
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class KnowledgeEmbeddingRepositoryImpl implements KnowledgeEmbeddingRepository {
    private final KnowledgeEmbeddingMapper knowledgeEmbeddingMapper;
    private final EmbeddingProperties embeddingProperties;

    @Override
    public void saveKnowledgeEmbeddings(List<KnowledgeEmbedding> embeddings, String tenantId) {
        if (CollectionUtils.isEmpty(embeddings)) {
            return;
        }
        try {
            // 直接使用批量插入
            knowledgeEmbeddingMapper.setTenantId(tenantId).batchSaveKnowledgeEmbeddings(embeddings);
            log.info("成功批量保存知识特征向量: 数量={}", embeddings.size());
        } catch (Exception e) {
            log.error("批量保存知识特征向量失败", e);
            throw e;
        }
    }

    @Override
    public List<String> searchSimilarEmbeddingKnowledgeId(float[] queryEmbedding,
            UserIdentity userIdentity,
            ChannelType channelType,
            String knowledgeType,
            Double threshold,
            Integer limit) {
        String tenantId = VectorUtil.getTenantId(userIdentity, channelType);
        if (queryEmbedding == null || queryEmbedding.length == 0 || StringUtils.isBlank(tenantId)) {
            log.warn("搜索相似向量失败: 无效的参数 queryEmbedding={}, tenantId={}, knowledgeType={}",
                    queryEmbedding, tenantId, knowledgeType);
            return Collections.emptyList();
        }

        try {
            String embeddingString = VectorUtil.floatArrayToString(queryEmbedding);
            double thresholdValue = threshold != null ? threshold : embeddingProperties.getSimilarityThreshold();
            int limitValue = limit != null ? limit : embeddingProperties.getDefaultSearchLimit();

            return knowledgeEmbeddingMapper.setTenantId(tenantId)
                    .searchSimilarEmbeddingKnowledgeId(
                            tenantId,
                            embeddingString,
                            knowledgeType,
                            thresholdValue,
                            limitValue);
        } catch (Exception e) {
            log.error("搜索相似向量失败: tenantId={}, knowledgeType={}", tenantId, knowledgeType, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<KnowledgeEmbedding> searchEmbeddingWithScores(float[] queryEmbedding,
            UserIdentity userIdentity,
            ChannelType channelType,
            String knowledgeType,
            Double threshold,
            Integer limit) {
        String tenantId = VectorUtil.getTenantId(userIdentity, channelType);
        if (queryEmbedding == null || queryEmbedding.length == 0 || StringUtils.isBlank(tenantId)) {
            log.warn("带分数搜索相似向量失败: 无效的参数 queryEmbedding={}, tenantId={}, knowledgeType={}",
                    queryEmbedding, tenantId, knowledgeType);
            return Collections.emptyList();
        }

        try {
            String embeddingString = VectorUtil.floatArrayToString(queryEmbedding);
            double thresholdValue = threshold != null ? threshold : embeddingProperties.getSimilarityThreshold();
            int limitValue = limit != null ? limit : embeddingProperties.getDefaultSearchLimit();

            return knowledgeEmbeddingMapper.setTenantId(tenantId)
                    .searchEmbeddingWithScores(
                            tenantId,
                            embeddingString,
                            knowledgeType,
                            thresholdValue,
                            limitValue);
        } catch (Exception e) {
            log.error("带分数搜索相似向量失败: tenantId={}, knowledgeType={}", tenantId, knowledgeType, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<KnowledgeEmbedding> findByKnowledgeId(UserIdentity userIdentity,
            ChannelType channelType,
            String knowledgeType,
            String knowledgeId) {
        String tenantId = VectorUtil.getTenantId(userIdentity, channelType);
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(knowledgeType)
                || StringUtils.isBlank(knowledgeId)) {
            log.warn("获取知识向量失败: 参数无效 tenantId={}, knowledgeType={}, knowledgeId={}",
                    tenantId, knowledgeType, knowledgeId);
            return Collections.emptyList();
        }

        try {
            return knowledgeEmbeddingMapper.setTenantId(tenantId)
                    .findByKnowledgeId(tenantId, knowledgeType, knowledgeId);
        } catch (Exception e) {
            log.error("获取知识向量失败: tenantId={}, knowledgeType={}, knowledgeId={}",
                    tenantId, knowledgeType, knowledgeId, e);
            return Collections.emptyList();
        }
    }

    @Override
    public Map<String, List<KnowledgeEmbedding>> findByKnowledgeIdList(UserIdentity userIdentity,
            ChannelType channelType,
            String knowledgeType,
            List<String> knowledgeIds) {
        String tenantId = VectorUtil.getTenantId(userIdentity, channelType);
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(knowledgeType)
                || CollectionUtils.isEmpty(knowledgeIds)) {
            log.warn("批量获取知识向量失败: 参数无效 tenantId={}, knowledgeType={}, knowledgeIds={}",
                    tenantId, knowledgeType, knowledgeIds);
            return Collections.emptyMap();
        }

        try {
            // 批量查询
            List<KnowledgeEmbedding> allEmbeddings = knowledgeEmbeddingMapper.setTenantId(tenantId)
                    .findByKnowledgeIdList(tenantId, knowledgeType, knowledgeIds);

            if (CollectionUtils.isEmpty(allEmbeddings)) {
                return Collections.emptyMap();
            }

            // 按知识标识分组
            return allEmbeddings.stream()
                    .collect(Collectors.groupingBy(KnowledgeEmbedding::getKnowledgeId));
        } catch (Exception e) {
            log.error("批量获取知识向量失败: tenantId={}, knowledgeType={}, knowledgeIds.size={}",
                    tenantId, knowledgeType, knowledgeIds.size(), e);
            return Collections.emptyMap();
        }
    }

    /**
     * 搜索单个向量的最相似知识对象，限定在指定的知识标识列表中
     */
    @Override
    public KnowledgeEmbedding searchSimilarEmbeddingWithFilter(
            float[] queryEmbedding,
            UserIdentity userIdentity,
            ChannelType channelType,
            String knowledgeType,
            List<String> knowledgeIds,
            Double threshold) {

        String tenantId = VectorUtil.getTenantId(userIdentity, channelType);
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(knowledgeType) || queryEmbedding == null) {
            log.warn("搜索相似向量失败: 参数无效 tenantId={}, knowledgeType={}, queryEmbedding={}",
                    tenantId, knowledgeType, queryEmbedding);
            return null;
        }

        try {
            String embeddingString = VectorUtil.floatArrayToString(queryEmbedding);
            double searchThreshold = threshold != null ? threshold : embeddingProperties.getSimilarityThreshold();

            return knowledgeEmbeddingMapper.setTenantId(tenantId)
                    .searchEmbeddingsWithScores(
                            tenantId,
                            embeddingString,
                            knowledgeType,
                            knowledgeIds,
                            searchThreshold);

        } catch (Exception e) {
            log.error("搜索相似向量失败: tenantId={}, knowledgeType={}, knowledgeIds.size={}",
                    tenantId, knowledgeType, knowledgeIds != null ? knowledgeIds.size() : 0,e);
            return null;
        }
    }

    @Override
    public void deleteKnowledgeEmbedding(String tenantId, String knowledgeType, List<String> knowledgeIds) {
        if (StringUtils.isBlank(tenantId)) {
            log.warn("tenantId is blank, skip delete operation");
            return;
        }
        try {
            int affectedRows = knowledgeEmbeddingMapper.setTenantId(tenantId).deleteKnowledgeEmbeddings(tenantId, knowledgeType, knowledgeIds);
            log.info("Successfully deleted knowledge embeddings for tenant: {}, knowledgeType: {}, affected rows: {}",
                    tenantId, knowledgeType, affectedRows);
        } catch (Exception e) {
            log.error("Failed to delete knowledge embeddings for tenant: {}, knowledgeType: {}", tenantId,
                    knowledgeType, e);
            throw e;
        }
    }

  @Override
  public void deleteById(String id, String tenantId) {
    if (StringUtils.isBlank(id) || StringUtils.isBlank(tenantId)) {
      log.warn("删除知识向量失败: 参数无效 id={}, tenantId={}", id, tenantId);
      return;
    }

    try {
      int affected = knowledgeEmbeddingMapper.setTenantId(tenantId).deleteById(tenantId, id);
      log.info("删除知识向量成功: id={}, tenantId={}, affected={}", id, tenantId, affected);
    } catch (Exception e) {
      log.error("删除知识向量失败: id={}, tenantId={}", id, tenantId, e);
      throw e;
    }
  }

  @Override
  public void batchDeleteByIds(List<String> ids, String tenantId) {
    if (CollectionUtils.isEmpty(ids) || StringUtils.isBlank(tenantId)) {
      log.warn("批量删除知识向量失败: 参数无效 ids={}, tenantId={}", ids, tenantId);
      return;
    }

    try {
      int affected = knowledgeEmbeddingMapper.setTenantId(tenantId).batchDeleteByIds(tenantId, ids);
      log.info("批量删除知识向量成功: ids.size={}, tenantId={}, affected={}", ids.size(), tenantId, affected);
    } catch (Exception e) {
      log.error("批量删除知识向量失败: ids.size={}, tenantId={}", ids.size(), tenantId, e);
      throw e;
    }
  }
}

