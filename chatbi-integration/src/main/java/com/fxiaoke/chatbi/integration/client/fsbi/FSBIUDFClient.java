package com.fxiaoke.chatbi.integration.client.fsbi;

import com.facishare.bi.common.entities.QueryReportDataArg;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;
import com.fxiaoke.chatbi.common.model.dto.SaleProcessAnalysisResult;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@RestResource(value = "BI-UDF-REPORT", desc = "fs-bi的服务", contentType = "application/json")
public interface FSBIUDFClient {
  @POST(value = "/saleProcessAnalysisController/queryReportData", desc = "查询销售阶段转化分析报表")
  SaleProcessAnalysisResult queryReportData(@Body QueryReportDataArg udfQueryReportDataArg, @HeaderMap Map<String, String> var);

}