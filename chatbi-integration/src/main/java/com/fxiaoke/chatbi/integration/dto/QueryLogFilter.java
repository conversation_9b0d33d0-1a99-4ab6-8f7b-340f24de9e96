package com.fxiaoke.chatbi.integration.dto;

import lombok.Data;
import lombok.Builder;
import org.springframework.util.StringUtils;

/**
 * 问答记录筛选条件
 * Integration模块的筛选条件Bean，避免模块间循环依赖
 */
@Data
@Builder
public class QueryLogFilter {
    
    /**
     * 租户ID
     */
    private String tenantId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 来源类型
     */
    private String source;
    
    /**
     * 开始时间戳（毫秒）
     */
    private Long startTime;
    
    /**
     * 结束时间戳（毫秒）
     */
    private Long endTime;
    
    /**
     * 问题关键词搜索
     */
    private String question;
    
    /**
     * 用户反馈：1-点赞，-1-点踩，0-无反馈
     */
    private Integer feedback;
    
    /**
     * 标注状态：0-未标注，1-已标注
     */
    private Integer isAnnotated;
    
    /**
     * 检查租户ID是否有效
     */
    public boolean hasTenantId() {
        return StringUtils.hasText(tenantId);
    }
    
    /**
     * 检查用户ID是否有效
     */
    public boolean hasUserId() {
        return StringUtils.hasText(userId);
    }
    
    /**
     * 检查来源是否有效
     */
    public boolean hasSource() {
        return StringUtils.hasText(source);
    }
    
    /**
     * 检查是否有时间范围筛选
     */
    public boolean hasTimeRange() {
        return startTime != null || endTime != null;
    }
    
    /**
     * 检查是否有问题关键词搜索
     */
    public boolean hasQuestion() {
        return StringUtils.hasText(question);
    }
    
    /**
     * 检查是否有反馈筛选
     */
    public boolean hasFeedback() {
        return feedback != null;
    }
    
    /**
     * 检查是否有标注状态筛选
     */
    public boolean hasAnnotationStatus() {
        return isAnnotated != null;
    }
    
    /**
     * 检查是否有有效的筛选条件
     */
    public boolean hasFilters() {
        return hasTenantId() || hasUserId() || hasSource() || 
               hasTimeRange() || hasQuestion() || hasFeedback() || hasAnnotationStatus();
    }
} 