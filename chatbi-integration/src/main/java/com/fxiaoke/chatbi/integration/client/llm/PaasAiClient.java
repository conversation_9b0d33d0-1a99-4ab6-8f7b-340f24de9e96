package com.fxiaoke.chatbi.integration.client.llm;

import com.facishare.ai.api.dto.BaseResult;
import com.facishare.ai.api.dto.OpenAIEmbeddings;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

/**
 * FS-BI系统客户端接口
 * 提供与FS-BI系统交互的API
 */
@RestResource(value = "PAAS-AI", desc = "paas-ai的服务", contentType = "application/json")
public interface PaasAiClient {
  @POST(value = "/v1/openai/embeddings", desc = "自定义统计图查询")
  BaseResult embeddings(@Body OpenAIEmbeddings.Arg arg, @HeaderMap Map<String, String> headers);
} 