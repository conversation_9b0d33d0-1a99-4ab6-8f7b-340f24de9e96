package com.fxiaoke.chatbi.integration.dao.ch.accuracy;

import com.fxiaoke.chatbi.integration.model.ch.accuracy.QueryAnnotation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.github.mybatis.mapper.ITenant;

import java.util.List;

/**
 * 问题级标注记录数据访问Mapper
 * 基于MyBatis的ClickHouse数据访问
 */
@Mapper
public interface QueryAnnotationMapper extends ITenant<QueryAnnotationMapper> {

    /**
     * 保存问题级标注记录
     */
    int saveQueryAnnotation(@Param("annotation") QueryAnnotation annotation);

    /**
     * 更新问题级标注记录
     */
    int updateQueryAnnotation(@Param("annotation") QueryAnnotation annotation);

    /**
     * 根据请求ID查询标注记录
     */
    QueryAnnotation findByRequestId(@Param("requestId") String requestId);
    
    /**
     * 根据Session ID查询所有标注记录
     */
    List<QueryAnnotation> findBySessionId(@Param("sessionId") String sessionId);
    
    /**
     * 根据Session ID和租户ID查询所有标注记录
     */
    List<QueryAnnotation> findBySessionIdAndTenantId(@Param("sessionId") String sessionId, @Param("tenantId") String tenantId);
    
    /**
     * 逻辑删除标注记录
     */
    int deleteById(@Param("id") String id);
}
