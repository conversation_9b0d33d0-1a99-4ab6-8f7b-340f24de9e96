package com.fxiaoke.chatbi.integration.client.fsbi;

import com.facishare.bi.common.entities.*;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;
import com.fxiaoke.chatbi.common.model.dto.BaseResult;
import com.fxiaoke.chatbi.common.model.dto.ChartConfigArg;
import com.fxiaoke.chatbi.common.model.dto.ObjectRelationByObjNameResult;
import com.fxiaoke.chatbi.common.model.dto.ViewDataQueryArg;

import java.util.Map;

/**
 * FS-BI系统客户端接口
 * 提供与FS-BI系统交互的API
 */
@RestResource(value = "FS-BI", desc = "fs-bi的服务", contentType = "application/json")
public interface FSBIClient {
  /**
   * 查询自定义统计图数据
   * 
   * @param arg 查询参数
   * @param headers HTTP请求头
   * @return 查询结果
   */
  @POST(value = "/api/v1/stat/data/query", desc = "自定义统计图查询")
  BaseResult queryChartData(@Body ViewDataQueryArg arg, @HeaderMap Map<String, String> headers);

  /**
   * 查询自定义统计图配置
   * 
   * @param arg 查询参数
   * @param headers HTTP请求头
   * @return 配置查询结果
   */
  @POST(value = "/api/v1/stat/chartConfig/query",codec = "com.fxiaoke.chatbi.integration.client.fsbi.ChartConfigRestCodeC", desc = "自定义统计图配置查询")
  BaseResult queryChartConfig(@Body ChartConfigArg arg, @HeaderMap Map<String, String> headers);

  /**
   * 获取自定义统计图的数据过滤范围
   * 
   * @param arg 查询参数
   * @param headers HTTP请求头
   * @return 过滤结果
   */
  @POST(value = "/stat/filters/getFiltersResult", desc = "获取自定义统计图的数据范围")
  StatFilterResult queryFiltersResult(@Body StatFilterArg arg, @HeaderMap Map<String, String> headers);

  @POST(value = "stat/objRelationByObjName/query", desc = "获取左侧列表")
  ObjectRelationByObjNameResult getObjRelationByObjNameResult(@Body QueryChartDataArg arg, @HeaderMap Map<String, String> headers);


} 