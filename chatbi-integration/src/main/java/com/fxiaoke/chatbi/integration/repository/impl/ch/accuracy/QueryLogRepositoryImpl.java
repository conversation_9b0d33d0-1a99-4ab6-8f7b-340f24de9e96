package com.fxiaoke.chatbi.integration.repository.impl.ch.accuracy;

import com.fxiaoke.chatbi.integration.dao.ch.accuracy.QueryLogMapper;
import com.fxiaoke.chatbi.integration.dto.QueryLogDetailVO;
import com.fxiaoke.chatbi.integration.dto.QueryLogFilter;
import com.fxiaoke.chatbi.integration.dto.QueryLogListVO;
import com.fxiaoke.chatbi.integration.model.ch.accuracy.QueryLog;
import com.fxiaoke.chatbi.integration.repository.ch.accuracy.QueryLogRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 问答记录数据访问实现类
 * 基于ClickHouse的问答记录查询和统计操作
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class QueryLogRepositoryImpl implements QueryLogRepository {
    
    private final QueryLogMapper queryLogMapper;

    @Override
    public Page<QueryLog> findByConditions(QueryLogFilter filter, Pageable pageable) {
        try {
            log.info("开始分页查询问答记录: 筛选条件={}, page={}, size={}", 
                    filter, pageable.getPageNumber(), pageable.getPageSize());
            
            // 查询总数
            long total = queryLogMapper.setTenantId("-1").countByConditions(filter);
            
            if (total == 0) {
                return new PageImpl<>(Collections.emptyList(), pageable, 0);
            }
            
            // 直接查询QueryLog对象
            List<QueryLog> content = queryLogMapper.setTenantId("-1").findByConditions(
                    filter, pageable.getOffset(), pageable.getPageSize());
            
            log.info("成功查询问答记录: 总数={}, 当前页数量={}", total, content.size());
            return new PageImpl<>(content, pageable, total);
        } catch (Exception e) {
            log.error("分页查询问答记录失败: 筛选条件={}", filter, e);
            return new PageImpl<>(Collections.emptyList(), pageable, 0);
        }
    }
    
    @Override
    public Page<QueryLogListVO> findByConditionsWithAnnotationVO(QueryLogFilter filter, Pageable pageable) {
        try {
            log.info("开始优化联表分页查询问答记录VO: 筛选条件={}, page={}, size={}", 
                    filter, pageable.getPageNumber(), pageable.getPageSize());
            
            // 查询总数
            long total = queryLogMapper.setTenantId("-1").countByConditions(filter);
            
            if (total == 0) {
                return new PageImpl<>(Collections.emptyList(), pageable, 0);
            }
            
            // 直接查询VO对象，避免Map转换
            List<QueryLogListVO> content = queryLogMapper.setTenantId("-1").findByConditionsWithAnnotationVO(
                    filter, (int)pageable.getOffset(), pageable.getPageSize());
            
            log.info("成功优化联表查询问答记录VO: 总数={}, 当前页数量={}", total, content.size());
            return new PageImpl<>(content, pageable, total);
        } catch (Exception e) {
            log.error("优化联表分页查询问答记录VO失败: 筛选条件={}", filter, e);
            return new PageImpl<>(Collections.emptyList(), pageable, 0);
        }
    }

    @Override
    public Optional<QueryLog> findById(String id) {
        if (!StringUtils.hasText(id)) {
            log.warn("记录ID为空，无法执行查询: id={}", id);
            return Optional.empty();
        }
        
        try {
            QueryLog queryLog = queryLogMapper.setTenantId("-1").findById(id);
            return Optional.ofNullable(queryLog);
        } catch (Exception e) {
            log.error("根据ID查询问答记录失败: id={}", id, e);
            return Optional.empty();
        }
    }

    @Override
    public boolean update(QueryLog queryLog) {
        try {
            queryLogMapper.setTenantId("-1").updateQueryLog(queryLog);
            return true;
        } catch (Exception e) {
            log.error("更新问答记录失败: id={}", queryLog.getId(), e);
            return false;
        }
    }

    @Override
    public long countAll() {
        try {
            return queryLogMapper.setTenantId("-1").countAll();
        } catch (Exception e) {
            log.error("统计所有问答记录数量失败", e);
            return 0L;
        }
    }
    
    @Override
    public long countAnnotated() {
        return queryLogMapper.setTenantId("-1").countAnnotated();
    }

    @Override
    public Optional<QueryLogDetailVO> findDetailWithAnnotationById(String id) {
        if (!StringUtils.hasText(id)) {
            log.warn("记录ID为空，无法执行详情查询: id={}", id);
            return Optional.empty();
        }
        
        try {
            log.info("开始联表查询问答记录详情: id={}", id);
            QueryLogDetailVO detailVO = queryLogMapper.setTenantId("-1").findDetailWithAnnotationById(id);
            log.info("联表查询问答记录详情完成: id={}, found={}", id, detailVO != null);
            return Optional.ofNullable(detailVO);
        } catch (Exception e) {
            log.error("联表查询问答记录详情失败: id={}", id, e);
            return Optional.empty();
        }
    }

    @Override
    public boolean save(QueryLog queryLog) {
        try {
            log.info("开始保存问答记录: id={}", queryLog.getId());
            // chatbi-accuracy模块数据存储在CH系统库，统一使用tenantId="-1"
            int rows = queryLogMapper.setTenantId("-1").save(queryLog);
            boolean success = rows > 0;
            log.info("保存问答记录完成: id={}, success={}", queryLog.getId(), success);
            return success;
        } catch (Exception e) {
            log.error("保存问答记录失败: id={}", queryLog.getId(), e);
            return false;
        }
    }

    @Override
    public List<QueryLog> findBySessionId(String sessionId) {
        if (!StringUtils.hasText(sessionId)) {
            log.warn("会话ID为空，无法执行查询: sessionId={}", sessionId);
            return Collections.emptyList();
        }

        try {
            List<QueryLog> queryLogs = queryLogMapper.setTenantId("-1").findBySessionId(sessionId);
            log.info("根据sessionId查询问答记录成功: sessionId={}, count={}", sessionId, queryLogs.size());
            return queryLogs != null ? queryLogs : Collections.emptyList();
        } catch (Exception e) {
            log.error("根据sessionId查询问答记录失败: sessionId={}", sessionId, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<QueryLog> findBySessionIdAndTenantId(String sessionId, String tenantId) {
        if (!StringUtils.hasText(sessionId) || !StringUtils.hasText(tenantId)) {
            log.warn("参数为空，无法执行查询: sessionId={}, tenantId={}", sessionId, tenantId);
            return Collections.emptyList();
        }

        try {
            // chatbi-accuracy模块数据存储在CH系统库，统一使用tenantId="-1"
            List<QueryLog> queryLogs = queryLogMapper.setTenantId("-1")
                    .findBySessionIdAndTenantId(sessionId, tenantId);
            log.info("根据sessionId和tenantId查询问答记录成功: sessionId={}, tenantId={}, count={}",
                    sessionId, tenantId, queryLogs.size());
            return queryLogs != null ? queryLogs : Collections.emptyList();
        } catch (Exception e) {
            log.error("根据sessionId和tenantId查询问答记录失败: sessionId={}, tenantId={}", sessionId, tenantId, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<com.fxiaoke.chatbi.integration.model.ch.accuracy.SessionSummary> findSessionsByTenantId(String tenantId, int offset, int limit) {
        if (!StringUtils.hasText(tenantId)) {
            log.warn("租户ID为空，无法执行查询: tenantId={}", tenantId);
            return Collections.emptyList();
        }

        try {
            // chatbi-accuracy模块数据存储在CH系统库，统一使用tenantId="-1"
            List<com.fxiaoke.chatbi.integration.model.ch.accuracy.SessionSummary> sessions = queryLogMapper.setTenantId("-1")
                    .findSessionsByTenantId(tenantId, offset, limit);
            log.info("根据tenantId分页查询Session列表成功: tenantId={}, offset={}, limit={}, count={}",
                    tenantId, offset, limit, sessions.size());
            return sessions != null ? sessions : Collections.emptyList();
        } catch (Exception e) {
            log.error("根据tenantId分页查询Session列表失败: tenantId={}, offset={}, limit={}", tenantId, offset, limit, e);
            return Collections.emptyList();
        }
    }

    @Override
    public long countSessionsByTenantId(String tenantId) {
        if (!StringUtils.hasText(tenantId)) {
            log.warn("租户ID为空，无法执行统计: tenantId={}", tenantId);
            return 0;
        }

        try {
            // chatbi-accuracy模块数据存储在CH系统库，统一使用tenantId="-1"
            long count = queryLogMapper.setTenantId("-1").countSessionsByTenantId(tenantId);
            log.info("统计租户下Session数量成功: tenantId={}, count={}", tenantId, count);
            return count;
        } catch (Exception e) {
            log.error("统计租户下Session数量失败: tenantId={}", tenantId, e);
            return 0;
        }
    }

}