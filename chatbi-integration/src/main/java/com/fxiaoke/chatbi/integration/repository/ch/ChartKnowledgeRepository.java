package com.fxiaoke.chatbi.integration.repository.ch;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.knowledge.ChannelType;
import com.fxiaoke.chatbi.integration.model.ch.ChartKnowledge;

import java.util.List;

/**
 * 图表知识存储库接口
 * 定义图表知识的存储和检索的核心方法
 */
public interface ChartKnowledgeRepository {
    /**
     * 批量保存图表知识
     *
     * @param chartKnowledgeList 图表知识对象列表
     * @param tenantId           租户ID
     */
    void saveChartKnowledgeList(List<ChartKnowledge> chartKnowledgeList, String tenantId);

    /**
     * 直接根据字段ID列表检索图表
     * 检索条件为：图表的字段ID列表包含至少一个指定的字段ID
     *
     * @param fieldIds    字段ID列表
     * @param tenantId    租户ID
     * @param channelType 数据源类型（租户库/系统库）
     * @return 图表ID列表
     */
    List<String> searchChartsByFieldIds(List<String> fieldIds, String tenantId, ChannelType channelType);

    /**
     * 根据租户ID查询所有图表知识
     *
     * @param tenantId 租户ID
     * @return 图表知识列表
     */
    List<ChartKnowledge> findAllByTenant(String tenantId);

    /**
     * 根据图表ID列表批量获取图表知识
     *
     * @param viewIds      图表ID列表
     * @param userIdentity 用户身份信息
     * @param channelType  数据源类型
     * @return 图表知识列表
     */
    List<ChartKnowledge> batchGetByViewIds(List<String> viewIds, UserIdentity userIdentity, ChannelType channelType);

    /**
     * 根据关键词搜索图表
     * 搜索图表的keywords字段，使用LIKE匹配
     *
     * @param keyword     关键词
     * @param tenantId    租户ID
     * @param channelType 数据源类型
     * @return 匹配的图表ID列表
     */
    List<String> searchChartsByKeyword(String keyword, String tenantId, ChannelType channelType);

    /**
     * 根据分类关键词搜索图表
     * 分别在三个不同列中搜索对应的关键词
     *
     * @param dimensionKeyword 维度关键词
     * @param measureKeyword   指标关键词
     * @param filterKeyword    筛选条件关键词
     * @param tenantId         租户ID
     * @param channelType      数据源类型
     * @return 匹配的图表ID列表
     */
    List<String> searchChartsByCategorizedKeyword(
            String dimensionKeyword,
            String measureKeyword,
            String filterKeyword,
            String tenantId,
            ChannelType channelType);

    /**
     * 根据分类关键词搜索图表 同时
     * 分别在三个不同列中搜索对应的关键词
     *
     * @param dimensionKeyword 维度关键词
     * @param measureKeyword   指标关键词
     * @param filterKeyword    筛选条件关键词
     * @param tenantId         租户ID
     * @param channelType      数据源类型
     * @return 匹配的图表ID列表
     */
    List<ChartKnowledge> selectChartsByKeywordWithScores(
            List<String> dimensionKeyword,
            List<String> measureKeyword,
            List<String> filterKeyword,
            String tenantId,
            ChannelType channelType);

    /**
     * 根据租户ID和视图ID列表删除图表知识数据
     *
     * @param tenantId 租户ID
     * @param viewIds  视图ID列表
     */
    void deleteByTenantIdAndViewIds(String tenantId, List<String> viewIds);
}