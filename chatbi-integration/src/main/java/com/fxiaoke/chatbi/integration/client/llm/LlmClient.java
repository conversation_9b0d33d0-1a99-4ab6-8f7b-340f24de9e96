package com.fxiaoke.chatbi.integration.client.llm;

import com.alibaba.fastjson2.JSON;
import com.facishare.ai.api.dto.BaseArgument;
import com.facishare.ai.api.dto.OpenAIChatComplete;
import com.facishare.ai.api.model.Message;
import com.facishare.ai.api.model.service.FsAI;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fxiaoke.chatbi.common.config.KnowledgeProperties;
import com.fxiaoke.chatbi.common.model.action.ActionContext;
import com.fxiaoke.chatbi.integration.exception.LlmServiceException;
import com.fxiaoke.chatbi.integration.utils.LLMResponseParser;
import com.github.autoconf.ConfigFactory;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.StringReader;
import java.io.StringWriter;
import java.util.*;

/**
 * LLM客户端
 * 负责与大语言模型的交互
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LlmClient {

    private final KnowledgeProperties knowledgeProperties;
    private final Configuration freemarkerConfig;
    private static String llmTenantId;
    public static Map<String, Map<String, Object>> chartType2LayoutMap;


  @PostConstruct
    public void init() {
        try {
            ConfigFactory.getConfig("fs-bi-common", iConfig -> llmTenantId = iConfig.get("llmTenantId", "82313"));
        } catch (Exception e) {
            log.error("Failed to initialize llmTenantId", e);
            llmTenantId = "82313";
        }

        try {
            ConfigFactory.getConfig("fs-bi-view-layout", iConfig -> {
                try {
                    chartType2LayoutMap = parseConfig(iConfig.getString());
                } catch (Exception e) {
                    log.error("Failed to parse layout config", e);
                    chartType2LayoutMap = new HashMap<>();
                }
            });
        } catch (Exception e) {
            log.error("Failed to initialize fs-bi-view-layout", e);
        }
    }

    public static Map<String, Map<String, Object>> parseConfig(String json) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readValue(json, new TypeReference<>() {});
    }

    /**
     * 执行LLM调用
     *
     * @param prompt 提示词
     * @param context 执行上下文
     * @return 模型回复
     * @throws LlmServiceException 调用异常
     */
    public String chat(String prompt, ActionContext context) throws LlmServiceException {
        if (context.isTimeout()) {
            throw LlmServiceException.timeout();
        }

        String llmModel = getLlmModel(context);
        log.info("Calling LLM model {} with prompt: {}", llmModel, prompt);

        try {
            String requestId = context.getSessionId();
            log.info("请求ID: {} - 开始调用LLM API, 模型: {}", requestId, llmModel);
            
            // 设置上下文
            BaseArgument baseContext = new BaseArgument();
            baseContext.setBusiness("BI");
            if (context.getUserIdentity() != null) {
                baseContext.setTenantId(context.getUserIdentity().getTenantId());
                baseContext.setUserId(context.getUserIdentity().getUserId());
            }
            if(Objects.nonNull(context.getUserIdentity()) && Objects.equals(context.getUserIdentity().getTenantId(), "-1")) {
                baseContext.setTenantId(llmTenantId);
            }
            
            // 构建消息
            List<Message> messages = new ArrayList<>();
            Message message = new Message();
            message.setRole("user");
            message.setContent(prompt);
            messages.add(message);
            
            // 构建请求参数
            OpenAIChatComplete.Arg arg = new OpenAIChatComplete.Arg();
            arg.setMessages(messages);
            arg.setModel(llmModel);
            
            // 记录请求内容
            log.debug("请求ID: {} - 模型输入提示词: {}", requestId, JSON.toJSONString(messages));
            
            // 执行调用
            long start = System.currentTimeMillis();
            OpenAIChatComplete.Result result = FsAI.openai().chatComplete(baseContext, arg);
            long duration = System.currentTimeMillis() - start;

            // 记录响应
            log.info("请求ID: {} - 模型响应耗时: {}ms", requestId, duration);
            log.debug("请求ID: {} - 模型响应内容: {}", requestId, result.getMessage());

            
            
            return result.getMessage();
        } catch (Exception e) {
            log.error("Failed to call LLM", e);
            throw new LlmServiceException("Failed to call LLM", e);
        }
    }
    
    /**
     * 使用FreeMarker模板执行调用
     *
     * @param templateContent FreeMarker模板内容
     * @param variables 模板变量
     * @param context 执行上下文
     * @return 模型回复
     * @throws LlmServiceException 调用异常
     */
    public String chatWithTemplate(String templateContent, Map<String, Object> variables, ActionContext context) throws LlmServiceException {
        try {
            // 创建模板
            Template template = new Template("dynamicTemplate", new StringReader(templateContent), freemarkerConfig);
            
            // 处理模板
            StringWriter writer = new StringWriter();
            template.process(variables, writer);
            String prompt = writer.toString();
            
            return chat(prompt, context);
        } catch (IOException | TemplateException e) {
            log.error("Failed to process FreeMarker template", e);
            throw new LlmServiceException("Failed to process template", e);
        }
    }
    
    /**
     * 使用FreeMarker模板执行调用并返回指定类型对象
     *
     * @param templateContent FreeMarker模板内容
     * @param variables 模板变量
     * @param context 执行上下文
     * @param responseType 期望返回的对象类型
     * @return 指定类型的对象
     * @throws LlmServiceException 调用异常
     */
    public <T> T chatWithTemplate(String templateContent, Map<String, Object> variables, ActionContext context, Class<T> responseType) throws LlmServiceException {
        String response = chatWithTemplate(templateContent, variables, context);
        try {
            // 使用 LLMResponseParser 提取和解析 JSON
            T result = LLMResponseParser.parseJson(response, responseType);
            if (result == null) {
                log.error("Failed to parse LLM response to type {}, response: {}", responseType.getSimpleName(), response);
                throw new LlmServiceException("Failed to parse response to " + responseType.getSimpleName());
            }
            return result;
        } catch (Exception e) {
            log.error("Failed to parse LLM response to type {}: {}", responseType.getSimpleName(), response, e);
            throw new LlmServiceException("Failed to parse response to " + responseType.getSimpleName(), e);
        }
    }
    
    /**
     * 获取要使用的LLM模型
     * 优先从上下文获取,如果没有则使用默认配置
     */
    private String getLlmModel(ActionContext context) {
        String model = context.getLlmModel();
        return model != null ? model : knowledgeProperties.getLlmModel();
    }
} 