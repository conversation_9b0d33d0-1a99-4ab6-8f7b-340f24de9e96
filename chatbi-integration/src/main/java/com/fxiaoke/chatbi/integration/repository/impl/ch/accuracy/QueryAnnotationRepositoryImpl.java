package com.fxiaoke.chatbi.integration.repository.impl.ch.accuracy;

import com.fxiaoke.chatbi.integration.dao.ch.accuracy.QueryAnnotationMapper;
import com.fxiaoke.chatbi.integration.model.ch.accuracy.QueryAnnotation;
import com.fxiaoke.chatbi.integration.repository.ch.accuracy.QueryAnnotationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 问题级标注数据访问实现类
 * 基于ClickHouse的问题级标注记录CRUD操作
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class QueryAnnotationRepositoryImpl implements QueryAnnotationRepository {

    private final QueryAnnotationMapper queryAnnotationMapper;

    @Override
    public boolean save(QueryAnnotation annotation) {
        if (annotation == null || !StringUtils.hasText(annotation.getId())) {
            log.warn("保存参数无效: annotation={}", annotation);
            return false;
        }

        try {
            int result = queryAnnotationMapper.setTenantId("-1").saveQueryAnnotation(annotation);
            log.info("保存问题级标注记录成功: id={}, requestId={}, sessionId={}, result={}",
                    annotation.getId(), annotation.getRequestId(), annotation.getSessionId(), result);
            return result > 0;
        } catch (Exception e) {
            log.error("保存问题级标注记录失败: id={}, requestId={}, sessionId={}", 
                    annotation.getId(), annotation.getRequestId(), annotation.getSessionId(), e);
            return false;
        }
    }

    @Override
    public boolean update(QueryAnnotation annotation) {
        if (annotation == null || !StringUtils.hasText(annotation.getId())) {
            log.warn("更新参数无效: annotation={}", annotation);
            return false;
        }

        try {
            int result = queryAnnotationMapper.setTenantId("-1").updateQueryAnnotation(annotation);
            log.info("更新问题级标注记录成功: id={}, requestId={}, sessionId={}, result={}",
                    annotation.getId(), annotation.getRequestId(), annotation.getSessionId(), result);
            return result > 0;
        } catch (Exception e) {
            log.error("更新问题级标注记录失败: id={}, requestId={}, sessionId={}", 
                    annotation.getId(), annotation.getRequestId(), annotation.getSessionId(), e);
            return false;
        }
    }

    @Override
    public Optional<QueryAnnotation> findByRequestId(String requestId) {
        if (!StringUtils.hasText(requestId)) {
            log.warn("请求ID为空，无法执行查询: requestId={}", requestId);
            return Optional.empty();
        }

        try {
            QueryAnnotation annotation = queryAnnotationMapper.setTenantId("-1").findByRequestId(requestId);
            return Optional.ofNullable(annotation);
        } catch (Exception e) {
            log.error("根据requestId查询问题级标注记录失败: requestId={}", requestId, e);
            return Optional.empty();
        }
    }

    @Override
    public List<QueryAnnotation> findBySessionId(String sessionId) {
        if (!StringUtils.hasText(sessionId)) {
            log.warn("会话ID为空，无法执行查询: sessionId={}", sessionId);
            return Collections.emptyList();
        }

        try {
            List<QueryAnnotation> annotations = queryAnnotationMapper.setTenantId("-1").findBySessionId(sessionId);
            log.info("根据sessionId查询问题级标注记录成功: sessionId={}, count={}", sessionId, annotations.size());
            return annotations != null ? annotations : Collections.emptyList();
        } catch (Exception e) {
            log.error("根据sessionId查询问题级标注记录失败: sessionId={}", sessionId, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<QueryAnnotation> findBySessionIdAndTenantId(String sessionId, String tenantId) {
        if (!StringUtils.hasText(sessionId) || !StringUtils.hasText(tenantId)) {
            log.warn("参数为空，无法执行查询: sessionId={}, tenantId={}", sessionId, tenantId);
            return Collections.emptyList();
        }

        try {
            // chatbi-accuracy模块数据存储在CH系统库，统一使用tenantId="-1"
            List<QueryAnnotation> annotations = queryAnnotationMapper.setTenantId("-1")
                    .findBySessionIdAndTenantId(sessionId, tenantId);
            log.info("根据sessionId和tenantId查询问题级标注记录成功: sessionId={}, tenantId={}, count={}",
                    sessionId, tenantId, annotations.size());
            return annotations != null ? annotations : Collections.emptyList();
        } catch (Exception e) {
            log.error("根据sessionId和tenantId查询问题级标注记录失败: sessionId={}, tenantId={}", sessionId, tenantId, e);
            return Collections.emptyList();
        }
    }

    @Override
    public boolean deleteById(String id) {
        if (!StringUtils.hasText(id)) {
            log.warn("ID为空，无法执行删除: id={}", id);
            return false;
        }

        try {
            int result = queryAnnotationMapper.setTenantId("-1").deleteById(id);
            log.info("逻辑删除问题级标注记录成功: id={}, result={}", id, result);
            return result > 0;
        } catch (Exception e) {
            log.error("逻辑删除问题级标注记录失败: id={}", id, e);
            return false;
        }
    }
}
