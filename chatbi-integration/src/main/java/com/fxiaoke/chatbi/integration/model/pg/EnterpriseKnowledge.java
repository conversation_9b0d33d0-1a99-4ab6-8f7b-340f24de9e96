package com.fxiaoke.chatbi.integration.model.pg;

import com.fxiaoke.chatbi.common.model.enums.KnowledgeType;
import com.fxiaoke.chatbi.common.model.knowledge.Knowledge;
import lombok.Data;

import javax.persistence.Table;

/**
 * 企业知识库实体类
 */
@Data
@Table(name = "bi_enterprise_knowledge")
public class EnterpriseKnowledge implements Knowledge {
  private String id;                // varchar(100) NOT NULL
  private String tenantId;          // varchar(32) NOT NULL
  private String knowledgeType;     // varchar(50) NOT NULL
  private String sourceTerm;        // varchar(200) NOT NULL
  private String targetTerm;        // varchar(200) NOT NULL
  private String synonymTerm;       // varchar(500)
  private String config;            // text
  private String description;       // text
  private String createdBy;         // varchar(32)
  private Long createTime;          // bigint NOT NULL
  private Long lastModifiedTime;    // bigint NOT NULL
  private Long sysModifiedTime;     // bigint
  private Integer isDeleted;        // integer

  @Override
  public String getKnowledgeId() {
    return id;
  }

  @Override
  public KnowledgeType getKnowledgeTypeEnum() {
    return KnowledgeType.getByCode(knowledgeType);
  }
}