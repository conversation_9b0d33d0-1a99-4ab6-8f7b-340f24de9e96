<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.chatbi.integration.dao.pg.EnterpriseKnowledgeMapper">
    
    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, tenant_id, knowledge_type, source_term, target_term, synonym_term, config, 
        description, created_by, create_time, last_modified_time, sys_modified_time, is_deleted
    </sql>

    <!-- 根据术语查询（支持源术语和同义词） -->
    <select id="findByTerm" resultType="com.fxiaoke.chatbi.integration.model.pg.EnterpriseKnowledge">
        SELECT <include refid="Base_Column_List"/>
        FROM bi_enterprise_knowledge
        WHERE tenant_id = #{tenantId}
        AND is_deleted = 0
        AND (
            source_term = #{term}
            OR synonym_term LIKE CONCAT('%', #{term}, '%')
        )
        LIMIT 1
    </select>
    
    <!-- 根据租户ID和删除状态查询所有企业知识 -->
    <select id="findAllByTenantIdAndIsDeleted" resultType="com.fxiaoke.chatbi.integration.model.pg.EnterpriseKnowledge">
        SELECT <include refid="Base_Column_List"/>
        FROM bi_enterprise_knowledge
        WHERE tenant_id = #{tenantId}
        AND is_deleted = #{isDeleted}
        ORDER BY create_time DESC
    </select>

</mapper> 