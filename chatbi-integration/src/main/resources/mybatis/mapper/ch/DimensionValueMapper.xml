<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.chatbi.integration.dao.ch.DimensionValueMapper">

  <resultMap id="DimensionValueFullMap" type="com.fxiaoke.chatbi.integration.model.ch.DimensionValueFull">
    <id property="dimensionId" column="dimension_id"/>
    <result property="fieldId" column="field_id"/>
    <result property="value" column="value"/>
    <result property="displayValue" column="display_value"/>
    <result property="tenantId" column="tenant_id"/>
    <result property="weight" column="weight"/>
    <result property="isDeleted" column="is_deleted"/>
  </resultMap>

  <!-- 根据多个关键词模糊匹配维度值 -->
  <select id="searchByKeywords" resultMap="DimensionValueFullMap">
    SELECT
    dimension_id,
    field_id,
    value,
    display_value,
    tenant_id,
    weight,
    is_deleted
    FROM dim_value_full
    WHERE
    tenant_id = #{tenantId}
    AND is_deleted = 0
    AND dimension_id = #{dimensionId}
    AND (
    <foreach collection="keywords" item="keyword" separator=" OR ">
      display_value LIKE CONCAT('%', #{keyword}, '%')
    </foreach>
    )
    ORDER BY weight DESC
    LIMIT #{limit}
  </select>

  <!-- 根据多个关键词直接查询不重复的字段ID集合 -->
  <select id="searchFieldIdsByKeywords" resultType="java.lang.String">
    SELECT DISTINCT
    field_id
    FROM dim_value_full
    WHERE
    tenant_id = #{tenantId}
    AND is_deleted = 0
    AND field_id IS NOT NULL
    AND field_id != ''
    AND (
    <foreach collection="keywords" item="keyword" separator=" OR ">
      display_value LIKE CONCAT('%', #{keyword}, '%')
    </foreach>
    )
  </select>
  
  <!-- 根据值列表精确匹配维度值 -->
  <select id="exactMatchByValues" resultMap="DimensionValueFullMap">
    SELECT
    dimension_id,
    field_id,
    value,
    display_value,
    tenant_id,
    weight,
    is_deleted
    FROM dim_value_full
    WHERE
    tenant_id = #{tenantId}
    AND is_deleted = 0
    AND field_id IS NOT NULL
    AND field_id != ''
    AND (
    <foreach collection="values" item="value" separator=" OR ">
      display_value LIKE CONCAT('%', #{value}, '%')
    </foreach>
    )
    ORDER BY weight DESC
    <if test="limit > 0">
      LIMIT #{limit}
    </if>
  </select>
</mapper> 