<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.chatbi.integration.dao.ch.accuracy.QueryAnnotationMapper">

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, request_id, session_id, tenant_id, component_annotations, round_score, 
        round_tags, round_comments, attachments, created_by, create_time, 
        last_modified_time, is_deleted
    </sql>

    <!-- 保存问题级标注记录 -->
    <insert id="saveQueryAnnotation">
        INSERT INTO bi_query_annotation (
            id, request_id, session_id, tenant_id, component_annotations, round_score,
            round_tags, round_comments, attachments, created_by, create_time, 
            last_modified_time, is_deleted
        ) VALUES (
            #{annotation.id}, #{annotation.requestId}, #{annotation.sessionId}, #{annotation.tenantId},
            #{annotation.componentAnnotations}, #{annotation.roundScore},
            #{annotation.roundTags,typeHandler=com.github.mybatis.handler.list.ListTypeHandler},
            #{annotation.roundComments}, #{annotation.attachments},
            #{annotation.createdBy}, #{annotation.createTime}, #{annotation.lastModifiedTime}, 0
        )
    </insert>

    <!-- 更新问题级标注记录 -->
    <update id="updateQueryAnnotation">
        INSERT INTO bi_query_annotation (
            id, request_id, session_id, tenant_id, component_annotations, round_score,
            round_tags, round_comments, attachments, created_by, create_time, 
            last_modified_time, is_deleted
        ) VALUES (
            #{annotation.id}, #{annotation.requestId}, #{annotation.sessionId}, #{annotation.tenantId},
            #{annotation.componentAnnotations}, #{annotation.roundScore},
            #{annotation.roundTags,typeHandler=com.github.mybatis.handler.list.ListTypeHandler},
            #{annotation.roundComments}, #{annotation.attachments},
            #{annotation.createdBy}, #{annotation.createTime}, #{annotation.lastModifiedTime}, 0
        )
    </update>

    <!-- 根据请求ID查询标注记录 -->
    <select id="findByRequestId" resultType="com.fxiaoke.chatbi.integration.model.ch.accuracy.QueryAnnotation">
        SELECT <include refid="Base_Column_List"/>
        FROM bi_query_annotation FINAL
        WHERE request_id = #{requestId}
        AND is_deleted = 0
        LIMIT 1
    </select>

    <!-- 根据Session ID查询所有标注记录 -->
    <select id="findBySessionId" resultType="com.fxiaoke.chatbi.integration.model.ch.accuracy.QueryAnnotation">
        SELECT <include refid="Base_Column_List"/>
        FROM bi_query_annotation FINAL
        WHERE session_id = #{sessionId}
        AND is_deleted = 0
        ORDER BY create_time ASC
    </select>

    <!-- 根据Session ID和租户ID查询所有标注记录 -->
    <select id="findBySessionIdAndTenantId" resultType="com.fxiaoke.chatbi.integration.model.ch.accuracy.QueryAnnotation">
        SELECT <include refid="Base_Column_List"/>
        FROM bi_query_annotation FINAL
        WHERE session_id = #{sessionId}
        AND tenant_id = #{tenantId}
        AND is_deleted = 0
        ORDER BY create_time ASC
    </select>

    <!-- 逻辑删除标注记录 -->
    <update id="deleteById">
        INSERT INTO bi_query_annotation (
            id, request_id, session_id, tenant_id, component_annotations, round_score,
            round_tags, round_comments, attachments, created_by, create_time, 
            last_modified_time, is_deleted
        ) 
        SELECT 
            id, request_id, session_id, tenant_id, component_annotations, round_score,
            round_tags, round_comments, attachments, created_by, create_time,
            now() as last_modified_time, 1 as is_deleted
        FROM bi_query_annotation FINAL
        WHERE id = #{id}
        AND is_deleted = 0
    </update>

    <!-- 根据ID查询标注记录 -->
    <select id="findById" resultType="com.fxiaoke.chatbi.integration.model.ch.accuracy.QueryAnnotation">
        SELECT <include refid="Base_Column_List"/>
        FROM bi_query_annotation FINAL
        WHERE id = #{id}
        AND is_deleted = 0
        LIMIT 1
    </select>

    <!-- 批量查询标注记录 -->
    <select id="findByRequestIds" resultType="com.fxiaoke.chatbi.integration.model.ch.accuracy.QueryAnnotation">
        SELECT <include refid="Base_Column_List"/>
        FROM bi_query_annotation FINAL
        WHERE is_deleted = 0
        AND request_id IN
        <foreach collection="requestIds" item="requestId" open="(" separator="," close=")">
            #{requestId}
        </foreach>
    </select>

    <!-- 统计租户下的标注记录数量 -->
    <select id="countByTenantId" resultType="long">
        SELECT COUNT(*)
        FROM bi_query_annotation FINAL
        WHERE tenant_id = #{tenantId}
        AND is_deleted = 0
    </select>

    <!-- 根据标注人统计标注记录数量 -->
    <select id="countByCreatedBy" resultType="long">
        SELECT COUNT(*)
        FROM bi_query_annotation FINAL
        WHERE tenant_id = #{tenantId}
        AND created_by = #{createdBy}
        AND is_deleted = 0
    </select>

</mapper>
