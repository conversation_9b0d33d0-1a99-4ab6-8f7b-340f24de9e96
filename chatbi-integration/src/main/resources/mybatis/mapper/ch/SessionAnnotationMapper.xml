<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.chatbi.integration.dao.ch.accuracy.SessionAnnotationMapper">

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, session_id, tenant_id, user_id, session_score, session_tags, session_comments,
        created_by, create_time, last_modified_by, last_modified_time, is_deleted
    </sql>

    <!-- 保存Session级标注记录 -->
    <insert id="saveSessionAnnotation">
        INSERT INTO bi_session_annotation (
            id, session_id, tenant_id, user_id, session_score, session_tags, session_comments,
            created_by, create_time, last_modified_by, last_modified_time, is_deleted
        ) VALUES (
            #{annotation.id}, #{annotation.sessionId}, #{annotation.tenantId}, #{annotation.userId},
            #{annotation.sessionScore}, 
            #{annotation.sessionTags,typeHandler=com.github.mybatis.handler.list.ListTypeHandler},
            #{annotation.sessionComments}, #{annotation.createdBy}, #{annotation.createTime},
            #{annotation.lastModifiedBy}, #{annotation.lastModifiedTime}, 0
        )
    </insert>

    <!-- 更新Session级标注记录 -->
    <update id="updateSessionAnnotation">
        INSERT INTO bi_session_annotation (
            id, session_id, tenant_id, user_id, session_score, session_tags, session_comments,
            created_by, create_time, last_modified_by, last_modified_time, is_deleted
        ) VALUES (
            #{annotation.id}, #{annotation.sessionId}, #{annotation.tenantId}, #{annotation.userId},
            #{annotation.sessionScore}, 
            #{annotation.sessionTags,typeHandler=com.github.mybatis.handler.list.ListTypeHandler},
            #{annotation.sessionComments}, #{annotation.createdBy}, #{annotation.createTime},
            #{annotation.lastModifiedBy}, #{annotation.lastModifiedTime}, 0
        )
    </update>

    <!-- 根据Session ID查询标注记录 -->
    <select id="findBySessionId" resultType="com.fxiaoke.chatbi.integration.model.ch.accuracy.SessionAnnotation">
        SELECT <include refid="Base_Column_List"/>
        FROM bi_session_annotation FINAL
        WHERE session_id = #{sessionId}
        AND is_deleted = 0
        LIMIT 1
    </select>

    <!-- 根据Session ID和租户ID查询标注记录 -->
    <select id="findBySessionIdAndTenantId" resultType="com.fxiaoke.chatbi.integration.model.ch.accuracy.SessionAnnotation">
        SELECT <include refid="Base_Column_List"/>
        FROM bi_session_annotation FINAL
        WHERE session_id = #{sessionId}
        AND tenant_id = #{tenantId}
        AND is_deleted = 0
        LIMIT 1
    </select>

    <!-- 根据租户ID分页查询Session标注记录 -->
    <select id="findByTenantId" resultType="com.fxiaoke.chatbi.integration.model.ch.accuracy.SessionAnnotation">
        SELECT <include refid="Base_Column_List"/>
        FROM bi_session_annotation FINAL
        WHERE tenant_id = #{tenantId}
        AND is_deleted = 0
        ORDER BY create_time DESC
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <!-- 统计租户下的Session标注数量 -->
    <select id="countByTenantId" resultType="long">
        SELECT COUNT(*)
        FROM bi_session_annotation FINAL
        WHERE tenant_id = #{tenantId}
        AND is_deleted = 0
    </select>

    <!-- 逻辑删除Session标注记录 -->
    <update id="deleteById">
        INSERT INTO bi_session_annotation (
            id, session_id, tenant_id, user_id, session_score, session_tags, session_comments,
            created_by, create_time, last_modified_by, last_modified_time, is_deleted
        ) 
        SELECT 
            id, session_id, tenant_id, user_id, session_score, session_tags, session_comments,
            created_by, create_time, created_by as last_modified_by, now() as last_modified_time, 1 as is_deleted
        FROM bi_session_annotation FINAL
        WHERE id = #{id}
        AND is_deleted = 0
    </update>

    <!-- 根据ID查询Session标注记录 -->
    <select id="findById" resultType="com.fxiaoke.chatbi.integration.model.ch.accuracy.SessionAnnotation">
        SELECT <include refid="Base_Column_List"/>
        FROM bi_session_annotation FINAL
        WHERE id = #{id}
        AND is_deleted = 0
        LIMIT 1
    </select>

    <!-- 根据标注人统计Session标注记录数量 -->
    <select id="countByCreatedBy" resultType="long">
        SELECT COUNT(*)
        FROM bi_session_annotation FINAL
        WHERE tenant_id = #{tenantId}
        AND created_by = #{createdBy}
        AND is_deleted = 0
    </select>

</mapper>
