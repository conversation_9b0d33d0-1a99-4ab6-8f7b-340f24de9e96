<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.chatbi.integration.dao.ch.KnowledgeEmbeddingMapper">

  <resultMap id="KnowledgeEmbeddingMap" type="com.fxiaoke.chatbi.integration.model.ch.KnowledgeEmbedding">
    <result column="id" property="id"/>
    <result column="knowledge_id" property="knowledgeId"/>
    <result column="knowledge_type" property="knowledgeType"/>
    <result column="embedding" property="embedding" typeHandler="com.fxiaoke.chatbi.integration.dao.ch.typehandler.FloatArrayTypeHandler"/>
    <result column="feature" property="feature"/>
    <result column="weight" property="weight"/>
    <result column="tenant_id" property="tenantId"/>
  </resultMap>
  
  <!-- 带有相似度分数的知识向量结果映射 -->
  <resultMap id="KnowledgeEmbeddingWithScoreMap" type="com.fxiaoke.chatbi.integration.model.ch.KnowledgeEmbedding">
    <result column="id" property="id"/>
    <result column="knowledge_id" property="knowledgeId"/>
    <result column="knowledge_type" property="knowledgeType"/>
    <result column="embedding" property="embedding" typeHandler="com.fxiaoke.chatbi.integration.dao.ch.typehandler.FloatArrayTypeHandler"/>
    <result column="feature" property="feature"/>
    <result column="weight" property="weight"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="similarity_score" property="vectorScore"/>
  </resultMap>

  <!-- 批量保存知识向量 -->
  <insert id="batchSaveKnowledgeEmbeddings">
    INSERT INTO bi_knowledge_embedding (
    id,
    tenant_id,
    knowledge_type,
    knowledge_id,
    embedding,
    feature,
    weight
    ) VALUES
    <foreach collection="knowledgeEmbeddings" item="knowledgeEmbedding" separator=",">
      (
      #{knowledgeEmbedding.id},
      #{knowledgeEmbedding.tenantId},
      #{knowledgeEmbedding.knowledgeType},
      #{knowledgeEmbedding.knowledgeId},
      [<foreach collection="knowledgeEmbedding.embedding" item="val" separator=",">#{val}</foreach>],
      #{knowledgeEmbedding.feature},
      #{knowledgeEmbedding.weight}
      )
    </foreach>
  </insert>

  <!-- 搜索相似向量的知识标识 -->
  <select id="searchSimilarEmbeddingKnowledgeId" resultType="java.lang.String">
    WITH similarities AS (
      SELECT 
        knowledge_id,
        (1 - cosineDistance(embedding, ${embeddingString})) * weight AS similarity_score
      FROM bi_knowledge_embedding FINAL
      WHERE tenant_id = #{tenantId}
        AND is_deleted = 0
        AND length(embedding) > 0
        <if test="knowledgeType != null">
          AND knowledge_type = #{knowledgeType}
        </if>
    )
    SELECT knowledge_id
    FROM similarities
    WHERE similarity_score >= #{threshold}
    ORDER BY similarity_score DESC
    LIMIT #{limit}
  </select>
  
  <!-- 搜索相似向量的知识标识，并限定在allowedIds范围内 -->
  <select id="searchSimilarEmbeddingKnowledgeIdWithFilter" resultType="java.lang.String">
    WITH similarities AS (
      SELECT 
        knowledge_id,
        (1 - cosineDistance(embedding, ${embeddingString})) * weight AS similarity_score
      FROM bi_knowledge_embedding FINAL
      WHERE tenant_id = #{tenantId}
        AND is_deleted = 0
        AND length(embedding) > 0
        <if test="knowledgeType != null">
          AND knowledge_type = #{knowledgeType}
        </if>
        <if test="allowedIds != null and allowedIds.size() > 0">
          AND knowledge_id IN
          <foreach item="id" collection="allowedIds" open="(" separator="," close=")">
            #{id}
          </foreach>
        </if>
    )
    SELECT knowledge_id
    FROM similarities
    WHERE similarity_score >= #{threshold}
    ORDER BY similarity_score DESC
    LIMIT #{limit}
  </select>
  
  <!-- 搜索相似向量，并返回完整的知识向量对象（包含相似度分数） -->
  <select id="searchEmbeddingWithScores" resultMap="KnowledgeEmbeddingWithScoreMap">
    WITH similarities AS (
      SELECT 
        knowledge_id,
        knowledge_type,
        embedding,
        feature,
        weight,
        tenant_id,
        (1 - cosineDistance(embedding, ${embeddingString})) * weight AS similarity_score
      FROM bi_knowledge_embedding FINAL
      WHERE tenant_id = #{tenantId}
        AND is_deleted = 0 AND length(embedding) > 0
        <if test="knowledgeType != null">
          AND knowledge_type = #{knowledgeType}
        </if>
    )
    SELECT 
      knowledge_id,
      knowledge_type,
      embedding,
      feature,
      weight,
      tenant_id,
      similarity_score
    FROM similarities
    WHERE similarity_score >= #{threshold}
    ORDER BY similarity_score DESC
    LIMIT #{limit}
  </select>
  
  <!-- 根据知识标识获取所有特征向量 -->
  <select id="findByKnowledgeId" resultMap="KnowledgeEmbeddingMap">
    SELECT 
      knowledge_id, knowledge_type, feature, 
      weight, tenant_id
    FROM bi_knowledge_embedding FINAL
    WHERE tenant_id = #{tenantId}
      AND knowledge_type = #{knowledgeType}
      AND knowledge_id = #{knowledgeId}
      AND is_deleted = 0
  </select>
  
  <!-- 批量查询多个知识标识的特征向量 -->
  <select id="findByKnowledgeIdList" resultMap="KnowledgeEmbeddingMap">
    SELECT 
      knowledge_id, knowledge_type, feature, 
      weight, tenant_id
    FROM bi_knowledge_embedding FINAL
    WHERE tenant_id = #{tenantId}
      AND knowledge_type = #{knowledgeType}
      AND knowledge_id IN
      <foreach item="knowledgeId" collection="knowledgeIds" open="(" separator="," close=")">
        #{knowledgeId}
      </foreach>
      AND is_deleted = 0
  </select>

  <!-- 搜索向量的相似度,限定在指定的知识ID范围内,只返回最相似的结果 -->
  <select id="searchEmbeddingsWithScores" resultMap="KnowledgeEmbeddingWithScoreMap">
    WITH similarities AS (
    SELECT
    knowledge_id,
    knowledge_type,
    embedding,
    feature,
    weight,
    tenant_id,
    (1 - cosineDistance(embedding, ${embeddingString})) * weight AS similarity_score
    FROM bi_knowledge_embedding FINAL
    WHERE tenant_id = #{tenantId}
    AND is_deleted = 0 AND length(embedding) > 0
    <if test="knowledgeType != null">
      AND knowledge_type = #{knowledgeType}
    </if>
    <if test="knowledgeIds != null and knowledgeIds.size() > 0">
      AND knowledge_id IN
      <foreach item="id" collection="knowledgeIds" open="(" separator="," close=")">
        #{id}
      </foreach>
    </if>
    )
    SELECT *
    FROM similarities
    WHERE similarity_score >= #{threshold}
    ORDER BY similarity_score DESC
    LIMIT 1
  </select>

  <!-- 根据租户ID和知识类型删除知识向量数据 -->
  <update id="deleteKnowledgeEmbeddings">
    ALTER TABLE bi_knowledge_embedding DELETE WHERE tenant_id = #{tenantId}
    <if test="knowledgeType != null and knowledgeType != ''">
      AND knowledge_type = #{knowledgeType}
    </if>
    <if test="knowledgeIds != null and knowledgeIds.size() > 0">
      AND knowledge_id IN
      <foreach collection="knowledgeIds" item="id" open="(" separator="," close=")">
        #{id}
      </foreach>
    </if>
  </update>

  <!-- 根据ID删除知识向量（以增代删） -->
  <insert id="deleteById">
    INSERT INTO bi_knowledge_embedding (
      id, tenant_id, knowledge_type, knowledge_id,
      embedding, feature, weight, is_deleted
    )
    SELECT
      id, tenant_id, knowledge_type, knowledge_id,
      embedding, feature, weight, 1 as is_deleted
    FROM bi_knowledge_embedding FINAL
    WHERE tenant_id = #{tenantId} AND id = #{id} AND is_deleted = 0
    LIMIT 1
  </insert>

  <!-- 批量删除知识向量（以增代删） -->
  <insert id="batchDeleteByIds">
    INSERT INTO bi_knowledge_embedding (
      id, tenant_id, knowledge_type, knowledge_id,
      embedding, feature, weight, is_deleted
    )
    SELECT
      id, tenant_id, knowledge_type, knowledge_id,
      embedding, feature, weight, 1 as is_deleted
    FROM bi_knowledge_embedding FINAL
    WHERE tenant_id = #{tenantId}
      AND is_deleted = 0
      AND id IN
      <foreach item="id" collection="ids" open="(" separator="," close=")">
        #{id}
      </foreach>
  </insert>
</mapper>