<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.chatbi.integration.dao.ch.ChartKnowledgeMapper">

  <resultMap id="ChartKnowledgeMap" type="com.fxiaoke.chatbi.integration.model.ch.ChartKnowledge">
    <result column="view_id" property="viewId"/>
    <result column="view_name" property="viewName"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="chart_type" property="chartType"/>
    <result column="schema_id" property="schemaId"/>
    <result column="spec" property="spec"/>
    <result column="dimension_names" property="dimensionNames" typeHandler="com.github.mybatis.handler.list.ListTypeHandler"/>
    <result column="measure_names" property="measureNames" typeHandler="com.github.mybatis.handler.list.ListTypeHandler"/>
    <result column="filter_names" property="filterNames" typeHandler="com.github.mybatis.handler.list.ListTypeHandler"/>
    <result column="field_ids" property="fieldIds" typeHandler="com.github.mybatis.handler.list.ListTypeHandler"/>
    <result column="usage_count" property="usageCount"/>
    <result column="last_modified_time" property="lastModifiedTime"/>
  </resultMap>

  <resultMap id="ChartKnowledgeWithScoreMap" type="com.fxiaoke.chatbi.integration.model.ch.ChartKnowledge">
    <result column="view_id" property="viewId"/>
    <result column="view_name" property="viewName"/>
    <result column="tenant_id" property="tenantId"/>
    <result column="chart_type" property="chartType"/>
    <result column="schema_id" property="schemaId"/>
    <result column="spec" property="spec"/>
    <result column="dimension_names" property="dimensionNames" typeHandler="com.github.mybatis.handler.list.ListTypeHandler"/>
    <result column="measure_names" property="measureNames" typeHandler="com.github.mybatis.handler.list.ListTypeHandler"/>
    <result column="filter_names" property="filterNames" typeHandler="com.github.mybatis.handler.list.ListTypeHandler"/>
    <result column="field_ids" property="fieldIds" typeHandler="com.github.mybatis.handler.list.ListTypeHandler"/>
    <result column="usage_count" property="usageCount"/>
    <result column="last_modified_time" property="lastModifiedTime"/>
    <result column="keywordScore" property="keyWordScore"/>
  </resultMap>

  <!-- 批量保存图表知识 -->
  <insert id="batchSaveChartKnowledges">
    INSERT INTO bi_chart_knowledge (
      view_id,
      view_name,
      tenant_id,
      chart_type,
      schema_id,
      spec,
      dimension_names,
      measure_names,
      filter_names,
      field_ids,
      usage_count,
      last_modified_time
    ) VALUES
    <foreach collection="chartKnowledges" item="chartKnowledge" separator=",">
      (
        #{chartKnowledge.viewId},
        #{chartKnowledge.viewName,jdbcType=VARCHAR},
        #{chartKnowledge.tenantId},
        #{chartKnowledge.chartType,jdbcType=VARCHAR},
        #{chartKnowledge.schemaId,jdbcType=VARCHAR},
        #{chartKnowledge.spec},
        [<foreach collection="chartKnowledge.dimensionNames" item="val" separator=",">#{val}</foreach>],
        [<foreach collection="chartKnowledge.measureNames" item="val" separator=",">#{val}</foreach>],
        [<foreach collection="chartKnowledge.filterNames" item="val" separator=",">#{val}</foreach>],
        [<foreach collection="chartKnowledge.fieldIds" item="val" separator=",">#{val}</foreach>],
        #{chartKnowledge.usageCount,jdbcType=INTEGER},
        #{chartKnowledge.lastModifiedTime,jdbcType=BIGINT}
      )
    </foreach>
  </insert>


  <select id="findAllByTenantId" resultMap="ChartKnowledgeMap">
    SELECT
      view_id, view_name, tenant_id, chart_type, schema_id,
      spec, dimension_names, measure_names, filter_names, field_ids, usage_count, last_modified_time
    FROM bi_chart_knowledge FINAL
    WHERE tenant_id = #{tenantId}
      AND is_deleted = 0
  </select>

  <!-- 批量获取图表知识 -->
  <select id="batchGetByViewIds" resultMap="ChartKnowledgeMap">
    SELECT
      view_id, view_name, tenant_id, chart_type, schema_id,
      spec, dimension_names, measure_names, filter_names, field_ids, usage_count, last_modified_time
    FROM bi_chart_knowledge FINAL
    WHERE tenant_id = #{tenantId}
    AND view_id IN
    <foreach collection="viewIds" item="viewId" open="(" separator="," close=")">
      #{viewId}
    </foreach>
    AND is_deleted = 0
  </select>

  <!-- 直接根据字段ID检索图表ID列表 -->
  <select id="searchChartsByFieldIds" resultType="java.lang.String">
    SELECT DISTINCT view_id
    FROM bi_chart_knowledge FINAL
    WHERE tenant_id = #{tenantId}
    AND is_deleted = 0
    AND hasAny(field_ids, ${fieldIdsJson})
  </select>
  

  
  <!-- 根据关键词检索图表ID -->
  <select id="searchChartsByKeyword" resultType="java.lang.String">
    SELECT DISTINCT view_id
    FROM bi_chart_knowledge FINAL
    WHERE tenant_id = #{tenantId}
      AND is_deleted = 0
      AND (
        arrayExists(x -> positionCaseInsensitive(x, #{keyword}) > 0, dimension_names) OR
        arrayExists(x -> positionCaseInsensitive(x, #{keyword}) > 0, measure_names) OR
        arrayExists(x -> positionCaseInsensitive(x, #{keyword}) > 0, filter_names)
      )
  </select>

  <!-- 使用分类关键词检索图表ID -->
  <select id="searchChartsByCategorizedKeyword" resultType="java.lang.String">
    SELECT DISTINCT view_id
    FROM bi_chart_knowledge FINAL
    WHERE tenant_id = #{tenantId}
      AND is_deleted = 0
      AND (
        arrayExists(x -> positionCaseInsensitive(x, #{dimensionKeyword}) > 0, dimension_names) OR
        arrayExists(x -> positionCaseInsensitive(x, #{measureKeyword}) > 0, measure_names) OR
        arrayExists(x -> positionCaseInsensitive(x, #{filterKeyword}) > 0, filter_names)
      )
  </select>

  <!-- 关键词匹配图表并打分 -->
  <select id="selectChartsByKeywordWithScores" resultMap="ChartKnowledgeWithScoreMap">
    SELECT
    view_id, view_name, tenant_id, chart_type, schema_id,
    spec, dimension_names, measure_names, filter_names, field_ids, usage_count, last_modified_time,
    <if test="dimKeys != null and !dimKeys.isEmpty()">
      arrayCount(config -> arrayExists(key -> positionCaseInsensitive(config, key) > 0,
      <foreach collection="dimKeys" item="key" open="[" separator="," close="]">
        #{key}
      </foreach>
      ), arrayDistinct(dimension_names)) / length(
      <foreach collection="dimKeys" item="key" open="[" separator="," close="]">
        #{key}
      </foreach>
      ) AS dimRate,
    </if>
    <if test="dimKeys == null or dimKeys.isEmpty()">
      0 AS dimRate,
    </if>

    <if test="meaKeys != null and !meaKeys.isEmpty()">
      arrayCount(config -> arrayExists(key -> positionCaseInsensitive(config, key) > 0,
      <foreach collection="meaKeys" item="key" open="[" separator="," close="]">
        #{key}
      </foreach>
      ), arrayDistinct(measure_names)) / length(
      <foreach collection="meaKeys" item="key" open="[" separator="," close="]">
        #{key}
      </foreach>
      ) AS meaRate,
    </if>
    <if test="meaKeys == null or meaKeys.isEmpty()">
      0 AS meaRate,
    </if>

    <if test="filterKeys != null and !filterKeys.isEmpty()">
      arrayCount(config -> arrayExists(key -> positionCaseInsensitive(config, key) > 0,
      <foreach collection="filterKeys" item="key" open="[" separator="," close="]">
        #{key}
      </foreach>
      ), filter_names) / length(
      <foreach collection="filterKeys" item="key" open="[" separator="," close="]">
        #{key}
      </foreach>
      ) AS filterRate,
    </if>
    <if test="filterKeys == null or filterKeys.isEmpty()">
      0 AS filterRate,
    </if>

    0.4 * dimRate + 1 * meaRate + 0.2 * filterRate AS keywordScore

    FROM bi_chart_knowledge FINAL
    WHERE keywordScore > 0
    AND tenant_id = #{tenantId}
    AND is_deleted = 0
    AND keywordScore > 0
    ORDER BY keywordScore DESC LIMIT 100
  </select>

  <!-- 根据租户ID和视图ID列表删除图表知识数据 -->
  <update id="deleteByTenantIdAndViewIds">
    ALTER TABLE bi_chart_knowledge
    DELETE WHERE tenant_id = #{tenantId}
    <if test="viewIds != null and viewIds.size() > 0">
      AND view_id IN
      <foreach item="viewId" collection="viewIds" open="(" separator="," close=")">
        #{viewId}
      </foreach>
    </if>
  </update>

</mapper>