<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.chatbi.integration.dao.ch.accuracy.QueryLogMapper">

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, trace_id, session_id, tenant_id, user_id, query, response, response_components,
        response_time, is_context_enabled, source, feedback, feedback_comment, is_annotated,
        query_time, create_time, action_logs, tags, created_by, is_deleted
    </sql>

    <!-- 扩展的基础列（包含标注信息） -->
    <sql id="Extended_Column_List">
        ql.id, ql.trace_id, ql.session_id, ql.tenant_id, ql.user_id, ql.query, ql.response, ql.response_components,
        ql.response_time, ql.is_context_enabled, ql.source, ql.feedback, ql.feedback_comment, ql.is_annotated,
        ql.query_time, ql.create_time, ql.action_logs, ql.tags, ql.created_by, ql.is_deleted,
        -- 标注信息字段
        a.id as annotation_id, a.result_score, a.tags as annotation_tags, a.error_types, 
        a.expected_result, a.created_by as annotation_created_by, a.create_time as annotation_create_time
    </sql>

    <!-- 动态WHERE条件 -->
    <sql id="Where_Conditions">
        <where>
            AND is_deleted = 0
            <if test="filter.tenantId != null and filter.tenantId != ''">
                AND tenant_id = #{filter.tenantId}
            </if>
            <if test="filter.userId != null and filter.userId != ''">
                AND user_id = #{filter.userId}
            </if>
            <if test="filter.source != null and filter.source != ''">
                AND source = #{filter.source}
            </if>
            <if test="filter.startTime != null">
                AND create_time &gt;= #{filter.startTime}
            </if>
            <if test="filter.endTime != null">
                AND create_time &lt;= #{filter.endTime}
            </if>
            <if test="filter.feedback != null">
                AND feedback = #{filter.feedback}
            </if>
            <if test="filter.isAnnotated != null">
                AND is_annotated = #{filter.isAnnotated}
            </if>
            <if test="filter.question != null and filter.question != ''">
                AND query LIKE CONCAT('%', #{filter.question}, '%')
            </if>
        </where>
    </sql>

    <!-- 根据条件分页查询问答记录 -->
    <select id="findByConditions" resultType="com.fxiaoke.chatbi.integration.model.ch.accuracy.QueryLog">
        SELECT <include refid="Base_Column_List"/>
        FROM bi_query_log FINAL
        <include refid="Where_Conditions"/>
        ORDER BY create_time DESC
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <!-- 统计符合条件的记录总数 -->
    <select id="countByConditions" resultType="long">
        SELECT COUNT(*)
        FROM bi_query_log FINAL
        <include refid="Where_Conditions"/>
    </select>

    <!-- 根据ID查询问答记录 -->
    <select id="findById" resultType="com.fxiaoke.chatbi.integration.model.ch.accuracy.QueryLog">
        SELECT <include refid="Base_Column_List"/>
        FROM bi_query_log FINAL
        WHERE id = #{id}
        AND is_deleted = 0
        LIMIT 1
    </select>

    <!-- 根据ID查询问答记录详情（联表查询，直接返回VO） -->
    <select id="findDetailWithAnnotationById" resultMap="QueryLogDetailVOResultMap">
        SELECT <include refid="Extended_Column_List"/>
        FROM bi_query_log ql FINAL
        LEFT JOIN bi_query_annotation a FINAL 
            ON ql.id = a.request_id AND a.is_deleted = 0
        WHERE ql.id = #{id}
        AND ql.is_deleted = 0
        LIMIT 1
    </select>

    <!-- 更新问答记录 -->
    <update id="updateQueryLog">
        INSERT INTO bi_query_log (
            id, trace_id, session_id, tenant_id, user_id, query, response, response_components,
            response_time, is_context_enabled, source, feedback, feedback_comment, is_annotated,
            query_time, create_time, action_logs, tags, created_by, is_deleted
        ) VALUES (
            #{queryLog.id}, #{queryLog.traceId}, #{queryLog.sessionId}, #{queryLog.tenantId}, 
            #{queryLog.userId}, #{queryLog.query}, #{queryLog.response}, #{queryLog.responseComponents},
            #{queryLog.responseTime}, #{queryLog.isContextEnabled}, #{queryLog.source}, 
            #{queryLog.feedback}, #{queryLog.feedbackComment}, #{queryLog.isAnnotated},
            #{queryLog.queryTime}, #{queryLog.createTime}, #{queryLog.actionLogs}, 
            #{queryLog.tags,typeHandler=com.github.mybatis.handler.list.ListTypeHandler}, 
            #{queryLog.createdBy}, 0
        )
    </update>

    <!-- 统计问答记录总数 -->
    <select id="countAll" resultType="long">
        SELECT COUNT(*)
        FROM bi_query_log FINAL
        WHERE is_deleted = 0
    </select>

    <!-- 统计已标注的问答记录数量 -->
    <select id="countAnnotated" resultType="long">
        SELECT COUNT(*)
        FROM bi_query_log FINAL
        WHERE is_annotated = 1
        AND is_deleted = 0
    </select>

    <!-- 根据条件分页查询问答记录（联表查询，直接返回VO） -->
    <select id="findByConditionsWithAnnotationVO" resultMap="QueryLogListVOResultMap">
        SELECT <include refid="Extended_Column_List"/>
        FROM bi_query_log ql FINAL
        LEFT JOIN bi_query_annotation a FINAL 
            ON ql.id = a.request_id AND a.is_deleted = 0
        <where>
            AND ql.is_deleted = 0
            <if test="filter.tenantId != null and filter.tenantId != ''">
                AND ql.tenant_id = #{filter.tenantId}
            </if>
            <if test="filter.userId != null and filter.userId != ''">
                AND ql.user_id = #{filter.userId}
            </if>
            <if test="filter.source != null and filter.source != ''">
                AND ql.source = #{filter.source}
            </if>
            <if test="filter.startTime != null">
                AND ql.create_time &gt;= #{filter.startTime}
            </if>
            <if test="filter.endTime != null">
                AND ql.create_time &lt;= #{filter.endTime}
            </if>
            <if test="filter.feedback != null">
                AND ql.feedback = #{filter.feedback}
            </if>
            <if test="filter.isAnnotated != null">
                AND ql.is_annotated = #{filter.isAnnotated}
            </if>
            <if test="filter.question != null and filter.question != ''">
                AND ql.query LIKE CONCAT('%', #{filter.question}, '%')
            </if>
        </where>
        ORDER BY ql.create_time DESC
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <!-- 根据ID查询问答记录详情（联表查询，直接返回VO） -->
    <resultMap id="QueryLogDetailVOResultMap" type="com.fxiaoke.chatbi.integration.dto.QueryLogDetailVO">
        <!-- 问答记录字段 -->
        <result column="id" property="id"/>
        <result column="trace_id" property="traceId"/>
        <result column="session_id" property="sessionId"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="user_id" property="userId"/>
        <result column="query" property="query"/>
        <result column="response" property="answer"/>
        <result column="response_components" property="responseComponents"/>
        <result column="response_time" property="responseTime"/>
        <result column="is_context_enabled" property="isContextEnabled"/>
        <result column="source" property="source"/>
        <result column="feedback" property="feedback"/>
        <result column="feedback_comment" property="feedbackComment"/>
        <result column="is_annotated" property="isAnnotated"/>
        <result column="query_time" property="queryTime"/>
        <result column="create_time" property="createTime"/>
        <result column="action_logs" property="executionLogs"/>
        <result column="tags" property="tags" typeHandler="com.github.mybatis.handler.list.ListTypeHandler"/>
        <result column="created_by" property="createdBy"/>
        <!-- 嵌套的标注信息对象 -->
        <association property="annotation" javaType="com.fxiaoke.chatbi.integration.dto.AnnotationVO">
            <result column="annotation_id" property="id"/>
            <result column="result_score" property="resultScore"/>
            <result column="annotation_tags" property="tags" typeHandler="com.github.mybatis.handler.list.ListTypeHandler"/>
            <result column="error_types" property="errorTypes" typeHandler="com.github.mybatis.handler.list.ListTypeHandler"/>
            <result column="expected_result" property="expectedResult"/>
            <result column="annotation_created_by" property="createdBy"/>
            <result column="annotation_create_time" property="createTime"/>
        </association>
    </resultMap>

    <!-- 优化的ResultMap：直接映射到QueryLogListVO -->
    <resultMap id="QueryLogListVOResultMap" type="com.fxiaoke.chatbi.integration.dto.QueryLogListVO">
        <!-- 问答记录字段 -->
        <result column="id" property="id"/>
        <result column="trace_id" property="traceId"/>
        <result column="session_id" property="sessionId"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="user_id" property="userId"/>
        <result column="query" property="query"/>
        <result column="response" property="response"/>
        <result column="response_components" property="responseComponents"/>
        <result column="response_time" property="responseTime"/>
        <result column="is_context_enabled" property="isContextEnabled"/>
        <result column="source" property="source"/>
        <result column="feedback" property="feedback"/>
        <result column="feedback_comment" property="feedbackComment"/>
        <result column="is_annotated" property="isAnnotated"/>
        <result column="query_time" property="queryTime"/>
        <result column="create_time" property="createTime"/>
        <result column="action_logs" property="actionLogs"/>
        <result column="tags" property="tags" typeHandler="com.github.mybatis.handler.list.ListTypeHandler"/>
        <result column="created_by" property="createdBy"/>
        <!-- 嵌套的标注信息对象 -->
        <association property="annotation" javaType="com.fxiaoke.chatbi.integration.dto.AnnotationVO">
            <result column="annotation_id" property="id"/>
            <result column="result_score" property="resultScore"/>
            <result column="annotation_tags" property="tags" typeHandler="com.github.mybatis.handler.list.ListTypeHandler"/>
            <result column="error_types" property="errorTypes" typeHandler="com.github.mybatis.handler.list.ListTypeHandler"/>
            <result column="expected_result" property="expectedResult"/>
            <result column="annotation_created_by" property="createdBy"/>
            <result column="annotation_create_time" property="createTime"/>
        </association>
    </resultMap>


    <insert id="save" parameterType="com.fxiaoke.chatbi.integration.model.ch.accuracy.QueryLog">
        INSERT INTO bi_query_log (
            id,
            trace_id,
            session_id,
            tenant_id,
            user_id,
            tags,
            query,
            response,
            response_components,
            response_time,
            is_context_enabled,
            source,
            feedback,
            feedback_comment,
            is_annotated,
            query_time,
            created_by,
            create_time,
            action_logs,
            last_modified_time
        ) VALUES (
                     #{queryLog.id},
                     #{queryLog.traceId},
                     #{queryLog.sessionId},
                     #{queryLog.tenantId},
                     #{queryLog.userId},
                     [<foreach collection="queryLog.tags" item="val" separator=",">#{val}</foreach>],
                     #{queryLog.query},
                     #{queryLog.response},
                     #{queryLog.responseComponents},
                     #{queryLog.responseTime},
                     #{queryLog.isContextEnabled},
                     #{queryLog.source},
                     #{queryLog.feedback},
                     #{queryLog.feedbackComment},
                     #{queryLog.isAnnotated},
                     #{queryLog.queryTime},
                     #{queryLog.createdBy},
                     #{queryLog.createTime},
                     #{queryLog.actionLogs},
                     #{queryLog.lastModifiedTime}
        )
    </insert>

    <!-- 根据Session ID查询所有问答记录 -->
    <select id="findBySessionId" resultType="com.fxiaoke.chatbi.integration.model.ch.accuracy.QueryLog">
        SELECT <include refid="Base_Column_List"/>
        FROM bi_query_log FINAL
        WHERE session_id = #{sessionId}
        AND is_deleted = 0
        ORDER BY query_time ASC
    </select>

    <!-- 根据Session ID和租户ID查询所有问答记录 -->
    <select id="findBySessionIdAndTenantId" resultType="com.fxiaoke.chatbi.integration.model.ch.accuracy.QueryLog">
        SELECT <include refid="Base_Column_List"/>
        FROM bi_query_log FINAL
        WHERE session_id = #{sessionId}
        AND tenant_id = #{tenantId}
        AND is_deleted = 0
        ORDER BY query_time ASC
    </select>

    <!-- 根据租户ID分组查询Session列表 -->
    <select id="findSessionsByTenantId" resultType="com.fxiaoke.chatbi.integration.model.ch.accuracy.SessionSummary">
        SELECT
            session_id as sessionId,
            tenant_id as tenantId,
            user_id as userId,
            COUNT(*) as conversationCount,
            MIN(query) as firstQuestion,
            MAX(query_time) as lastQueryTime,
            MAX(is_annotated) as isAnnotated,
            MIN(create_time) as createTime
        FROM bi_query_log FINAL
        WHERE tenant_id = #{tenantId}
        AND is_deleted = 0
        GROUP BY session_id, tenant_id, user_id
        ORDER BY lastQueryTime DESC
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <!-- 统计租户下的Session数量 -->
    <select id="countSessionsByTenantId" resultType="long">
        SELECT COUNT(DISTINCT session_id)
        FROM bi_query_log FINAL
        WHERE tenant_id = #{tenantId}
        AND is_deleted = 0
    </select>

</mapper>