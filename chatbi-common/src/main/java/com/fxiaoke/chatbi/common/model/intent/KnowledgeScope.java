package com.fxiaoke.chatbi.common.model.intent;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.chatbi.common.model.action.ActionOutput;
import com.fxiaoke.chatbi.common.model.prompts.PromptTemplateType;
import com.fxiaoke.chatbi.common.model.reasoning.ReasoningData;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 知识范围
 * 定义RAG检索的相关知识边界
 */
@Data
public class KnowledgeScope implements ActionOutput, ReasoningData {

    /**
     * 图表ID列表
     */
    private List<String> viewIds;

    /**
     * 各个渠道返回图表基本信息
     */
    private Map<String, Integer> chartInfoMap = new HashMap<>();

    /**
     * 最终的图表信息
     */
    private Map<String, String> chartKnowledge;

    @Override
    public Map<String, Object> getDescription() {

        Map<String, Object> description = new HashMap<>();

        //召回的图表格式
        AtomicInteger viewNum = new AtomicInteger();
        chartInfoMap.forEach((key, value) -> viewNum.set(value + viewNum.get()));

        description.put("viewNum", viewNum.get());

        String viewName = chartKnowledge.getOrDefault("viewName", "");

        description.put("viewName", viewName);

        String chartType = chartKnowledge.getOrDefault("chartType", "");

        description.put("chartType", chartType);

        String dimensions = chartKnowledge.getOrDefault("dimensions", "");

        description.put("dimensions", dimensions);

        String measures = chartKnowledge.getOrDefault("measures", "");

        description.put("measures", measures);

        String filters = chartKnowledge.getOrDefault("filters", "");

        description.put("filters", filters);

        return description;
    }

    @Override
    public Map<String, Object> getActionLog() {
        return getDescription();
    }

    @Override
    public PromptTemplateType getReasoningTemplateType() {
        return PromptTemplateType.REASONING_CONVERT_KNOWLEDGE;
    }

    @Override
    public PromptTemplateType getActionLogTemplateType() {
        return PromptTemplateType.RECALL_LOG;
    }

    @Override
    public boolean shouldCollectReasoning() {
        // 只有当至少有一种知识存在时才收集推理数据
        return CollectionUtils.isNotEmpty(viewIds);
    }
}
