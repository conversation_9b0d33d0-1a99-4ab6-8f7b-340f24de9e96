package com.fxiaoke.chatbi.common.exception;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.Locale;

/**
 * 异常消息解析器
 * 负责将异常转换为国际化错误消息
 */
@Component
public class MessageResolver {

  private final InnerI18nService i18nService;

  @Autowired
  public MessageResolver(InnerI18nService i18nService) {
    this.i18nService = i18nService;
  }

  /**
   * 解析异常消息
   *
   * @param exception 基础异常
   * @param locale    区域设置
   * @return 国际化错误消息
   */
  public String resolveMessage(BaseException exception, Locale locale) {
    // 获取错误码对应的错误枚举
    ChatbiErrorCodeEnum errorEnum = ChatbiErrorCodeEnum.getByCode(exception.getErrorCode());

    // 获取基础错误消息模板
    String template = i18nService.get(exception.getI18nKey(), locale,
      errorEnum != null ? errorEnum.getErrorMessage() : exception.getErrorCode());

    // 如果没有参数，直接返回消息
    if (exception.getArgs() == null || exception.getArgs().length == 0) {
      return template;
    }

    // 使用MessageFormat处理带占位符的消息
    return MessageFormat.format(template, exception.getArgs());
  }

  /**
   * 解析错误消息
   *
   * @param errorEnum 错误枚举
   * @param locale    区域设置
   * @param args      消息参数
   * @return 国际化错误消息
   */
  public String resolveMessage(ChatbiErrorCodeEnum errorEnum, Locale locale, Object... args) {
    String template = i18nService.get(errorEnum.getI18nKey(), locale, errorEnum.getErrorMessage());

    if (args == null || args.length == 0) {
      return template;
    }

    return MessageFormat.format(template, args);
  }

  /**
   * 解析错误消息（使用默认区域设置）
   *
   * @param errorEnum 错误枚举
   * @param args      消息参数
   * @return 国际化错误消息
   */
  public String resolveMessage(ChatbiErrorCodeEnum errorEnum, Object... args) {
    return resolveMessage(errorEnum, Locale.CHINA, args);
  }
} 