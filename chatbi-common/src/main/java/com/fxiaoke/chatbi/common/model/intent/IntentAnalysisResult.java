package com.fxiaoke.chatbi.common.model.intent;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 意图分析结果
 * 封装意图分析过程中的产物，包括处理后的查询文本、识别的意图和相关知识范围
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IntentAnalysisResult {
    
    /**
     * 处理后的查询文本
     */
    private String processedQuery;
    
    /**
     * 识别的用户意图
     */
    private UserIntent intent;
    
    /**
     * 相关知识范围
     */
    private KnowledgeScope knowledgeScope;
    
    /**
     * 分析是否成功
     */
    private boolean success;
    
    /**
     * 错误信息（如果分析失败）
     */
    private String errorMessage;
    
    /**
     * 创建成功结果
     */
    public static IntentAnalysisResult success(String processedQuery, UserIntent intent, KnowledgeScope knowledgeScope) {
        return IntentAnalysisResult.builder()
                .processedQuery(processedQuery)
                .intent(intent)
                .knowledgeScope(knowledgeScope)
                .success(true)
                .build();
    }
    
    /**
     * 创建失败结果
     */
    public static IntentAnalysisResult failure(String errorMessage) {
        return IntentAnalysisResult.builder()
                .success(false)
                .errorMessage(errorMessage)
                .build();
    }
} 