package com.fxiaoke.chatbi.common.model.dto;

import com.facishare.bi.common.entities.Permission;
import com.facishare.bi.common.entities.StatFieldInfoDto;
import lombok.Data;

import java.util.List;


@Data
public class ObjectRelationByObjNameResult {
  private String templateName;
  private String schemaName;
  private List<StatFieldInfoDto> dimensionFields;
  private List<MeasureOrStatDimField> dimensionFieldsGroupByObjName;
  private List<MeasureOrStatDimField> measureFields;
  private int calcFieldAmount=6;//计算指标的个数
  private int ratioFieldAmount=4;//同环比的个数
  private int calcUseFieldAmount=10;//计算指标使用字段的个数

  /**
   * 是否可以新建指标
   * 目标主题暂不支持新建指标
   */
  private boolean canCreateMeasure;

  /**
   * 图表编辑态指标权限控制
   */
  private Permission measurePermission;
}
