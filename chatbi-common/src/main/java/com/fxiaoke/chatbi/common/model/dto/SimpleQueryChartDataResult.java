package com.fxiaoke.chatbi.common.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * 极简版的图表数据查询结果
 * 只包含LLM分析所需的核心数据
 */
@Data
public class SimpleQueryChartDataResult implements Serializable {
    private static final long serialVersionUID = -1L;
    
    /**
     * 数据集
     */
    @JSONField(name = "dataSet")
    private List<SimpleStatColumnData> dataSet;
    
    /**
     * 数据更新时间
     */
    @JSONField(name = "statUpdateTime")
    private String statUpdateTime;
} 