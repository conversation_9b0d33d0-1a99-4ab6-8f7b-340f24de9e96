package com.fxiaoke.chatbi.common.sse.publisher;

import com.fxiaoke.chatbi.common.sse.event.SseEvent;

/**
 * SSE事件发布服务接口
 * 负责发布SSE事件到客户端
 */
public interface SseEventPublisher {
    /**
     * 发布事件到指定会话
     * 
     * @param sessionId 会话ID
     * @param event     要发布的事件
     */
    void publish(String sessionId, SseEvent event);

    /**
     * 完成指定会话的SSE连接
     * 
     * @param sessionId 会话ID
     */
    void complete(String sessionId);
}