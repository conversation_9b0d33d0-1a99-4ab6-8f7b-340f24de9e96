package com.fxiaoke.chatbi.common.model.request;

import com.fxiaoke.chatbi.common.model.enums.KnowledgeType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 知识提示词请求模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KnowledgePromptRequest {
    
    /**
     * 知识类型列表
     * 为空则返回所有类型知识
     */
    private KnowledgeType[] knowledgeTypes;
    
    /**
     * 图表ID列表
     * 当需要获取特定图表知识时使用
     */
    private List<String> chartIds;
    
    /**
     * 请求类型
     * 1: 完整知识
     * 2: 图表知识
     * 3: 特定图表知识
     * 4: 特定类型知识
     */
    private Integer requestType;
} 