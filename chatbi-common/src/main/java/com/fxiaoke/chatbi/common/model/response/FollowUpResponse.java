package com.fxiaoke.chatbi.common.model.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 追问阶段响应
 * 包含处理查询的第四阶段数据：后续追问建议
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class FollowUpResponse extends AsyncResponse {
  /**
   * 追问问题列表
   * 基于当前分析结果，提供给用户的后续问题建议
   * 包含需要澄清的问题或建议的分析方向
   */
  private List<String> followUpQuestions;
} 