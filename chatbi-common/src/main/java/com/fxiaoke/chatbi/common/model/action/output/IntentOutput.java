package com.fxiaoke.chatbi.common.model.action.output;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.chatbi.common.model.action.ActionOutput;
import com.fxiaoke.chatbi.common.model.intent.FilterInfo;
import com.fxiaoke.chatbi.common.model.intent.UserIntent;
import com.fxiaoke.chatbi.common.model.prompts.PromptTemplateType;
import com.fxiaoke.chatbi.common.model.reasoning.ReasoningData;
import lombok.Builder;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 输出结果
 */
@Data
@Builder
public class IntentOutput implements ActionOutput, ReasoningData {
  private UserIntent intent;

  @Override
  public Map<String, Object> getDescription() {
    Map<String, Object> preHandleStage = new HashMap<>();

    //分析类型
    preHandleStage.put("intentAnalysisType", intent.getExtractedInfo().getAnalysisType() + "分析");
    //维度
    preHandleStage.put("dimensions", String.join(",", intent.getExtractedInfo().getDimensions()));
    //指标
    preHandleStage.put("measures", String.join(",", intent.getExtractedInfo().getMeasures()));
    //过滤
    preHandleStage.put("intentFilters", intent.getExtractedInfo().getFilters().stream()
            .map(FilterInfo::getField).collect(Collectors.joining("、")));
    
    return preHandleStage;
  }

  @Override
  public Map<String, Object> getActionLog() {
    return getDescription();
  }

  @Override
  public PromptTemplateType getReasoningTemplateType() {
    return PromptTemplateType.REASONING_CONVERT_INTENT;
  }

  @Override
  public PromptTemplateType getActionLogTemplateType() {
    return PromptTemplateType.INTENT_LOG;
  }
}
