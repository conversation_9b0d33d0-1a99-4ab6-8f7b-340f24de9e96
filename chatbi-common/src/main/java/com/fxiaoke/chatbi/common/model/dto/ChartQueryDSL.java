package com.fxiaoke.chatbi.common.model.dto;

import lombok.Data;

import java.util.List;

/**
 * 简化的图表数据查询参数
 * 结构设计参考ChartDesc.java，保持与业务模型一致性
 */
@Data
public class ChartQueryDSL {

  /**
   * 图表ID
   */
  private String viewId;

  /**
   * 图表名称
   */
  private String chartName;

  /**
   * 图表类型
   */
  private String chartType;

  /**
   * 主题ID
   */
  private String schemaId;


  /**
   * 过滤条件
   */
  private List<FilterConfig> filters;

  /**
   * 结果限制
   * 默认20条
   */
  private Integer limit = 20;

  /**
   * 分析类型
   * TREND：趋势分析
   * COMPARISON：对比分析
   * DISTRIBUTION：分布分析
   * RANKING：排行分析
   * MOM：环比分析
   * YOY：同比分析
   */
  private String analysisType;

  /**
   * 相关性
   * 例如：0.85
   */
  private float relevance;

  private String reasoning;
}
