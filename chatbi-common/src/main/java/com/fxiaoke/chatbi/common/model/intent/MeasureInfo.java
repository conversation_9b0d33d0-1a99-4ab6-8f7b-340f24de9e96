package com.fxiaoke.chatbi.common.model.intent;

import lombok.Builder;
import lombok.Data;

/**
 * 查询信息
 * 存储从意图中提取的查询相关信息
 */


/**
 * 指标信息
 */
@Data
@Builder
public class MeasureInfo {
  /**
   * 指标代码
   */
  private String code;

  /**
   * 指标名称
   */
  private String name;

  /**
   * 聚合方式
   * 例如: SUM, AVG, COUNT等
   */
  private String aggregation;

  /**
   * 格式化
   * 例如: 数字、百分比、金额等
   */
  private String format;
}


