package com.fxiaoke.chatbi.common.model.intent;

import lombok.Builder;
import lombok.Data;

/**
 * 时间范围
 */
@Data
@Builder
public class TimeRange {
  /**
   * 开始时间
   */
  private Long startTime;

  /**
   * 结束时间
   */
  private Long endTime;

  /**
   * 时间粒度
   * 例如: DAY, WEEK, MONTH, YEAR
   */
  private String granularity;

  /**
   * 相对时间表达式
   * 例如: last_7_days, last_month等
   */
  private String relativeExpr;
}
