package com.fxiaoke.chatbi.common.sse.emitter.impl;

import com.fxiaoke.chatbi.common.sse.emitter.SseEventEmitter;
import com.fxiaoke.chatbi.common.sse.emitter.SseEventEmitterRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 内存中的事件发送器存储库实现
 * 使用ConcurrentHashMap存储SSE连接
 */
@Slf4j
@Component
public class InMemorySseEventEmitterRepository implements SseEventEmitterRepository {
    private final Map<String, SseEventEmitter> emitters = new ConcurrentHashMap<>();
    private final Map<String, Long> emitterLastAccessTime = new ConcurrentHashMap<>();

    // 连接超时时间，默认30分钟
    private static final long TIMEOUT_MINUTES = 30;

    @Override
    public void save(SseEventEmitter emitter) {
        String sessionId = emitter.getSessionId();
        log.info("保存SSE连接: sessionId={}", sessionId);
        emitters.put(sessionId, emitter);
        emitterLastAccessTime.put(sessionId, System.currentTimeMillis());
    }

    @Override
    public SseEventEmitter get(String sessionId) {
        SseEventEmitter emitter = emitters.get(sessionId);
        if (emitter == null) {
            log.warn("未找到SSE连接: sessionId={}", sessionId);
            return null;
        }

        if (!emitter.isActive()) {
            log.warn("SSE连接已关闭，将其移除: sessionId={}", sessionId);
            remove(sessionId);
            return null;
        }

        // 更新最后访问时间
        emitterLastAccessTime.put(sessionId, System.currentTimeMillis());
        return emitter;
    }

    @Override
    public void remove(String sessionId) {
        log.info("移除SSE连接: sessionId={}", sessionId);
        emitters.remove(sessionId);
        emitterLastAccessTime.remove(sessionId);
    }

    /**
     * 清理过期的连接
     * 每10分钟执行一次
     */
    @Scheduled(fixedRate = 10, timeUnit = TimeUnit.MINUTES)
    public void cleanupExpiredEmitters() {
        long now = System.currentTimeMillis();
        List<String> expiredSessionIds = new ArrayList<>();

        // 查找过期的连接
        for (Map.Entry<String, Long> entry : emitterLastAccessTime.entrySet()) {
            String sessionId = entry.getKey();
            long lastAccessTime = entry.getValue();

            // 检查是否过期（超过30分钟未访问）
            if (now - lastAccessTime > TimeUnit.MINUTES.toMillis(TIMEOUT_MINUTES)) {
                expiredSessionIds.add(sessionId);
            }
        }

        // 移除过期的连接
        for (String sessionId : expiredSessionIds) {
            SseEventEmitter emitter = emitters.get(sessionId);
            if (emitter != null) {
                try {
                    emitter.complete();
                } catch (Exception e) {
                    log.warn("关闭过期SSE连接时发生异常: sessionId={}", sessionId, e);
                }
            }
            remove(sessionId);
        }

        if (!expiredSessionIds.isEmpty()) {
            log.info("已清理{}个过期的SSE连接", expiredSessionIds.size());
        }
    }

    /**
     * 获取当前活跃连接数
     */
    public int getActiveEmitterCount() {
        return (int) emitters.values().stream()
                .filter(SseEventEmitter::isActive)
                .count();
    }
}