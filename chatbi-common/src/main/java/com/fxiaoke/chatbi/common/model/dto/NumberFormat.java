package com.fxiaoke.chatbi.common.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 数值型数值格式化
 * @date 2022/1/19 14:16
 */
@Data
public class NumberFormat {
  /**
   * 是否展示千分隔符
   */
  @JSONField(name = "isShowThousandSplit")
  private boolean isShowThousandSplit = true;
  /**
   * 单位，无 则值为0,以 万 为单位，则此值为10000，以亿为单位，则此值为 100000000
   */
  @JSONField(name = "unit")
  private int unit = 0;
}
