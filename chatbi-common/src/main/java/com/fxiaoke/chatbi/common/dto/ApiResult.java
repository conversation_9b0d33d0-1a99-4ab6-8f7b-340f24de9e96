package com.fxiaoke.chatbi.common.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiResult<T> implements Serializable {
  private static final long serialVersionUID = 1L;

  // HTTP状态码常量
  public static final int SUCCESS = 200;
  public static final int BAD_REQUEST = 400;
  public static final int INTERNAL_ERROR = 500;

  private int code;
  private String message;
  private String i18nCode;
  private String timestamp;
  private T data;

  public ApiResult() {
    this.timestamp = String.valueOf(System.currentTimeMillis());
  }

  public ApiResult(int code, String message, String i18nCode, T data) {
    this.code = code;
    this.message = message;
    this.i18nCode = i18nCode;
    this.timestamp = String.valueOf(System.currentTimeMillis());
    this.data = data;
  }

  /**
   * 返回成功结果
   *
   * @param data 数据载荷
   * @return ApiResult实例
   */
  public static <T> ApiResult<T> success(T data) {
    ApiResult<T> result = new ApiResult<>();
    result.setCode(SUCCESS);
    result.setData(data);
    return result;
  }

  /**
   * 返回成功结果（无数据）
   *
   * @return ApiResult实例
   */
  public static <T> ApiResult<T> ok() {
    ApiResult<T> result = new ApiResult<>();
    result.setCode(SUCCESS);
    return result;
  }

  /**
   * 返回错误结果（带错误信息）
   *
   * @param message 错误信息
   * @return ApiResult实例
   */
  public static <T> ApiResult<T> error(String message) {
    ApiResult<T> result = new ApiResult<>();
    result.setCode(INTERNAL_ERROR);
    result.setMessage(message);
    return result;
  }

  /**
   * 判断响应是否成功
   *
   * @return 是否成功
   */
  public boolean isSuccess() {
    return this.code == SUCCESS;
  }

  /**
   * 创建构建器
   */
  public static <T> Builder<T> builder() {
    return new Builder<>();
  }

  /**
   * ApiResult建造者类
   */
  public static class Builder<T> {
    private int code = SUCCESS;
    private String message;
    private String i18nCode;
    private T data;

    public Builder<T> code(int code) {
      this.code = code;
      return this;
    }

    public Builder<T> message(String message) {
      this.message = message;
      return this;
    }

    public Builder<T> i18nCode(String i18nCode) {
      this.i18nCode = i18nCode;
      return this;
    }

    public Builder<T> data(T data) {
      this.data = data;
      return this;
    }

    public ApiResult<T> build() {
      return new ApiResult<>(code, message, i18nCode, data);
    }
  }
}