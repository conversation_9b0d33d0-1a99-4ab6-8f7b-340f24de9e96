package com.fxiaoke.chatbi.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 向量化相关配置参数
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "chatbi.embedding")
public class EmbeddingProperties {
    
    /**
     * 向量化模型名称
     * 默认使用text-embedding-ada-002
     */
    private String embeddingModel = "FXIAOKE-MODEL-text-embedding-ada-002";
    
    /**
     * 批处理大小
     * 当批量向量化时，每批处理的文本数量
     */
    private int batchSize = 20;
    
    /**
     * 向量维度
     * 生成的向量维度大小
     */
    private int embeddingDimension = 1536;
    /**
     * 向量相似度阈值
     * 低于此阈值的结果将被过滤
     */
    private double similarityThreshold = 0.6;
    
    /**
     * 默认检索结果数量限制
     */
    private int defaultSearchLimit = 10;
    
    /**
     * 是否启用向量索引
     */
    private boolean useEmbeddingIndex = true;
} 