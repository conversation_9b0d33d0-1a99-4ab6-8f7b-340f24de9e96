package com.fxiaoke.chatbi.common.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 商机2.0配置
 * @date 2022/1/19 14:08
 */
@Data
public class FormatConfig {
  /**
   * 数值显示格式
   */
  @JSONField(name = "numberFormatConfig")
  private NumberFormatConfig numberFormatConfig;
  /**
   * 转化率格式配置 totalConversionRate-总转化率 stageConversionRate-阶段间转化率
   */
  @JSONField(name = "conversionRateFormatConfig")
  private String conversionRateFormatConfig;
  /**
   * 是否统计跳过状态 1-统计 0-不统计
   */
  @JSONField(name = "statisticsSkipStatus")
  private Integer statisticsSkipStatus;
}
