package com.fxiaoke.chatbi.common.mapstruct;

import com.facishare.bi.common.entities.stat.FilterList;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Mapper(componentModel = "spring", nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
@Slf4j
public abstract class FilterEntityMapper {

  public static FilterEntityMapper INSTANCE = Mappers.getMapper(FilterEntityMapper.class);

  public abstract List<com.facishare.bi.common.entities.FilterList> toFilterList(List<FilterList> filterLists);

}
