package com.fxiaoke.chatbi.common.model.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 解读阶段响应
 * 包含处理查询的第三阶段数据：数据解读
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class InsightResponse extends AsyncResponse {
  /**
   * 总结
   */
  private String quickInsight;
  
  /**
   * 完整解读
   * 包含详细的数据分析和业务建议
   */
  private String fullInsight;
} 