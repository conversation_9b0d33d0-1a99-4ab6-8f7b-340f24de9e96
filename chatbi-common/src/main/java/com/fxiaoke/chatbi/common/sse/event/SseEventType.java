package com.fxiaoke.chatbi.common.sse.event;

/**
 * SSE事件类型枚举
 * 定义系统支持的所有SSE事件类型
 */
public enum SseEventType {
    /**
     * 思考事件，用于推送推理过程
     */
    THINKING("thinking"),

    /**
     * 提示事件，用于推送提示信息
     */
    TIPS("tips"),

    /**
     * 数据事件，用于推送图表数据
     */
    DATA("data"),

    /**
     * 建议事件，用于推送追问建议信息
     */
    SUGGEST("suggest"),

    /**
     * 完成事件，用于标记会话结束
     */
    FINISH("finish");

    private final String eventName;

    SseEventType(String eventName) {
        this.eventName = eventName;
    }

    public String getEventName() {
        return eventName;
    }
}