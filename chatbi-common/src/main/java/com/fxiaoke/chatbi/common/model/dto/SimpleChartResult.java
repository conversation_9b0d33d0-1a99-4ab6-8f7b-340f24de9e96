package com.fxiaoke.chatbi.common.model.dto;

import com.facishare.bi.common.entities.QueryChartDataResult;
import com.facishare.bi.common.entities.StatChartConfResult;
import com.fxiaoke.chatbi.common.utils.QueryChartDataConverter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 超轻量级图表结果类
 * 整合图表数据和图表配置的核心信息
 * 便于LLM解析和前端渲染
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SimpleChartResult implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 图表标题
     */
    private String title;
    
    /**
     * 图表类型
     * 如：line, bar, pie等
     */
    private String chartType;
    
    /**
     * 图表数据集
     */
    private List<SimpleStatColumnData> dataset;
    
    /**
     * 维度字段名称列表
     */
    private List<String> dimensions;
    
    /**
     * 指标字段名称列表
     */
    private List<String> measures;
    
    /**
     * 数据更新时间
     */
    private String updateTime;
    
    /**
     * 是否在图表中显示标题
     * 当外部容器已经显示了标题时，可以设置为false以避免重复显示
     * 默认为false，表示不在图表中显示标题（由外部容器控制）
     */
    @Builder.Default
    private Boolean showTitleInChart = false;
    
    /**
     * 从复杂查询结果转换为简单结果
     */
    public static SimpleChartResult fromFullResult(QueryChartDataResult chartData, StatChartConfResult chartConfig) {
        if (chartData == null) {
            return createEmptyResult(chartConfig);
        }
        
        SimpleChartResultBuilder builder = SimpleChartResult.builder();
        
        // 设置基本信息
        if (chartConfig != null) {
            builder.title(chartConfig.getViewName());
            builder.chartType(chartConfig.getChartType());
        }
        
        // 设置数据集
        SimpleQueryChartDataResult simpleData = convertToSimpleData(chartData);
        if (simpleData != null) {
            builder.dataset(simpleData.getDataSet());
            builder.updateTime(simpleData.getStatUpdateTime());
        }
        
        // 提取维度和指标字段
        List<String> dimensions = new ArrayList<>();
        List<String> measures = new ArrayList<>();
        if (simpleData != null && simpleData.getDataSet() != null) {
            for (SimpleStatColumnData column : simpleData.getDataSet()) {
                // 简单判断：有total或average的通常是指标，否则是维度
                if (column.getTotal() != null || column.getAverage() != null) {
                    measures.add(column.getFieldName());
                } else {
                    dimensions.add(column.getFieldName());
                }
            }
        }
        
        builder.dimensions(dimensions);
        builder.measures(measures);
        
        return builder.build();
    }
    
    /**
     * 将复杂的QueryChartDataResult转换为简化版
     * 使用QueryChartDataConverter进行转换
     */
    private static SimpleQueryChartDataResult convertToSimpleData(QueryChartDataResult fullResult) {
        if (fullResult == null) {
            return null;
        }
        
        // 使用QueryChartDataConverter工具类来转换数据
        return QueryChartDataConverter.toSimple(fullResult);
    }
    
    /**
     * 创建空结果
     */
    private static SimpleChartResult createEmptyResult(StatChartConfResult chartConfig) {
        return SimpleChartResult.builder()
            .title(chartConfig != null ? chartConfig.getViewName() : "未知图表")
            .chartType(chartConfig != null ? chartConfig.getChartType() : "unknown")
            .dataset(Collections.emptyList())
            .dimensions(Collections.emptyList())
            .measures(Collections.emptyList())
            .build();
    }
} 