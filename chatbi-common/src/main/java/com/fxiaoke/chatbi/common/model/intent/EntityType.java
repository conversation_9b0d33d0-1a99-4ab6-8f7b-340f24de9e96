package com.fxiaoke.chatbi.common.model.intent;

/**
 * 实体类型枚举
 * 用于标识从用户输入中提取的不同类型实体
 */
public enum EntityType {
    /**
     * 维度，如部门、产品、区域等
     */
    DIMENSION("维度"),
    
    /**
     * 指标，如销售额、客户数、转化率等
     */
    METRIC("指标"),
    
    /**
     * 过滤条件，如"大于1000元"、"前10名"等
     */
    FILTER("过滤条件"),
    
    /**
     * 时间范围，如"上个月"、"近7天"等
     */
    TIME_RANGE("时间范围"),
    
    /**
     * 图表类型，如柱状图、折线图、饼图等
     */
    CHART_TYPE("图表类型"),
    
    /**
     * 聚合方式，如求和、平均值、计数等
     */
    AGGREGATION("聚合方式"),
    
    /**
     * 排序方式，如升序、降序等
     */
    SORT("排序方式"),
    
    /**
     * 其他类型
     */
    OTHER("其他");
    
    private final String description;
    
    EntityType(String description) {
        this.description = description;
    }
    
    /**
     * 获取实体类型描述
     * @return 描述文本
     */
    public String getDescription() {
        return description;
    }
} 