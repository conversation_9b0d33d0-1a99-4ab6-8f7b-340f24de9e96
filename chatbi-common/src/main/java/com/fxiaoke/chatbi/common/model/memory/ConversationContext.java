package com.fxiaoke.chatbi.common.model.memory;

import com.fxiaoke.chatbi.common.model.ChatMessage;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 对话上下文
 * 存储与BI分析相关的上下文信息，作为后端服务不需要维护完整的对话历史
 */
@Data
public class ConversationContext {
    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 用户身份信息
     */
    private UserIdentity userIdentity;

    /**
     * 对话历史 - 对于BI分析服务，只需保留必要的历史记录用于上下文理解
     */
    private List<ChatMessage> history = new ArrayList<>();
    
    /**
     * 工作记忆 - 存储当前分析会话的临时数据
     */
    private Map<String, Object> workingMemory = new HashMap<>();
    
    /**
     * 最后一次分析意图
     */
    private String lastIntent;
    
    /**
     * 最后一次执行的计划ID
     */
    private String lastPlanId;
    
    /**
     * 创建时间
     */
    private long createTime = System.currentTimeMillis();
    
    /**
     * 最后更新时间
     */
    private long updateTime = System.currentTimeMillis();
} 