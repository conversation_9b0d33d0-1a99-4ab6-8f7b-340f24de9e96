package com.fxiaoke.chatbi.common.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 过滤配置
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FilterConfig {
  /**
   * 字段ID
   */
  private String fieldId;

  /**
   * 字段名称
   */
  private String fieldName;

  /**
   * 操作符
   * EQ：等于
   * GT：大于
   * LT：小于
   * LIKE：包含
   * BETWEEN：介于
   */
  private String operator;
  /**
   * 过滤值1
   */
  private String value1;

  /**
   * 过滤值2
   */
  private String value2;

  /**
   * 日期范围代码
   */
  private String dateRange;

  /**
   * 时间粒度
   * DAY：天
   * WEEK：周
   * MONTH：月
   * QUARTER：季
   * YEAR：年
   */
  private String timeGranularity;
}
