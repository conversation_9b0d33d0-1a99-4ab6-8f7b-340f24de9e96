package com.fxiaoke.chatbi.common.model.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * ECharts配置选项
 * 用于表示ECharts的配置选项，可以直接被前端使用
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EChartsOption {
    /**
     * 标题配置
     */
    private Title title;

    /**
     * 提示框配置
     */
    private Tooltip tooltip;

    /**
     * 图例配置
     */
    private Legend legend;

    /**
     * X轴配置
     */
    private List<Axis> xAxis;

    /**
     * Y轴配置
     */
    private List<Axis> yAxis;

    /**
     * 系列配置
     */
    private List<Series> series;

    /**
     * 颜色配置
     */
    private List<String> color;

    /**
     * 网格配置
     */
    private Grid grid;

    /**
     * 工具箱配置
     */
    private Toolbox toolbox;

    /**
     * 数据缩放配置
     */
    private List<DataZoom> dataZoom;

    /**
     * 视觉映射配置
     */
    private List<VisualMap> visualMap;

    /**
     * 其他配置项
     */
    private Map<String, Object> extraOptions;

    /**
     * 标题配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Title {
        private String text;
        private String subtext;
        private String left;
        private String top;
    }

    /**
     * 提示框配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Tooltip {
        private String trigger;
        private String formatter;
        private AxisPointer axisPointer;
    }

    /**
     * 坐标轴指示器配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AxisPointer {
        private String type;
    }

    /**
     * 图例配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Legend {
        private String type;
        private List<String> data;
        private String left;
        private String top;
    }

    /**
     * 坐标轴配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Axis {
        private String type;
        private List<String> data;
        private String name;
        private AxisLabel axisLabel;
        private Boolean boundaryGap;
    }

    /**
     * 坐标轴标签配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AxisLabel {
        private Integer rotate;
        private String formatter;
    }

    /**
     * 系列配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Series {
        EChartsOption.ItemStyle itemStyle;
        private String name;
        private String type;
        private List<Object> data;
        private String stack;
        private Label label;
        private Emphasis emphasis;
        
        // 饼图特定属性
        private String radius;
        private List<String> center;
        
        // 折线图特定属性
        private Boolean smooth;
        
        // 漏斗图特定属性
        private String sort;          // 排序方式：'ascending'或'descending'
        private String width;         // 宽度，支持百分比
        private String height;        // 高度，支持百分比
        private String minSize;       // 最小尺寸，如'0%', '20%'
        private String maxSize;       // 最大尺寸，如'100%'
        private Integer gap;          // 间距
        private String orient;        // 方向，'vertical'或'horizontal'
        private String funnelAlign;   // 对齐方式：'left', 'right', 'center'
        
        // 其他扩展属性
        private Map<String, Object> extraOptions;
    }

    /**
     * 标签配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Label {
        private Boolean show;
        private String position;
        private String formatter;
        private Integer fontSize;
    }

    /**
     * 高亮配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Emphasis {
        private Label label;
        private ItemStyle itemStyle;
    }

    /**
     * 项目样式配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ItemStyle {
        private String color;
        private Integer borderWidth;
        private String borderColor;
        private Integer shadowBlur;
        private Integer shadowOffsetX;
        private String shadowColor;
    }

    /**
     * 网格配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Grid {
        private String left;
        private String right;
        private String bottom;
        private String top;
        private Boolean containLabel;
    }

    /**
     * 工具箱配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Toolbox {
        private Boolean show;
        private Feature feature;
    }

    /**
     * 工具箱功能配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Feature {
        private SaveAsImage saveAsImage;
        private DataView dataView;
        private MagicType magicType;
        private Restore restore;
    }

    /**
     * 保存为图片配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SaveAsImage {
        private Boolean show;
    }

    /**
     * 数据视图配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DataView {
        private Boolean show;
    }

    /**
     * 魔法类型配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MagicType {
        private Boolean show;
        private List<String> type;
    }

    /**
     * 还原配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Restore {
        private Boolean show;
    }

    /**
     * 数据缩放配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DataZoom {
        private String type;
        private Integer start;
        private Integer end;
    }

    /**
     * 视觉映射配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VisualMap {
        private String type;
        private Integer min;
        private Integer max;
        private List<String> color;
    }
} 