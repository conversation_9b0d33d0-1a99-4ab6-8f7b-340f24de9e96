package com.fxiaoke.chatbi.common.sse.emitter;

import com.fxiaoke.chatbi.common.sse.event.SseEvent;

/**
 * SSE事件发送器接口
 * 负责发送SSE事件到客户端
 */
public interface SseEventEmitter {
    /**
     * 发送SSE事件
     * 
     * @param event 要发送的事件
     */
    void emit(SseEvent event);

    /**
     * 完成SSE连接
     */
    void complete();

    /**
     * 检查SSE连接是否活跃
     * 
     * @return 如果连接活跃返回true，否则返回false
     */
    boolean isActive();

    /**
     * 获取会话ID
     * 
     * @return 会话ID
     */
    String getSessionId();
}