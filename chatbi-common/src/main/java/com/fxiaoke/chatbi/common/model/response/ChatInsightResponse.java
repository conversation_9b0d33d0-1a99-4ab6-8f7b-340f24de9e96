package com.fxiaoke.chatbi.common.model.response;

import com.fxiaoke.chatbi.common.model.LoadingStatus;
import com.fxiaoke.chatbi.common.model.MessageType;
import com.fxiaoke.chatbi.common.model.Recommendation;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * ChatBI API响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatInsightResponse {
  private String requestId;
  private LoadingStatus loadingStatus;
  private String message;
  private MessageType messageType;


  /*
   * 简单解读
   */
  private String quickInsight;
  /**
   * 完整解读
   * 包含详细的数据分析和业务建议
   */
  private String fullInsight;

  private List<Recommendation> recommendations;

  private List<String> followUpQuestions;


}