package com.fxiaoke.chatbi.common.exception;

import lombok.Getter;

/**
 * 基础异常类
 * 所有业务异常的基类，与错误码枚举关联
 * 不直接包含错误消息，仅包含错误码和用于消息模板的参数
 */
@Getter
public class BaseException extends RuntimeException {

  private static final long serialVersionUID = 1L;

  /**
   * 错误码
   */
  private final String errorCode;

  /**
   * 国际化键
   */
  private final String i18nKey;

  /**
   * 消息参数，用于替换消息模板中的占位符
   */
  private final Object[] args;

  /**
   * 创建异常
   *
   * @param errorEnum 错误枚举
   */
  public BaseException(ChatbiErrorCodeEnum errorEnum) {
    this(errorEnum, null, (Object[]) null);
  }

  /**
   * 创建异常
   *
   * @param errorEnum 错误枚举
   * @param cause     原因
   */
  public BaseException(ChatbiErrorCodeEnum errorEnum, Throwable cause) {
    this(errorEnum, cause, (Object[]) null);
  }

  /**
   * 创建异常
   *
   * @param errorEnum 错误枚举
   * @param args      消息参数，用于替换消息模板中的占位符
   */
  public BaseException(ChatbiErrorCodeEnum errorEnum, Object... args) {
    this(errorEnum, null, args);
  }

  /**
   * 创建异常
   *
   * @param errorEnum 错误枚举
   * @param cause     原因
   * @param args      消息参数，用于替换消息模板中的占位符
   */
  public BaseException(ChatbiErrorCodeEnum errorEnum, Throwable cause, Object... args) {
    super(cause);
    this.errorCode = errorEnum.getErrorCode();
    this.i18nKey = errorEnum.getI18nKey();
    this.args = args;
  }

  /**
   * 获取错误消息
   * 仅返回错误码，实际错误消息由消息解析器处理
   */
  @Override
  public String getMessage() {
    return "ErrorCode: " + errorCode;
  }
} 