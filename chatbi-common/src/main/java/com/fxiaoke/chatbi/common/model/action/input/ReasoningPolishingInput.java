package com.fxiaoke.chatbi.common.model.action.input;

import com.fxiaoke.chatbi.common.model.action.ActionInput;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReasoningPolishingInput extends ActionInput {
  /**
   * 原始推理内容
   */
  private String originalReasoning;
  
  /**
   * Freemarker模板内容
   * 用于直接处理推理内容，不依赖LLM
   */
  private String templateContent;
}
