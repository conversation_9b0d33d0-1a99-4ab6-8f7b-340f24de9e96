package com.fxiaoke.chatbi.common.sse.event.impl;

import com.fxiaoke.chatbi.common.sse.SSEBody;
import com.fxiaoke.chatbi.common.sse.event.SseEventType;
import lombok.Getter;

/**
 * 思考事件实现
 * 用于推送推理过程
 */
@Getter
public class ThinkingSseEvent extends BaseSseEvent {
    private final String reasoning;

    /**
     * 构造函数
     * 
     * @param reasoning 推理内容
     * @param delta     是否为增量数据
     */
    public ThinkingSseEvent(String reasoning, boolean delta) {
        super(SseEventType.THINKING, delta);
        this.reasoning = reasoning;
    }

    @Override
    public Object getEventData() {
        return new SSEBody(reasoning, isDelta());
    }
}