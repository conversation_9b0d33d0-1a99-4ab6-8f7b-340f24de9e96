package com.fxiaoke.chatbi.common.exception;

import com.fxiaoke.i18n.client.I18nClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.Locale;

@Service
public class InnerI18nService implements InitializingBean {

  private I18nClient i18nClient;

  public String get(String code, Locale locale) {
    return get(code, 0, locale.toLanguageTag(), code);
  }

  public String get(String code, Locale locale, String defaultVal) {
    return get(code, 0, locale.toLanguageTag(), defaultVal);
  }

  public String get(String code, String locale, String defaultVal) {
    return get(code, 0, locale, defaultVal);
  }

  public String get(String code, long tenantId, String locale, String defaultVal) {
    if (StringUtils.isEmpty(code)) {
      return defaultVal;
    }
    try {
      return i18nClient.get(code, tenantId, locale, defaultVal);
    } catch (Exception e) {
      return defaultVal;
    }
  }

  @Override
  public void afterPropertiesSet() throws Exception {
    i18nClient = I18nClient.getInstance();
    i18nClient.initWithTags("server");
  }
}
