package com.fxiaoke.chatbi.common.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 运营管理系统操作上下文
 * 用于记录管理员身份和当前操作的租户信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OperationContext {
    /**
     * 管理员ID
     * 标识执行操作的管理员
     */
    private String adminId;
    
    /**
     * 管理员名称
     */
    private String adminName;
    
    /**
     * 管理员角色
     */
    private List<String> roles;
    
    /**
     * 目标租户ID
     * 当前操作针对的租户
     */
    private String targetTenantId;
    
    /**
     * 便捷获取当前操作的租户ID
     * @return 目标租户ID
     */
    public String getTenantId() {
        return targetTenantId;
    }
    
    /**
     * 将OperationContext转换为UserIdentity
     * 用于向后兼容需要UserIdentity的服务接口
     * @return 转换后的UserIdentity对象
     */
    public UserIdentity toUserIdentity() {
        return UserIdentity.builder()
                .userId(this.adminId)
                .tenantId(this.targetTenantId)
                .build();
    }
} 