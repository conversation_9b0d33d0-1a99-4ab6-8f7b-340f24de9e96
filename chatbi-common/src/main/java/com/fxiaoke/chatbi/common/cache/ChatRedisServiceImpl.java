package com.fxiaoke.chatbi.common.cache;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.github.jedis.support.JedisCmd;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class ChatRedisServiceImpl implements ChatRedisService {
    @Autowired
    @Qualifier("chatRedisSupport")
    private JedisCmd jedis;

    @Override
    public void set(String key, String value, long expireSeconds) {
        try {
            jedis.setex(key, expireSeconds, value);
          } catch (Exception e) {
            log.error("Error setting Redis key: {}", key, e);
            throw e;
          }
    }

    @Override
    public String get(String key) {
        try {
            return jedis.get(key);
          } catch (Exception e) {
            log.error("Error getting Redis key: {}", key, e);
            return null;
          }
    }

}
