package com.fxiaoke.chatbi.common.config;

import com.github.jedis.support.JedisFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Redis配置类
 */
@Configuration
public class RedisConfig {
  /**
   * Redis键前缀 - ChatBI数据
   */
  public static final String CHATBI_DATA_KEY_PREFIX = "chatbi:data:";

  /**
   * Redis键前缀 - 数据解读数据
   */
  public static final String CHATBI_INSIGHT_KEY_PREFIX = "chatbi:insight:";

  /**
   * Redis键前缀 - 追问建议数据
   */
  public static final String CHATBI_FOLLOWUP_KEY_PREFIX = "chatbi:followup:";

  /**
   * Redis键前缀 - 追问建议数据
   */
  public static final String CHATBI_ACRTION_LOG_KEY_PREFIX = "chatbi:log:";

  /**
   * 配置ChatBI Redis支持
   *
   * @return JedisFactoryBean实例
   */
  @Bean(name = "chatRedisSupport")
  public JedisFactoryBean chatRedisSupport() {
    JedisFactoryBean jedisFactoryBean = new JedisFactoryBean();
    jedisFactoryBean.setConfigName("fs-bi-cache-redis");
    return jedisFactoryBean;
  }
}
