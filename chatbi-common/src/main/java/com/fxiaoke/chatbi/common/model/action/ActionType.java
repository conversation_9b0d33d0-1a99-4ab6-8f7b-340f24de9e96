package com.fxiaoke.chatbi.common.model.action;

import lombok.Getter;

/**
 * Action类型枚举
 */
@Getter
public enum ActionType {

  /**
   * 查询计划
   */
  QUERY_DSL("query_dsl", "查询计划"),

  /**
   * 数据查询
   */
  DATA_QUERY("data_query", "数据查询"),

  /**
   * 数据洞察
   */
  DATA_INSIGHT("data_insight", "数据洞察"),

  /**
   * 推荐
   */
  FOLLOW_UP_QUESTION("follow_up_question", "推荐"),

  /**
   * 推理润色
   */
  REASONING_POLISHING("reasoning_polishing", "推理润色"),

  /**
   * 意图识别
   */
  INTENT_RECOGNITION("intent_recognition", "意图识别"),

  /**
   * 问题预处理
   */
  QUERY_PREPROCESSING("query_preprocessing", "问题预处理"),

  /**
   * 知识检索
   */
  KNOWLEDGE_RETRIEVAL("knowledge_retrieval", "知识检索"),

  /**
   * 归因分析
   */
  ATTRIBUTION("attribution", "归因分析"),

  /**
   * 对话生成
   */
  RESPONSE_GENERATION("response_generation", "对话生成");

  /**
   * 类型编码
   */
  private final String code;

  /**
   * 类型描述
   */
  private final String desc;

  ActionType(String code, String desc) {
    this.code = code;
    this.desc = desc;
  }

  /**
   * 通过code获取ActionType
   */
  public static ActionType getByCode(String code) {
    if (code == null || code.isEmpty()) {
      return null;
    }

    for (ActionType type : ActionType.values()) {
      if (type.getCode().equals(code)) {
        return type;
      }
    }

    return null;
  }

  /**
   * 从字符串获取ActionType
   *
   * @param name 枚举名称
   * @return ActionType实例，如果不存在返回null
   */
  public static ActionType fromString(String name) {
    try {
      return ActionType.valueOf(name);
    } catch (IllegalArgumentException e) {
      return null;
    }
  }
} 