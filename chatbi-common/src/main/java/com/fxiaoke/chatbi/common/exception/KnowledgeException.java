package com.fxiaoke.chatbi.common.exception;

/**
 * Knowledge模块异常
 * 用于知识检索和存储过程中的异常情况
 */
public class KnowledgeException extends BaseException {

  private static final long serialVersionUID = 1L;

  /**
   * 创建异常
   *
   * @param errorEnum 错误枚举
   */
  public KnowledgeException(ChatbiErrorCodeEnum errorEnum) {
    super(errorEnum);
  }

  /**
   * 创建异常
   *
   * @param errorEnum 错误枚举
   * @param cause     原因
   */
  public KnowledgeException(ChatbiErrorCodeEnum errorEnum, Throwable cause) {
    super(errorEnum, cause);
  }

  /**
   * 创建异常
   *
   * @param errorEnum 错误枚举
   * @param args      消息参数，用于替换消息模板中的占位符
   */
  public KnowledgeException(ChatbiErrorCodeEnum errorEnum, Object... args) {
    super(errorEnum, args);
  }

  /**
   * 创建异常
   *
   * @param errorEnum 错误枚举
   * @param cause     原因
   * @param args      消息参数，用于替换消息模板中的占位符
   */
  public KnowledgeException(ChatbiErrorCodeEnum errorEnum, Throwable cause, Object... args) {
    super(errorEnum, cause, args);
  }
} 