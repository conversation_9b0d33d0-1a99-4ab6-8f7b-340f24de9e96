package com.fxiaoke.chatbi.common.model.prompts;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

/**
 * 提示词模板类型
 */
@Getter
@Slf4j
public enum PromptTemplateType {
    INTENT_RECOGNITION("intent_recognition", "意图识别"),
    CHART_INSIGHT("data_insight", "解读"),
    QUERY_DSL("query_dsl", "查询DSL"),
    FOLLOW_UP_QUESTION("follow_up_question", "追问建议"),
    CHART_QUESTION_GEN("chart_question_gen", "图表问题生成"),
    QUERY_DSL_FILTER("query_dsl_filter", "增量匹配DSL生成"),
    QUERY_DSL_SIMILAR("query_dsl_similar", "相似匹配DSL生成"),
    REASONING_CONVERT_PROCESS("reasoning_convert_process", "推理-预处理"),
    REASONING_CONVERT_KNOWLEDGE("reasoning_convert_knowledge", "推理-知识检索"),
    REASONING_CONVERT_INTENT("reasoning_convert_intent", "推理-意图识别"),
    REASONING_CONVERT_DSL("reasoning_convert_dsl", "推理-dsl"),
    RECALL_LOG("recall_log", "多路召回日志"),
    INTENT_LOG("intent_log", "意图识别日志"),
    DSL_LOG("dsl_log", "dsl日志"),
    QUERY_PRE_PROCESS_LOG("query_pre_process_log", "查询预处理日志"),
    POLISHING_LOG("polishing_log", "润色日志"),
    OTHER("other", "其他");

    private final String templateName;
    private final String description;
    private final Resource resource;

    PromptTemplateType(String templateName, String description) {
        this.templateName = templateName;
        this.description = description;
        this.resource = new ClassPathResource(String.format("prompts/%s.ftl", templateName));
    }

    @Override
    public String toString() {
        return String.format("%s(%s)", name(), description);
    }
}
