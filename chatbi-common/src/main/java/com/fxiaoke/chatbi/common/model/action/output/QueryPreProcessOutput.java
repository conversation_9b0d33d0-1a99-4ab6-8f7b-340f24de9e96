package com.fxiaoke.chatbi.common.model.action.output;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.chatbi.common.model.action.ActionOutput;
import com.fxiaoke.chatbi.common.model.prompts.PromptTemplateType;
import com.fxiaoke.chatbi.common.model.reasoning.ReasoningData;
import lombok.Builder;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 输出结果
 */
@Data
@Builder
public class QueryPreProcessOutput implements ActionOutput, ReasoningData {
    /**
     * 原始查询
     */
    private String originalQuery;

    /**
     * 处理后的查询
     */
    private String processedQuery;

    /**
     * 术语替换映射
     */
    private Map<String, String> termReplacements;

    /**
     * 是否进行了替换
     */
    public boolean hasReplacements() {
        return termReplacements != null && !termReplacements.isEmpty();
    }

    @Override
    public Map<String, Object> getDescription() {
        return new HashMap<>();
//    Map<String, Object> preHandleStage = new HashMap<>();
//    preHandleStage.put("description", !hasReplacements() ? "未进行术语替换" : "进行术语替换");
//    preHandleStage.put("originalQuery", originalQuery);
//    preHandleStage.put("processedQuery", processedQuery);
//    preHandleStage.put("termReplacements", JSON.toJSONString(termReplacements));
//    return preHandleStage;
    }

    @Override
    public Map<String, Object> getActionLog() {
        Map<String, Object> preHandleStage = new HashMap<>();
        preHandleStage.put("description", !hasReplacements() ? "未进行术语替换" : "进行术语替换");
        preHandleStage.put("originalQuery", originalQuery);
        preHandleStage.put("processedQuery", processedQuery);
        preHandleStage.put("termReplacements", JSON.toJSONString(termReplacements));
        return preHandleStage;
    }

    @Override
    public PromptTemplateType getReasoningTemplateType() {
        return PromptTemplateType.REASONING_CONVERT_PROCESS;
    }

    @Override
    public PromptTemplateType getActionLogTemplateType() {
        return PromptTemplateType.QUERY_PRE_PROCESS_LOG;
    }
}
