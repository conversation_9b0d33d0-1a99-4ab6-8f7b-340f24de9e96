package com.fxiaoke.chatbi.common.sse.event.impl;

import com.fxiaoke.chatbi.common.sse.event.SseEvent;
import com.fxiaoke.chatbi.common.sse.event.SseEventType;
import lombok.Getter;

/**
 * SSE事件基础实现类
 * 提供通用的事件属性和方法
 */
@Getter
public abstract class BaseSseEvent implements SseEvent {
    private final SseEventType eventType;
    private final boolean delta;

    /**
     * 构造函数
     * 
     * @param eventType 事件类型
     * @param delta     是否为增量数据
     */
    protected BaseSseEvent(SseEventType eventType, boolean delta) {
        this.eventType = eventType;
        this.delta = delta;
    }

    @Override
    public String getEventName() {
        return eventType.getEventName();
    }

    @Override
    public boolean isDelta() {
        return delta;
    }
}