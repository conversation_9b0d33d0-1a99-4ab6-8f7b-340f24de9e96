package com.fxiaoke.chatbi.common.utils;

import com.alibaba.fastjson.JSON;
import com.facishare.bi.common.dto.CellTypeEnum;
import com.facishare.bi.common.entities.QueryChartDataResult;
import com.facishare.bi.common.entities.StatColumnDataDto;
import com.facishare.bi.common.entities.stat.Cell;
import com.fxiaoke.chatbi.common.model.dto.SimpleCell;
import com.fxiaoke.chatbi.common.model.dto.SimpleQueryChartDataResult;
import com.fxiaoke.chatbi.common.model.dto.SimpleStatColumnData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 图表数据转换工具类
 * 将完整的图表数据转换为极简版本，只保留LLM分析所需的核心数据
 */
@Slf4j
public class QueryChartDataConverter {

    /**
     * 将完整的QueryChartDataResult转换为极简版的SimpleQueryChartDataResult
     */
    public static SimpleQueryChartDataResult toSimple(QueryChartDataResult source) {
        if (source == null) {
            return null;
        }

        SimpleQueryChartDataResult target = new SimpleQueryChartDataResult();
        target.setStatUpdateTime(source.getStatUpdateTime());
        target.setDataSet(convertDataSet(source.getDataSet()));
        
        return target;
    }

    /**
     * 转换数据集
     * 限制结果集大小不超过100行
     */
    private static List<SimpleStatColumnData> convertDataSet(List<StatColumnDataDto> source) {
        if (CollectionUtils.isEmpty(source)) {
            return Collections.emptyList();
        }

        // 获取数据行数（以第一个列的数据量为准）
        int rowCount = 0;
        if (!source.isEmpty() && source.get(0) != null && 
            CollectionUtils.isNotEmpty(source.get(0).getValue())) {
            rowCount = source.get(0).getValue().size();
        }
        
        // 确定需要保留的行数（最多100行）
        int maxRows = Math.min(rowCount, 100);
        
        List<SimpleStatColumnData> target = new ArrayList<>(source.size());
        for (StatColumnDataDto dto : source) {
            if (dto == null) {
                continue;
            }

            SimpleStatColumnData simple = new SimpleStatColumnData();
            simple.setFieldName(dto.getFieldName());
            
            // 限制每列的数据行数
            List<Cell> limitedCells = CollectionUtils.isEmpty(dto.getValue()) ? 
                Collections.emptyList() : 
                dto.getValue().subList(0, Math.min(dto.getValue().size(), maxRows));
                
            simple.setValue(convertCells(limitedCells));
            simple.setTotal(dto.getTotal());
            simple.setAverage(dto.getAverage());
            
            target.add(simple);
        }

        //移除小计、总计行
        removeSubTotal(target, source);

        return target;
    }

    /**
     * 转换单元格数据
     */
    private static List<SimpleCell> convertCells(List<Cell> source) {
        if (CollectionUtils.isEmpty(source)) {
            return Collections.emptyList();
        }

        List<SimpleCell> target = new ArrayList<>(source.size());
        for (Cell cell : source) {
            if (cell == null) {
                continue;
            }

            SimpleCell simple = new SimpleCell();
            simple.setFormattedShowValue(cell.getFormattedShowValue());
            
            target.add(simple);
        }
        return target;
    }

    private static void removeSubTotal(List<SimpleStatColumnData> target, List<StatColumnDataDto> source) {
        try {
            // 获取第一个符合条件的StatColumnDataDto对象
            Optional<StatColumnDataDto> firstMeasureField = source.stream()
                    .filter(statColumnDataDto -> StringUtils.isNotBlank(statColumnDataDto.getAggrType()))
                    .findFirst();

            // 如果不存在符合条件的对象，直接返回
            if (firstMeasureField.isEmpty()) {
                return;
            }

            // 计算需要保留的索引
            Set<Integer> indexes = IntStream.range(0, firstMeasureField.get().getValue().size())
                    .filter(i -> !CellTypeEnum.getEnum(firstMeasureField.get().getValue().get(i).getCellType()).isDetail())
                    .boxed()
                    .collect(Collectors.toSet());

            // 如果没有索引需要处理，直接返回
            if (indexes.isEmpty()) {
                return;
            }

            // 更新target中的每个SimpleStatColumnData对象
            target.forEach(simpleStatColumnData -> {
                List<SimpleCell> cells = IntStream.range(0, simpleStatColumnData.getValue().size())
                        .filter(i -> !indexes.contains(i))
                        .mapToObj(simpleStatColumnData.getValue()::get)
                        .collect(Collectors.toList());
                simpleStatColumnData.setValue(cells);
            });
        } catch (Exception e) {
            log.error("处理小计失败，source:{}", JSON.toJSONString(source), e);
        }

    }

}