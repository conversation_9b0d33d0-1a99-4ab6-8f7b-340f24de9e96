package com.fxiaoke.chatbi.common.model.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 推理阶段响应
 * 包含处理查询的第一阶段数据：推理过程
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ReasoningResponse extends AsyncResponse {
  /**
   * 推理过程
   */
  private String reasoning;

  private String message;
  
  /**
   * 澄清问题（如果需要澄清）
   */
  private String clarificationQuestion;
} 