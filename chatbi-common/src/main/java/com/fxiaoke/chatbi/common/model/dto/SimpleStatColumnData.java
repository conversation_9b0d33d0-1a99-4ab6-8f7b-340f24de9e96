package com.fxiaoke.chatbi.common.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * 极简版的统计列数据
 * 只包含LLM分析所需的核心数据
 */
@Data
public class SimpleStatColumnData implements Serializable {
    private static final long serialVersionUID = -1L;

    /**
     * 字段名称（用于展示）
     */
    @JSONField(name = "fieldName")
    private String fieldName;

    /**
     * 数据值列表
     */
    @JSONField(name = "value")
    private List<SimpleCell> value;

    /**
     * 总计值
     */
    @JSONField(name = "total")
    private String total;

    /**
     * 平均值
     */
    @JSONField(name = "average")
    private String average;
} 