package com.fxiaoke.chatbi.common.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum StatChartTypeEnum {

    NULL(null, "未知类型"),

    /**
     * 柱状图
     */
    BAR("bar", "柱状图"),

    /**
     * 折线图
     */
    LINE("line", "折线图"),

    /**
     * 饼图
     */
    PIE("pie", "饼图"),

    /**
     * 漏斗图
     */
    FUNNEL("funnel", "漏斗图"),

    /**
     * 转化率漏斗图
     */
    CVR_FUNNEL("cvrfunnel", "转化率漏斗图"),

    /**
     * 统计表
     */
    TABLE("table", "统计表"),

    /**
     * 双轴图
     */
    DOUBLE_Y("doubley", "双轴图"),

    /**
     * KPI卡片
     */
    CARD("card", "KPI卡片"),

    /**
     * 仪表盘
     */
    GAUGE("gauge", "仪表盘"),

    /**
     * 地图(热力)
     */
    MAP_HOT("maphot", "热力地图"),

    /**
     * 地图(气泡)
     */
    MAP_BUBBLE("mapbubble", "气泡地图"),

    /**
     * 国际地图(气泡)
     */
    WORLD_BUBBLE("worldbubble", "国际气泡地图"),

    /**
     * 国际地图(热力)
     */
    WORLD_HOT("worldhot", "国际热力地图"),

    /**
     * 堆叠柱状图
     */
    STACK_BAR("stackbar", "堆叠柱状图"),

    /**
     * 堆叠折线图
     */
    STACK_LINE("stackline", "堆叠折线图"),

    /**
     * 热力图
     */
    HEAT_MAP("heatmap", "热力图"),

    /**
     * 交叉表
     */
    PIVOT_TABLE("pivottable", "交叉表"),

    /**
     * 气泡图
     */
    SCATTER("scatter", "气泡图"),

    /**
     * 雷达图
     */
    RADAR("radar", "雷达图"),

    /**
     * 矩形树图
     */
    TREEMAP("treemap", "矩形树图");

    private String key;

    private String name;

    StatChartTypeEnum(String key, String name) {
        this.key = key;
        this.name = name;
    }

    StatChartTypeEnum() {
    }

    public static StatChartTypeEnum getEnum(String aggKey){
        StatChartTypeEnum[] values = values();
        for (StatChartTypeEnum value : values) {
            if (Objects.equals(value.getKey(), aggKey)) {
                return value;
            }
        }

        return NULL;
    }

    public static String getName(String key){
        StatChartTypeEnum[] values = values();
        for (StatChartTypeEnum value : values) {
            if (Objects.equals(value.getKey(), key)) {
                return value.getName();
            }
        }

        return NULL.getName();
    }

}
