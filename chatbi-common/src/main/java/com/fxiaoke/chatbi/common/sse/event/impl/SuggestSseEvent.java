package com.fxiaoke.chatbi.common.sse.event.impl;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.chatbi.common.model.response.ChartDataResponse;
import com.fxiaoke.chatbi.common.sse.Artifact;
import com.fxiaoke.chatbi.common.sse.Part;
import com.fxiaoke.chatbi.common.sse.SSEData;
import com.fxiaoke.chatbi.common.sse.SSESuggest;
import com.fxiaoke.chatbi.common.sse.event.SseEventType;
import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;

/**
 * 建议事件实现
 * 用于推送追问建议
 */
@Getter
public class SuggestSseEvent extends BaseSseEvent {
    private final List<String> suggestList;

    /**
     * 构造函数
     *
     * @param suggestList 图表数据响应
     * @param delta    是否为增量数据
     */
    public SuggestSseEvent(List<String> suggestList, boolean delta) {
        super(SseEventType.SUGGEST, delta);
        this.suggestList = suggestList;
    }

    @Override
    public Object getEventData() {
        return new SSESuggest(suggestList);
    }
}