package com.fxiaoke.chatbi.common.model.response;

import com.fxiaoke.chatbi.common.model.LoadingStatus;
import com.fxiaoke.chatbi.common.model.MessageType;
import com.fxiaoke.chatbi.common.model.ResponseType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 异步响应基类
 * 包含所有异步响应类共有的字段和属性
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public abstract class AsyncResponse {
  /**
   * 请求ID，用于关联各阶段数据
   */
  protected String requestId;
  
  /**
   * 加载状态
   */
  protected LoadingStatus loadingStatus;

  protected MessageType messageType;

  protected ResponseType responseType;
} 