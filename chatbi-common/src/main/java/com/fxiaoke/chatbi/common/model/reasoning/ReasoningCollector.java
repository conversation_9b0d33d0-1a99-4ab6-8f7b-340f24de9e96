package com.fxiaoke.chatbi.common.model.reasoning;

import com.fxiaoke.chatbi.common.model.action.ActionType;
import lombok.Data;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 推理收集器
 * 负责收集各阶段的推理信息，最终用于LLM润色
 */
@Data
public class ReasoningCollector {

  /**
   * 推理数据存储，存储各个Action的Output
   */
  private final Map<ActionType, String> reasoningData = new LinkedHashMap<>();

  /**
   * 日志数据，存储各个Action的日志信息
   */
  private final Map<ActionType, String> actionLog = new LinkedHashMap<>();


  /**
   * 添加推理数据
   *
   * @param actionType Action类型
   * @param data       Action的Output（需实现ReasoningData接口）
   */
  public void addReasoningData(ActionType actionType, String data) {
    if (actionType != null && data != null) {
      reasoningData.put(actionType, data);
    }

  }

  /**
   * 添加action log
   *
   * @param actionType Action类型
   * @param data       Action的log
   */
  public void addActionLog(ActionType actionType, String data) {
    if (actionType != null && data != null) {
      actionLog.put(actionType, data);
    }

  }

  /**
   * 获取完整推理数据
   *
   * @return 推理数据Map（只读）
   */
  public Map<ActionType, String> getReasoningData() {
    return Collections.unmodifiableMap(reasoningData);
  }


  public Map<String, String> getReasoningsMap() {
    Map<String, String> allReasons = new LinkedHashMap<>();
    reasoningData.forEach((actionType, data) -> {
      if (actionType == ActionType.QUERY_DSL) {
        return;
      }
      allReasons.put(actionType.name(), data);
    });
    return allReasons;
  }

  public Map<String, String> getActionLog() {
    Map<String, String> allActionLog = new LinkedHashMap<>();
    actionLog.forEach((actionType, data) -> {
      allActionLog.put(actionType.name(), data);
    });
    return allActionLog;
  }
}