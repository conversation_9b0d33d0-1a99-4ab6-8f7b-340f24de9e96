package com.fxiaoke.chatbi.common.model.action.output;

import com.fxiaoke.chatbi.common.model.action.ActionOutput;
import com.fxiaoke.chatbi.common.model.dto.ChartQueryDSL;
import com.fxiaoke.chatbi.common.model.dto.ChartResultData;
import lombok.Builder;
import lombok.Data;

/**
 * 数据查询输出结果
 * 使用通用的ChartResultData接口处理不同类型的图表结果
 */
@Data
@Builder
public class DataQueryOutput implements ActionOutput {
    /**
     * 查询参数
     */
    private ChartQueryDSL queryArg;

    /**
     * 图表名称
     */
    private String title;

    /**
     * 图表结果数据
     * 通过ChartResultData接口支持各种类型的图表结果
     */
    private ChartResultData resultData;

    /**
     * 获取用于LLM的JSON格式数据
     *
     * @return 适合LLM处理的JSON字符串
     */
    public String getJsonForLLM() {
        return resultData != null ? resultData.toJsonForLLM() : "{}";
    }
}
