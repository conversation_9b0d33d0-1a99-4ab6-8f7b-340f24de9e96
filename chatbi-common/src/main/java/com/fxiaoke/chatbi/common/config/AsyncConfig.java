package com.fxiaoke.chatbi.common.config;

import com.github.trace.executor.MonitorAsyncTaskExecutor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步执行配置类
 */
@Configuration
@EnableAsync
public class AsyncConfig {

  /**
   * 配置基础线程池任务执行器
   */
  @Bean("threadPoolTaskExecutor")
  public ThreadPoolTaskExecutor threadPoolTaskExecutor() {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    // 核心线程数16，最大线程数50
    executor.setCorePoolSize(16);
    executor.setMaxPoolSize(50);
    // 队列容量10
    executor.setQueueCapacity(10);
    // 线程名前缀
    executor.setThreadNamePrefix("async-task-");
    // 拒绝策略：由调用者所在的线程执行
    executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
    // 线程池关闭时等待所有任务完成
    executor.setWaitForTasksToCompleteOnShutdown(true);
    // 等待时间（秒）
    executor.setAwaitTerminationSeconds(60);
    return executor;
  }

  /**
   * 配置带监控功能的异步任务执行器
   */
  @Bean("monitorAsyncTaskExecutor")
  public TaskExecutor monitorAsyncTaskExecutor() {
    return new MonitorAsyncTaskExecutor(threadPoolTaskExecutor());
  }
} 