package com.fxiaoke.chatbi.common.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.dyuproject.protostuff.Tag;
import com.facishare.bi.common.entities.CustomizeFilter;
import com.facishare.bi.common.entities.FilterList;
import com.facishare.bi.common.entities.GlobalFilter;
import lombok.Data;

import java.util.List;

/**
 * 统计字段到明细数据的查询参数
 * <p>
 * Created by huo<PERSON> on 17/1/10.
 */
@Data
public class SaleProcessAnalysisDetailInfoArg {

  /**
   * 统计图主题id或模版id
   */
  @Tag(1)
  @JSONField(name = "id")
  private String id;

  /**
   * 是否视图
   */
  @Tag(2)
  @JSONField(name = "isView")
  private int isView;


  /**
   * 当前分页的编号，从第1页开始。为0表示不分页
   */
  @Tag(4)
  @JSONField(name = "pageNumber")
  private int pageNumber;

  /**
   * 每页记录数
   */
  @Tag(5)
  @JSONField(name = "pageSize")
  private int pageSize;


  @Tag(8)
  @JSONField(name = "parentID")
  private String parentID;


  /**
   * 场景筛选器
   */
  @Tag(10)
  @JSONField(name = "defaultFilterOptionIDs")
  private List<String> defaultFilterOptionIDs;

  /**
   * 数据范围筛选器
   */
  @Tag(11)
  @JSONField(name = "filterList")
  private List<FilterList> filterList;


  @Tag(11)
  @JSONField(name = "stepCode")
  private String stepCode;

  @Tag(12)
  @JSONField(name = "code")
  private String code;

  @Tag(13)
  @JSONField(name = "globalFilter")
  private GlobalFilter globalFilter;

  private int isPreview = 0;
  /**
   * 自定义筛选器配置数据
   */
  private List<CustomizeFilter> customizeFilterConfig;
  /**
   * 查询请求来源 board-从驾驶舱来的请求查询
   */
  private String from;
  /**
   * 查询请求来源id
   */
  private String fromID;

}
