package com.fxiaoke.chatbi.common.sse.emitter.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fxiaoke.chatbi.common.sse.emitter.SseEventEmitter;
import com.fxiaoke.chatbi.common.sse.event.SseEvent;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;

/**
 * Spring SseEmitter适配器
 * 将自定义SseEventEmitter接口适配到Spring框架的SseEmitter
 */
@Slf4j
public class SpringSseEventEmitter implements SseEventEmitter {
    private final SseEmitter emitter;

    @Getter
    private final String sessionId;

    private volatile boolean active = true;

    /**
     * 构造函数
     * 
     * @param emitter   Spring SseEmitter实例
     * @param sessionId 会话ID
     */
    public SpringSseEventEmitter(SseEmitter emitter, String sessionId) {
        this.emitter = emitter;
        this.sessionId = sessionId;

        // 添加完成回调
        this.emitter.onCompletion(() -> {
            log.info("SSE连接已完成: sessionId={}", sessionId);
            active = false;
        });

        // 添加超时回调
        this.emitter.onTimeout(() -> {
            log.warn("SSE连接已超时: sessionId={}", sessionId);
            active = false;
        });

        // 添加错误回调
        this.emitter.onError(e -> {
            log.error("SSE连接发生错误: sessionId={}", sessionId, e);
            active = false;
        });
    }

    @Override
    public void emit(SseEvent event) {
        if (!active) {
            log.warn("尝试向已关闭的SSE连接发送事件: {}, sessionId={}", event.getEventName(), sessionId);
            return;
        }

        try {
            emitter.send(SseEmitter.event()
                    .name(event.getEventName())
                    .data(JSON.toJSONString(event.getEventData()),
                            MediaType.TEXT_PLAIN));
            log.debug("已发送SSE事件: {}, sessionId={}", event.getEventName(), sessionId);
        } catch (IOException e) {
            log.error("发送SSE事件失败: {}, sessionId={}", event.getEventName(), sessionId, e);
            active = false;
        }
    }

    @Override
    public void complete() {
        if (active) {
            log.info("主动关闭SSE连接: sessionId={}", sessionId);
            emitter.complete();
            active = false;
        }
    }

    @Override
    public boolean isActive() {
        return active;
    }
}