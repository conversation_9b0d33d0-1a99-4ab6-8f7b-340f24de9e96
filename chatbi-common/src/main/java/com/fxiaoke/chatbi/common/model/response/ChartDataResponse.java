package com.fxiaoke.chatbi.common.model.response;

import com.facishare.bi.common.entities.StatChartConfResult;
import com.fxiaoke.chatbi.common.model.dto.ViewDataQueryArg;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 图表数据阶段响应
 * 包含处理查询的第二阶段数据：图表相关数据
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ChartDataResponse extends AsyncResponse {
  /**
   * 图表结果集
   */
  private Object queryData;

  /**
   * 图表配置
   */
  private StatChartConfResult chartConfigData;

  /**
   * 图表名称
   */
  private String title;

  /**
   * 图表查询参数
   */
  private ViewDataQueryArg queryChartDataArg;

  /**
   * action 日志
   */
  private String actionLog;
} 