package com.fxiaoke.chatbi.common.sse.event.factory;

import com.fxiaoke.chatbi.common.model.response.AsyncResponse;
import com.fxiaoke.chatbi.common.model.response.ChartDataResponse;
import com.fxiaoke.chatbi.common.sse.event.SseEvent;

import java.util.List;

/**
 * SSE事件工厂接口
 * 创建各类SSE事件
 */
public interface SseEventFactory {
    /**
     * 创建思考事件
     * 
     * @param reasoning 推理内容
     * @param delta     是否为增量数据
     * @return 思考事件
     */
    SseEvent createThinkingEvent(String reasoning, boolean delta);

    /**
     * 创建提示事件
     * 
     * @param tips  提示内容
     * @param delta 是否为增量数据
     * @return 提示事件
     */
    SseEvent createTipsEvent(String tips, boolean delta);

    /**
     * 创建数据事件
     * 
     * @param response 异步响应数据，可以是图表数据、洞察数据等各种类型
     * @param delta    是否为增量数据
     * @return 数据事件
     */
    SseEvent createDataEvent(AsyncResponse response, boolean delta);

    /**
     * 创建建议事件
     *
     * @param suggestions 追问建议
     * @param delta       是否为增量数据
     * @return 建议事件
     */
    SseEvent createSuggestEvent(List<String> suggestions, boolean delta);

    /**
     * 创建完成事件
     * 
     * @return 完成事件
     */
    SseEvent createFinishEvent();
}