package com.fxiaoke.chatbi.common.model.dto;

import com.alibaba.fastjson2.JSON;
import com.facishare.bi.common.entities.QueryChartDataResult;
import com.facishare.bi.common.entities.StatChartConfResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 标准图表结果集
 * 用于处理普通的图表数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StandardChartResult implements ChartResultData {

    private QueryChartDataResult queryData;

    private StatChartConfResult chartConfig;

    private SimpleChartResult simpleChartResult;

    @Override
    public String getChartType() {
        return chartConfig != null ? chartConfig.getChartType() : "unknown";
    }

    @Override
    public String toJsonForLLM() {
        // 使用SimpleChartResult作为LLM输入
        if (simpleChartResult == null && queryData != null && chartConfig != null) {
            simpleChartResult = SimpleChartResult.fromFullResult(queryData, chartConfig);
        }
        return JSON.toJSONString(simpleChartResult);
    }

    @Override
    public int getDataSetSize() {
        if (queryData != null && queryData.getDataSet() != null) {
            return queryData.getDataSet().size();
        }
        return 0;
    }


}