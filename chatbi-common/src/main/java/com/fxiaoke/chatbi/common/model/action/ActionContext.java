package com.fxiaoke.chatbi.common.model.action;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.reasoning.ReasoningCollector;
import lombok.Data;

/**
 * Action执行上下文
 * 包含单个Action执行所需的上下文信息
 */
@Data
public class ActionContext {

  /**
   * 上下文ID，用于追踪一次完整的Action执行过程
   */
  private String sessionId;

  /**
   * 身份
   */
  private UserIdentity userIdentity;

  /**
   * 执行开始时间
   */
  private long startTime;

  /**
   * 超时时间（毫秒）
   */
  private long timeout = 300000000;

  /**
   * LLM模型
   */
  private String llmModel;

  /**
   * 推理收集器
   * 用于收集各阶段的推理信息
   */
  private ReasoningCollector reasoningCollector;

  /**
   * 创建一个新的上下文,使用指定的会话ID
   */
  public ActionContext() {
    this.startTime = System.currentTimeMillis();
  }

  /**
   * 创建一个新的上下文,使用指定的会话ID
   */
  public ActionContext(String sessionId) {
    this.sessionId = sessionId;
    this.startTime = System.currentTimeMillis();
  }

  /**
   * 判断是否已经超时
   */
  public boolean isTimeout() {
    return System.currentTimeMillis() - startTime > timeout;
  }

}