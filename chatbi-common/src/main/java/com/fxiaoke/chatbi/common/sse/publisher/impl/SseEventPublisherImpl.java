package com.fxiaoke.chatbi.common.sse.publisher.impl;

import com.fxiaoke.chatbi.common.sse.emitter.SseEventEmitter;
import com.fxiaoke.chatbi.common.sse.emitter.SseEventEmitterRepository;
import com.fxiaoke.chatbi.common.sse.event.SseEvent;
import com.fxiaoke.chatbi.common.sse.publisher.SseEventPublisher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * SSE事件发布服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SseEventPublisherImpl implements SseEventPublisher {
    private final SseEventEmitterRepository emitterRepository;

    @Override
    public void publish(String sessionId, SseEvent event) {
        SseEventEmitter emitter = emitterRepository.get(sessionId);
        if (emitter != null && emitter.isActive()) {
            emitter.emit(event);
        } else {
            log.warn("未找到活跃的SSE连接或连接已关闭: sessionId={}, eventType={}",
                    sessionId, event.getEventName());
        }
    }

    @Override
    public void complete(String sessionId) {
        SseEventEmitter emitter = emitterRepository.get(sessionId);
        if (emitter != null) {
            emitter.complete();
            emitterRepository.remove(sessionId);
        } else {
            log.warn("尝试完成不存在的SSE连接: sessionId={}", sessionId);
        }
    }
}