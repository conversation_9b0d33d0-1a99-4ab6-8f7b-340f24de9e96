package com.fxiaoke.chatbi.common.model.intent;

import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * 提取的信息
 * 与intent_recognition.ftl输出的JSON结构保持一致
 */
@Data
public class ExtractedInfo {

  /**
   * 分析类型
   * 例如: 趋势、分布、排名、对比等
   */
  private String analysisType;

  /**
   * 指标名称列表
   * 例如: ["销售额", "利润率"]
   */
  private List<String> measures;

  /**
   * 维度名称列表
   * 例如: ["日期", "产品", "区域"]
   */
  private List<String> dimensions;

  /**
   * 过滤条件
   */
  private List<FilterInfo> filters;

  /**
   * 时间范围描述
   * 例如: "最近三个月"
   */
  private String timeRange;

  /**
   * 图表类型建议
   * 例如: "折线图"、"柱状图"等
   */
  private String chartType;

  /**
   * 筛选条件可能涉及到的字段ID; 非大模型提取
   */
  private Set<String> filterFieldIds;
}