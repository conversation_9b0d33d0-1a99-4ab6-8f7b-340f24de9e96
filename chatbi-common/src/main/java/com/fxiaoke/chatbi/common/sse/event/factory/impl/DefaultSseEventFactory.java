package com.fxiaoke.chatbi.common.sse.event.factory.impl;

import com.fxiaoke.chatbi.common.model.response.AsyncResponse;
import com.fxiaoke.chatbi.common.model.response.ChartDataResponse;
import com.fxiaoke.chatbi.common.sse.event.SseEvent;
import com.fxiaoke.chatbi.common.sse.event.factory.SseEventFactory;
import com.fxiaoke.chatbi.common.sse.event.impl.*;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 默认SSE事件工厂实现
 */
@Component
public class DefaultSseEventFactory implements SseEventFactory {
    @Override
    public SseEvent createThinkingEvent(String reasoning, boolean delta) {
        return new ThinkingSseEvent(reasoning, delta);
    }

    @Override
    public SseEvent createTipsEvent(String tips, boolean delta) {
        return new TipsSseEvent(tips, delta);
    }

    /**
     * 创建数据事件
     * 
     * @param response 异步响应数据，可以是图表数据、洞察数据等各种类型
     * @param delta    是否为增量数据
     * @return 数据事件
     */
    @Override
    public SseEvent createDataEvent(AsyncResponse response, boolean delta) {
        return new DataSseEvent(response, delta);
    }

    @Override
    public SseEvent createSuggestEvent(List<String> suggestions, boolean delta) {
        return new SuggestSseEvent(suggestions, delta);
    }

    @Override
    public SseEvent createFinishEvent() {
        return new FinishSseEvent();
    }
}