package com.fxiaoke.chatbi.common.model.intent;

import lombok.Data;

/**
 * 用户意图
 * 与intent_recognition.ftl模板输出结构一致
 */
@Data
public class UserIntent {

  /**
   * 意图ID
   */
  private String intentId;

  /**
   * 原始指令
   */
  private String instructions;

  /**
   * 意图类型
   */
  private IntentType intentType;

  /**
   * 置信度
   */
  private double confidence;

  /**
   * 是否需要澄清
   */
  private boolean needsClarification;

  /**
   * 澄清问题
   */
  private String clarificationQuestion;

  /**
   * 是否是上下文延续
   */
  private boolean isContinuation;

  /**
   * 查询信息(仅分析意图有效)
   */
  private ExtractedInfo extractedInfo;
}