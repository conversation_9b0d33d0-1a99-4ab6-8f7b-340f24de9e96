package com.fxiaoke.chatbi.common.enums;

import com.facishare.bi.common.entities.filter.FilterOperatorMapByTypeEnum;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

/**
 * 扩展的过滤器操作符映射枚举
 * 在原有 FilterOperatorMapByTypeEnum 的基础上添加新的类型
 */
@Slf4j
@Getter
public enum AIFilterOperatorMapByTypeEnum {
    CIRCLE_IN(Arrays.asList("employee", "department", "out_employee", "out_department"), "EQ", 26, "属于", "circle");

    /**
     * 字段类型
     */
    private List<String> types;
    /**
     * paas操作符
     */
    private String paasOperator;
    /**
     * bi操作符
     */
    private int biOperator;
    /**
     * 描述
     */
    private String label;
    /**
     * paas的值类型
     */
    private String converterType;


    AIFilterOperatorMapByTypeEnum(List<String> types, String paasOperator, int biOperator, String label, String converterType) {
        this.types = types;
        this.paasOperator = paasOperator;
        this.biOperator = biOperator;
        this.label = label;
        this.converterType = converterType;
    }

    /**
     * pass2Bi operator
     *
     * @param type         字段类型
     * @param paasOperator paas操作符
     * @return bi操作符
     */
    public static int getBiOperatorByPaas(String type, String paasOperator) {
        // 优先从AI专用枚举中查找匹配项
        for (AIFilterOperatorMapByTypeEnum aiEnum : AIFilterOperatorMapByTypeEnum.values()) {
            if (aiEnum.getTypes().contains(type) && aiEnum.getPaasOperator().equals(paasOperator)) {
                return aiEnum.getBiOperator();
            }
        }

        // 回退到通用枚举查找
        for (FilterOperatorMapByTypeEnum commonEnum : FilterOperatorMapByTypeEnum.values()) {
            if (commonEnum.getTypes().contains(type) && commonEnum.getPaasOperator().equals(paasOperator)) {
                return commonEnum.getBiOperator();
            }
        }

        // 未找到匹配项时记录错误并返回默认值
        log.error("Unsupported filter operator - type: {}, paasOperator: {}", type, paasOperator);
        return FilterOperatorMapByTypeEnum.TEXT_ISN.getBiOperator();
    }
}
