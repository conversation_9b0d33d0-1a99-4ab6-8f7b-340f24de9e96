package com.fxiaoke.chatbi.common.model.enums;

import lombok.Getter;

/**
 * 知识类型枚举
 * 统一定义系统中所有知识的类型
 */
@Getter
public enum KnowledgeType {
    // 维度指标类型 (原KnowledgeEmbeddingTypeEnum)
    DIMENSION("Dim", "维度", KnowledgeCategory.FIELD),
    MEASURE("Measure", "指标", KnowledgeCategory.FIELD),
    
    // 图表类型
    CHART("Chart", "图表", KnowledgeCategory.TENANT),
    
    // 字典类型
    OPERATOR("FilterOperatorEnum", "操作符", KnowledgeCategory.DICTIONARY),
    DATE_RANGE("DateRangeEnum", "日期范围", KnowledgeCategory.DICTIONARY),
    RATIO_TYPE("RatioType", "同环比类型", KnowledgeCategory.DICTIONARY),
    CHART_TYPE("ChartType", "图表类型", KnowledgeCategory.DICTIONARY),
    
    // 租户知识类型
    SCHEMA("Schema", "主题知识", KnowledgeCategory.TENANT),
    FIELD_RELATION("FieldRelation", "字段关系", KnowledgeCategory.TENANT),
    ENTERPRISE("Enterprise", "企业知识", KnowledgeCategory.TENANT);
    
    /**
     * 知识类型编码
     * 用于存储和向量化时的标识
     */
    private final String code;
    
    /**
     * 知识类型描述
     */
    private final String description;
    
    /**
     * 知识分类
     */
    private final KnowledgeCategory category;
    
    KnowledgeType(String code, String description, KnowledgeCategory category) {
        this.code = code;
        this.description = description;
        this.category = category;
    }
    
    /**
     * 获取所有指定分类的知识类型
     * 
     * @param category 知识分类
     * @return 该分类下的所有知识类型
     */
    public static KnowledgeType[] getTypesByCategory(KnowledgeCategory category) {
        if (category == KnowledgeCategory.ALL) {
            return values();
        }
        
        return java.util.Arrays.stream(values())
                .filter(type -> type.getCategory() == category)
                .toArray(KnowledgeType[]::new);
    }
    
    /**
     * 根据代码获取知识类型
     * 
     * @param code 类型代码
     * @return 匹配的知识类型，如果没有匹配则返回null
     */
    public static KnowledgeType getByCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (KnowledgeType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        
        return null;
    }
    
    /**
     * 知识分类枚举
     * 用于对知识类型进行分组
     */
    @Getter
    public enum KnowledgeCategory {
        DICTIONARY("字典知识"),
        FIELD("字段知识"),
        TENANT("租户知识"),
        ALL("全部知识");
        
        private final String description;
        
        KnowledgeCategory(String description) {
            this.description = description;
        }
    }
} 