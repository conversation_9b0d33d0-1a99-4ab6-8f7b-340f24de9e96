package com.fxiaoke.chatbi.common.model.reasoning;

import com.fxiaoke.chatbi.common.model.prompts.PromptTemplateType;
import java.util.Map;

/**
 * 推理数据接口
 * Action的Output类需要实现此接口以支持推理数据收集
 */
public interface ReasoningData {
    
    /**
     * 获取推理描述信息
     * 用于解释当前执行阶段的结果
     *
     * @return 推理描述文本
     */
    Map<String, Object> getDescription();


    /**
     * 获取日志信息
     * 用于收集日志
     *
     * @return 推理描述文本
     */
    Map<String, Object> getActionLog();


    
    /**
     * 获取推理模板类型
     * 每种推理数据类型可以指定其专用的模板
     *
     * @return 推理模板类型
     */
    default PromptTemplateType getReasoningTemplateType() {
        return PromptTemplateType.REASONING_CONVERT_INTENT; // 默认模板类型
    }


    /**
     * 获取推理模板类型
     * 每种推理数据类型可以指定其专用的模板
     *
     * @return 推理模板类型
     */
    default PromptTemplateType getActionLogTemplateType() {
        return PromptTemplateType.REASONING_CONVERT_INTENT; // 默认模板类型
    }


    
    /**
     * 是否需要收集推理数据
     * 如果推理数据不完整或者不需要收集，可以返回false
     *
     * @return 是否需要收集
     */
    default boolean shouldCollectReasoning() {
        Map<String, Object> description = getDescription();
        return description != null && !description.isEmpty();
    }
} 