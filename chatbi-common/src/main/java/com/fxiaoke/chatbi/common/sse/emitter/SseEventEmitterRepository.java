package com.fxiaoke.chatbi.common.sse.emitter;

/**
 * SSE事件发送器存储库接口
 * 管理SSE连接的存储和检索
 */
public interface SseEventEmitterRepository {
    /**
     * 保存事件发送器
     * 
     * @param emitter 要保存的事件发送器
     */
    void save(SseEventEmitter emitter);

    /**
     * 根据会话ID获取事件发送器
     * 
     * @param sessionId 会话ID
     * @return 对应的事件发送器，如果不存在返回null
     */
    SseEventEmitter get(String sessionId);

    /**
     * 根据会话ID移除事件发送器
     * 
     * @param sessionId 会话ID
     */
    void remove(String sessionId);
}