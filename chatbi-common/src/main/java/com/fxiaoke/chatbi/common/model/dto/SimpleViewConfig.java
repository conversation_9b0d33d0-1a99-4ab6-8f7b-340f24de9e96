package com.fxiaoke.chatbi.common.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.io.Serializable;

/**
 * 简化版的视图配置
 * 用于减少传递给LLM的数据量
 */
@Data
public class SimpleViewConfig implements Serializable {
    private static final long serialVersionUID = -1L;

    /**
     * 转化率公式
     * 1：总转化率公式
     * 2：阶段转化率公式
     */
    @JSONField(name = "conversionFormula")
    private String conversionFormula;

    /**
     * 统计图下钻类型
     * -1查看明细（默认）
     * 1自由下钻
     * 2固定下钻
     */
    @JSONField(name = "drillType")
    private int drillType;
} 