package com.fxiaoke.chatbi.common.utils;

import com.facishare.cep.plugin.model.UserInfo;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


@Slf4j
@UtilityClass
public class UserInfoConvertUtil {
    /**
     * 创建用户信息
     */
    public UserInfo createUserInfo(UserIdentity userIdentity) {
        String tenantId = userIdentity.getTenantId();
        UserInfo userInfo = new UserInfo();
        userInfo.setEmployeeId(Integer.parseInt(userIdentity.getUserId()));
        userInfo.setEnterpriseId(Integer.parseInt(tenantId));
        userInfo.setEnterpriseAccount(userIdentity.getEa());
        return userInfo;
    }
}
