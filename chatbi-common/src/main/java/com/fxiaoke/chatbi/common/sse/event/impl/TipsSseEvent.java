package com.fxiaoke.chatbi.common.sse.event.impl;

import com.fxiaoke.chatbi.common.sse.SSEBody;
import com.fxiaoke.chatbi.common.sse.event.SseEventType;
import lombok.Getter;

/**
 * 提示事件实现
 * 用于推送提示信息
 */
@Getter
public class TipsSseEvent extends BaseSseEvent {
    private final String tips;

    /**
     * 构造函数
     * 
     * @param tips  提示内容
     * @param delta 是否为增量数据
     */
    public TipsSseEvent(String tips, boolean delta) {
        super(SseEventType.TIPS, delta);
        this.tips = tips;
    }

    @Override
    public Object getEventData() {
        return new SSEBody(tips, isDelta());
    }
}