package com.fxiaoke.chatbi.common.model.response;

import com.fxiaoke.chatbi.common.model.Recommendation;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 推荐响应
 * 专用于推荐功能的轻量级响应对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecommendationResponse {
  /**
   * 推荐问题列表
   */
  private List<Recommendation> recommendations;
} 