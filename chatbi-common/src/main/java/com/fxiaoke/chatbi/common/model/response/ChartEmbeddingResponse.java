package com.fxiaoke.chatbi.common.model.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChartEmbeddingResponse {
  /**
   * 图表id
   */
  private String viewId;

  /**
   * 图表名称
   */
  private String viewName;

  /**
   * 图表向量问题
   */
  private List<String> features;
}
