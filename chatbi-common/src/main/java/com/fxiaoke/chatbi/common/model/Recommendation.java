package com.fxiaoke.chatbi.common.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Recommendation {
    /**
     * 推荐项ID
     */
    private String id;

    /**
     * 推荐类型：
     * QUESTION - 问题推荐
     * CHART - 图表推荐
     */
    private RecommendationType type;

    /**
     * 展示文本
     */
    private String text;

    /**
     * 描述
     */
    private String description;

    /**
     * 额外参数
     * 不同type下存储不同的配置信息
     */
    private Map<String, Object> params;

    // 添加chartType作为直接属性
    private String chartType;

    public enum RecommendationType {
        QUESTION,
        CHART
    }
}
