package com.fxiaoke.chatbi.common.model.request;

import com.fxiaoke.chatbi.common.model.ChatMessage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatBiRequest {
  private String sessionId;
  private String instructions;
  private List<ChatMessage> history;
  private String llmModel;
}
