package com.fxiaoke.chatbi.common.model;

/**
 * 数据加载状态枚举
 */
public enum LoadingStatus {
    /**
     * 数据正在加载中
     */
    LOADING,
    
    /**
     * 数据加载完成
     */
    COMPLETED,
    
    /**
     * 数据加载出错
     */
    ERROR;

    public boolean isLoading() {
        return this == LOADING;
    }

    public boolean isCompleted() {
        return this == COMPLETED;
    }

    public boolean isError() {
        return this == ERROR;
    }
    @Override
    public String toString() {
        return this.name();
    }
}
