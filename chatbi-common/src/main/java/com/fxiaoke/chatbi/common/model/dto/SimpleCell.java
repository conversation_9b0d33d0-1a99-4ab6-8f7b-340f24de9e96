package com.fxiaoke.chatbi.common.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import java.io.Serializable;

/**
 * 极简版的单元格数据
 * 只包含LLM分析所需的核心数据
 */
@Data
public class SimpleCell implements Serializable {
    private static final long serialVersionUID = -1L;

    /**
     * 格式化后的显示值（用于展示和分析）
     */
    @JSONField(name = "formattedShowValue")
    private String formattedShowValue;
} 