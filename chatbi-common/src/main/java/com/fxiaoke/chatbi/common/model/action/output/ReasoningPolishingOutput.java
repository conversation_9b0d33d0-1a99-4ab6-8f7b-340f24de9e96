package com.fxiaoke.chatbi.common.model.action.output;

import com.fxiaoke.chatbi.common.model.action.ActionOutput;
import com.fxiaoke.chatbi.common.model.prompts.PromptTemplateType;
import com.fxiaoke.chatbi.common.model.reasoning.ReasoningData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReasoningPolishingOutput implements ActionOutput, ReasoningData {
    /**
     * 润色后的推理内容
     */
    private String polishedReasoning;

    /**
     * 处理时间
     */
    @Builder.Default
    private LocalDateTime processTime = LocalDateTime.now();

    /**
     * 原始内容长度
     */
    private int originalLength;

    /**
     * 润色后内容长度
     */
    private int polishedLength;

    @Override
    public Map<String, Object> getDescription() {
        Map<String, Object> description = new HashMap<>();
        description.put("polishedReasoning", polishedReasoning);
        description.put("processTime", processTime);
        description.put("originalLength", originalLength);
        description.put("polishedLength", polishedLength);
        description.put("compressionRatio", originalLength > 0 ? (double) polishedLength / originalLength : 1.0);
        return description;
    }

    @Override
    public Map<String, Object> getActionLog() {
        return getDescription();
    }

    @Override
    public PromptTemplateType getReasoningTemplateType() {
        return PromptTemplateType.REASONING_CONVERT_PROCESS;
    }

    @Override
    public PromptTemplateType getActionLogTemplateType() {
        return PromptTemplateType.POLISHING_LOG;
    }
}
