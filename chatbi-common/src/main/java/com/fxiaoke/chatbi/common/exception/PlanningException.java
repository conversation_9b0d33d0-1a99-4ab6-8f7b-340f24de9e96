package com.fxiaoke.chatbi.common.exception;

/**
 * Planning模块异常
 * 用于计划生成和执行过程中的异常情况
 */
public class PlanningException extends BaseException {

  private static final long serialVersionUID = 1L;

  /**
   * 创建异常
   *
   * @param errorEnum 错误枚举
   */
  public PlanningException(ChatbiErrorCodeEnum errorEnum) {
    super(errorEnum);
  }

  /**
   * 创建异常
   *
   * @param errorEnum 错误枚举
   * @param cause     原因
   */
  public PlanningException(ChatbiErrorCodeEnum errorEnum, Throwable cause) {
    super(errorEnum, cause);
  }

  /**
   * 创建异常
   *
   * @param errorEnum 错误枚举
   * @param args      消息参数，用于替换消息模板中的占位符
   */
  public PlanningException(ChatbiErrorCodeEnum errorEnum, Object... args) {
    super(errorEnum, args);
  }

  /**
   * 创建异常
   *
   * @param errorEnum 错误枚举
   * @param cause     原因
   * @param args      消息参数，用于替换消息模板中的占位符
   */
  public PlanningException(ChatbiErrorCodeEnum errorEnum, Throwable cause, Object... args) {
    super(errorEnum, cause, args);
  }
} 