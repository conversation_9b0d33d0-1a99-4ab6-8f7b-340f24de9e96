package com.fxiaoke.chatbi.common.model.dto;

import com.alibaba.fastjson.JSONObject;
import lombok.Generated;

public class BaseResult {
    private Integer errCode = 0;
    private String errMessage = "OK";
    private JSONObject result;

    @Generated
    public BaseResult() {
    }

    @Generated
    public Integer getErrCode() {
        return this.errCode;
    }

    @Generated
    public String getErrMessage() {
        return this.errMessage;
    }

    @Generated
    public JSONObject getResult() {
        return this.result;
    }

    @Generated
    public void setErrCode(Integer errCode) {
        this.errCode = errCode;
    }

    @Generated
    public void setErrMessage(String errMessage) {
        this.errMessage = errMessage;
    }

    @Generated
    public void setResult(JSONObject result) {
        this.result = result;
    }

    @Generated
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof BaseResult)) {
            return false;
        } else {
            BaseResult other = (BaseResult)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                Object this$errCode = this.getErrCode();
                Object other$errCode = other.getErrCode();
                if (this$errCode == null) {
                    if (other$errCode != null) {
                        return false;
                    }
                } else if (!this$errCode.equals(other$errCode)) {
                    return false;
                }

                Object this$errMessage = this.getErrMessage();
                Object other$errMessage = other.getErrMessage();
                if (this$errMessage == null) {
                    if (other$errMessage != null) {
                        return false;
                    }
                } else if (!this$errMessage.equals(other$errMessage)) {
                    return false;
                }

                Object this$result = this.getResult();
                Object other$result = other.getResult();
                if (this$result == null) {
                    if (other$result != null) {
                        return false;
                    }
                } else if (!this$result.equals(other$result)) {
                    return false;
                }

                return true;
            }
        }
    }

    @Generated
    protected boolean canEqual(Object other) {
        return other instanceof BaseResult;
    }

    @Generated
    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Object $errCode = this.getErrCode();
        result = result * 59 + ($errCode == null ? 43 : $errCode.hashCode());
        Object $errMessage = this.getErrMessage();
        result = result * 59 + ($errMessage == null ? 43 : $errMessage.hashCode());
        Object $result = this.getResult();
        result = result * 59 + ($result == null ? 43 : $result.hashCode());
        return result;
    }

    @Generated
    public String toString() {
        return "BaseResult(errCode=" + this.getErrCode() + ", errMessage=" + this.getErrMessage() + ", result=" + this.getResult() + ")";
    }
}
