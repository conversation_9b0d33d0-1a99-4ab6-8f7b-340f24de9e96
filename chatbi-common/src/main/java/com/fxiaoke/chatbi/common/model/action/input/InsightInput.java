package com.fxiaoke.chatbi.common.model.action.input;

import com.fxiaoke.chatbi.common.model.action.ActionInput;
import com.fxiaoke.chatbi.common.model.action.output.DataQueryOutput;
import lombok.Data;

/**
 * 数据洞察输入参数
 */
@Data
public class InsightInput extends ActionInput {
    /**
     * 查询结果
     */
    private DataQueryOutput queryOutput;

    /**
     * 计划ID
     */
    private String planId;
}
