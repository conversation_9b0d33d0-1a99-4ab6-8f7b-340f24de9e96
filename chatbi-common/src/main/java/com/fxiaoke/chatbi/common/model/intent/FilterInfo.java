package com.fxiaoke.chatbi.common.model.intent;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 过滤条件
 */
@Data
@Builder
public class FilterInfo {
  /**
   * 字段
   */
  private String field;

  /**
   * 操作符
   * 例如: =, >, <, IN, BETWEEN等
   */
  private String operator;

  /**
   * 值
   */
  private List<String> values;

  /**
   * 值类型
   * 例如: STRING, NUMBER, DATE等
   */
  private String fieldType;
  
  /**
   * 用户是否明确指定了该字段
   * false: 用户明确指定的字段 (如"下单日期是今年")
   * true: 需要系统推断的字段 (如仅提到"今年"但未指定具体日期字段)
   */
  private Boolean userSpecified;
}
