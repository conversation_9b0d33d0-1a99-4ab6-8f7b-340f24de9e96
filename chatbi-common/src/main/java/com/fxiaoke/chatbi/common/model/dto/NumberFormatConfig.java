package com.fxiaoke.chatbi.common.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 数值格式化配置
 * @date 2022/1/19 14:08
 */
@Data
public class NumberFormatConfig {
  /**
   * 格式化类型 default-默认 customize-自定义
   */
  @JSONField(name = "type")
  private String type;
  /**
   * 金额型数值格式化配置
   */
  @JSONField(name = "amount")
  private AmountFormat amount;
  /**
   * 数值型数值格式化配置
   */
  @JSONField(name = "number")
  private NumberFormat number;

}
