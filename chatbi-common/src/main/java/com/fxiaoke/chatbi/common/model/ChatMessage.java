package com.fxiaoke.chatbi.common.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;


/**
 * 聊天消息模型
 * 表示系统中的单条消息实体
 */
// 使用Lombok的@Data注解，自动生成所有常用方法
// 使用Lombok的@Builder注解，允许使用构建器模式创建对象
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatMessage {
  private String role;
  private String content;
  private Map<String, Object> data;
}