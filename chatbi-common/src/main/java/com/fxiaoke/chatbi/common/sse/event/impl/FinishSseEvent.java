package com.fxiaoke.chatbi.common.sse.event.impl;

import com.fxiaoke.chatbi.common.sse.SSEFinish;
import com.fxiaoke.chatbi.common.sse.event.SseEventType;

/**
 * 完成事件实现
 * 用于标记会话结束
 */
public class FinishSseEvent extends BaseSseEvent {
    /**
     * 构造函数
     */
    public FinishSseEvent() {
        super(SseEventType.FINISH, false);
    }

    @Override
    public Object getEventData() {
        return new SSEFinish("empty");
    }
}