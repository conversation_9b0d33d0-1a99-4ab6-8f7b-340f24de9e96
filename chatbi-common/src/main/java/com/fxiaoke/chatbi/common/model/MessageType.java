package com.fxiaoke.chatbi.common.model;
public enum MessageType {
    /**
     * 文本消息
     */
    // 普通文本消息类型
    TEXT,
    
    /**
     * 图片消息
     */
    // 包含图像的消息类型
    IMAGE,
    
    /**
     * 数据消息
     */
    // 包含数据或图表的消息类型
    CHART_DATA,
    
    /**
     * 确认消息
     */
    // 用于确认操作的消息类型
    CONFIRMATION,
    
    /**
     * 系统消息
     */
    // 系统生成的通知或提示消息
    SYSTEM

    , /**
    * 错误消息
    */
   // 系统生成的错误消息
   ERROR
}
