package com.fxiaoke.chatbi.common.config;

import com.google.common.collect.Maps;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@Configuration
@ConfigurationProperties(prefix = "chatbi.knowledge")
public class KnowledgeProperties {
  /**
   * 所有图表ID列表，逗号分隔
   */
  private String chartIds;

  /**
   * 推荐图表ID列表，逗号分隔
   */
  private String recommendChartIds;

  /**
   * LLM模型名称
   */
  private String llmModel = "qwen-plus";

  /**
   * ABTest模型列表
   */
  private String abTestModels;
  
  /**
   * 是否启用ABTest功能，默认为false
   */
  private Boolean enableAbTest = false;
  
  /**
   * 多路召回结果限制数量，默认为20
   */
  private Integer chartResultLimit = 20;
  
  /**
   * 记录的顶部图表数量，用于推理，默认为3
   */
  private Integer topChartCount = 3;

  /**
   * 支持的图表类型列表，逗号分隔
   */
  private String supportChartTypes;

  /**
   * 系统库支持召回的图表ID列表，逗号分隔
   */
  private String sysSupportChartIds;

  private String actionLogGray;

  /**
   * 获取支持的图表类型列表
   */
  public List<String> getSupportChartTypeList() {
    if (!StringUtils.hasText(supportChartTypes)) {
      return Collections.emptyList();
    }
    return Arrays.asList(supportChartTypes.split(","));
  }

  /**
   * 获取系统库支持召回的图表
   */
  public List<String> getSysSupportChartIdList() {
    if (!StringUtils.hasText(sysSupportChartIds)) {
      return Collections.emptyList();
    }
    return Arrays.asList(sysSupportChartIds.split(","));
  }

  /**
   * 打印actionLog灰度  指定企业指定用户
   */
  public Map<String, List<String>> getActionLogGray() {
    if (!StringUtils.hasText(actionLogGray)) {
      return Maps.newHashMap();
    }
    return Arrays.stream(actionLogGray.split(";"))
            .map(pair -> pair.split(":"))
            .collect(Collectors.toMap(
                    parts -> parts[0],
                    parts -> Arrays.asList(parts[1].split(","))
            ));

  }

  /**
   * 获取所有图表ID列表
   */
  public List<String> getChartIdList() {
    if (!StringUtils.hasText(chartIds)) {
      return Collections.emptyList();
    }
    return Arrays.asList(chartIds.split(","));
  }

  /**
   * 获取ABTest模型列表
   */
  public List<String> getAbTestModels() {
    if (!StringUtils.hasText(abTestModels)) {
      return Collections.emptyList();
    }
    return Arrays.asList(abTestModels.split(","));
  }

  /**
   * 获取推荐图表ID列表
   */
  public List<String> getRecommendChartIdList() {
    if (!StringUtils.hasText(recommendChartIds)) {
      return Collections.emptyList();
    }
    return Arrays.asList(recommendChartIds.split(","));
  }
}