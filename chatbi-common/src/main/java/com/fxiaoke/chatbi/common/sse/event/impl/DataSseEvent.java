package com.fxiaoke.chatbi.common.sse.event.impl;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.chatbi.common.model.ResponseType;
import com.fxiaoke.chatbi.common.model.response.AsyncResponse;
import com.fxiaoke.chatbi.common.model.response.ChartDataResponse;
import com.fxiaoke.chatbi.common.model.response.InsightResponse;
import com.fxiaoke.chatbi.common.sse.Artifact;
import com.fxiaoke.chatbi.common.sse.Part;
import com.fxiaoke.chatbi.common.sse.SSEData;
import com.fxiaoke.chatbi.common.sse.event.SseEventType;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 数据事件实现
 * 用于推送各类数据响应，包括图表数据和洞察数据等
 */
@Getter
@Slf4j
public class DataSseEvent extends BaseSseEvent {
    private final AsyncResponse response;

    /**
     * 构造函数
     * 
     * @param response 异步响应数据，可以是图表数据、洞察数据等
     * @param delta    是否为增量数据
     */
    public DataSseEvent(AsyncResponse response, boolean delta) {
        super(SseEventType.DATA, delta);
        this.response = response;
    }

    @Override
    public Object getEventData() {
        if (response == null) {
            log.warn("DataSseEvent收到空响应");
            return new SSEData(new Artifact(Lists.newArrayList(), 0, isDelta()));
        }

        ResponseType responseType = response.getResponseType();

        // 处理洞察类型的响应
        if (responseType == ResponseType.CHARTINSIGHT) {
            if (response instanceof InsightResponse) {
                InsightResponse insightResponse = (InsightResponse) response;
                String content = insightResponse.getFullInsight();

                if (content == null || content.isEmpty()) {
                    log.warn("InsightResponse的fullInsight为空");
                    content = "暂无洞察结果";
                }

                Artifact artifact = new Artifact(Lists.newArrayList(
                        new Part("text", content, null)), 1, isDelta());
                return new SSEData(artifact);
            } else {
                log.warn("响应类型为CHARTINSIGHT但实际对象不是InsightResponse: {}", response.getClass().getName());
            }
        }

        // 默认处理方式 - 适用于ChartDataResponse和其他类型
        Artifact artifact = new Artifact(Lists.newArrayList(
                new Part("bi_chart_data", null, response)), 0, isDelta());
        return new SSEData(artifact);
    }
}