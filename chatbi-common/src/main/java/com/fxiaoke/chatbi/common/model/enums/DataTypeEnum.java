package com.fxiaoke.chatbi.common.model.enums;

import lombok.Getter;

/**
 * 数据类型枚举
 * 定义所有支持的字段数据类型
 */
@Getter
public enum DataTypeEnum {
    
    STRING("STRING", "字符型"),
    NUMBER("NUMBER", "数值型"),
    DATE("DATE", "日期型"),
    BOOLEAN("BOOLEAN", "布尔型"),
    ARRAY("ARRAY", "数组型"),
    OBJECT("OBJECT", "对象型"),
    UNKNOWN("UNKNOWN", "未知类型");
    
    private final String code;
    private final String description;
    
    DataTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    /**
     * 根据编码获取枚举
     * @param code 类型编码
     * @return 对应的枚举值，如果未找到则返回UNKNOWN
     */
    public static DataTypeEnum fromCode(String code) {
        if (code == null) {
            return UNKNOWN;
        }
        
        for (DataTypeEnum type : values()) {
            if (type.getCode().equalsIgnoreCase(code)) {
                return type;
            }
        }
        
        return UNKNOWN;
    }
    
    /**
     * 检查给定类型是否为数值型
     */
    public static boolean isNumber(String code) {
        return NUMBER.getCode().equalsIgnoreCase(code);
    }
    
    /**
     * 检查给定类型是否为日期型
     */
    public static boolean isDate(String code) {
        return DATE.getCode().equalsIgnoreCase(code);
    }
    
    /**
     * 检查给定类型是否为字符型
     */
    public static boolean isString(String code) {
        return STRING.getCode().equalsIgnoreCase(code);
    }
    
    /**
     * 检查给定类型是否为布尔型
     */
    public static boolean isBoolean(String code) {
        return BOOLEAN.getCode().equalsIgnoreCase(code);
    }
} 