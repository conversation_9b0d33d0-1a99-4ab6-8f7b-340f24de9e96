package com.fxiaoke.chatbi.common.model;

import org.apache.logging.log4j.util.Strings;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户身份信息实体类
 * 用于封装系统参数中的用户身份相关信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserIdentity {
  /**
   * 企业账号
   * 必填参数，标识用户所属的企业
   */
  private String ea;

  /**
   * 租户ID
   * 必填参数，标识用户所属的租户
   */
  private String tenantId;

  /**
   * 用户ID
   * 必填参数，标识具体用户
   */
  private String userId;

  private String permissionUserId;

  private String appId;

  private String outEI;

  private String outUserId;

  private String outIdentifyType;

  private String locale;

  private String channel;

public boolean hasOutEI() {
    return Strings.isNotEmpty(outEI);
}
  
  
  
} 