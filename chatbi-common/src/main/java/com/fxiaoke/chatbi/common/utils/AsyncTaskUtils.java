package com.fxiaoke.chatbi.common.utils;

import com.github.trace.executor.MonitorTaskWrapper;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Supplier;

/**
 * 异步任务工具类
 * 用于适配不同类型的函数式接口
 */
@Slf4j
public class AsyncTaskUtils {

  /**
   * 将Supplier适配到MonitorTaskWrapper中
   * 允许在CompletableFuture.supplyAsync中使用MonitorTaskWrapper
   *
   * @param supplier 原始Supplier
   * @param <T>      返回类型
   * @return 包装后的Supplier
   */
  public static <T> Supplier<T> wrapSupplier(Supplier<T> supplier) {
    return () -> {
      try {
        // 将Supplier转换为Callable后用MonitorTaskWrapper包装
        return MonitorTaskWrapper.wrap(() -> supplier.get()).call();
      } catch (Exception e) {
        // 将受检异常转为运行时异常
        if (e instanceof RuntimeException) {
          throw (RuntimeException) e;
        }
        throw new RuntimeException("Error executing wrapped supplier", e);
      }
    };
  }

}