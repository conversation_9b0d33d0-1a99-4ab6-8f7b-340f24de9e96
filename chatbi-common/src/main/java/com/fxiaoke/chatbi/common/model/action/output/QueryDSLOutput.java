package com.fxiaoke.chatbi.common.model.action.output;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.chatbi.common.model.action.ActionOutput;
import com.fxiaoke.chatbi.common.model.dto.ChartQueryDSL;
import com.fxiaoke.chatbi.common.model.dto.DimensionConfig;
import com.fxiaoke.chatbi.common.model.prompts.PromptTemplateType;
import com.fxiaoke.chatbi.common.model.reasoning.ReasoningData;
import lombok.Builder;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 输出结果
 */
@Data
@Builder
public class QueryDSLOutput implements ActionOutput, ReasoningData {
    /**
     * 查询DSL
     */
    private ChartQueryDSL chartQueryDSL;


    @Override
    public Map<String, Object> getDescription() {
        return new HashMap<>();
//    Map<String, Object> preHandleStage = new HashMap<>();
//    //分析类型
//    preHandleStage.put("analysisType", chartQueryDSL.getAnalysisType());
//    //图表类型
//    preHandleStage.put("chartType", chartQueryDSL.getChartType());
//    //过滤条件
//    preHandleStage.put("filters", chartQueryDSL.getFilters());
//    //最大返回条目
//    preHandleStage.put("maxPageSize", chartQueryDSL.getLimit());
//    //图表名称
//    preHandleStage.put("chartName", chartQueryDSL.getChartName());
//    //最终推理
//    preHandleStage.put("reasoning", chartQueryDSL.getReasoning());
//    return preHandleStage;
    }

    @Override
    public Map<String, Object> getActionLog() {
        Map<String, Object> preHandleStage = new HashMap<>();
        preHandleStage.put("viewId", chartQueryDSL.getViewId());
        preHandleStage.put("chartName", chartQueryDSL.getChartName());
        preHandleStage.put("schemaId", chartQueryDSL.getSchemaId());
        //分析类型
        preHandleStage.put("analysisType", chartQueryDSL.getAnalysisType());
        //图表类型
        preHandleStage.put("chartType", chartQueryDSL.getChartType());
        //过滤条件
        preHandleStage.put("filters", chartQueryDSL.getFilters());
        //最大返回条目
        preHandleStage.put("maxPageSize", chartQueryDSL.getLimit());
        return preHandleStage;
    }

    @Override
    public PromptTemplateType getReasoningTemplateType() {
        return PromptTemplateType.REASONING_CONVERT_DSL;
    }

    @Override
    public PromptTemplateType getActionLogTemplateType() {
        return PromptTemplateType.DSL_LOG;
    }
}
