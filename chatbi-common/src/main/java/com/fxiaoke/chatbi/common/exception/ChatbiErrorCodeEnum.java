package com.fxiaoke.chatbi.common.exception;

import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

/**
 * Chatbi系统全局错误码枚举
 * 错误码结构：10位字符串，分为5段
 * 1. S标识(1位)：S-服务端，C-客户端
 * 2. 状态(1位)：1-成功，2-警告，3-错误
 * 3. 固定值07(2位)
 * 4. 模块(2位)：
 *    00-通用模块
 *    01-Action模块
 *    02-Planning模块
 *    03-Knowledge模块
 *    04-Memory模块
 *    05-Integration模块
 *    06-LLM模块
 *    07-AI模型模块
 * 5. 状态码(4位)：模块内的具体错误码，从0001开始
 */
@Getter
public enum ChatbiErrorCodeEnum {
    
    // === 通用模块 (00) ===
    SUCCESS("S10700000", "操作成功", "chatbi.common.SUCCESS"),
    SYSTEM_ERROR("S30700001", "系统错误", "chatbi.common.SYSTEM_ERROR"),
    PARAM_ERROR("S30700002", "参数错误", "chatbi.common.PARAM_ERROR"),
    TIMEOUT_ERROR("S30700003", "请求超时", "chatbi.common.TIMEOUT_ERROR"),
    AUTH_ERROR("S30700004", "认证失败", "chatbi.common.AUTH_ERROR"),
    NOT_FOUND("S30700005", "资源不存在", "chatbi.common.NOT_FOUND"),
    
    // === Action模块 (01) ===
    ACTION_NOT_FOUND("S30701001", "未找到对应的Action", "chatbi.action.ACTION_NOT_FOUND"),
    DATA_QUERY_ERROR("S30701002", "数据查询失败", "chatbi.action.DATA_QUERY_ERROR"),
    DATA_INSIGHT_ERROR("S30701003", "数据洞察失败", "chatbi.action.DATA_INSIGHT_ERROR"),
    ATTRIBUTION_ERROR("S30701004", "归因分析失败", "chatbi.action.ATTRIBUTION_ERROR"),
    PREDICTION_ERROR("S30701005", "预测分析失败", "chatbi.action.PREDICTION_ERROR"),
    RECOMMENDATION_ERROR("S30701006", "推荐失败", "chatbi.action.RECOMMENDATION_ERROR"),
    
    // === Planning模块 (02) ===
    PLANNING_ERROR("S30702001", "计划生成失败", "chatbi.planning.PLANNING_ERROR"),
    PLAN_EXECUTION_ERROR("S30702002", "计划执行失败", "chatbi.planning.EXECUTION_ERROR"),
    INTENT_RECOGNITION_ERROR("S30702003", "意图识别失败", "chatbi.planning.INTENT_RECOGNITION_ERROR"),
    INTENT_ANALYSIS_FAILED("S30702004", "意图分析失败", "chatbi.planning.INTENT_ANALYSIS_FAILED"),
    QUERY_DSL_GENERATION_FAILED("S30702005", "查询DSL生成失败", "chatbi.planning.QUERY_DSL_GENERATION_FAILED"),
    PREPROCESSING_FAILED("S30702006", "查询预处理失败", "chatbi.planning.PREPROCESSING_FAILED"),
    INTENT_RECOGNITION_FAILED("S30702007", "意图识别失败", "chatbi.planning.INTENT_RECOGNITION_FAILED"),
    KNOWLEDGE_RETRIEVAL_FAILED("S30702008", "知识检索失败", "chatbi.planning.KNOWLEDGE_RETRIEVAL_FAILED"),
    
    // === Knowledge模块 (03) ===
    KNOWLEDGE_RETRIEVAL_ERROR("S30703001", "知识检索失败", "chatbi.knowledge.RETRIEVAL_ERROR"),
    KNOWLEDGE_NOT_FOUND("S30703002", "知识不存在", "chatbi.knowledge.NOT_FOUND"),
    KNOWLEDGE_SAVE_ERROR("S30703003", "知识保存失败", "chatbi.knowledge.SAVE_ERROR"),
    
    // === Memory模块 (04) ===
    MEMORY_SAVE_ERROR("S30704001", "会话记忆保存失败", "chatbi.memory.SAVE_ERROR"),
    MEMORY_RETRIEVE_ERROR("S30704002", "会话记忆检索失败", "chatbi.memory.RETRIEVE_ERROR"),
    SESSION_NOT_FOUND("S30704003", "会话不存在", "chatbi.memory.SESSION_NOT_FOUND"),
    
    // === LLM模块 (06) ===
    LLM_GENERAL_ERROR("S30706001", "LLM调用失败", "chatbi.llm.GENERAL_ERROR"),
    LLM_TIMEOUT("S30706002", "LLM调用超时", "chatbi.llm.TIMEOUT"),
    LLM_QUOTA_EXCEEDED("S30706003", "LLM调用配额超限", "chatbi.llm.QUOTA_EXCEEDED"),
    LLM_INVALID_REQUEST("S30706004", "无效的LLM请求", "chatbi.llm.INVALID_REQUEST"),
    
    // === AI模型模块 (07) ===
    AI_MODEL_TIMEOUT("S30707001", "调用模型超时，请您重试", "chatbi.ai.AI_MODEL_TIMEOUT"),
    AI_MODEL_INVALID_REQUEST_ERROR("S30707002", "模型请求参数有误，请仔细检查", "chatbi.ai.AI_MODEL_INVALID_REQUEST_ERROR"),
    AI_MODEL_ARREARS_ERROR("S30707003", "拒绝访问，请确保您的帐户处于良好状态", "chatbi.ai.AI_MODEL_ARREARS_ERROR"),
    AI_MODEL_SYSTEM_ERROR("S30707004", "调用AI接口出现系统报错，请稍后再试", "chatbi.ai.AI_MODEL_SYSTEM_ERROR"),
    AI_MODEL_ACCESS_DENIED_ERROR("S30707005", "模型访问被拒绝，请您检查模型配置", "chatbi.ai.AI_MODEL_ACCESS_DENIED_ERROR"),
    AI_MODEL_UNAVAILABLE_ERROR("S30707006", "模型不可用，请您检查模型配置", "chatbi.ai.AI_MODEL_UNAVAILABLE_ERROR"),
    AI_MODEL_QUOTA_EXCEEDED_ERROR("S30707007", "模型被限流或配额不足，请您检查模型配置", "chatbi.ai.AI_MODEL_QUOTA_EXCEEDED_ERROR"),
    INVALID_OUTPUT("S30707008", "无效的输出结果", "chatbi.ai.INVALID_OUTPUT");

    private final String errorCode;
    private final String errorMessage;
    private final String i18nKey;

    ChatbiErrorCodeEnum(String errorCode, String errorMessage, String i18nKey) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
        this.i18nKey = i18nKey;
    }

    /**
     * 根据错误码获取对应的国际化键
     *
     * @param errorCode 错误码
     * @return 国际化键
     */
    public static String getI18nKeyByErrorCode(String errorCode) {
        Optional<ChatbiErrorCodeEnum> first = Arrays.stream(ChatbiErrorCodeEnum.values())
                .filter(x -> x.errorCode.equals(errorCode))
                .findFirst();
        return first.map(ChatbiErrorCodeEnum::getI18nKey).orElse("");
    }

    /**
     * 根据错误码获取错误枚举
     *
     * @param errorCode 错误码
     * @return 错误枚举
     */
    public static ChatbiErrorCodeEnum getByCode(String errorCode) {
        if (errorCode == null || errorCode.isEmpty()) {
            return null;
        }
        
        Optional<ChatbiErrorCodeEnum> first = Arrays.stream(ChatbiErrorCodeEnum.values())
                .filter(x -> x.errorCode.equals(errorCode))
                .findFirst();
        return first.orElse(SYSTEM_ERROR);
    }
} 