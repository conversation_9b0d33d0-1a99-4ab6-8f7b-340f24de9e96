package com.fxiaoke.chatbi.common.model.dto;

import com.alibaba.fastjson2.JSON;
import com.facishare.bi.common.entities.StatChartConfResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 标准图表结果集
 * 用于处理普通的图表数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpecialChartResult implements ChartResultData {

    private StatChartConfResult chartConfig;

    private SaleProcessAnalysisResult saleProcessAnalysisResult;


    @Override
    public String getChartType() {
        return "oppo2";
    }

    @Override
    public int getDataSetSize() {
        return saleProcessAnalysisResult != null ? saleProcessAnalysisResult.getStages().size() : 0;
    }

    @Override
    public String toJsonForLLM() {
        return saleProcessAnalysisResult != null ? JSON.toJSONString(saleProcessAnalysisResult.getStages()) : "";
    }

}