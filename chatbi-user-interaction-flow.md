# ChatBI系统用户交互完整流程 (v2)

## 一、初始交互流程（优化版）

```mermaid
sequenceDiagram
    actor User as 用户
    participant Client as 客户端
    participant API as API层
    participant Service as 服务层
    participant DB as 数据库
    
    User->>Client: 进入系统
    
    Client->>API: POST /api/chatbi/topics/examples
    API->>Service: topicService.getAllTopicsWithExamples()
    Service->>DB: 查询主题及示例问题
    DB-->>Service: 返回主题和示例数据
    Service-->>API: 返回主题和示例列表
    API-->>Client: 返回主题和示例JSON
    
    alt 用户选择特定主题
        User->>Client: 选择特定主题
        Client->>API: POST /api/chatbi/topics/examples/by-name
        API->>Service: topicService.getTopicExamples()
        Service->>DB: 查询特定主题示例
        DB-->>Service: 返回示例数据
        Service-->>API: 返回示例列表
        API-->>Client: 返回示例JSON
        Client-->>User: 显示特定主题的示例问题
    end
    
    Client-->>User: 显示主题和示例问题
    User->>Client: 输入或选择问题
```

## 二、查询处理流程（优化版）

```mermaid
sequenceDiagram
    actor User as 用户
    participant Client as 客户端
    participant API as API层
    participant ChatService as ChatBiService
    participant TopicService as TopicService
    participant ConvService as ConversationService
    participant LLM as 大语言模型
    participant DB as 数据库
    
    User->>Client: 输入问题
    Client->>API: POST /api/chatbi/query
    API->>TopicService: identifyTopicForQuery(query)
    TopicService-->>API: 返回识别的主题
    
    API->>ChatService: processQuery(query, userIdentity)
    
    ChatService->>ConvService: createConversation()
    ConvService->>DB: 保存新会话
    DB-->>ConvService: 返回会话ID
    
    ChatService->>LLM: 发送查询请求
    LLM-->>ChatService: 返回分析结果
    
    alt 需要澄清
        ChatService-->>API: 返回确认选项
        API-->>Client: 返回确认选项JSON
        Client-->>User: 显示确认选项
        User->>Client: 选择确认选项
        Client->>API: POST /api/chatbi/confirm
        API->>ChatService: processConfirmation()
        ChatService->>LLM: 发送确认请求
        LLM-->>ChatService: 返回确认后结果
    end
    
    ChatService-->>API: 返回ChatBiResponse(带主题信息)
    API-->>Client: 返回响应JSON
    Client-->>User: 显示分析结果
```

## 三、多轮对话流程（优化版）

```mermaid
sequenceDiagram
    actor User as 用户
    participant Client as 客户端
    participant API as API层
    participant ChatService as ChatBiService
    participant TopicService as TopicService
    participant SessionMgr as 会话管理器
    participant LLM as 大语言模型
    
    User->>Client: 输入后续问题
    Client->>API: POST /api/chatbi/query (带sessionId)
    
    API->>TopicService: identifyTopicForQuery(query)
    TopicService-->>API: 返回识别的主题
    
    API->>ChatService: processQuery(query, userIdentity, sessionId)
    
    ChatService->>SessionMgr: getSessionHistory(sessionId)
    SessionMgr-->>ChatService: 返回历史消息
    
    ChatService->>LLM: 发送查询(带历史上下文)
    LLM-->>ChatService: 返回连贯回复
    
    ChatService->>SessionMgr: saveToSessionHistory()
    ChatService-->>API: 返回ChatBiResponse(带主题信息)
    API-->>Client: 返回响应JSON
    Client-->>User: 显示回复
```

## 四、图表交互流程

```mermaid
sequenceDiagram
    actor User as 用户
    participant Client as 客户端
    participant API as API层
    participant ChatService as ChatBiService
    participant ChartService as ChartService
    participant DB as 数据库
    
    Note over User,DB: 图表生成流程
    ChatService->>ChartService: createChart(data)
    ChartService->>DB: 保存图表数据
    DB-->>ChartService: 返回图表ID
    ChartService-->>ChatService: 返回图表对象
    ChatService-->>API: 在响应中包含图表
    API-->>Client: 返回带图表的JSON
    Client-->>User: 显示图表
    
    Note over User,DB: 图表查看流程
    User->>Client: 点击查看图表详情
    Client->>API: POST /api/charts/get
    API->>ChartService: findChartById()
    ChartService->>DB: 查询图表数据
    DB-->>ChartService: 返回图表数据
    ChartService-->>API: 返回Chart对象
    API-->>Client: 返回图表JSON
    Client-->>User: 显示图表详情
    
    Client->>API: POST /api/charts/view
    API->>ChartService: incrementViewCount()
    ChartService->>DB: 更新查看次数
    
    Note over User,DB: 图表收藏流程
    User->>Client: 点击收藏图表
    Client->>API: POST /api/charts/toggle-favorite
    API->>ChartService: toggleFavorite()
    ChartService->>DB: 更新收藏状态
    DB-->>ChartService: 返回更新结果
    ChartService-->>API: 返回更新后的Chart
    API-->>Client: 返回更新后的JSON
    Client-->>User: 更新收藏状态显示
```

## 五、用户反馈流程

```mermaid
sequenceDiagram
    actor User as 用户
    participant Client as 客户端
    participant API as API层
    participant Service as ChatBiService
    participant DB as 数据库
    
    User->>Client: 提供反馈(点赞/踩)
    Client->>API: POST /api/chatbi/feedback
    API->>Service: processFeedback()
    Service->>DB: 保存反馈数据
    DB-->>Service: 确认保存
    Service-->>API: 返回确认
    API-->>Client: 返回确认JSON
    Client-->>User: 显示反馈已记录
    
    User->>Client: 对特定消息提供反馈
    Client->>API: POST /api/chatbi/feedback/message
    API->>Service: processMessageFeedback()
    Service->>DB: 保存消息反馈
    DB-->>Service: 确认保存
    Service-->>API: 返回确认
    API-->>Client: 返回确认JSON
    Client-->>User: 显示反馈已记录
```

## 六、对话管理流程

```mermaid
sequenceDiagram
    actor User as 用户
    participant Client as 客户端
    participant API as API层
    participant ConvService as ConversationService
    participant MsgService as MessageService
    participant DB as 数据库
    
    Note over User,DB: 查看历史对话
    User->>Client: 进入历史对话页面
    Client->>API: POST /api/conversations/by-user
    API->>ConvService: findConversationsByUserId()
    ConvService->>DB: 查询用户对话
    DB-->>ConvService: 返回对话列表
    ConvService-->>API: 返回对话列表
    API-->>Client: 返回对话JSON
    Client-->>User: 显示对话列表
    
    User->>Client: 选择特定对话
    Client->>API: POST /api/conversations/get
    API->>ConvService: findConversationById()
    ConvService->>DB: 查询对话详情
    DB-->>ConvService: 返回对话数据
    ConvService-->>API: 返回对话对象
    API-->>Client: 返回对话JSON
    
    Client->>API: POST /api/messages/by-conversation
    API->>MsgService: findMessagesByConversationId()
    MsgService->>DB: 查询对话消息
    DB-->>MsgService: 返回消息列表
    MsgService-->>API: 返回消息列表
    API-->>Client: 返回消息JSON
    Client-->>User: 显示完整对话历史
    
    Note over User,DB: 对话操作
    User->>Client: 归档对话
    Client->>API: POST /api/conversations/archive
    API->>ConvService: archiveConversation()
    ConvService->>DB: 更新对话状态
    DB-->>ConvService: 确认更新
    ConvService-->>API: 返回确认
    API-->>Client: 返回确认JSON
    Client-->>User: 显示归档成功
```

## 七、推荐交互流程（优化版）

```mermaid
sequenceDiagram
    actor User as 用户
    participant Client as 客户端
    participant API as API层
    participant ChatService as ChatBiService
    participant TopicService as TopicService
    participant LLM as 大语言模型
    
    ChatService->>ChatService: generateSuggestions()
    ChatService->>TopicService: getTopicExamples(currentTopic)
    TopicService-->>ChatService: 返回相关主题示例
    
    ChatService->>LLM: 请求生成推荐问题
    LLM-->>ChatService: 返回推荐问题
    ChatService-->>API: 在响应中包含推荐问题
    API-->>Client: 返回带推荐的JSON
    Client-->>User: 显示推荐问题
    
    User->>Client: 点击推荐问题
    Client->>API: POST /api/chatbi/query
    Note right of Client: 使用推荐问题作为查询内容
    
    API->>ChatService: processQuery()
    Note right of API: 进入正常查询流程
```

## 八、完整用户会话示例（优化版）

### 销售业绩分析场景

| 步骤 | 用户操作 | 系统响应 | API调用 |
|------|---------|---------|---------|
| 1 | 进入系统 | 加载主题及其示例问题 | `POST /api/chatbi/topics/examples` |
| 2 | 选择"销售分析"主题 | 加载销售分析相关示例 | `POST /api/chatbi/topics/examples/by-name` |
| 3 | 提问："分析上个季度的销售业绩" | 识别主题，创建会话，返回初步分析和图表 | `POST /api/chatbi/query` |
| 4 | 系统需要澄清，用户选择"按销售代表分析" | 处理确认，返回详细分析 | `POST /api/chatbi/confirm` |
| 5 | 追问："为什么张三的业绩下降了？" | 分析历史上下文，返回原因分析 | `POST /api/chatbi/query` (带sessionId) |
| 6 | 查看图表详情 | 返回详细图表数据，记录查看 | `POST /api/charts/get`<br>`POST /api/charts/view` |
| 7 | 收藏图表 | 更新收藏状态 | `POST /api/charts/toggle-favorite` |
| 8 | 选择系统推荐问题："如何提高张三的业绩？" | 分析并返回改进建议 | `POST /api/chatbi/query` |
| 9 | 提供反馈(点赞) | 记录反馈 | `POST /api/chatbi/feedback` |
| 10 | 对特定分析提供反馈 | 记录特定消息反馈 | `POST /api/chatbi/feedback/message` |
| 11 | 结束会话 | 保存完整对话历史 | - |

## 九、系统架构图（优化版）

```mermaid
graph TD
    User[用户] --> Client[客户端界面]
    
    subgraph 前端交互层
        Client --> Query[查询输入]
        Client --> Topics[主题浏览]
        Client --> Charts[图表展示]
        Client --> History[历史记录]
        Client --> Feedback[反馈机制]
    end
    
    subgraph API层
        Query --> ChatBiController[ChatBiController]
        Topics --> TopicController[TopicController]
        Charts --> ChartController[ChartController]
        History --> ConversationController[ConversationController]
        History --> MessageController[MessageController]
        Feedback --> FeedbackAPI[反馈API]
    end
    
    subgraph 服务层
        ChatBiController --> ChatBiService[ChatBiService]
        TopicController --> TopicService[TopicService]
        ChatBiService --> TopicService
        ChatBiService --> IntentRecognition[意图识别]
        ChatBiService --> ContextManagement[上下文管理]
        
        ChartController --> ChartService[ChartService]
        ConversationController --> ConversationService[ConversationService]
        MessageController --> MessageService[MessageService]
        ChatBiService --> ExampleService[ExampleService]
    end
    
    subgraph 数据层
        TopicService --> TopicRepo[主题存储]
        ExampleService --> ExampleRepo[示例存储]
        ConversationService --> ConversationRepo[会话存储]
        MessageService --> MessageRepo[消息存储]
        ChartService --> ChartRepo[图表存储]
        ContextManagement --> SessionCache[会话缓存]
    end
    
    subgraph 外部服务
        ChatBiService --> LLM[大语言模型]
        IntentRecognition --> NLP[自然语言处理]
    end
```

## 主要优化点说明

1. **初始交互流程优化**：
   - 使用`/api/chatbi/topics/examples`一次性获取主题及示例，减少API调用次数
   - 增加按主题名称获取示例的流程，支持更精细的交互

2. **查询处理流程优化**：
   - 增加主题识别步骤，使系统能更准确理解用户意图
   - 在响应中添加主题信息，便于前端展示和后续交互

3. **多轮对话流程优化**：
   - 每次查询都进行主题识别，支持用户在对话中切换主题
   - 保持主题连贯性，提升用户体验

4. **推荐交互流程优化**：
   - 结合当前主题生成更相关的推荐问题
   - 利用主题示例作为推荐问题的补充来源

5. **系统架构优化**：
   - 增加TopicService与ChatBiService的交互
   - 明确各服务间的依赖关系
   - 增加主题浏览模块，提升用户探索体验 