<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fxiaoke</groupId>
        <artifactId>fs-bi-agent</artifactId>
        <version>9.5.0-SNAPSHOT</version>
    </parent>

    <artifactId>chatbi-knowledge</artifactId>
    <packaging>jar</packaging>
    <name>chatbi-knowledge</name>
    <description>ChatBI 知识系统模块，提供领域知识和业务规则</description>

    <dependencies>
        <!-- 模块依赖 -->
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>chatbi-common</artifactId>
        </dependency>
        
        <!-- Spring依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        
        <!-- 数据库相关 -->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>runtime</scope>
        </dependency>
        
        <!-- 业务相关依赖 -->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-bi-common-entities</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-bi-metadata-context</artifactId>
        </dependency>
        
        <!-- AI相关依赖 -->
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-paas-ai-api</artifactId>
        </dependency>
        
        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
      <dependency>
        <groupId>com.fxiaoke</groupId>
        <artifactId>chatbi-integration</artifactId>
      </dependency>
    </dependencies>
</project> 