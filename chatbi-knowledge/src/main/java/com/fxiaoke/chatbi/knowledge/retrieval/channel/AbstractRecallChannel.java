package com.fxiaoke.chatbi.knowledge.retrieval.channel;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.intent.UserIntent;
import com.fxiaoke.chatbi.common.model.knowledge.ChannelType;
import com.fxiaoke.chatbi.integration.model.ch.ChartKnowledge;
import com.fxiaoke.chatbi.integration.repository.ch.ChartKnowledgeRepository;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * 召回渠道抽象基类
 * 提供基础实现和公共逻辑
 */
@Slf4j
public abstract class AbstractRecallChannel implements RecallChannel {
  protected final ChartKnowledgeRepository chartKnowledgeRepository;
  @Setter
  protected ChannelType channelType = ChannelType.TENANT; // 默认为租户库
  @Getter
  protected RecallMethod recallMethod; // 召回方法类型


  /**
   * 构造函数
   *
   * @param chartKnowledgeRepository 图表向量存储库
   * @param recallMethod              召回方法类型
   */
  protected AbstractRecallChannel(ChartKnowledgeRepository chartKnowledgeRepository, RecallMethod recallMethod) {
    this.chartKnowledgeRepository = chartKnowledgeRepository;
    this.recallMethod = recallMethod;
  }

  @Override
  public List<ChartKnowledge> recall(UserIntent intent, UserIdentity identity) {
    long startTime = System.currentTimeMillis();
    try {
      log.info("[{}] 开始召回图表, 用户: {}, 数据源: {}", getChannelDescription(), identity.getUserId(), getChannelType());
      List<ChartKnowledge> results = doRecall(intent, identity);

      long endTime = System.currentTimeMillis();
      int resultCount = results != null ? results.size() : 0;
      log.info("[{}] 召回图表完成, 结果数量: {}, 耗时: {}ms", getChannelDescription(), resultCount, (endTime -
        startTime));

      return results;
    } catch (Exception e) {
      log.error("[{}] 召回图表异常", getChannelDescription(), e);
      return Collections.emptyList();
    }
  }

  @Override
  public ChannelType getChannelType() {
    return channelType;
  }

  /**
   * 获取完整的渠道描述
   *
   * @return 数据源类型+召回方法的描述
   */
  protected String getChannelDescription() {
    return getChannelType() + "-" + getChannelName();
  }

  /**
   * 获取完整的图表嵌入信息
   *
   * @param viewIds  图表ID列表
   * @param identity 用户身份信息
   * @return 图表嵌入信息列表
   */
  protected List<ChartKnowledge> getChartKnowledges(List<String> viewIds, UserIdentity identity) {
    if (CollectionUtils.isEmpty(viewIds)) {
      return Collections.emptyList();
    }

    try {
      log.debug("[{}] 获取图表嵌入信息, 图表数量: {}", getChannelDescription(), viewIds.size());

      return chartKnowledgeRepository.batchGetByViewIds(viewIds, identity, getChannelType());
    } catch (Exception e) {
      log.error("[{}] 获取图表嵌入信息异常", getChannelDescription(), e);
      return Collections.emptyList();
    }
  }

  /**
   * 具体召回实现
   * 由子类实现具体的召回逻辑
   *
   * @param intent   用户意图
   * @param identity 用户身份
   * @return 召回的图表列表
   */
  protected abstract List<ChartKnowledge> doRecall(UserIntent intent, UserIdentity identity);
} 