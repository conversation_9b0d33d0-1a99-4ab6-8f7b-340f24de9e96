package com.fxiaoke.chatbi.knowledge.dictionary;

import lombok.Getter;

/**
 * 统计图表类型枚举
 * 用于描述数据可视化的图表类型
 */
@Getter
public enum EasyStatChartTypeEnum {

    NULL("null", "未知类型"),

    // 基础图表类型
    BAR("bar", "柱状图"),
    LINE("line", "折线图"),
    PIE("pie", "饼图"),
    FUNNEL("funnel", "漏斗图"),
    CVR_FUNNEL("cvrfunnel", "转化率漏斗图"),
    TABLE("table", "统计表"),
    DOUBLE_Y("doubley", "双轴图"),
    CARD("card", "KPI卡片"),
    GAUGE("gauge", "仪表盘"),

    // 地图类型
    MAP_HOT("maphot", "地图(热力)"),
    MAP_BUBBLE("mapbubble", "地图(气泡)"),
    WORLD_BUBBLE("worldbubble", "国际地图(气泡)"),
    WORLD_HOT("worldhot", "国际地图(热力)"),

    // 高级图表类型
    STACK_BAR("stackbar", "堆叠柱状图"),
    STACK_LINE("stackline", "堆叠折线图"),
    HEAT_MAP("heatmap", "热力图"),
    PIVOT_TABLE("pivottable", "交叉表"),
    SCATTER("scatter", "气泡图"),
    RADAR("radar", "雷达图"),
    TREEMAP("treemap", "矩形树图");

    private final String key;
    private final String description;

    EasyStatChartTypeEnum(String key, String description) {
        this.key = key;
        this.description = description;
    }

    EasyStatChartTypeEnum() {
        this.key = null;
        this.description = null;
    }
}
