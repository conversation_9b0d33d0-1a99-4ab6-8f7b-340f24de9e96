package com.fxiaoke.chatbi.knowledge.retrieval.aspect;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.action.ActionType;
import com.fxiaoke.chatbi.common.model.intent.ExtractedInfo;
import com.fxiaoke.chatbi.common.model.intent.FilterInfo;
import com.fxiaoke.chatbi.common.model.intent.UserIntent;
import com.fxiaoke.chatbi.common.model.prompts.PromptTemplateType;
import com.fxiaoke.chatbi.common.model.reasoning.ReasoningCollector;
import com.fxiaoke.chatbi.integration.model.ch.ChartKnowledge;
import com.fxiaoke.chatbi.knowledge.building.metadata.ChartDesc;
import com.fxiaoke.chatbi.knowledge.retrieval.channel.KeywordRecallChannel;
import com.fxiaoke.chatbi.knowledge.retrieval.channel.RecallChannel;
import com.fxiaoke.chatbi.knowledge.retrieval.channel.RecallMethod;
import com.fxiaoke.chatbi.knowledge.retrieval.channel.RecallWeightConfig;
import com.fxiaoke.chatbi.prompts.PromptTemplateService;
import com.fxiaoke.common.Pair;
import com.google.common.collect.Lists;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.io.StringReader;
import java.io.StringWriter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 多路召回信息收集切面
 * 只拦截总协调器方法，收集完整的召回信息
 */
@Slf4j
@Aspect
@Component
public class RecallReasoningAspect {

    private final RecallWeightConfig weightConfig;
    private final PromptTemplateService promptTemplateService;
    private final Configuration freemarkerConfig;

    public RecallReasoningAspect(RecallWeightConfig weightConfig,
            PromptTemplateService promptTemplateService,
            Configuration freemarkerConfig) {
        this.weightConfig = weightConfig;
        this.promptTemplateService = promptTemplateService;
        this.freemarkerConfig = freemarkerConfig;
    }

    @Around("execution(* com.fxiaoke.chatbi.knowledge.retrieval.MultiChannelRecallCoordinator.executeMultiChannelRecall(..))")
    public Object collectRecallInfo(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取第三个参数 - ReasoningCollector
        Object[] args = joinPoint.getArgs();
        UserIntent intent = args.length > 0 ? (UserIntent) args[0] : null;
        UserIdentity userIdentity = args.length > 1 ? (UserIdentity) args[1] : null;
        ReasoningCollector collector = args.length > 2 ? (ReasoningCollector) args[2] : null;
        // 如果没有收集器，直接执行原方法
        if (collector == null) {
            return joinPoint.proceed();
        }

        long startTime = System.currentTimeMillis();

        try {
            // 执行原方法
            @SuppressWarnings("unchecked")
            Map<RecallChannel, List<ChartKnowledge>> result = (Map<RecallChannel, List<ChartKnowledge>>) joinPoint
                    .proceed();

            // 收集召回信息
            collectRecallResults(result, startTime, collector, intent, userIdentity);

            return result;
        } catch (Exception e) {
            // 收集异常信息
            Map<String, Object> errorInfo = new HashMap<>();
            errorInfo.put("error", e.getMessage());
            errorInfo.put("timeUsed", System.currentTimeMillis() - startTime);
            collector.addActionLog(ActionType.KNOWLEDGE_RETRIEVAL, JSON.toJSONString(errorInfo));
            return joinPoint.proceed();
        }
    }

    /**
     * 收集召回结果信息
     */
    private void collectRecallResults(Map<RecallChannel, List<ChartKnowledge>> results,
            long startTime,
            ReasoningCollector collector,
            UserIntent intent,
            UserIdentity userIdentity) {
        try {
            long endTime = System.currentTimeMillis();
            Map<String, Object> recallInfo = new HashMap<>();

            Set<String> filterFieldIds = intent.getExtractedInfo().getFilterFieldIds();
            KeywordRecallChannel.KeywordCategories keywordCategories = extractCategorizedKeywords(
                    intent.getExtractedInfo());

            // 1. 基本统计信息
            int totalCharts = results.values().stream().mapToInt(List::size).sum();
            recallInfo.put("totalChannels", results.size());
            recallInfo.put("totalCharts", totalCharts);
            recallInfo.put("timeUsed", endTime - startTime);

            // 2. 各渠道top3 召回图
            List<Map<String, Object>> channelsInfo = new ArrayList<>();
            results.forEach((channel, charts) -> {
                Map<String, Object> channelInfo = new HashMap<>();
                channelInfo.put("channelName", channel.getChannelName());
                channelInfo.put("channelType", channel.getChannelType());
                channelInfo.put("recallMethod", channel.getRecallMethod());
                channelInfo.put("weight", weightConfig.calculateTotalWeight(
                        channel.getChannelType(),
                        channel.getRecallMethod()));
                channelInfo.put("resultCount", charts.size());
                if (CollectionUtils.isEmpty(charts)) {
                    channelsInfo.add(channelInfo);
                    return;
                }
                if (channel.getRecallMethod() == RecallMethod.FIELD_ID) {
                    List<Map<String, Object>> topResults = charts.stream()
                            .map(chart -> {
                                Pair<Double, List<String>> fieldIdMatchRatePair = getFieldIdMatchRate(chart,
                                        filterFieldIds);
                                Double fieldIdMatchRate = fieldIdMatchRatePair.getKey();
                                ChartDesc chartDesc = JSON.parseObject(chart.getSpec(), ChartDesc.class);
                                Map<String, Object> chartInfo = new HashMap<>();
                                chartInfo.put("chartId", chart.getViewId());
                                chartInfo.put("chartName", chartDesc.getName());
                                chartInfo.put("fieldIdMatchRate", fieldIdMatchRate);
                                return chartInfo;
                            }).sorted((a, b) -> Double.compare(
                                    (Double) b.get("fieldIdMatchRate"),
                                    (Double) a.get("fieldIdMatchRate")))
                            .limit(3)
                            .collect(Collectors.toList());
                    channelInfo.put("topResults", topResults);
                } else if (channel.getRecallMethod() == RecallMethod.KEYWORD) {
                    // 每个渠道的前3个结果
                    List<Map<String, Object>> topResults = charts.stream()
                            .map(chart -> {
                                ChartDesc chartDesc = JSON.parseObject(chart.getSpec(), ChartDesc.class);
                                Map<String, Object> chartInfo = new HashMap<>();
                                chartInfo.put("chartId", chart.getViewId());
                                chartInfo.put("chartName", chartDesc.getName());
                                chartInfo.put("keyWordScore", chart.getKeyWordScore());

                                // 添加维度、指标、数据范围的匹配信息
                                Map<String, Double> categoryMatchRates = new HashMap<>();
                                Map<String, List<String>> matchedKeywords = new HashMap<>();

                                // 计算各类型的匹配度
                                // 维度匹配
                                categoryMatchRates.put("dimensionMatchRate", 0.0);
                                matchedKeywords.put("matchedDimensions", Lists.newArrayList());
                                List<String> dimensionKeywords = keywordCategories.dimensions();
                                List<String> chartDimensionNames = chart.getDimensionNames();
                                if (CollectionUtils.isNotEmpty(dimensionKeywords) && CollectionUtils.isNotEmpty(chartDimensionNames)) {
                                    // 找出图表中匹配到的维度名称
                                    List<String> matchedDimensions = chartDimensionNames.stream()
                                            .filter(dim -> dimensionKeywords.stream().anyMatch(dim::contains))
                                            .collect(Collectors.toList());
                                    double dimensionMatchRate = (double) matchedDimensions.size()
                                            / chartDimensionNames.size();
                                    categoryMatchRates.put("dimensionMatchRate", dimensionMatchRate);
                                    matchedKeywords.put("matchedDimensions", matchedDimensions);
                                }

                                // 指标匹配
                                categoryMatchRates.put("measureMatchRate", 0.0);
                                matchedKeywords.put("matchedMeasures", Lists.newArrayList());
                                List<String> measureKeywords = keywordCategories.measures();
                                List<String> chartMeasureNames = chart.getMeasureNames();
                                if (CollectionUtils.isNotEmpty(measureKeywords)) {
                                    // 找出图表中匹配到的指标名称
                                    List<String> matchedMeasures = chartMeasureNames.stream()
                                            .filter(metric -> measureKeywords.stream().anyMatch(metric::contains))
                                            .collect(Collectors.toList());
                                    double metricMatchRate = (double) matchedMeasures.size() / chartMeasureNames.size();
                                    categoryMatchRates.put("measureMatchRate", metricMatchRate);
                                    matchedKeywords.put("matchedMeasures", matchedMeasures);
                                }

                                // 数据范围匹配
                                categoryMatchRates.put("filterMatchRate", 0.0);
                                matchedKeywords.put("matchedfilters", Lists.newArrayList());
                                List<String> filterKeywords = keywordCategories.filters();
                                List<String> chartFilterNames = chart.getFilterNames();
                                if (CollectionUtils.isNotEmpty(filterKeywords)) {
                                    // 找出图表中匹配到的筛选条件名称
                                    List<String> matchedScopes = chartFilterNames.stream()
                                            .filter(scope -> filterKeywords.stream().anyMatch(scope::contains))
                                            .collect(Collectors.toList());
                                    double scopeMatchRate = (double) matchedScopes.size() / chartFilterNames.size();
                                    categoryMatchRates.put("filterMatchRate", scopeMatchRate);
                                    matchedKeywords.put("matchedfilters", matchedScopes);
                                }
                                chartInfo.put("categoryMatchRates", categoryMatchRates);
                                chartInfo.put("matchedKeywords", matchedKeywords);
                                return chartInfo;
                            }).sorted((a, b) -> Double.compare(
                                    (Double) b.get("keyWordScore"),
                                    (Double) a.get("keyWordScore")))
                            .limit(3)
                            .collect(Collectors.toList());
                    channelInfo.put("topResults", topResults);
                } else if (channel.getRecallMethod() == RecallMethod.VECTOR) {
                    // 每个渠道的前3个结果
                    List<Map<String, Object>> topResults = charts.stream()
                            .map(chart -> {
                                ChartDesc chartDesc = JSON.parseObject(chart.getSpec(), ChartDesc.class);
                                Map<String, Object> chartInfo = new HashMap<>();
                                chartInfo.put("chartId", chart.getViewId());
                                chartInfo.put("chartName", chartDesc.getName());
                                chartInfo.put("vectorScore", chart.getVectorScore());
                                return chartInfo;
                            }).sorted(Comparator.comparingDouble(a -> (Double) a.get("vectorScore")))
                            .limit(3)
                            .collect(Collectors.toList());
                    channelInfo.put("topResults", topResults);
                }
                channelsInfo.add(channelInfo);
            });
            recallInfo.put("channels", channelsInfo);

            try {
                // 1. 获取模板内容
                String templateContent = promptTemplateService.getTemplate(PromptTemplateType.RECALL_LOG);

                // 2. 创建模板
                Template template = new Template("dynamicTemplate", new StringReader(templateContent),
                        freemarkerConfig);

                // 3. 处理模板
                StringWriter writer = new StringWriter();
                template.process(recallInfo, writer);
                // 4. 保存到推理收集器
                collector.addActionLog(ActionType.KNOWLEDGE_RETRIEVAL, writer.toString());

                log.debug("已收集并格式化多路召回信息: 渠道数={}, 总图表数={}, 耗时={}ms",
                        results.size(), totalCharts, endTime - startTime);
            } catch (Exception e) {
                // 如果模板处理失败，退回到直接保存JSON
                log.warn("处理召回推理模板失败，退回到直接保存JSON数据: {}", e.getMessage());
                collector.addActionLog(ActionType.KNOWLEDGE_RETRIEVAL, JSON.toJSONString(recallInfo));
            }
        } catch (Exception e) {
            log.warn("收集召回信息时发生异常", e);
        }
    }

    public Pair<Double, List<String>> getFieldIdMatchRate(ChartKnowledge chartKnowledge, Set<String> filterFieldIds) {
        if (Objects.isNull(chartKnowledge) || CollectionUtils.isEmpty(chartKnowledge.getFieldIds())
                || CollectionUtils.isEmpty(filterFieldIds)) {
            return Pair.build(0.0, Collections.emptyList());
        }
        List<String> matchFieldIds = Lists.newArrayList();
        int matchCount = 0;
        for (String fieldId : chartKnowledge.getFieldIds()) {
            if (filterFieldIds.contains(fieldId)) {
                matchCount++;
                matchFieldIds.add(fieldId);
            }
        }
        return Pair.build((double) matchCount / filterFieldIds.size(), matchFieldIds);
    }

    /**
     * 提取分类关键词
     * 将关键词分为维度、指标和筛选条件三类
     */
    private KeywordRecallChannel.KeywordCategories extractCategorizedKeywords(ExtractedInfo extractedInfo) {
        if (extractedInfo == null) {
            return new KeywordRecallChannel.KeywordCategories(
                    Collections.emptyList(),
                    Collections.emptyList(),
                    Collections.emptyList());
        }

        // 提取维度关键词
        List<String> dimensionKeywords = CollectionUtils.isEmpty(extractedInfo.getDimensions())
                ? Collections.emptyList()
                : new ArrayList<>(extractedInfo.getDimensions());

        // 提取指标关键词
        List<String> measureKeywords = CollectionUtils.isEmpty(extractedInfo.getMeasures()) ? Collections.emptyList()
                : new ArrayList<>(extractedInfo.getMeasures());

        // 提取筛选条件关键词
        List<String> filterKeywords = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(extractedInfo.getFilters())) {
            // 只提取字段名作为筛选条件关键词
            extractedInfo.getFilters().stream()
                    .filter(filter -> StringUtils.isNotBlank(filter.getField()))
                    .map(FilterInfo::getField)
                    .forEach(filterKeywords::add);
        }

        // 额外添加时间范围
        if (StringUtils.isNotBlank(extractedInfo.getTimeRange())) {
            filterKeywords.add(extractedInfo.getTimeRange());
        }

        return new KeywordRecallChannel.KeywordCategories(dimensionKeywords, measureKeywords, filterKeywords);
    }
}
