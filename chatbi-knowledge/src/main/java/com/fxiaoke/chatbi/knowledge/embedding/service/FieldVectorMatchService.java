package com.fxiaoke.chatbi.knowledge.embedding.service;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.enums.KnowledgeType;
import com.fxiaoke.chatbi.common.model.knowledge.ChannelType;
import com.fxiaoke.chatbi.integration.model.ch.KnowledgeEmbedding;
import com.fxiaoke.chatbi.integration.repository.ch.KnowledgeEmbeddingRepository;
import com.fxiaoke.chatbi.knowledge.embedding.core.TextVectorizer;
import com.fxiaoke.common.Pair;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 字段向量匹配服务
 * 提供基于向量检索的字段匹配功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FieldVectorMatchService {
    private final KnowledgeEmbeddingRepository knowledgeEmbeddingRepository;
    private final TextVectorizer textVectorizer;

    // 默认阈值
    private static final double DEFAULT_THRESHOLD = 0.3;

    /**
     * 单字段ID匹配，并限制在候选字段ID范围内
     *
     * @param fieldName    输入的字段名称
     * @param candidateIds 候选字段ID列表
     * @param userIdentity 用户身份信息
     * @return 包含字段ID和字段名称的Pair对象，如果未匹配则返回null
     */
    public Pair<String, KnowledgeEmbedding> matchFieldId(String fieldName, List<String> candidateIds, UserIdentity userIdentity) {
        if (StringUtils.isBlank(fieldName) || userIdentity == null || CollectionUtils.isEmpty(candidateIds)) {
            return null;
        }

        try {
            log.info("开始匹配字段: {}, 候选字段数: {}", fieldName, candidateIds.size());

            // 向量化输入字段名
            float[] queryVector = textVectorizer.vectorize(fieldName, userIdentity);

            // 先尝试匹配维度字段
            KnowledgeEmbedding dimensionMatch = knowledgeEmbeddingRepository.searchSimilarEmbeddingWithFilter(
                queryVector, 
                userIdentity, 
                ChannelType.TENANT, 
                KnowledgeType.DIMENSION.getCode(),
                candidateIds, 
                DEFAULT_THRESHOLD
            );

            // 如果找到维度字段匹配
            if (dimensionMatch != null) {
                String fieldId = dimensionMatch.getKnowledgeId();
                String matchedFieldName = dimensionMatch.getFeature(); // Feature字段通常包含字段名
                log.info("匹配到维度字段: {} -> {} ({}), 相似度: {}", fieldName, fieldId, matchedFieldName, dimensionMatch.getVectorScore());
                return new Pair<>(fieldId, dimensionMatch);
            }

            // 如果未找到维度字段匹配，尝试匹配指标字段
            KnowledgeEmbedding measureMatch = knowledgeEmbeddingRepository.searchSimilarEmbeddingWithFilter(
                queryVector, 
                userIdentity, 
                ChannelType.TENANT, 
                KnowledgeType.MEASURE.getCode(),
                candidateIds, 
                DEFAULT_THRESHOLD
            );

            // 如果找到指标字段匹配
            if (measureMatch != null) {
                String fieldId = measureMatch.getKnowledgeId();
                String matchedFieldName = measureMatch.getFeature(); // Feature字段通常包含字段名
                log.info("匹配到指标字段: {} -> {} ({}), 相似度: {}", fieldName, fieldId, matchedFieldName, measureMatch.getVectorScore());
                return new Pair<>(fieldId, measureMatch);
            }

            log.info("未找到匹配字段: {}", fieldName);
            return null;
        } catch (Exception e) {
            log.error("匹配字段失败: {}", fieldName, e);
            return null;
        }
    }
    
    /**
     * 只从维度类知识检索的字段ID匹配
     * 适用于字符型字段，跳过指标类知识检索
     *
     * @param fieldName    输入的字段名称
     * @param candidateIds 候选字段ID列表
     * @param userIdentity 用户身份信息
     * @return 包含字段ID和字段名称的Pair对象，如果未匹配则返回null
     */
    public Pair<String, KnowledgeEmbedding> matchFieldIdOnlyDimension(String fieldName, List<String> candidateIds, UserIdentity userIdentity) {
        if (StringUtils.isBlank(fieldName) || userIdentity == null || CollectionUtils.isEmpty(candidateIds)) {
            return null;
        }

        try {
            log.info("开始匹配字符型字段(仅维度): {}, 候选字段数: {}", fieldName, candidateIds.size());

            // 向量化输入字段名
            float[] queryVector = textVectorizer.vectorize(fieldName, userIdentity);

            // 只尝试匹配维度字段，跳过指标字段匹配
            KnowledgeEmbedding dimensionMatch = knowledgeEmbeddingRepository.searchSimilarEmbeddingWithFilter(
                queryVector, 
                userIdentity, 
                ChannelType.TENANT, 
                KnowledgeType.DIMENSION.getCode(),
                candidateIds, 
                DEFAULT_THRESHOLD
            );

            // 如果找到维度字段匹配
            if (dimensionMatch != null) {
                String fieldId = dimensionMatch.getKnowledgeId();
                String matchedFieldName = dimensionMatch.getFeature(); // Feature字段通常包含字段名
                log.info("字符型字段匹配到维度字段: {} -> {} ({}), 相似度: {}", fieldName, fieldId, matchedFieldName, dimensionMatch.getVectorScore());
                return new Pair<>(fieldId, dimensionMatch);
            }

            log.info("字符型字段未找到匹配维度字段: {}", fieldName);
            return null;
        } catch (Exception e) {
            log.error("匹配字符型字段失败: {}", fieldName, e);
            return null;
        }
    }
}