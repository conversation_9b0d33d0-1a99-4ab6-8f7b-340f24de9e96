package com.fxiaoke.chatbi.knowledge.retrieval.weight;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 排序权重配置类
 * 管理图表排序的相关权重配置：
 * - 使用频率权重
 * - 相关性权重
 * - 时效性权重
 * - 召回方式权重
 */
@Configuration
public class RankingWeightConfig {
    // 排序权重配置
    @Value("${chatbi.chart.weight.ranking.frequency:0.4}")
    private double frequencyWeight;

    @Value("${chatbi.chart.weight.ranking.relevance:0.4}")
    private double relevanceWeight;

    @Value("${chatbi.chart.weight.ranking.timeliness:0.2}")
    private double timelinessWeight;

    // 时效性衰减配置
    @Value("${chatbi.chart.weight.timeliness.days:30}")
    private int timelinessDecayDays;

    // 召回方式权重配置
    @Value("${chatbi.chart.weight.method.fieldId:2.0}")
    private double fieldIdWeight;

    @Value("${chatbi.chart.weight.method.keyword:1.5}")
    private double keywordWeight;

    @Value("${chatbi.chart.weight.method.vector:1.5}")
    private double vectorWeight;

    /**
     * 获取频率权重
     */
    public double getFrequencyWeight() {
        return frequencyWeight;
    }

    /**
     * 获取相关性权重
     */
    public double getRelevanceWeight() {
        return relevanceWeight;
    }

    /**
     * 获取时效性权重
     */
    public double getTimelinessWeight() {
        return timelinessWeight;
    }

    /**
     * 获取时效性衰减天数
     */
    public int getTimelinessDecayDays() {
        return timelinessDecayDays;
    }

    /**
     * 获取字段ID匹配权重
     */
    public double getFieldIdWeight() {
        return fieldIdWeight;
    }

    /**
     * 获取关键词匹配权重
     */
    public double getKeywordWeight() {
        return keywordWeight;
    }

    /**
     * 获取向量相似度权重
     */
    public double getVectorWeight() {
        return vectorWeight;
    }
} 