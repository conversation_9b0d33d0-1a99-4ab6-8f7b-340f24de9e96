package com.fxiaoke.chatbi.knowledge.service.impl.field;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.integration.model.ch.KnowledgeEmbedding;
import com.fxiaoke.chatbi.integration.model.ch.StatField;
import com.fxiaoke.chatbi.integration.repository.ch.KnowledgeEmbeddingRepository;
import com.fxiaoke.chatbi.integration.repository.ch.StatFieldRepository;
import com.fxiaoke.chatbi.knowledge.building.core.KnowledgeVectorizer;
import com.fxiaoke.chatbi.knowledge.building.core.VectorizeContext;
import com.fxiaoke.chatbi.knowledge.service.FieldKnowledgeService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 字段知识服务实现
 * 提供字段知识的访问和管理功能
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class FieldKnowledgeServiceImpl implements FieldKnowledgeService {
    private final StatFieldRepository statFieldRepository;
    private final KnowledgeVectorizer<StatField> statFieldVectorizer;
    private final KnowledgeEmbeddingRepository knowledgeEmbeddingRepository;
    
    @Override
    public void buildFieldKnowledge(UserIdentity userIdentity) {
        log.info("Building field knowledge for tenant: {}", userIdentity.getTenantId());
        processFieldKnowledge(userIdentity);
    }
    
    @Override
    public List<StatField> listAllByTenant(String tenantId) {
        log.debug("Listing knowledge stat fields for tenant: {}", tenantId);
        return statFieldRepository.getKnowledgeStatFields(tenantId);
    }
    
    /**
     * 处理字段知识的向量化
     *
     * @param userIdentity 用户身份
     */
    private void processFieldKnowledge(UserIdentity userIdentity) {
        log.info("Processing field knowledge for tenant: {}", userIdentity.getTenantId());
        
        // 获取维度指标字段
        List<StatField> statFields = listAllByTenant(userIdentity.getTenantId());
        if (CollectionUtils.isEmpty(statFields)) {
            log.info("No stat fields found for tenant: {}", userIdentity.getTenantId());
            return;
        }
        
        log.info("Found {} stat fields to process", statFields.size());
        
        // 创建向量化上下文
        VectorizeContext context = VectorizeContext.builder()
                .tenantId(userIdentity.getTenantId())
                .batchId(System.currentTimeMillis())
                .build();
        
        // 分批处理字段
        for (List<StatField> batch : Lists.partition(statFields, 500)) {
            log.info("Processing batch of {} stat fields", batch.size());
            
            List<CompletableFuture<List<KnowledgeEmbedding>>> futures = new ArrayList<>();
            
            for (StatField field : batch) {
                futures.add(CompletableFuture.supplyAsync(() -> 
                    statFieldVectorizer.vectorize(field, null, context)));
            }
            
            // 收集结果
            List<KnowledgeEmbedding> embeddings = futures.stream()
                    .map(CompletableFuture::join)
                    .filter(Objects::nonNull)
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
            
            // 保存向量
            saveEmbeddings(embeddings, userIdentity.getTenantId());
        }
        
        log.info("Completed processing field knowledge for tenant: {}", userIdentity.getTenantId());
    }
    
    /**
     * 批量保存知识向量
     *
     * @param embeddings 知识向量列表
     * @param tenantId   租户ID
     */
    private void saveEmbeddings(List<KnowledgeEmbedding> embeddings, String tenantId) {
        if (CollectionUtils.isEmpty(embeddings)) {
            log.info("No embeddings to save");
            return;
        }
        
        log.info("Saving {} knowledge embeddings", embeddings.size());
        for (List<KnowledgeEmbedding> batch : Lists.partition(embeddings, 200)) {
            knowledgeEmbeddingRepository.saveKnowledgeEmbeddings(batch, tenantId);
        }
    }
} 