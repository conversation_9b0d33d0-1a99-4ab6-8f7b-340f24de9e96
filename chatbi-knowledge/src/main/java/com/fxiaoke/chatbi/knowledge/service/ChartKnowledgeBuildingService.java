package com.fxiaoke.chatbi.knowledge.service;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.response.ChartEmbeddingResponse;

import java.util.List;

/**
 * 图表知识服务接口
 * 定义图表知识库的构建、更新、删除等功能
 */
public interface ChartKnowledgeBuildingService {

    /**
     * 构建单个图表的知识库
     *
     * @param userIdentity 用户身份
     * @param viewId       图表ID
     */
    void buildChartKnowledge(UserIdentity userIdentity, String viewId);

    /**
     * 构建租户所有图表的知识库
     *
     * @param userIdentity 用户身份
     */
    void buildAllChartKnowledge(UserIdentity userIdentity);

    /**
     * 批量构建图表知识库
     *
     * @param userIdentity 用户身份
     * @param viewIds      图表ID列表
     */
    void batchBuildChartKnowledge(UserIdentity userIdentity, List<String> viewIds);
    
    /**
     * 获取租户的所有图表特征
     *
     * @param userIdentity 用户身份
     * @return 图表特征响应列表
     */
    List<ChartEmbeddingResponse> listTenantChartFeatures(UserIdentity userIdentity);
} 