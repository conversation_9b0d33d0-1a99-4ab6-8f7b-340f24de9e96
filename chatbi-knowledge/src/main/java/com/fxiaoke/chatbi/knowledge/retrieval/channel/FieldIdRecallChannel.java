package com.fxiaoke.chatbi.knowledge.retrieval.channel;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.intent.UserIntent;
import com.fxiaoke.chatbi.integration.model.ch.ChartKnowledge;
import com.fxiaoke.chatbi.integration.repository.ch.ChartKnowledgeRepository;
import com.fxiaoke.chatbi.knowledge.retrieval.ChartRetriever;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * 基于字段ID的图表召回渠道
 * 负责根据筛选值识别的字段ID执行图表检索
 */
@Slf4j
public class FieldIdRecallChannel extends AbstractRecallChannel {

  private final ChartRetriever chartRetriever;

  /**
   * 构造函数
   *
   * @param chartKnowledgeRepository 图表知识存储库
   * @param chartRetriever           图表检索器
   */
  public FieldIdRecallChannel(ChartKnowledgeRepository chartKnowledgeRepository,
                              ChartRetriever chartRetriever) {
    super(chartKnowledgeRepository, RecallMethod.FIELD_ID);
    this.chartRetriever = chartRetriever;
  }

  @Override
  protected List<ChartKnowledge> doRecall(UserIntent intent, UserIdentity identity) {
    try {
      // 根据筛选值提取字段ID
      Set<String> filterFieldIds = intent.getExtractedInfo().getFilterFieldIds();

      if (CollectionUtils.isEmpty(filterFieldIds)) {
        log.info("[{}] 未找到字段ID，跳过召回", getChannelDescription());
        return Collections.emptyList();
      }

      log.info("[{}] 提取到字段ID: {}", getChannelDescription(), filterFieldIds);

      // 直接使用ChartRetriever进行图表检索，传递数据源类型
      List<String> viewIds = chartRetriever.searchByFieldIds(new ArrayList<>(filterFieldIds), identity, getChannelType());

      if (CollectionUtils.isEmpty(viewIds)) {
        log.info("[{}] 未找到匹配的图表", getChannelDescription());
        return Collections.emptyList();
      }

      log.info("[{}] 找到匹配的图表数量: {}", getChannelDescription(), viewIds.size());

      // 获取完整的图表信息
      return getChartKnowledges(viewIds, identity);
    } catch (Exception e) {
      log.error("[{}] 召回处理异常", getChannelDescription(), e);
      return Collections.emptyList();
    }
  }
} 