package com.fxiaoke.chatbi.knowledge.building.metadata;

import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 关系管理器
 * 用于管理字段关系信息
 */
@Component
public class RelationManager {
    
    /**
     * 生成字段关系知识文本
     * 
     * @param relations 字段关系列表
     * @return 字段关系知识文本
     */
    public String generateRelationKnowledge(List<FieldRelationDesc> relations) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("## 字段关系\n\n");
        if (relations != null && !relations.isEmpty()) {
            relations.forEach(relation -> {
                prompt.append("- ").append(relation.getSourceFieldId())
                      .append(" → ").append(relation.getTargetFieldId())
                      .append(" [").append(relation.getRelationType()).append("]\n");
            });
            prompt.append("\n");
        } else {
            prompt.append("暂无字段关系\n\n");
        }
        
        return prompt.toString();
    }
} 