package com.fxiaoke.chatbi.knowledge.retrieval.weight;

import com.fxiaoke.chatbi.common.model.knowledge.ChannelType;
import com.fxiaoke.chatbi.integration.model.ch.ChartKnowledge;
import com.fxiaoke.chatbi.knowledge.retrieval.channel.RecallWeightConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 图表排序计算器
 * 负责计算图表的最终排序得分，得分由以下因素组成：
 * 1. 基础权重：渠道权重
 * 2. 动态得分：
 * - 使用频率得分 (40%)
 * - 相关性得分 (40%)
 * - 时效性得分 (20%)
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ChartRankingCalculator {
    private final RankingWeightConfig rankingWeightConfig;
    private final RecallWeightConfig recallWeightConfig;
    private static final long MILLIS_PER_DAY = 24 * 60 * 60 * 1000L;

    /**
     * 计算图表的最终排序得分
     *
     * @param chart           图表知识
     * @param channelType     渠道类型
     * @param fieldMatchRatio 字段匹配比例
     * @return 最终排序得分
     */
    public double calculateFinalWeight(ChartKnowledge chart,
                                     ChannelType channelType,
                                     double fieldMatchRatio) {
        // 1. 基础权重 (只考虑渠道)
        double baseWeight = recallWeightConfig.getChannelWeight(channelType);
        
        // 2. 计算加权相似度得分
        double similarityScore = calculateWeightedSimilarityScore(chart, fieldMatchRatio);
        
        // 3. 计算动态特征得分
        double frequencyScore = calculateFrequencyScore(chart.getUsageCount());
        double timelinessScore = calculateTimelinessScore(chart.getLastModifiedTime());
        
        // 4. 最终得分计算
        return baseWeight * (
            rankingWeightConfig.getFrequencyWeight() * frequencyScore +
            rankingWeightConfig.getRelevanceWeight() * similarityScore +
            rankingWeightConfig.getTimelinessWeight() * timelinessScore
        );
    }

    /**
     * 计算加权相似度得分
     * 综合考虑字段匹配、关键词匹配和向量相似度
     */
    private double calculateWeightedSimilarityScore(ChartKnowledge chart, double fieldMatchRatio) {
        double totalWeight = 0.0;
        double weightedSum = 0.0;
        
        // 字段匹配分数
        if (fieldMatchRatio > 0) {
            weightedSum += fieldMatchRatio * rankingWeightConfig.getFieldIdWeight();
            totalWeight += rankingWeightConfig.getFieldIdWeight();
        }
        
        // 关键词匹配分数
        if (chart.getKeyWordScore() != null && chart.getKeyWordScore() > 0) {
            weightedSum += chart.getKeyWordScore() * rankingWeightConfig.getKeywordWeight();
            totalWeight += rankingWeightConfig.getKeywordWeight();
        }
        
        // 向量相似度分数
        if (chart.getVectorScore() != null && chart.getVectorScore() > 0) {
            weightedSum += chart.getVectorScore() * rankingWeightConfig.getVectorWeight();
            totalWeight += rankingWeightConfig.getVectorWeight();
        }
        
        return totalWeight > 0 ? weightedSum / totalWeight : 0.0;
    }

    /**
     * 计算使用频率得分
     * 使用对数函数平滑使用频率
     */
    private double calculateFrequencyScore(Integer usageCount) {
        if (usageCount == null || usageCount <= 0) {
            return 0.0;
        }
        return (1.0 + Math.log10(usageCount)) / 10.0;
    }

    /**
     * 计算时效性得分
     * 使用指数衰减函数
     */
    private double calculateTimelinessScore(Long lastUpdateTime) {
        if (lastUpdateTime == null || lastUpdateTime <= 0) {
            return 0.5; // 默认中等时效性
        }
        
        long now = System.currentTimeMillis();
        long daysDiff = (now - lastUpdateTime) / MILLIS_PER_DAY;
        
        return Math.exp(-1.0 * daysDiff / rankingWeightConfig.getTimelinessDecayDays());
    }
} 