package com.fxiaoke.chatbi.knowledge.retrieval.channel;

import com.fxiaoke.chatbi.common.model.knowledge.ChannelType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 召回权重配置类
 * 统一管理渠道和召回方式的权重配置
 */
@Configuration
public class RecallWeightConfig {
    // 渠道权重配置
    @Value("${chatbi.chart.weight.channel.tenant:2.0}")
    private double tenantWeight;

    @Value("${chatbi.chart.weight.channel.system:1.0}")
    private double systemWeight;

    // 召回方式权重配置
    @Value("${chatbi.chart.weight.method.fieldId:2.0}")
    private double fieldIdWeight;

    @Value("${chatbi.chart.weight.method.keyword:1.5}")
    private double keywordWeight;

    @Value("${chatbi.chart.weight.method.vector:1.5}")
    private double vectorWeight;

    /**
     * 获取渠道权重
     */
    public double getChannelWeight(ChannelType channelType) {
        return switch (channelType) {
            case TENANT -> tenantWeight;
            case SYSTEM -> systemWeight;
        };
    }

    /**
     * 获取召回方式权重
     */
    public double getMethodWeight(RecallMethod method) {
        return switch (method) {
            case FIELD_ID -> fieldIdWeight;
            case KEYWORD -> keywordWeight;
            case VECTOR -> vectorWeight;
        };
    }

    /**
     * 计算总权重
     * 渠道权重 * 召回方式权重
     */
    public double calculateTotalWeight(ChannelType channelType, RecallMethod method) {
        return getChannelWeight(channelType) * getMethodWeight(method);
    }
} 