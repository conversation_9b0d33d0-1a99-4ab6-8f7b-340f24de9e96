package com.fxiaoke.chatbi.knowledge.retrieval;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.intent.ExtractedInfo;
import com.fxiaoke.chatbi.common.model.knowledge.ChannelType;
import com.fxiaoke.chatbi.integration.model.ch.ChartKnowledge;
import com.fxiaoke.chatbi.integration.model.ch.KnowledgeEmbedding;

import java.util.List;
import java.util.Map;

/**
 * 图表检索器接口
 * 定义图表检索的基础方法
 */
public interface ChartRetriever {

  /**
   * 基于向量搜索检索图表ID列表
   *
   * @param queryText     查询文本
   * @param userIdentity  用户身份
   * @param extractedInfo 提取的结构化信息
   * @param channelType   数据源类型（租户库/系统库）
   * @return 图表ID列表
   */
  List<String> searchByVector(String queryText, UserIdentity userIdentity, ExtractedInfo extractedInfo, ChannelType channelType);

  /**
   * 基于向量搜索检索图表ID列表，并返回相似度分数
   *
   * @param queryText     查询文本
   * @param userIdentity  用户身份
   * @param extractedInfo 提取的结构化信息
   * @param channelType   数据源类型（租户库/系统库）
   * @return 图表ID与相似度分数的映射
   */
  Map<String, Double> searchByVectorWithScores(String queryText, UserIdentity userIdentity, ExtractedInfo extractedInfo, ChannelType channelType);

  /**
   * 基于向量搜索检索知识嵌入列表，包含命中的特征和分数
   *
   * @param queryText     查询文本
   * @param userIdentity  用户身份
   * @param extractedInfo 提取的结构化信息
   * @param channelType   数据源类型（租户库/系统库）
   * @return 知识嵌入列表，包含特征内容、权重和相似度分数
   */
  List<KnowledgeEmbedding> searchEmbeddingWithScores(String queryText, UserIdentity userIdentity, ExtractedInfo extractedInfo, ChannelType channelType);

  /**
   * 基于关键词检索图表ID列表
   *
   * @param keywords     关键词列表
   * @param userIdentity 用户身份
   * @param channelType  数据源类型（租户库/系统库）
   * @return 图表ID列表
   */
  List<String> searchByKeywords(List<String> keywords, UserIdentity userIdentity, ChannelType channelType);

  /**
   * 基于多类关键词检索图表ID列表
   * 分别匹配维度、指标和筛选条件三类关键词
   *
   * @param dimensionKeywords 维度关键词列表
   * @param measureKeywords   指标关键词列表
   * @param filterKeywords    筛选条件关键词列表
   * @param userIdentity      用户身份
   * @param channelType       数据源类型（租户库/系统库）
   * @return 图表ID列表
   */
  List<String> searchByCategorizedKeywords(
      List<String> dimensionKeywords,
      List<String> measureKeywords,
      List<String> filterKeywords,
      UserIdentity userIdentity,
      ChannelType channelType);


  /**
   * 基于多类关键词检索图表ID列表并打分
   * 分别匹配维度、指标和筛选条件三类关键词
   *
   * @param dimensionKeywords 维度关键词列表
   * @param measureKeywords   指标关键词列表
   * @param filterKeywords    筛选条件关键词列表
   * @param userIdentity      用户身份
   * @param channelType       数据源类型（租户库/系统库）
   * @return 图表ID列表
   */
  List<ChartKnowledge> searchByCategorizedKeywordsAndScores(
      List<String> dimensionKeywords,
      List<String> measureKeywords,
      List<String> filterKeywords,
      UserIdentity userIdentity,
      ChannelType channelType);

  /**
   * 基于字段ID检索图表ID列表
   *
   * @param fieldIds     字段ID列表
   * @param userIdentity 用户身份
   * @param channelType  数据源类型（租户库/系统库）
   * @return 图表ID列表
   */
  List<String> searchByFieldIds(List<String> fieldIds, UserIdentity userIdentity, ChannelType channelType);

}