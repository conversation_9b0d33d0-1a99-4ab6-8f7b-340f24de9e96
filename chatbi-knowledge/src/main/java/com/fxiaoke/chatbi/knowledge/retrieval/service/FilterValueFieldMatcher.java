package com.fxiaoke.chatbi.knowledge.retrieval.service;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.intent.ExtractedInfo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 筛选条件字段匹配服务
 * 负责根据筛选条件中的字符串值查找对应的字段ID
 */
public interface FilterValueFieldMatcher {

    /**
     * 根据筛选条件的字符串值查找对应的维度字段ID
     *
     * @param extractedInfo 提取的用户意图信息
     * @param userIdentity  用户身份信息
     * @return 匹配的字段ID集合
     */
    Set<String> findFieldIdsByFilterValues(ExtractedInfo extractedInfo, UserIdentity userIdentity);

    /**
     * 根据多个字段值批量查找匹配的字段ID和置信度
     * 一次性处理所有字段值，提高性能
     *
     * @param fieldValues  字段值列表
     * @param candidateIds 候选字段ID列表（必需，不能为空）
     * @param userIdentity 用户身份信息
     * @return 字段ID到置信度的映射
     */
    Map<String, Float> matchFieldsByFilterValues(
            List<String> fieldValues,
            List<String> candidateIds,
            UserIdentity userIdentity);
}