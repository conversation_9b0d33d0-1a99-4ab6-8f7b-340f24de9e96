package com.fxiaoke.chatbi.knowledge.building.metadata;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ViewFieldDesc {
    /**
     * 字段ID
     */
    private String fieldId;

    /**
     * 排序方式：不排序、升序、降序
     */
    private String orderType;

    /**
     * 聚合方式（仅指标字段）：SUM、AVG、COUNT等
     */
    private String aggregator;

    /**
     * 同环比类型
     */
    private String ratioType;

    /**
     * 字段名称
     */
    private String fieldName;
}
