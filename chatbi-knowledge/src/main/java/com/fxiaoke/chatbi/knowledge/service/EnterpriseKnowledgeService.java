package com.fxiaoke.chatbi.knowledge.service;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.integration.model.pg.EnterpriseKnowledge;

import java.util.List;

/**
 * 企业知识服务接口
 * 定义企业知识的访问和管理方法
 */
public interface EnterpriseKnowledgeService {

  /**
   * 构建企业知识向量
   *
   * @param userIdentity 用户身份
   */
  void buildEnterpriseKnowledge(UserIdentity userIdentity);

  /**
   * 获取租户的所有有效知识条目
   *
   * @param tenantId 租户ID
   * @return 知识条目列表
   */
  List<EnterpriseKnowledge> listAllByTenant(String tenantId);
} 