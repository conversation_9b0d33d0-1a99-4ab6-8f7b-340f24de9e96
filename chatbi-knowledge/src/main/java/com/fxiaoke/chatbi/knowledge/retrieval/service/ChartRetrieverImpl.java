package com.fxiaoke.chatbi.knowledge.retrieval.service;

import com.facishare.bi.common.GrayManager;
import com.fxiaoke.chatbi.common.config.EmbeddingProperties;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.enums.KnowledgeType;
import com.fxiaoke.chatbi.common.model.intent.ExtractedInfo;
import com.fxiaoke.chatbi.common.model.knowledge.ChannelType;
import com.fxiaoke.chatbi.integration.model.ch.ChartKnowledge;
import com.fxiaoke.chatbi.integration.model.ch.KnowledgeEmbedding;
import com.fxiaoke.chatbi.integration.model.ch.RetrievalQuery;
import com.fxiaoke.chatbi.integration.repository.ch.ChartKnowledgeRepository;
import com.fxiaoke.chatbi.integration.repository.ch.KnowledgeEmbeddingRepository;
import com.fxiaoke.chatbi.knowledge.retrieval.ChartRetriever;
import com.fxiaoke.chatbi.knowledge.embedding.core.TextVectorizer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 图表检索器实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChartRetrieverImpl implements ChartRetriever {

    private final ChartKnowledgeRepository chartKnowledgeRepository;
    private final KnowledgeEmbeddingRepository knowledgeEmbeddingRepository;
    private final TextVectorizer textVectorizer;
    private final EmbeddingProperties vectorizationProps;

    @Override
    public List<String> searchByVector(String queryText,
                                       UserIdentity userIdentity,
                                       ExtractedInfo extractedInfo,
                                       ChannelType channelType) {
        if (!validateBasicParams(userIdentity, queryText)) {
            return Collections.emptyList();
        }

        try {
            log.info("基于向量检索图表, query={}, 数据源={}", queryText, channelType);

            // 1. 向量化查询文本
            float[] queryEmbedding = textVectorizer.vectorize(queryText, userIdentity);

            // 2. 设置相似度阈值和结果数量限制
            int limit = calculateResultLimit(extractedInfo);
            double threshold = vectorizationProps.getSimilarityThreshold();

            // 3. 直接使用KnowledgeEmbeddingRepository检索相似图表ID
            List<String> viewIds = knowledgeEmbeddingRepository.searchSimilarEmbeddingKnowledgeId(
                queryEmbedding,
                userIdentity,
                channelType,
                KnowledgeType.CHART.getCode(),
                threshold,
                limit
            );
            
            log.info("基于向量检索到图表ID数量: {}, 数据源={}", viewIds.size(), channelType);
            return viewIds;
        } catch (Exception e) {
            log.error("基于向量检索图表失败: query={}, 数据源={}", queryText, channelType, e);
            return Collections.emptyList();
        }
    }

    @Override
    public Map<String, Double> searchByVectorWithScores(String queryText,
                                                      UserIdentity userIdentity,
                                                      ExtractedInfo extractedInfo,
                                                      ChannelType channelType) {
        if (!validateBasicParams(userIdentity, queryText)) {
            return Collections.emptyMap();
        }

        try {
            log.info("基于向量检索图表(带分数), query={}, 数据源={}", queryText, channelType);

            // 1. 向量化查询文本
            float[] queryEmbedding = textVectorizer.vectorize(queryText, userIdentity);

            // 2. 设置相似度阈值和结果数量限制
            int limit = calculateResultLimit(extractedInfo);
            double threshold = vectorizationProps.getSimilarityThreshold();

            // 3. 使用KnowledgeEmbeddingRepository检索相似图表记录(带分数)
            List<KnowledgeEmbedding> knowledgeEmbeddings = knowledgeEmbeddingRepository.searchEmbeddingWithScores(
                queryEmbedding,
                userIdentity,
                channelType,
                KnowledgeType.CHART.getCode(),
                threshold,
                limit
            );

            // 4. 提取图表ID和相似度分数
            Map<String, Double> viewIdsWithScores = new LinkedHashMap<>(); // 保持顺序
            for (KnowledgeEmbedding embedding : knowledgeEmbeddings) {
                viewIdsWithScores.put(embedding.getKnowledgeId(), embedding.getVectorScore());
            }
            
            log.info("基于向量检索到图表ID数量(带分数): {}, 数据源={}", viewIdsWithScores.size(), channelType);
            return viewIdsWithScores;
        } catch (Exception e) {
            log.error("基于向量检索图表失败(带分数): query={}, 数据源={}", queryText, channelType, e);
            return Collections.emptyMap();
        }
    }

    @Override
    public List<KnowledgeEmbedding> searchEmbeddingWithScores(String queryText,
                                                            UserIdentity userIdentity,
                                                            ExtractedInfo extractedInfo,
                                                            ChannelType channelType) {
        if (!validateBasicParams(userIdentity, queryText)) {
            return Collections.emptyList();
        }

        try {
            log.info("基于向量检索知识嵌入(带分数), query={}, 数据源={}", queryText, channelType);

            // 1. 向量化查询文本
            float[] queryEmbedding = textVectorizer.vectorize(queryText, userIdentity);

            // 2. 设置相似度阈值和结果数量限制
            int limit = calculateResultLimit(extractedInfo);
            double threshold = vectorizationProps.getSimilarityThreshold();

            // 3. 使用KnowledgeEmbeddingRepository检索相似图表记录(带分数)
            List<KnowledgeEmbedding> knowledgeEmbeddings = knowledgeEmbeddingRepository.searchEmbeddingWithScores(
                queryEmbedding,
                userIdentity,
                channelType,
                KnowledgeType.CHART.getCode(),
                threshold,
                limit
            );
            
            log.info("基于向量检索到知识嵌入数量(带分数): {}, 数据源={}", knowledgeEmbeddings.size(), channelType);
            return knowledgeEmbeddings;
        } catch (Exception e) {
            log.error("基于向量检索知识嵌入失败(带分数): query={}, 数据源={}", queryText, channelType, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<String> searchByKeywords(List<String> keywords, UserIdentity userIdentity, ChannelType channelType) {
        if (userIdentity == null) {
            log.warn("用户身份为空，无法检索");
            return Collections.emptyList();
        }

        if (CollectionUtils.isEmpty(keywords)) {
            log.warn("关键词列表为空，无法检索: userIdentity={}", userIdentity);
            return Collections.emptyList();
        }

        try {
            log.info("基于关键词检索图表: {}, 数据源={}", keywords, channelType);
            
            // 使用ChartKnowledgeRepository的searchChartsByKeyword方法
            // 直接在数据库层使用SQL LIKE进行搜索，避免将所有数据加载到内存中
            List<String> viewIds = new ArrayList<>();
            
            for (String keyword : keywords) {
                if (StringUtils.isBlank(keyword)) {
                    continue;
                }
                
                List<String> matchedIds = chartKnowledgeRepository.searchChartsByKeyword(
                    keyword,
                    userIdentity.getTenantId(),
                    channelType
                );
                
                if (!CollectionUtils.isEmpty(matchedIds)) {
                    if (CollectionUtils.isEmpty(viewIds)) {
                        viewIds = new ArrayList<>(matchedIds);
                    } else {
                        // 合并结果，保持唯一性
                        viewIds.addAll(matchedIds);
                        viewIds = viewIds.stream().distinct().collect(Collectors.toList());
                    }
                }
            }
            
            log.info("基于关键词检索到图表ID数量: {}, 数据源={}", viewIds.size(), channelType);
            return viewIds;
        } catch (Exception e) {
            log.error("基于关键词检索图表失败: keywords={}, 数据源={}", keywords, channelType, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<String> searchByCategorizedKeywords(List<String> dimensionKeywords,
                                                  List<String> measureKeywords,
                                                  List<String> filterKeywords,
                                                  UserIdentity userIdentity,
                                                  ChannelType channelType) {
        if (userIdentity == null) {
            log.warn("用户身份为空，无法检索");
            return Collections.emptyList();
        }

        boolean hasDimensions = !CollectionUtils.isEmpty(dimensionKeywords);
        boolean hasMeasures = !CollectionUtils.isEmpty(measureKeywords);
        boolean hasFilters = !CollectionUtils.isEmpty(filterKeywords);

        if (!hasDimensions && !hasMeasures && !hasFilters) {
            log.warn("所有关键词列表均为空，无法检索: userIdentity={}", userIdentity);
            return Collections.emptyList();
        }

        try {
            log.info("基于分类关键词检索图表 - 维度: {}, 指标: {}, 筛选: {}, 数据源: {}", 
                    dimensionKeywords, measureKeywords, filterKeywords, channelType);
            
            // 存储结果集
            Set<String> resultViewIds = new HashSet<>();
            String tenantId = userIdentity.getTenantId();
            
            // 所有维度关键词的组合结果
            if (hasDimensions) {
                for (String dimKeyword : dimensionKeywords) {
                    if (StringUtils.isBlank(dimKeyword)) {
                        continue;
                    }
                    // 对于每个维度关键词，使用空字符串作为其他类型的关键词
                    List<String> ids = chartKnowledgeRepository.searchChartsByCategorizedKeyword(
                            dimKeyword, "", "", tenantId, channelType);
                    if (!CollectionUtils.isEmpty(ids)) {
                        resultViewIds.addAll(ids);
                    }
                }
            }
            
            // 所有指标关键词的组合结果
            if (hasMeasures) {
                for (String measKeyword : measureKeywords) {
                    if (StringUtils.isBlank(measKeyword)) {
                        continue;
                    }
                    // 对于每个指标关键词，使用空字符串作为其他类型的关键词
                    List<String> ids = chartKnowledgeRepository.searchChartsByCategorizedKeyword(
                            "", measKeyword, "", tenantId, channelType);
                    if (!CollectionUtils.isEmpty(ids)) {
                        resultViewIds.addAll(ids);
                    }
                }
            }
            
            // 所有筛选条件关键词的组合结果
            if (hasFilters) {
                for (String filterKeyword : filterKeywords) {
                    if (StringUtils.isBlank(filterKeyword)) {
                        continue;
                    }
                    // 对于每个筛选条件关键词，使用空字符串作为其他类型的关键词
                    List<String> ids = chartKnowledgeRepository.searchChartsByCategorizedKeyword(
                            "", "", filterKeyword, tenantId, channelType);
                    if (!CollectionUtils.isEmpty(ids)) {
                        resultViewIds.addAll(ids);
                    }
                }
            }
            
            // 转换为List并返回
            List<String> viewIds = new ArrayList<>(resultViewIds);
            log.info("基于分类关键词检索到图表ID数量: {}, 数据源={}", viewIds.size(), channelType);
            return viewIds;
        } catch (Exception e) {
            log.error("基于分类关键词检索图表失败: 维度={}, 指标={}, 筛选={}, 数据源={}", 
                    dimensionKeywords, measureKeywords, filterKeywords, channelType, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<ChartKnowledge> searchByCategorizedKeywordsAndScores(List<String> dimensionKeywords,
                                                  List<String> measureKeywords,
                                                  List<String> filterKeywords,
                                                  UserIdentity userIdentity,
                                                  ChannelType channelType) {
        if (userIdentity == null) {
            log.warn("用户身份为空，无法检索");
            return Collections.emptyList();
        }


        if (CollectionUtils.isEmpty(dimensionKeywords) && CollectionUtils.isEmpty(measureKeywords) && CollectionUtils.isEmpty(filterKeywords)) {
            log.warn("所有关键词列表均为空，无法检索: userIdentity={}", userIdentity);
            return Collections.emptyList();
        }

        try {
            log.info("基于分类关键词检索图表 - 维度: {}, 指标: {}, 筛选: {}, 数据源: {}",
                    dimensionKeywords, measureKeywords, filterKeywords, channelType);

            // 存储结果集
            String tenantId = userIdentity.getTenantId();

            // 所有维度关键词的组合结果+得分
            List<ChartKnowledge> chartKnowledges = chartKnowledgeRepository.selectChartsByKeywordWithScores(dimensionKeywords, measureKeywords, filterKeywords, tenantId, channelType);

            log.info("基于分类关键词检索到图表ID数量: {}, 数据源={}", chartKnowledges.size(), channelType);
            return chartKnowledges;
        } catch (Exception e) {
            log.error("基于分类关键词检索图表失败: 维度={}, 指标={}, 筛选={}, 数据源={}",
                    dimensionKeywords, measureKeywords, filterKeywords, channelType, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<String> searchByFieldIds(List<String> fieldIds, UserIdentity userIdentity, ChannelType channelType) {
        if (userIdentity == null) {
            log.warn("用户身份为空，无法检索");
            return Collections.emptyList();
        }

        if (CollectionUtils.isEmpty(fieldIds)) {
            log.warn("字段ID列表为空，无法检索: userIdentity={}", userIdentity);
            return Collections.emptyList();
        }

        try {
            log.info("基于字段ID检索图表: {}, 数据源={}", fieldIds, channelType);

            // 直接从数据库查询包含指定字段ID的图表
            List<String> viewIds = chartKnowledgeRepository.searchChartsByFieldIds(fieldIds, userIdentity.getTenantId(), channelType);

            log.info("基于字段ID检索到图表ID数量: {}, 数据源={}", viewIds.size(), channelType);
            return viewIds;
        } catch (Exception e) {
            log.error("基于字段ID检索图表失败: fieldIds={}, 数据源={}", fieldIds, channelType, e);
            return Collections.emptyList();
        }
    }

    /**
     * 验证基本检索参数
     */
    private boolean validateBasicParams(UserIdentity userIdentity, String query) {
        if (userIdentity == null) {
            log.warn("用户身份为空，无法检索");
            return false;
        }

        if (StringUtils.isBlank(query)) {
            log.warn("查询文本为空，无法检索: userIdentity={}", userIdentity);
            return false;
        }

        return true;
    }

    /**
     * 计算检索结果数量限制
     * 根据提取信息动态调整结果数量
     */
    private int calculateResultLimit(ExtractedInfo extractedInfo) {
        // 默认限制数量
        int defaultLimit = vectorizationProps.getDefaultSearchLimit();

        // 如果没有提取信息，使用默认限制
        if (extractedInfo == null) {
            return defaultLimit;
        }

        // 根据提取信息的复杂度调整限制
        // 如果提取信息包含更多维度和指标，可能需要更多结果
        int dimensionCount = extractedInfo.getDimensions() != null ? extractedInfo.getDimensions().size() : 0;
        int measureCount = extractedInfo.getMeasures() != null ? extractedInfo.getMeasures().size() : 0;

        // 基于维度和指标数量动态调整结果数量
        int adjustedLimit = defaultLimit + (dimensionCount + measureCount) * 2;

        // 确保不超过最大限制
        int maxLimit = 50; // 暂时使用固定值，后续可添加到配置中
        return Math.min(adjustedLimit, maxLimit);
    }
}