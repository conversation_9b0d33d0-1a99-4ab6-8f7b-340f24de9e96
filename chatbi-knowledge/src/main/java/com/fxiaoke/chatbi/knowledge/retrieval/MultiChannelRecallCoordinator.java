package com.fxiaoke.chatbi.knowledge.retrieval;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.intent.UserIntent;
import com.fxiaoke.chatbi.common.model.knowledge.ChannelType;
import com.fxiaoke.chatbi.integration.model.ch.ChartKnowledge;
import com.fxiaoke.chatbi.knowledge.retrieval.channel.RecallChannel;
import com.fxiaoke.chatbi.knowledge.retrieval.channel.RecallWeightConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * 多路召回协调器
 * 负责协调多个召回渠道，并行执行召回，合并结果
 */
@Slf4j
@Service
public class MultiChannelRecallCoordinator {

    private final List<RecallChannel> recallChannels;
    private final RecallWeightConfig weightConfig;

    @Resource(name = "monitorAsyncTaskExecutor")
    private TaskExecutor monitorAsyncTaskExecutor;

    @Value("${chatbi.knowledge.recall.timeout:5000}")
    private long timeoutMillis;

    public MultiChannelRecallCoordinator(List<RecallChannel> recallChannels, RecallWeightConfig weightConfig) {
        this.recallChannels = recallChannels;
        this.weightConfig = weightConfig;

        log.info("多路召回协调器初始化完成，已注册渠道数量: {}", recallChannels.size());
        for (RecallChannel channel : recallChannels) {
            log.info("已注册召回渠道: {}, 类型: {}, 方法: {}",
                    channel.getChannelName(),
                    channel.getChannelType(),
                    channel.getRecallMethod());
        }
    }

    /**
     * 执行多路召回
     * 包含基于渠道权重的去重，保留权重最高的结果
     */
    public Map<RecallChannel, List<ChartKnowledge>> executeMultiChannelRecall(UserIntent intent,
                                                                              UserIdentity identity) {
        Map<String, ChartInfo> viewIdMap = new HashMap<>();
        Map<RecallChannel, Integer> channelCounts = new HashMap<>();

        // 1. 并行执行各渠道召回，简化依赖链
        List<CompletableFuture<RecallResult>> futures = recallChannels.stream()
                .map(channel -> CompletableFuture
                        .supplyAsync(() -> {
                            try {
                                log.info("[{}] 开始执行召回", channel.getChannelName());
                                List<ChartKnowledge> charts = channel.recall(intent, identity);
                                log.info("[{}] 召回完成，结果数量: {}", channel.getChannelName(),
                                        charts != null ? charts.size() : 0);
                                return new RecallResult(channel, charts);
                            } catch (Exception e) {
                                log.error("[{}] 召回异常", channel.getChannelName(), e);
                                return new RecallResult(channel, Collections.emptyList());
                            }
                        }, monitorAsyncTaskExecutor))
                .collect(Collectors.toList());

        // 2. 直接等待并处理结果
        List<RecallResult> results = new ArrayList<>();
        for (CompletableFuture<RecallResult> future : futures) {
            try {
                RecallResult result = future.get(timeoutMillis, TimeUnit.MILLISECONDS);
                if (result != null) {
                    results.add(result);
                }
            } catch (TimeoutException e) {
                log.warn("任务超时");
            } catch (Exception e) {
                log.error("任务异常", e);
            }
        }

        // 3. 处理召回结果
        for (RecallResult result : results) {
            RecallChannel channel = result.getChannel();
            ChannelType channelType = channel.getChannelType();

            if (channelType == null) {
                log.warn("渠道类型为空: {}", channel.getChannelName());
                continue;
            }

            List<ChartKnowledge> validCharts = result.getCharts().stream()
                    .filter(chart -> chart != null && chart.getViewId() != null)
                    .collect(Collectors.toList());

            // 记录原始召回数量
            channelCounts.put(channel, validCharts.size());

            // 获取渠道权重并更新图表映射
            double channelWeight = weightConfig.getChannelWeight(channelType);
            updateChartMapping(validCharts, channel, channelWeight, viewIdMap);
        }


        // 5. 返回按渠道组织的去重结果
        return organizeResults(viewIdMap);
    }

    /**
     * 更新图表映射，合并多路召回的评分
     */
    private void updateChartMapping(List<ChartKnowledge> charts,
                                    RecallChannel channel,
                                    double channelWeight,
                                    Map<String, ChartInfo> viewIdMap) {
        for (ChartKnowledge chart : charts) {
            String viewId = chart.getViewId();
            if (viewId == null) {
                continue;
            }

            ChartInfo existing = viewIdMap.get(viewId);
            if (existing == null) {
                // 首次添加
                viewIdMap.put(viewId, new ChartInfo(chart, channel, channelWeight));
                log.debug("召回图表: {}, 渠道: {}, 权重: {}",
                        viewId, channel.getChannelName(), channelWeight);
            } else {
                // 合并评分
                ChartKnowledge mergedChart = mergeChartScores(existing.chart, chart);
                // 选择权重较高的渠道
                RecallChannel selectedChannel = channelWeight > existing.channelWeight ? channel : existing.channel;
                double selectedWeight = Math.max(channelWeight, existing.channelWeight);

                viewIdMap.put(viewId, new ChartInfo(mergedChart, selectedChannel, selectedWeight));
                log.debug("合并图表评分: {}, 最终渠道: {}, 权重: {}",
                        viewId, selectedChannel.getChannelName(), selectedWeight);
            }
        }
    }

    /**
     * 合并图表评分
     * 对相同图表的多路召回结果进行评分合并
     */
    private ChartKnowledge mergeChartScores(ChartKnowledge existing, ChartKnowledge current) {
        // 创建新的图表对象，避免修改原对象
        ChartKnowledge merged = new ChartKnowledge();

        // 复制基础信息
        merged.setViewId(existing.getViewId());
        merged.setTenantId(existing.getTenantId());
        merged.setChartType(existing.getChartType());
        merged.setSchemaId(existing.getSchemaId());
        merged.setSpec(existing.getSpec());
        merged.setDimensionNames(existing.getDimensionNames());
        merged.setMeasureNames(existing.getMeasureNames());
        merged.setFilterNames(existing.getFilterNames());
        merged.setFieldIds(existing.getFieldIds());
        merged.setUsageCount(existing.getUsageCount());
        merged.setLastModifiedTime(existing.getLastModifiedTime());

        // 合并评分 - 取最高分
        merged.setKeyWordScore(Math.max(
                existing.getKeyWordScore() != null ? existing.getKeyWordScore() : 0.0,
                current.getKeyWordScore() != null ? current.getKeyWordScore() : 0.0
        ));

        merged.setVectorScore(Math.max(
                existing.getVectorScore() != null ? existing.getVectorScore() : 0.0,
                current.getVectorScore() != null ? current.getVectorScore() : 0.0
        ));

        log.debug("评分合并 - 图表: {}, 关键词分数: {} -> {}, 向量分数: {} -> {}",
                existing.getViewId(),
                existing.getKeyWordScore(),
                merged.getKeyWordScore(),
                existing.getVectorScore(),
                merged.getVectorScore());

        return merged;
    }

    /**
     * 组织最终结果
     */
    private Map<RecallChannel, List<ChartKnowledge>> organizeResults(Map<String, ChartInfo> viewIdMap) {
        return viewIdMap.values().stream()
                .collect(Collectors.groupingBy(
                        info -> info.channel,
                        Collectors.mapping(
                                info -> info.chart,
                                Collectors.toList()
                        )
                ));
    }

    /**
     * 图表信息包装类
     */
    private static class ChartInfo {
        private final ChartKnowledge chart;
        private final RecallChannel channel;
        private final double channelWeight;

        public ChartInfo(ChartKnowledge chart, RecallChannel channel, double channelWeight) {
            this.chart = chart;
            this.channel = channel;
            this.channelWeight = channelWeight;
        }
    }

    /**
     * 召回结果包装类
     */
    private static class RecallResult {
        private final RecallChannel channel;
        private final List<ChartKnowledge> charts;

        public RecallResult(RecallChannel channel, List<ChartKnowledge> charts) {
            this.channel = channel;
            this.charts = charts != null ? charts : Collections.emptyList();
        }

        public RecallChannel getChannel() {
            return channel;
        }

        public List<ChartKnowledge> getCharts() {
            return charts;
        }
    }
}