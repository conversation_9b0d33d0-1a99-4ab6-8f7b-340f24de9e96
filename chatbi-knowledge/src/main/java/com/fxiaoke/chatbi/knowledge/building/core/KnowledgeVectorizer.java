package com.fxiaoke.chatbi.knowledge.building.core;

import com.fxiaoke.chatbi.common.model.knowledge.Knowledge;
import com.fxiaoke.chatbi.integration.model.ch.KnowledgeEmbedding;

import java.util.List;

/**
 * 知识向量化接口
 * @param <T> 知识类型
 */
public interface KnowledgeVectorizer<T extends Knowledge> {

  /**
   * 向量化知识
   * @param knowledge 知识对象
   * @param features 特征列表
   * @param context 向量化上下文
   * @return 向量化结果
   */
  List<KnowledgeEmbedding> vectorize(T knowledge, List<String> features, VectorizeContext context);
}
