package com.fxiaoke.chatbi.knowledge.building.metadata;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChartDesc {
    /**
     * 图表ID
     */
    private String viewId;
    
    /**
     * 图表名称
     */
    private String name;
    
    /**
     * 图表类型
     */
    private String chartType;
    
    /**
     * 统计主题ID
     */
    private String schemaId;
    
    /**
     * 图表用途描述
     */
    private String description;

    /**
     * 维度ID列表及其分析配置
     */
    private List<ViewFieldDesc> dimensions;

    /**
     * 指标ID列表及其分析配置
     */
    private List<ViewFieldDesc> measures;

    /**
     * 数据过滤范围
     */
    private List<ViewFilterDesc> filters;
}
