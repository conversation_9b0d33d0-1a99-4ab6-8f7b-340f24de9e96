package com.fxiaoke.chatbi.knowledge.service.impl.chart;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.knowledge.ChannelType;
import com.fxiaoke.chatbi.common.model.enums.KnowledgeType;
import com.fxiaoke.chatbi.common.model.response.ChartEmbeddingResponse;
import com.fxiaoke.chatbi.integration.model.ch.ChartKnowledge;
import com.fxiaoke.chatbi.integration.model.ch.KnowledgeEmbedding;
import com.fxiaoke.chatbi.integration.repository.ch.ChartKnowledgeRepository;
import com.fxiaoke.chatbi.integration.repository.ch.KnowledgeEmbeddingRepository;
import com.fxiaoke.chatbi.knowledge.building.metadata.ChartDesc;
import com.fxiaoke.chatbi.knowledge.service.ChartKnowledgeBuildingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 图表知识服务实现
 * 委托具体的图表知识构建管理器处理图表知识构建任务
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChartKnowledgeBuildingServiceImpl implements ChartKnowledgeBuildingService {
    private final ChartKnowledgeBuildingManager buildingManager;
    private final ChartKnowledgeRepository chartKnowledgeRepository;
    private final KnowledgeEmbeddingRepository knowledgeEmbeddingRepository;

    @Override
    public void buildChartKnowledge(UserIdentity userIdentity, String viewId) {
        if (userIdentity == null || StringUtils.isEmpty(userIdentity.getTenantId())) {
            log.error("Invalid userIdentity for building chart knowledge");
            return;
        }

        if (StringUtils.isEmpty(viewId)) {
            log.warn("ViewId is empty, will build knowledge for all charts");
            buildAllChartKnowledge(userIdentity);
            return;
        }

        log.info("Building chart knowledge for viewId: {}, tenant: {}", viewId, userIdentity.getTenantId());
        buildingManager.processChart(userIdentity, viewId);
    }

    @Override
    public void buildAllChartKnowledge(UserIdentity userIdentity) {
        if (userIdentity == null || StringUtils.isEmpty(userIdentity.getTenantId())) {
            log.error("Invalid userIdentity for building all chart knowledge");
            return;
        }

        log.info("Building knowledge for all charts of tenant: {}", userIdentity.getTenantId());
        buildingManager.processAllCharts(userIdentity);
    }

    @Override
    public void batchBuildChartKnowledge(UserIdentity userIdentity, List<String> viewIds) {
        if (userIdentity == null || StringUtils.isEmpty(userIdentity.getTenantId())) {
            log.error("Invalid userIdentity for batch building chart knowledge");
            return;
        }

        if (CollectionUtils.isEmpty(viewIds)) {
            log.warn("ViewIds is empty, will not build any chart knowledge");
            return;
        }

        log.info("Batch building chart knowledge for {} viewIds, tenant: {}", viewIds.size(), userIdentity.getTenantId());
        viewIds.forEach(viewId -> buildingManager.processChart(userIdentity, viewId));
    }
    
    @Override
    public List<ChartEmbeddingResponse> listTenantChartFeatures(UserIdentity userIdentity) {
        if (userIdentity == null || StringUtils.isEmpty(userIdentity.getTenantId())) {
            log.error("Invalid userIdentity for getting chart features");
            return Collections.emptyList();
        }
        
        String tenantId = userIdentity.getTenantId();
        log.info("获取图表特征, tenantId={}", tenantId);
        
        // 查询所有的图表知识
        List<ChartKnowledge> chartKnowledges = chartKnowledgeRepository.findAllByTenant(tenantId);
        if (CollectionUtils.isEmpty(chartKnowledges)) {
            log.warn("未找到图表知识, tenantId={}", tenantId);
            return Collections.emptyList();
        }
        
        log.info("成功查询到图表知识, count={}", chartKnowledges.size());
        
        // 提取所有视图ID
        List<String> viewIds = chartKnowledges.stream()
            .map(ChartKnowledge::getViewId)
            .collect(Collectors.toList());
        
        // 批量查询所有视图的特征向量
        long startTime = System.currentTimeMillis();
        Map<String, List<KnowledgeEmbedding>> embeddingsMap = knowledgeEmbeddingRepository.findByKnowledgeIdList(
            userIdentity,
            ChannelType.TENANT,
            KnowledgeType.CHART.getCode(),
            viewIds
        );
        long endTime = System.currentTimeMillis();
        log.info("批量查询图表特征耗时: {}ms, 查询数量: {}, 结果数量: {}", 
            (endTime - startTime), viewIds.size(), embeddingsMap.size());
        
        // 将图表知识转换为响应对象
        List<ChartEmbeddingResponse> embeddingResponses = new ArrayList<>(chartKnowledges.size());
        
        for (ChartKnowledge knowledge : chartKnowledges) {
            try {
                // 解析图表描述
                ChartDesc chartDesc = null;
                String viewName = "";
                
                if (StringUtils.isNotBlank(knowledge.getSpec())) {
                    chartDesc = JSON.parseObject(knowledge.getSpec(), ChartDesc.class);
                    viewName = chartDesc != null ? chartDesc.getName() : "";
                }
                
                // 从映射中获取当前视图的特征向量
                List<KnowledgeEmbedding> embeddings = embeddingsMap.getOrDefault(knowledge.getViewId(), Collections.emptyList());
                
                // 提取特征文本
                List<String> features = Collections.emptyList();
                if (!CollectionUtils.isEmpty(embeddings)) {
                    features = embeddings.stream()
                        .map(KnowledgeEmbedding::getFeature)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList());
                }
                
                // 构建响应对象
                ChartEmbeddingResponse response = ChartEmbeddingResponse.builder()
                    .viewId(knowledge.getViewId())
                    .viewName(viewName)
                    .features(features)
                    .build();
                    
                embeddingResponses.add(response);
            } catch (Exception e) {
                log.error("处理图表特征失败: viewId={}, error={}", knowledge.getViewId(), e.getMessage(), e);
                // 继续处理下一个图表
            }
        }
        
        log.info("成功构建图表特征响应, count={}", embeddingResponses.size());
        return embeddingResponses;
    }
} 