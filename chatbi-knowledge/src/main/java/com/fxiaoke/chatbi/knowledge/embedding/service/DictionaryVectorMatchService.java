package com.fxiaoke.chatbi.knowledge.embedding.service;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.enums.KnowledgeType;
import com.fxiaoke.chatbi.common.model.knowledge.ChannelType;
import com.fxiaoke.chatbi.common.utils.AsyncTaskUtils;
import com.fxiaoke.chatbi.integration.model.ch.KnowledgeEmbedding;
import com.fxiaoke.chatbi.integration.repository.ch.KnowledgeEmbeddingRepository;
import com.fxiaoke.chatbi.knowledge.embedding.core.TextVectorizer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * 字典向量匹配服务
 * 提供基于向量检索的字典类型（操作符、日期范围等）匹配功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DictionaryVectorMatchService {
    private final KnowledgeEmbeddingRepository knowledgeEmbeddingRepository;
    private final TextVectorizer textVectorizer;
    
    // 线程池，用于并行处理
    private final ExecutorService executorService = Executors.newFixedThreadPool(
        Runtime.getRuntime().availableProcessors() + 1
    );
    
    // 默认超时时间（毫秒）
    @Value("${chatbi.dictionary.match.timeout:2000}")
    private long timeoutMillis;

    // 默认阈值
    private static final double DEFAULT_THRESHOLD = 0.3;
    // 默认结果数量
    private static final int DEFAULT_LIMIT = 1;

    /**
     * 匹配操作符
     * 将输入的操作符文本匹配到预定义的操作符枚举
     * 使用并行处理提高效率
     *
     * @param operators    操作符文本列表
     * @param userIdentity 用户身份信息
     * @return 操作符文本到标准操作符ID的映射
     */
    public Map<String, String> matchOperators(List<String> operators, UserIdentity userIdentity) {
        return matchDictionaryItems(operators, KnowledgeType.OPERATOR, userIdentity);
    }

    /**
     * 匹配日期范围
     * 将输入的日期范围文本匹配到预定义的日期范围枚举
     *
     * @param dateRange    日期范围文本
     * @param userIdentity 用户身份信息
     * @return 匹配的标准日期范围ID，如果未匹配则返回null
     */
    public String matchDateRange(String dateRange, UserIdentity userIdentity) {
        if (StringUtils.isBlank(dateRange) || userIdentity == null) {
            return null;
        }

        Map<String, String> result = matchDictionaryItems(
            Collections.singletonList(dateRange), 
            KnowledgeType.DATE_RANGE, 
            userIdentity
        );
        
        return result.get(dateRange);
    }

    /**
     * 通用字典项匹配方法
     * 将输入文本列表匹配到指定类型的字典
     * 使用CompletableFuture真正并行处理
     *
     * @param items        要匹配的文本列表
     * @param dictType     字典类型
     * @param userIdentity 用户身份信息
     * @return 文本到字典项ID的映射
     */
    public Map<String, String> matchDictionaryItems(List<String> items, KnowledgeType dictType, UserIdentity userIdentity) {
        if (CollectionUtils.isEmpty(items) || dictType == null || userIdentity == null) {
            return Collections.emptyMap();
        }

        // 过滤空值
        List<String> validItems = items.stream()
                .filter(StringUtils::isNotBlank)
                .distinct()
                .toList();
        
        if (validItems.isEmpty()) {
            return Collections.emptyMap();
        }

        log.info("开始匹配字典项[{}]: {}", dictType.getDescription(), validItems);
        long startTime = System.currentTimeMillis();
        
        // 结果映射
        Map<String, String> resultMap = new ConcurrentHashMap<>();
        
        // 为每个项创建异步任务
        List<CompletableFuture<MatchResult>> futures = validItems.stream()
                .map(item -> createMatchTask(item, dictType, userIdentity))
                .toList();
        
        // 等待所有任务完成或超时
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        
        try {
            // 设置超时
            allFutures.get(timeoutMillis, TimeUnit.MILLISECONDS);
        } catch (TimeoutException e) {
            log.warn("字典匹配部分项超时，将继续处理已完成的项");
        } catch (Exception e) {
            log.error("字典匹配等待任务完成时发生异常", e);
        }
        
        // 收集所有已完成任务的结果
        for (CompletableFuture<MatchResult> future : futures) {
            if (future.isDone() && !future.isCompletedExceptionally()) {
                try {
                    MatchResult result = future.get();
                    if (result.isMatched()) {
                        resultMap.put(result.getItem(), result.getMatchedId());
                    }
                } catch (Exception e) {
                    log.error("获取匹配结果时发生异常", e);
                }
            }
        }
        
        long endTime = System.currentTimeMillis();
        log.info("匹配字典项[{}]完成, 总耗时: {}ms, 匹配数量: {}/{}", 
                dictType.getDescription(), (endTime - startTime), resultMap.size(), validItems.size());
        
        return resultMap;
    }

    /**
     * 创建单个项的匹配任务
     *
     * @param item         要匹配的文本
     * @param dictType     字典类型
     * @param userIdentity 用户身份信息
     * @return 异步任务
     */
    private CompletableFuture<MatchResult> createMatchTask(String item, KnowledgeType dictType, UserIdentity userIdentity) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                String matchedId = matchDictionary(item, dictType, userIdentity);
                return new MatchResult(item, matchedId, matchedId != null);
            } catch (Exception e) {
                log.error("匹配字典项[{}]时发生异常: {}", dictType.getDescription(), item, e);
                return new MatchResult(item, null, false);
            }
        }, executorService);
    }
    
    /**
     * 通用字典匹配方法
     * 将输入文本匹配到指定类型的字典
     *
     * @param text         要匹配的文本
     * @param dictType     字典类型
     * @param userIdentity 用户身份信息
     * @return 匹配的字典项ID，如果未匹配则返回null
     */
    public String matchDictionary(String text, KnowledgeType dictType, UserIdentity userIdentity) {
        if (StringUtils.isBlank(text) || dictType == null || userIdentity == null) {
            return null;
        }

        try {
            // 向量化输入文本
            float[] queryVector = textVectorizer.vectorize(text, userIdentity);
            
            // 搜索相似向量
            List<KnowledgeEmbedding> matches = knowledgeEmbeddingRepository.searchEmbeddingWithScores(
                queryVector,
                userIdentity,
                ChannelType.SYSTEM, // 字典数据通常存储在系统库
                dictType.getCode(),
                DEFAULT_THRESHOLD,
                DEFAULT_LIMIT
            );
            
            // 如果找到匹配，返回字典项ID
            if (!matches.isEmpty()) {
                KnowledgeEmbedding match = matches.get(0);
                String dictionaryId = match.getKnowledgeId();
                String standardName = match.getFeature();
                log.info("匹配到字典项[{}]: {} -> {} ({}), 相似度: {}", 
                        dictType.getDescription(), text, dictionaryId, standardName, match.getVectorScore());
                return dictionaryId;
            } else {
                log.info("未找到匹配字典项[{}]: {}", dictType.getDescription(), text);
                return null;
            }
        } catch (Exception e) {
            log.error("匹配字典项失败[{}]: {}", dictType.getDescription(), text, e);
            return null;
        }
    }
    
    /**
     * 匹配结果包装类
     */
    private static class MatchResult {
        private final String item;
        private final String matchedId;
        private final boolean matched;
        
        public MatchResult(String item, String matchedId, boolean matched) {
            this.item = item;
            this.matchedId = matchedId;
            this.matched = matched;
        }
        
        public String getItem() {
            return item;
        }
        
        public String getMatchedId() {
            return matchedId;
        }
        
        public boolean isMatched() {
            return matched;
        }
    }
} 