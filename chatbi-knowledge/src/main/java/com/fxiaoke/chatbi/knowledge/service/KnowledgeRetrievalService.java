package com.fxiaoke.chatbi.knowledge.service;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.intent.KnowledgeScope;
import com.fxiaoke.chatbi.common.model.intent.UserIntent;
import com.fxiaoke.chatbi.common.model.reasoning.ReasoningCollector;

public interface KnowledgeRetrievalService {
  /**
   * 知识召回；召回图表、字典知识、相关元数据知识
   *
   * @param intent
   * @param userIdentity
   * @param reasoningCollector
   * @return
   */
  KnowledgeScope retrieveKnowledge(UserIntent intent, UserIdentity userIdentity, ReasoningCollector reasoningCollector);
}
