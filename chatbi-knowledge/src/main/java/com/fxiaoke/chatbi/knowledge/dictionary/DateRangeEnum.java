package com.fxiaoke.chatbi.knowledge.dictionary;

import java.util.List;
import java.util.Arrays;

/**
 * 日期范围枚举
 * 用于描述数据分析中的时间范围选择
 */
public enum DateRangeEnum {

  // 年度相关
  CURRENTYEAR(6, "CURRENTYEAR", Arrays.asList("本年度", "本年", "当年", "今年")),
  LASTYEAR(7, "LASTYEAR", Arrays.asList("上一年度", "上年", "去年")),
  NEXTYEAR(10, "NEXTYEAR", Arrays.asList("下一年度", "下年", "明年")),

  // 季度相关
  CURRENTSEASON(13, "CURRENTSEASON", Arrays.asList("本季度", "当季")),
  LASTSEASON(14, "LASTSEASON", Arrays.asList("上一季度", "上季度")),
  NEXTSEASON(15, "NEXTSEASON", Arrays.asList("下一季度", "下季度")),

  // 月份相关
  CURRENTMONTH(4, "CURRENTMONTH", Arrays.asList("本月", "当月")),
  LASTMONTH(5, "LASTMONTH", Arrays.asList("上月", "上个月")),
  LAST2MONTH(38, "LAST2MONTH", Arrays.asList("上上月", "上上个月")),
  NEXTMONTH(8, "NEXTMONTH", Arrays.asList("下月", "下个月")),

  // 周相关
  CURRENTWEEK(2, "CURRENTWEEK", Arrays.asList("本周", "当周")),
  LASTWEEK(3, "LASTWEEK", Arrays.asList("上周", "上个周")),
  NEXTWEEK(9, "NEXTWEEK", Arrays.asList("下周", "下个周")),

  // 日相关
  TODAY(11, "TODAY", Arrays.asList("今天", "当天", "本日")),
  YESTERDAY(1, "YESTERDAY", Arrays.asList("昨天", "昨日")),
  TOMORROW(12, "TOMORROW", Arrays.asList("明天", "明日")),

  // 自定义
  CUSTOM(16, "CUSTOM", Arrays.asList("自定义")),

  // 半年相关
  LASTHALFYEAR(17, "LASTHALFYEAR", Arrays.asList("上半年")),
  NEXTHALFYEAR(18, "NEXTHALFYEAR", Arrays.asList("下半年")),

  // 财年相关
  CURRENTFISCALYEAR(20, "CURRENTFISCALYEAR", Arrays.asList("本财年", "当前财年")),
  LASTFISCALYEAR(21, "LASTFISCALYEAR", Arrays.asList("上一财年", "上财年")),
  NEXTFISCALYEAR(22, "NEXTFISCALYEAR", Arrays.asList("下一财年", "下财年")),
  LASTHALFFISCALYEAR(23, "LASTHALFFISCALYEAR", Arrays.asList("上半财年")),
  NEXTHALFFISCALYEAR(24, "NEXTHALFFISCALYEAR", Arrays.asList("下半财年")),
  CURRENTFISCALMONTH(28, "CURRENTFISCALMONTH", Arrays.asList("本财月", "当前财月")),
  LASTFISCALMONTH(29, "LASTFISCALMONTH", Arrays.asList("上一财月", "上财月")),
  NEXTFISCALMONTH(30, "NEXTFISCALMONTH", Arrays.asList("下一财月", "下财月")),

  // 同比相关
  THEMONTHOFLASTYEAR(31, "THEMONTHOFLASTYEAR", Arrays.asList("去年本月", "去年当月")),
  THESEASONOFLASTYEAR(32, "THESEASONOFLASTYEAR", Arrays.asList("去年本季度", "去年当季")),
  THEDATEFROMLASTYEARTOTHISMONTH(33, "THEDATEFROMLASTYEARTOTHISMONTH", Arrays.asList("去年年初至去年当月", "去年累计")),
  THEMONTHOFLASTYEARAFTER1MONTH(34, "THEMONTHOFLASTYEARAFTER1MONTH", Arrays.asList("去年当月后第1个月")),
  THEMONTHOFLASTYEARAFTER2MONTH(35, "THEMONTHOFLASTYEARAFTER2MONTH", Arrays.asList("去年当月后第2个月")),
  THEMONTHOFLASTYEARAFTER3MONTH(36, "THEMONTHOFLASTYEARAFTER3MONTH", Arrays.asList("去年当月后第3个月")),
  THEDATEOFLASTYEARAFTERRANGE3MONTH(37, "THEDATEOFLASTYEARAFTERRANGE3MONTH", Arrays.asList("去年当月后累计3个月"));

  private final int id;
  private final String code;
  private final List<String> expressions;

  DateRangeEnum(int id, String code, List<String> expressions) {
    this.id = id;
    this.code = code;
    this.expressions = expressions;
  }

  public int getId() {
    return id;
  }

  public String getCode() {
    return code;
  }

  public List<String> getExpressions() {
    return expressions;
  }
  
  /**
   * 根据编码获取对应的日期范围枚举
   *
   * @param code 编码，如 "TODAY"、"YESTERDAY" 等
   * @return 对应的枚举实例，如未找到则返回 null
   */
  public static DateRangeEnum fromCode(String code) {
    if (code == null) {
      return null;
    }
    
    for (DateRangeEnum dateRange : DateRangeEnum.values()) {
      if (dateRange.code.equals(code)) {
        return dateRange;
      }
    }
    
    return null;
  }
  
  /**
   * 根据Code获取对应的ID
   *
   * @param code 编码，如 "TODAY"、"YESTERDAY" 等
   * @return 对应的ID值，如未找到则返回 -1
   */
  public static String getIdByCode(String code) {
    DateRangeEnum dateRange = fromCode(code);
    return dateRange != null ? String.valueOf(dateRange.getId()) : null;
  }
}
