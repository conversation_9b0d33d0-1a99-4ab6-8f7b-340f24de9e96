package com.fxiaoke.chatbi.knowledge.building.enterprise;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.integration.model.ch.KnowledgeEmbedding;
import com.fxiaoke.chatbi.integration.model.pg.EnterpriseKnowledge;
import com.fxiaoke.chatbi.knowledge.building.core.KnowledgeVectorizer;
import com.fxiaoke.chatbi.knowledge.building.core.VectorizeContext;
import com.fxiaoke.chatbi.knowledge.embedding.core.TextVectorizer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 企业知识向量化器
 * 负责处理企业知识库的向量化
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class EnterpriseKnowledgeVectorizer implements KnowledgeVectorizer<EnterpriseKnowledge> {
    private final TextVectorizer textVectorizer;

    @Override
    public List<KnowledgeEmbedding> vectorize(EnterpriseKnowledge knowledge, List<String> features, VectorizeContext context) {
        if (knowledge == null) {
            log.warn("Cannot vectorize null knowledge");
            return new ArrayList<>();
        }

        if (CollectionUtils.isEmpty(features)) {
            // 如果没有提供特征，尝试从知识对象中提取
            features = extractFeatures(knowledge);
            if (CollectionUtils.isEmpty(features)) {
                log.warn("No features to vectorize for enterprise knowledge: {}", knowledge.getId());
                return new ArrayList<>();
            }
        }

        log.info("Vectorizing EnterpriseKnowledge: {}, features size: {}", knowledge.getId(), features.size());

        // 创建用户身份信息，用于向量化
        UserIdentity userIdentity = UserIdentity.builder()
                .tenantId(knowledge.getTenantId())
                .build();

        // 并行向量化所有特征
        List<CompletableFuture<KnowledgeEmbedding>> futures = new ArrayList<>();
        for (String feature : features) {
            futures.add(CompletableFuture.supplyAsync(() -> buildKnowledgeEmbedding(knowledge, feature, userIdentity, context)));
        }

        // 等待所有向量化任务完成，过滤掉可能的失败结果
        return futures.stream()
                .map(CompletableFuture::join)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 从企业知识中提取特征
     *
     * @param knowledge 企业知识
     * @return 特征列表
     */
    private List<String> extractFeatures(EnterpriseKnowledge knowledge) {
        List<String> features = new ArrayList<>();
        
        // 添加源术语
        if (StringUtils.isNotEmpty(knowledge.getSourceTerm())) {
            features.add(knowledge.getSourceTerm());
        }
        
        // 添加同义术语
        if (StringUtils.isNotEmpty(knowledge.getSynonymTerm())) {
            List<String> synonymTerms = Arrays.stream(knowledge.getSynonymTerm().split(";"))
                    .filter(StringUtils::isNotEmpty)
                    .toList();
            features.addAll(synonymTerms);
        }
        
        return features.stream().distinct().toList();
    }

    /**
     * 为单个特征构建知识向量
     *
     * @param knowledge    企业知识
     * @param feature      特征文本
     * @param userIdentity 用户身份
     * @param context      向量化上下文
     * @return 知识向量
     */
    private KnowledgeEmbedding buildKnowledgeEmbedding(EnterpriseKnowledge knowledge, String feature, UserIdentity userIdentity, VectorizeContext context) {
        try {
            return KnowledgeEmbedding.builder()
                    .knowledgeType(knowledge.getKnowledgeType())
                    .knowledgeId(knowledge.getTargetTerm())
                    .tenantId(knowledge.getTenantId())
                    .feature(feature)
                    .embedding(textVectorizer.vectorize(feature, userIdentity))
                    .weight(1.0f)
                    .build();
        } catch (Exception e) {
            log.error("Failed to vectorize feature: {} for knowledge: {}", feature, knowledge.getId(), e);
            return null;
        }
    }
} 