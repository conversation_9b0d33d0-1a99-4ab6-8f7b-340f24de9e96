package com.fxiaoke.chatbi.knowledge.building.core;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import lombok.Builder;
import lombok.Data;

/**
 * 知识构建上下文
 */
@Data
@Builder
public class BuildContext {
  /**
   * 用户身份
   */
  private UserIdentity userIdentity;

  /**
   * 租户ID
   */
  private String tenantId;

  /**
   * 批次ID
   */
  private Long batchId;

  /**
   * 系统标识
   */
  private Byte sysFlag;
}