package com.fxiaoke.chatbi.knowledge.retrieval;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.prompts.PromptTemplateType;
import com.fxiaoke.chatbi.integration.model.ch.ChartKnowledge;
import com.fxiaoke.chatbi.knowledge.retrieval.channel.RecallChannel;
import com.fxiaoke.chatbi.knowledge.retrieval.weight.ChartRankingCalculator;
import com.fxiaoke.chatbi.prompts.PromptTemplateService;
import com.fxiaoke.common.Pair;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 图表排序服务
 * 主要职责：
 * 1. 计算图表相关性得分
 * 2. 通过 WeightCalculator 计算最终权重
 * 3. 对结果进行去重和排序
 */
@Slf4j
@Service
public class ChartRankingService {
    private final ChartRankingCalculator chartRankingCalculator;
    private final PromptTemplateService promptTemplateService;

    public ChartRankingService(ChartRankingCalculator chartRankingCalculator, PromptTemplateService promptTemplateService) {
        this.chartRankingCalculator = chartRankingCalculator;
        this.promptTemplateService = promptTemplateService;
    }

    /**
     * 对多路召回结果进行排序
     *
     * @param channelResults 渠道召回结果映射
     * @param fieldIds       字段ID列表
     * @param topN           返回的前N个结果
     * @param userIdentity
     * @return 排序后的图表ID和主题ID列表
     */
    public List<Pair<String, String>> rankCharts(Map<RecallChannel, List<ChartKnowledge>> channelResults, List<String> fieldIds, int topN, UserIdentity userIdentity) {

        if (MapUtils.isEmpty(channelResults)) {
            return Collections.emptyList();
        }

        //过滤图表
        filterViewsByConfig(channelResults, userIdentity);

        List<ChartScoreCard> sortedScoreCards = channelResults.entrySet().stream()
                .filter(entry -> CollectionUtils.isNotEmpty(entry.getValue()))
                .flatMap(entry -> entry.getValue().stream()
                        .filter(chart -> chart != null && chart.getViewId() != null)
                        .map(chart -> calculateChartScore(chart, entry.getKey(), fieldIds))
                        .filter(Objects::nonNull))
                .collect(Collectors.groupingBy(ChartScoreCard::getViewId,
                        Collectors.maxBy(Comparator.comparing(ChartScoreCard::getFinalScore))))
                .values().stream()
                .filter(Optional::isPresent)
                .map(Optional::get)
                .sorted(Comparator.comparing(ChartScoreCard::getFinalScore).reversed())
                .collect(Collectors.toList());

        // 打印前5张评分卡的详细信息
        if (!sortedScoreCards.isEmpty()) {
            log.info("Top 5 Chart Score Cards:");
            sortedScoreCards.stream()
                    .limit(5)
                    .forEach(card -> log.info("Chart[{}] from[{}] Scores: field={}, keyword={}, vector={}, usage={}, final={}, update={}, schema={}",
                            card.getViewId(),
                            card.getChannel().getChannelType(),
                            String.format("%.2f", card.getFieldMatchRatio()),
                            card.getKeywordScore() != null ? String.format("%.2f", card.getKeywordScore()) : "N/A",
                            card.getVectorScore() != null ? String.format("%.2f", card.getVectorScore()) : "N/A",
                            card.getUsageCount(),
                            String.format("%.2f", card.getFinalScore()),
                            card.getLastModifiedTime(),
                            card.getSchemaId()));
        }

        return sortedScoreCards.stream()
                .limit(topN)
                .map(score -> new Pair<>(score.getViewId(), score.getSchemaId()))
                .collect(Collectors.toList());
    }

    private void filterViewsByConfig(Map<RecallChannel, List<ChartKnowledge>> channelResults, UserIdentity userIdentity) {
        String templateContent = promptTemplateService.getTemplate(PromptTemplateType.OTHER);
        Map<String, Map<String, List<String>>> stringListMap = promptTemplateService.convertToComplexMap(templateContent);
        if (MapUtils.isEmpty(stringListMap)) {
            return;
        }
        Map<String, List<String>> ei2ViewIds = stringListMap.get("filterViewsByConfig");

        if (MapUtils.isEmpty(ei2ViewIds)) {
            return;
        }

        List<String> viewIds = ei2ViewIds.get(userIdentity.getTenantId());
        if (CollectionUtils.isEmpty(viewIds)) {
            return;
        }

        for (Map.Entry<RecallChannel, List<ChartKnowledge>> recallChannelListEntry : channelResults.entrySet()) {
            List<ChartKnowledge> value = recallChannelListEntry.getValue();
            if (CollectionUtils.isEmpty(value)) {
                continue;
            }
            value.removeIf(chartKnowledge -> {
                String viewId = chartKnowledge.getViewId();
                return !viewIds.contains(viewId);
            });
        }
    }

    /**
     * 计算单个图表的得分
     */
    private ChartScoreCard calculateChartScore(ChartKnowledge chart, RecallChannel channel, List<String> fieldIds) {
        try {
            // 计算字段匹配率
            double fieldMatchRatio = calculateFieldMatchRatio(chart, fieldIds);

            // 计算最终权重
            double finalScore = chartRankingCalculator.calculateFinalWeight(chart, channel.getChannelType(), fieldMatchRatio);

            return new ChartScoreCard(
                    chart.getViewId(),
                    chart.getSchemaId(),
                    channel,
                    fieldMatchRatio,
                    chart.getUsageCount(),
                    chart.getLastModifiedTime(),
                    chart.getVectorScore(),
                    chart.getKeyWordScore(),
                    finalScore
            );
        } catch (Exception e) {
            log.warn("Failed to calculate score for chart {}: {}", chart.getViewId(), e.getMessage());
            return null;
        }
    }

    /**
     * 计算字段匹配率
     *
     * @return 返回0到1之间的匹配率，如果fieldIds为空则返回1
     */
    private double calculateFieldMatchRatio(ChartKnowledge chart, List<String> fieldIds) {
        // 如果没有字段要求，返回最大匹配度
        if (CollectionUtils.isEmpty(fieldIds)) {
            return 1.0;
        }

        // 如果图表没有字段信息，返回最小匹配度
        if (chart.getFieldIds() == null) {
            return 0.0;
        }

        return (double) fieldIds.stream().filter(fieldId -> chart.getFieldIds().contains(fieldId)).count() / fieldIds.size();
    }

    /**
     * 图表评分卡
     * 记录图表在各个维度的得分情况
     */
    @Data
    private static class ChartScoreCard {
        private final String viewId;          // 图表ID
        private final String schemaId;        // 主题ID
        private final RecallChannel channel; // 召回渠道
        private final double fieldMatchRatio; // 字段匹配率
        private final int usageCount;         // 使用次数
        private final Long lastModifiedTime;    // 最后更新时间
        private final Double vectorScore;     // 归一化后的向量相似度得分(0,1]
        private final Double keywordScore;    // 关键词得分
        private final double finalScore;      // 最终得分

        public ChartScoreCard(String viewId, String schemaId, RecallChannel channel, double fieldMatchRatio, Integer usageCount,
                              Long lastModifiedTime, Double vectorScore, Double keywordScore, double finalScore) {
            this.viewId = viewId;
            this.schemaId = schemaId;
            this.channel = channel;
            this.fieldMatchRatio = fieldMatchRatio;
            this.usageCount = usageCount != null ? usageCount : 0;
            this.lastModifiedTime = lastModifiedTime != null ? lastModifiedTime : 0L;
            this.vectorScore = vectorScore;
            this.keywordScore = keywordScore;
            this.finalScore = finalScore;
        }
    }
} 