package com.fxiaoke.chatbi.knowledge.retrieval.channel;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.intent.UserIntent;
import com.fxiaoke.chatbi.integration.model.ch.ChartKnowledge;
import com.fxiaoke.chatbi.integration.repository.ch.ChartKnowledgeRepository;
import com.fxiaoke.chatbi.knowledge.retrieval.ChartRetriever;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 基于向量的图表召回渠道
 */
@Slf4j
public class VectorRecallChannel extends AbstractRecallChannel {

  private final ChartRetriever chartRetriever;

  // 搜索范围常量

  /**
   * 构造函数
   *
   * @param chartKnowledgeRepository 图表向量存储库
   * @param chartRetriever           图表检索器
   */
  public VectorRecallChannel(ChartKnowledgeRepository chartKnowledgeRepository, ChartRetriever chartRetriever) {
    super(chartKnowledgeRepository, RecallMethod.VECTOR);
    this.chartRetriever = chartRetriever;
  }

  @Override
  protected List<ChartKnowledge> doRecall(UserIntent intent, UserIdentity identity) {
    // 提取用户查询意图
    String query = intent.getInstructions();
    if (query == null || query.trim().isEmpty()) {
      log.info("[{}] 用户查询为空，跳过向量召回", getChannelDescription());
      return Collections.emptyList();
    }

    log.info("[{}] 开始向量搜索，查询: {}", getChannelDescription(), query);

    // 使用ChartRetriever进行向量搜索，并获取相似度分数
    Map<String, Double> viewIdScoreMap = chartRetriever.searchByVectorWithScores(query, identity, intent.getExtractedInfo(), getChannelType());

    if (viewIdScoreMap.isEmpty()) {
      log.info("[{}] 向量搜索未找到匹配图表", getChannelDescription());
      return Collections.emptyList();
    }

    log.info("[{}] 向量搜索找到匹配图表: {}", getChannelDescription(), viewIdScoreMap.size());

    // 获取完整的图表信息，并设置相似度分数
    List<ChartKnowledge> chartKnowledges = getChartKnowledges(viewIdScoreMap.keySet().stream().toList(), identity);
    
    // 为每个图表设置向量得分
    for (ChartKnowledge chart : chartKnowledges) {
      Double score = viewIdScoreMap.get(chart.getViewId());
      if (score != null) {
        chart.setVectorScore(score);
      }
    }

    return chartKnowledges;
  }
} 