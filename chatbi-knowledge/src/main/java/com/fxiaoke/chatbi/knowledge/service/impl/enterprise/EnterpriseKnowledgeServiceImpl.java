package com.fxiaoke.chatbi.knowledge.service.impl.enterprise;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.integration.model.ch.KnowledgeEmbedding;
import com.fxiaoke.chatbi.integration.model.pg.EnterpriseKnowledge;
import com.fxiaoke.chatbi.integration.repository.ch.KnowledgeEmbeddingRepository;
import com.fxiaoke.chatbi.integration.repository.pg.EnterpriseKnowledgeRepository;
import com.fxiaoke.chatbi.knowledge.building.core.KnowledgeVectorizer;
import com.fxiaoke.chatbi.knowledge.building.core.VectorizeContext;
import com.fxiaoke.chatbi.knowledge.service.EnterpriseKnowledgeService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 企业知识服务实现
 * 提供企业知识的访问和管理功能
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class EnterpriseKnowledgeServiceImpl implements EnterpriseKnowledgeService {
    private final EnterpriseKnowledgeRepository enterpriseKnowledgeRepository;
    private final KnowledgeVectorizer<EnterpriseKnowledge> enterpriseKnowledgeVectorizer;
    private final KnowledgeEmbeddingRepository knowledgeEmbeddingRepository;

    @Override
    public void buildEnterpriseKnowledge(UserIdentity userIdentity) {
        log.info("Building enterprise knowledge for tenant: {}", userIdentity.getTenantId());
        processEnterpriseKnowledge(userIdentity);
    }

    @Override
    public List<EnterpriseKnowledge> listAllByTenant(String tenantId) {
        log.info("获取租户所有知识条目: tenantId={}", tenantId);
        try {
            return enterpriseKnowledgeRepository.findAllByTenantIdAndIsDeleted(tenantId, 0);
        } catch (Exception e) {
            log.error("获取租户知识条目失败: tenantId={}", tenantId, e);
            return Collections.emptyList();
        }
    }
    
    /**
     * 处理企业知识的向量化
     *
     * @param userIdentity 用户身份
     */
    private void processEnterpriseKnowledge(UserIdentity userIdentity) {
        log.info("Processing enterprise knowledge for tenant: {}", userIdentity.getTenantId());
        
        // 获取企业知识列表
        List<EnterpriseKnowledge> knowledgeList = listAllByTenant(userIdentity.getTenantId());
        if (CollectionUtils.isEmpty(knowledgeList)) {
            log.info("No enterprise knowledge found for tenant: {}", userIdentity.getTenantId());
            return;
        }
        
        log.info("Found {} enterprise knowledge entries to process", knowledgeList.size());
        
        // 创建向量化上下文
        VectorizeContext context = VectorizeContext.builder()
                .tenantId(userIdentity.getTenantId())
                .batchId(System.currentTimeMillis())
                .build();
        for (List<EnterpriseKnowledge> enterpriseKnowledgeList : Lists.partition(knowledgeList, 300)) {
            // 并行处理每个知识条目
            List<CompletableFuture<List<KnowledgeEmbedding>>> futures = new ArrayList<>();
            for (EnterpriseKnowledge knowledge : enterpriseKnowledgeList) {
                futures.add(CompletableFuture.supplyAsync(() ->
                        enterpriseKnowledgeVectorizer.vectorize(knowledge, null, context)));
            }

            // 收集结果
            List<KnowledgeEmbedding> embeddings = futures.stream()
                    .map(CompletableFuture::join)
                    .filter(Objects::nonNull)
                    .flatMap(List::stream)
                    .collect(Collectors.toList());

            // 循环保存向量
            saveEmbeddings(embeddings, userIdentity.getTenantId());
        }
        log.info("Completed processing enterprise knowledge for tenant: {}", userIdentity.getTenantId());
    }
    
    /**
     * 批量保存知识向量
     *
     * @param embeddings 知识向量列表
     * @param tenantId   租户ID
     */
    private void saveEmbeddings(List<KnowledgeEmbedding> embeddings, String tenantId) {
        if (CollectionUtils.isEmpty(embeddings)) {
            log.info("No embeddings to save");
            return;
        }
        
        log.info("Saving {} knowledge embeddings", embeddings.size());
        for (List<KnowledgeEmbedding> batch : Lists.partition(embeddings, 200)) {
            knowledgeEmbeddingRepository.saveKnowledgeEmbeddings(batch, tenantId);
        }
    }
} 