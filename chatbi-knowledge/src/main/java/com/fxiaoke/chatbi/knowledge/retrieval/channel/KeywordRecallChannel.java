package com.fxiaoke.chatbi.knowledge.retrieval.channel;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.intent.ExtractedInfo;
import com.fxiaoke.chatbi.common.model.intent.FilterInfo;
import com.fxiaoke.chatbi.common.model.intent.UserIntent;
import com.fxiaoke.chatbi.integration.model.ch.ChartKnowledge;
import com.fxiaoke.chatbi.integration.repository.ch.ChartKnowledgeRepository;
import com.fxiaoke.chatbi.knowledge.retrieval.ChartRetriever;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 基于关键词的图表召回渠道
 * 区分维度、指标和筛选条件三类关键词，对应ChartKnowledge的三个关键字列
 */
@Slf4j
public class KeywordRecallChannel extends AbstractRecallChannel {

    private final ChartRetriever chartRetriever;

    /**
     * 关键词分类记录
     * 将用户意图中的关键词分为三类，对应图表知识的三个维度
     */
    public record KeywordCategories(
            List<String> dimensions,
            List<String> measures,
            List<String> filters
    ) {
        public boolean isEmpty() {
            return dimensions.isEmpty() && measures.isEmpty() && filters.isEmpty();
        }
        
        public int totalKeywordsCount() {
            return dimensions.size() + measures.size() + filters.size();
        }
    }

    /**
     * 构造函数
     *
     * @param chartKnowledgeRepository 图表向量存储库
     * @param chartRetriever           图表检索器
     */
    public KeywordRecallChannel(ChartKnowledgeRepository chartKnowledgeRepository,
                                ChartRetriever chartRetriever) {
        super(chartKnowledgeRepository, RecallMethod.KEYWORD);
        this.chartRetriever = chartRetriever;
    }

    @Override
    protected List<ChartKnowledge> doRecall(UserIntent intent, UserIdentity identity) {
        try {
            // 提取三类关键词
            KeywordCategories keywords = extractCategorizedKeywords(intent.getExtractedInfo());
            
            // 判断是否有可用的检索条件
            if (keywords.isEmpty()) {
                log.info("[{}] 未找到有效关键词，跳过召回", getChannelDescription());
                return Collections.emptyList();
            }

            log.info("[{}] 检索条件 - 维度关键词: {}, 指标关键词: {}, 筛选关键词: {}", 
                    getChannelDescription(), keywords.dimensions, keywords.measures, keywords.filters);

            List<ChartKnowledge> chartKnowledges = chartRetriever.searchByCategorizedKeywordsAndScores(keywords.dimensions,
                    keywords.measures,
                    keywords.filters,
                    identity,
                    getChannelType());
            if (CollectionUtils.isEmpty(chartKnowledges)) {
                log.info("[{}] 未找到匹配的图表", getChannelDescription());
                return Collections.emptyList();
            }

            log.info("[{}] 找到匹配的图表数量: {}", getChannelDescription(), chartKnowledges.size());

            // 获取完整的图表信息
            return chartKnowledges;
        } catch (Exception e) {
            log.error("[{}] 召回处理异常", getChannelDescription(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 提取分类关键词
     * 将关键词分为维度、指标和筛选条件三类
     */
    private KeywordCategories extractCategorizedKeywords(ExtractedInfo extractedInfo) {
        if (extractedInfo == null) {
            return new KeywordCategories(
                    Collections.emptyList(),
                    Collections.emptyList(),
                    Collections.emptyList()
            );
        }
        
        // 提取维度关键词
        List<String> dimensionKeywords = CollectionUtils.isEmpty(extractedInfo.getDimensions()) ?
                Collections.emptyList() : new ArrayList<>(extractedInfo.getDimensions());
        
        // 提取指标关键词
        List<String> measureKeywords = CollectionUtils.isEmpty(extractedInfo.getMeasures()) ?
                Collections.emptyList() : new ArrayList<>(extractedInfo.getMeasures());
        
        // 提取筛选条件关键词
        List<String> filterKeywords = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(extractedInfo.getFilters())) {
            // 只提取字段名作为筛选条件关键词
            extractedInfo.getFilters().stream()
                .filter(filter -> StringUtils.isNotBlank(filter.getField()))
                .map(FilterInfo::getField)
                .forEach(filterKeywords::add);
        }
        
        // 额外添加时间范围
        if (StringUtils.isNotBlank(extractedInfo.getTimeRange())) {
            filterKeywords.add(extractedInfo.getTimeRange());
        }
        
        return new KeywordCategories(dimensionKeywords, measureKeywords, filterKeywords);
    }
} 