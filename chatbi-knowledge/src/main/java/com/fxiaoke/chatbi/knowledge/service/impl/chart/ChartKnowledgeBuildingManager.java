package com.fxiaoke.chatbi.knowledge.service.impl.chart;

import com.facishare.bi.metadata.context.dto.ads.StatView;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.integration.model.ch.ChartKnowledge;
import com.fxiaoke.chatbi.integration.model.ch.KnowledgeEmbedding;
import com.fxiaoke.chatbi.integration.repository.ch.ChartKnowledgeRepository;
import com.fxiaoke.chatbi.integration.repository.ch.KnowledgeEmbeddingRepository;
import com.fxiaoke.chatbi.knowledge.building.chart.ChartKnowledgeBuilder;
import com.fxiaoke.chatbi.knowledge.building.chart.ChartKnowledgeFeatureExtractor;
import com.fxiaoke.chatbi.knowledge.building.core.BuildContext;
import com.fxiaoke.chatbi.knowledge.building.core.KnowledgeBuildResult;
import com.fxiaoke.chatbi.knowledge.building.core.KnowledgeVectorizer;
import com.fxiaoke.chatbi.knowledge.building.core.VectorizeContext;
import com.fxiaoke.common.Pair;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * 图表知识构建管理器
 * 协调和管理图表知识的构建、特征提取和向量化过程
 * 
 * 该类是拆分后实体模型的关键协调者：
 * 1. ChartKnowledge - 存储图表知识元数据（使用ChartKnowledgeRepository保存）
 * 2. KnowledgeEmbedding - 存储知识向量数据（使用KnowledgeEmbeddingRepository保存）
 * 
 * 主要职责：
 * - 协调将StatView转换为ChartKnowledge元数据
 * - 提取ChartKnowledge的特征文本
 * - 向量化特征文本生成KnowledgeEmbedding
 * - 将两种不同实体分别存储到对应的存储库中
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ChartKnowledgeBuildingManager {
    private final ChartKnowledgeBuilder knowledgeBuilder;
    private final ChartKnowledgeFeatureExtractor featureExtractor;
    private final KnowledgeVectorizer<ChartKnowledge> knowledgeVectorizer;
    private final ChartKnowledgeRepository chartKnowledgeRepository;
    private final KnowledgeEmbeddingRepository knowledgeEmbeddingRepository;

    /**
     * 处理单个图表的知识构建和向量化
     * 完整流程：StatView -> ChartKnowledge -> 特征提取 -> KnowledgeEmbedding
     *
     * @param userIdentity 用户身份
     * @param viewId       图表ID
     */
    public void processChart(UserIdentity userIdentity, String viewId) {
        log.info("Processing chart knowledge for viewId: {}, tenant: {}", viewId, userIdentity.getTenantId());
        
        // 1. 获取图表
        List<StatView> statViews = knowledgeBuilder.getStatViewList(userIdentity, viewId);
        if (CollectionUtils.isEmpty(statViews)) {
            log.warn("No StatView found for viewId: {}", viewId);
            return;
        }
        
        // 创建构建上下文
        BuildContext buildContext = BuildContext.builder()
                .userIdentity(userIdentity)
                .tenantId(userIdentity.getTenantId())
                .batchId(System.currentTimeMillis())
                .build();
        
        // 创建向量化上下文
        VectorizeContext vectorizeContext = VectorizeContext.builder()
                .tenantId(userIdentity.getTenantId())
                .batchId(buildContext.getBatchId())
                .sysFlag(buildContext.getSysFlag())
                .build();
        
        // 分别收集需要保存的ChartKnowledge和KnowledgeEmbedding实体
        List<ChartKnowledge> knowledgesToSave = new ArrayList<>();
        List<KnowledgeEmbedding> embeddingsToSave = new ArrayList<>();
        
        // 2. 处理每个图表
        for (StatView statView : statViews) {
            try {
                // 构建知识 - 从StatView到ChartKnowledge
                KnowledgeBuildResult<ChartKnowledge> buildResult = knowledgeBuilder.build(statView, buildContext);
                if (buildResult == null || buildResult.getKnowledge() == null) {
                    log.warn("Failed to build knowledge for viewId: {}", statView.getViewId());
                    continue;
                }
                
                ChartKnowledge knowledge = buildResult.getKnowledge();
                List<String> features = buildResult.getFeatures();
                knowledgesToSave.add(knowledge);
                
                // 提取特征 - 从ChartKnowledge提取特征文本
                features = featureExtractor.extract(knowledge, buildContext, features);
                if (CollectionUtils.isEmpty(features)) {
                    log.warn("No features extracted for viewId: {}", statView.getViewId());
                    continue;
                }
                
                // 向量化特征 - 将特征文本转换为KnowledgeEmbedding向量
                List<KnowledgeEmbedding> embeddings = knowledgeVectorizer.vectorize(knowledge, features, vectorizeContext);
                if (!CollectionUtils.isEmpty(embeddings)) {
                    embeddingsToSave.addAll(embeddings);
                }
            } catch (Exception e) {
                log.error("Error processing chart: {}", statView.getViewId(), e);
            }
        }
        
        // 3. 批量保存知识和向量 - 分别保存到不同的存储库
        if (CollectionUtils.isNotEmpty(knowledgesToSave)) {
            log.info("Saving {} chart knowledge entities to ChartKnowledge table", knowledgesToSave.size());
            chartKnowledgeRepository.saveChartKnowledgeList(knowledgesToSave, userIdentity.getTenantId());
        }
        
        if (CollectionUtils.isNotEmpty(embeddingsToSave)) {
            log.info("Saving {} knowledge embeddings to KnowledgeEmbedding table", embeddingsToSave.size());
            for (List<KnowledgeEmbedding> batch : Lists.partition(embeddingsToSave, 200)) {
                knowledgeEmbeddingRepository.saveKnowledgeEmbeddings(batch, userIdentity.getTenantId());
            }
        }
    }

    /**
     * 处理租户所有图表的知识构建和向量化
     *
     * @param userIdentity 用户身份
     */
    public void processAllCharts(UserIdentity userIdentity) {
        log.info("Processing all chart knowledge for tenant: {}", userIdentity.getTenantId());
        
        // 获取所有图表
        List<StatView> allStatViews = knowledgeBuilder.getStatViewList(userIdentity, null);
        if (CollectionUtils.isEmpty(allStatViews)) {
            log.warn("No charts found for tenant: {}", userIdentity.getTenantId());
            return;
        }

        log.info("Found {} charts to process for tenant: {}", allStatViews.size(), userIdentity.getTenantId());

        // 创建构建上下文
        BuildContext buildContext = BuildContext.builder()
                .userIdentity(userIdentity)
                .tenantId(userIdentity.getTenantId())
                .batchId(System.currentTimeMillis())
                .build();

        // 创建向量化上下文
        VectorizeContext vectorizeContext = VectorizeContext.builder()
                .tenantId(userIdentity.getTenantId())
                .batchId(buildContext.getBatchId())
                .sysFlag(buildContext.getSysFlag())
                .build();

        for (List<StatView> views : Lists.partition(allStatViews, 200)) {
            List<CompletableFuture<Pair<ChartKnowledge, List<KnowledgeEmbedding>>>> futures = new ArrayList<>(); // 用于存储所有的 CompletableFuture
            // 2. 处理每个图表
            for (StatView statView : views) {
                futures.add(CompletableFuture.supplyAsync(() -> {
                    try {
                        // 构建知识 - 从StatView到ChartKnowledge
                        KnowledgeBuildResult<ChartKnowledge> buildResult = knowledgeBuilder.build(statView, buildContext);
                        if (buildResult == null || buildResult.getKnowledge() == null) {
                            log.warn("Failed to build knowledge for viewId: {}", statView.getViewId());
                            return null;
                        }
                        ChartKnowledge knowledge = buildResult.getKnowledge();
                        List<String> features = buildResult.getFeatures();

                        // 提取特征 - 从ChartKnowledge提取特征文本
                        features = featureExtractor.extract(knowledge, buildContext, features);
                        if (CollectionUtils.isEmpty(features)) {
                            log.warn("No features extracted for viewId: {}", statView.getViewId());
                            return null;
                        }

                        // 向量化特征 - 将特征文本转换为KnowledgeEmbedding向量
                        List<KnowledgeEmbedding> embeddings = knowledgeVectorizer.vectorize(knowledge, features, vectorizeContext);
                        return Pair.build(knowledge, embeddings);
                    } catch (Exception e) {
                        log.error("Error processing chart: {}", statView.getViewId(), e);
                    }
                    return null;
                }));
            }
            // 等待所有的 CompletableFuture 完成，并过滤掉 null 值
            List<ChartKnowledge> knowledgesToSave = futures.stream()
                    .map(CompletableFuture::join) // 等待每个 CompletableFuture 完成
                    .filter(Objects::nonNull)
                    .map(Pair::getKey)
                    .filter(Objects::nonNull)
                    .toList();

            List<KnowledgeEmbedding> embeddingsToSave = futures.stream()
                    .map(CompletableFuture::join) // 等待每个 CompletableFuture 完成
                    .filter(Objects::nonNull)
                    .map(Pair::getValue)
                    .filter(Objects::nonNull)
                    .flatMap(List::stream)
                    .toList();
            // 3. 批量保存知识和向量 - 分别保存到不同的存储库
            if (CollectionUtils.isNotEmpty(knowledgesToSave)) {
                log.info("Saving {} chart knowledge entities to ChartKnowledge table", knowledgesToSave.size());
                chartKnowledgeRepository.saveChartKnowledgeList(knowledgesToSave, userIdentity.getTenantId());
            }

            if (CollectionUtils.isNotEmpty(embeddingsToSave)) {
                log.info("Saving {} knowledge embeddings to KnowledgeEmbedding table", embeddingsToSave.size());
                for (List<KnowledgeEmbedding> batch : Lists.partition(embeddingsToSave, 200)) {
                    knowledgeEmbeddingRepository.saveKnowledgeEmbeddings(batch, userIdentity.getTenantId());
                }
            }
        }
    }
}