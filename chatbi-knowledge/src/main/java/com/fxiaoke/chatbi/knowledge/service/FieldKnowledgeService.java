package com.fxiaoke.chatbi.knowledge.service;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.integration.model.ch.StatField;

import java.util.List;

/**
 * 字段知识服务接口
 * 定义字段知识的访问和管理方法
 */
public interface FieldKnowledgeService {
    /**
     * 构建字段知识向量
     *
     * @param userIdentity 用户身份
     */
    void buildFieldKnowledge(UserIdentity userIdentity);
    
    /**
     * 获取租户的所有知识字段
     *
     * @param tenantId 租户ID
     * @return 字段列表
     */
    List<StatField> listAllByTenant(String tenantId);
} 