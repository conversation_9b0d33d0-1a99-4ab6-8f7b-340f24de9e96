package com.fxiaoke.chatbi.knowledge.dictionary;

import java.util.List;
import java.util.Arrays;

/**
 * 同环比类型枚举
 * 用于描述数据分析中的同比、环比等比较类型
 * RS: 同期比较
 * RSM: 同比上月
 * RSW: 同比上周
 * RR: 环比
 */
public enum RatioTypeEnum {

  NULL(0, "无", "NULL", Arrays.asList("无")),
  
  // 年度相关比较
  RATIOSAMERATE(1, "与去年同比增长率", "S_R", Arrays.asList("同比增长率", "去年同期增长")),
  RATIOSAMEVAL(2, "与去年同比增长值", "S_V", Arrays.asList("同比增长值", "较去年同期增加")),
  RATIOSAMETERMYEAR(9, "去年同期", "TY_V", Arrays.asList("去年同期值")),

  // 月度相关比较
  RATIOSAMEMONRATE(3, "同比上月增长率", "SM_R", Arrays.asList("环比增长率", "上月增长")),
  RATIOSAMEMONVAL(4, "同比上月增长值", "SM_V", Arrays.asList("环比增长值", "较上月增加")),
  RATIOSAMETERMMONTH(10, "上月同期", "TM_V", Arrays.asList("上月值")),

  // 周相关比较
  RATIOSAMEWEKRATE(5, "同比上周增长率", "SW_R", Arrays.asList("周环比增长率", "上周增长")),
  RATIOSAMEWEKVAL(6, "同比上周增长值", "SW_V", Arrays.asList("周环比增长值", "较上周增加")),
  RATIONSAMETERMWEEK(11, "上周同期", "TW_V", Arrays.asList("上周值")),

  // 环比相关
  RATIORINGRATE(7, "环比增长率", "R_R", Arrays.asList("环比增长率", "上期增长")),
  RATIORINGVAL(8, "环比增长值", "R_V", Arrays.asList("环比增长值", "较上期增加")),
  RATIONRINGLASTPERIOD(20, "上期值", "RP_V", Arrays.asList("上期值"));

  private final int index;
  private final String desc;
  private final String code;
  private final List<String> expressions;

  RatioTypeEnum(int index, String desc, String code, List<String> expressions) {
    this.index = index;
    this.desc = desc;
    this.code = code;
    this.expressions = expressions;
  }

  /**
   * 获取指标描述
   */
  public String getDesc() {
    return desc;
  }

  /**
   * 获取指标代码
   */
  public String getCode() {
    return code;
  }

  /**
   * 获取指标索引
   */
  public int getIndex() {
    return index;
  }

  public List<String> getExpressions() {
    return expressions;
  }
}
