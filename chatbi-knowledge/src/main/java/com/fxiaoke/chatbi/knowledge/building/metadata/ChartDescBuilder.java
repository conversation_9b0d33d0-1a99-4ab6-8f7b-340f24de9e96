package com.fxiaoke.chatbi.knowledge.building.metadata;

import com.facishare.bi.metadata.context.dto.ads.StatView;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 图表描述构建器
 * 负责将StatView转换为ChartDesc
 */
@Component
public class ChartDescBuilder {

    /**
     * 转换图表列表
     *
     * @param views StatView列表
     * @return ChartDesc列表
     */
    public List<ChartDesc> convertViews(List<StatView> views) {
        if (views == null || views.isEmpty()) {
            return new ArrayList<>();
        }

        return views.stream()
                .filter(view -> view.getIsDelete() != 1)
                .<ChartDesc>map(view -> ChartDesc.builder()
                        .viewId(view.getViewId())
                        .name(view.getViewName())
                        .chartType(view.getChartType())
                        .schemaId(view.getSchemaId())
                        .description(view.getDescription())
                        .dimensions(convertViewDimensions(view))
                        .measures(convertViewMeasures(view))
                        .filters(convertViewFilters(view))
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 转换图表维度
     *
     * @param view StatView
     * @return 维度字段描述列表
     */
    private List<ViewFieldDesc> convertViewDimensions(StatView view) {
        return view.normalDimensionFields().stream().filter(field -> Objects.nonNull(field.getFieldName()) && !field.getFieldName().contains("BI_"))
                .map(field -> ViewFieldDesc.builder()
                        .fieldId(field.getFieldId())
                        .orderType(convertOrderType(field.getOrderType()))
                        .fieldName(field.getFieldName())
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 转换图表指标
     *
     * @param view StatView
     * @return 指标字段描述列表
     */
    private List<ViewFieldDesc> convertViewMeasures(StatView view) {
        return view.measureFields().stream().filter(field -> Objects.nonNull(field.getFieldName()) && !field.getFieldName().contains("BI_"))
                .map(field -> ViewFieldDesc.builder()
                        .fieldId(field.getFieldId())
                        .orderType(convertOrderType(field.getOrderType()))
                        .aggregator(field.getAggrType())
                        .fieldName(field.getFieldName())
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 转换图表过滤条件
     *
     * @param view StatView
     * @return 过滤条件描述列表
     */
    private List<ViewFilterDesc> convertViewFilters(StatView view) {
        return view.getViewFilters().stream().filter(filter -> Objects.nonNull(filter.getFieldName()) && !filter.getFieldName().contains("BI_"))
                .map(filter -> ViewFilterDesc.builder()
                        .fieldId(filter.getFieldId())
                        .operator(String.valueOf(filter.getOperator()))
                        .dateRangeId(filter.getDateRangeId())
                        .fieldName(filter.getFieldName())
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 转换排序类型
     *
     * @param orderType 排序类型编码
     * @return 排序类型描述
     */
    private String convertOrderType(int orderType) {
        switch (orderType) {
            case 1:
                return "升序";
            case 2:
                return "降序";
            default:
                return "不排序";
        }
    }
} 