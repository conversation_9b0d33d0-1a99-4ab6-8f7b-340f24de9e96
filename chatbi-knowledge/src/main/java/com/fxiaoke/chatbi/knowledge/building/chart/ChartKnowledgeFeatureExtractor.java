package com.fxiaoke.chatbi.knowledge.building.chart;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.action.ActionContext;
import com.fxiaoke.chatbi.integration.client.llm.LlmClient;
import com.fxiaoke.chatbi.integration.exception.LlmServiceException;
import com.fxiaoke.chatbi.integration.model.ch.ChartKnowledge;
import com.fxiaoke.chatbi.knowledge.building.core.BuildContext;
import com.fxiaoke.chatbi.knowledge.building.core.FeatureExtractor;
import com.fxiaoke.chatbi.prompts.PromptTemplateService;
import com.fxiaoke.chatbi.common.model.prompts.PromptTemplateType;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 图表知识特征提取器
 * 负责从图表知识中提取用于向量化的特征
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChartKnowledgeFeatureExtractor implements FeatureExtractor<ChartKnowledge> {
    private final LlmClient llmClient;
    private final PromptTemplateService promptTemplateService;

    @Override
    public List<String> extract(ChartKnowledge knowledge, BuildContext context, List<String> features) {
        if (knowledge == null) {
            log.warn("Cannot extract features from null knowledge");
            return Lists.newArrayList();
        }
        // 2. 尝试使用LLM生成更丰富的特征
        try {
            // 如果spec不为空，通过LLM生成问题作为特征
            if (knowledge.getSpec() != null) {
                List<String> generatedFeatures = generateIndustryQuestions(knowledge.getSpec(), context.getUserIdentity());
                if (!CollectionUtils.isEmpty(generatedFeatures)) {
                    features.addAll(generatedFeatures);
                }
            }
        } catch (Exception e) {
            log.error("Failed to generate features using LLM for viewId: {}", knowledge.getViewId(), e);
        }
        
        // 3. 从维度、指标和筛选条件名称中提取特征
        if (!CollectionUtils.isEmpty(knowledge.getDimensionNames())) {
            features.addAll(knowledge.getDimensionNames());
        }
        
        if (!CollectionUtils.isEmpty(knowledge.getMeasureNames())) {
            features.addAll(knowledge.getMeasureNames());
        }
        
        if (!CollectionUtils.isEmpty(knowledge.getFilterNames())) {
            features.addAll(knowledge.getFilterNames());
        }
        features.removeIf(StringUtils::isEmpty);
        return features;
    }

    /**
     * 生成行业相关图表问题作为特征
     *
     * @param spec         图表定义JSON
     * @param userIdentity 用户身份
     * @return 生成的特征列表
     */
    public List<String> generateIndustryQuestions(String spec, UserIdentity userIdentity) {
        try {
            ActionContext actionContext = new ActionContext();
            actionContext.setUserIdentity(userIdentity);
            
            // 获取提示词模板
            String templateContent = promptTemplateService.getTemplate(PromptTemplateType.CHART_QUESTION_GEN);
            
            // 准备模板变量
            Map<String, Object> variables = new HashMap<>();
            variables.put("spec", spec);
            variables.put("limit", 7); // 限制生成的问题数量
            
            // 调用大模型生成问题
            String generatedQuestions = llmClient.chatWithTemplate(templateContent, variables, actionContext);
            
            // 解析响应
            return List.of(generatedQuestions.split("\n"));
        } catch (LlmServiceException e) {
            log.error("Failed to generate features using LLM", e);
            return Lists.newArrayList();
        }
    }
}