package com.fxiaoke.chatbi.knowledge.service;

import com.fxiaoke.chatbi.common.model.UserIdentity;

/**
 * 知识建设服务接口
 * 定义各种知识类型的构建方法
 */
public interface KnowledgeBuildingService {
    
    /**
     * 构建所有类型的知识
     * 包括企业知识、字段知识、图表知识等
     * 
     * @param userIdentity 用户身份
     */
    void buildAllKnowledge(UserIdentity userIdentity);
    
    /**
     * 构建图表知识
     * 内部会调用ChartKnowledgeService
     * 
     * @param userIdentity 用户身份
     * @param viewId 图表ID
     */
    void buildChartKnowledge(UserIdentity userIdentity, String viewId);
    
    /**
     * 构建所有图表知识
     * 内部会调用ChartKnowledgeService
     * 
     * @param userIdentity 用户身份
     */
    void buildAllChartKnowledge(UserIdentity userIdentity);
    
    /**
     * 构建企业知识
     * 
     * @param userIdentity 用户身份
     */
    void buildEnterpriseKnowledge(UserIdentity userIdentity);
    
    /**
     * 构建字段知识（维度和指标）
     * 
     * @param userIdentity 用户身份
     */
    void buildFieldKnowledge(UserIdentity userIdentity);
} 