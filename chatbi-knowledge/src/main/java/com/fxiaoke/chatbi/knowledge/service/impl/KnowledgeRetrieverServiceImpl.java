package com.fxiaoke.chatbi.knowledge.service.impl;

import com.alibaba.fastjson.JSON;
import com.facishare.bi.metadata.context.dto.ads.StatView;
import com.facishare.bi.metadata.context.service.ads.IChartService;
import com.fxiaoke.chatbi.common.config.KnowledgeProperties;
import com.fxiaoke.chatbi.common.enums.StatChartTypeEnum;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.intent.ExtractedInfo;
import com.fxiaoke.chatbi.common.model.intent.KnowledgeScope;
import com.fxiaoke.chatbi.common.model.intent.UserIntent;
import com.fxiaoke.chatbi.common.model.reasoning.ReasoningCollector;
import com.fxiaoke.chatbi.common.utils.UserInfoConvertUtil;
import com.fxiaoke.chatbi.integration.model.ch.ChartKnowledge;
import com.fxiaoke.chatbi.knowledge.retrieval.ChartRankingService;
import com.fxiaoke.chatbi.knowledge.retrieval.MultiChannelRecallCoordinator;
import com.fxiaoke.chatbi.knowledge.retrieval.channel.RecallChannel;
import com.fxiaoke.chatbi.knowledge.retrieval.service.FilterValueFieldMatcher;
import com.fxiaoke.chatbi.knowledge.service.KnowledgeRetrievalService;
import com.fxiaoke.common.Pair;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 知识范围生成器
 * 负责根据提取的信息和维度值映射生成知识范围
 * 整合了图表召回功能
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class KnowledgeRetrieverServiceImpl implements KnowledgeRetrievalService {

  private final FilterValueFieldMatcher filterValueFieldMatcher;
  private final KnowledgeProperties knowledgeProperties;
  private final MultiChannelRecallCoordinator multiChannelRecallCoordinator;
  private final ChartRankingService chartRankingService;
  private final IChartService chartService;


  /**
   * 根据用户意图生成知识范围并检索相关图表
   *
   * @param userIntent         用户意图
   * @param userIdentity       用户身份信息
   * @param reasoningCollector 推理收集器
   * @return 知识范围（包含相关图表）
   */
  @Override
  public KnowledgeScope retrieveKnowledge(UserIntent userIntent,
                                          UserIdentity userIdentity,
                                          ReasoningCollector reasoningCollector) {

    try {
      log.info("开始检索知识，用户：{}, 意图：{}", userIdentity.getUserId(), userIntent.getInstructions());
      long startTime = System.currentTimeMillis();

      // 1. 字段映射和字段ID提取 - 新增步骤
      ExtractedInfo extractedInfo = userIntent.getExtractedInfo();
      
      Set<String> fieldIds = Collections.emptySet();
      if (extractedInfo != null && CollectionUtils.isNotEmpty(extractedInfo.getFilters())) {
        log.info("开始提取字段ID并映射，过滤条件数量: {}", extractedInfo.getFilters().size());
        
        // 进行字段映射和ID提取
        fieldIds = filterValueFieldMatcher.findFieldIdsByFilterValues(extractedInfo, userIdentity);
        
        // 设置筛选条件可能涉及到的字段ID
        extractedInfo.setFilterFieldIds(fieldIds);  
        // 记录映射结果
        log.info("根据筛选条件提取字段ID: {}个", fieldIds.size());
      }

      

      // 2. 执行多路召回
      Map<RecallChannel, List<ChartKnowledge>> channelResults =
          multiChannelRecallCoordinator.executeMultiChannelRecall(userIntent, userIdentity);

      // 如果没有任何渠道返回结果，则直接返回空结果
      if (channelResults.isEmpty()) {
        log.info("未找到任何匹配的图表");
        return createEmptyKnowledgeScope();
      }

      // 3. 使用提取的字段ID优化排序
      int resultLimit = knowledgeProperties.getChartResultLimit();
      List<Pair<String, String>> rankedViewIds = chartRankingService.rankCharts(
          channelResults, 
          new ArrayList<>(fieldIds), 
          resultLimit,userIdentity
      );

      if (rankedViewIds.isEmpty()) {
        log.info("排序后无有效图表结果");
        return createEmptyKnowledgeScope();
      }

      log.info("最终图表数量: {}", rankedViewIds.size());

      // 4. 生成知识内容
      KnowledgeScope knowledgeScope = new KnowledgeScope();
      knowledgeScope.setViewIds(rankedViewIds.stream().map(stringStringPair -> stringStringPair.first).collect(Collectors.toList()));

      Map<String, Integer> map = new HashMap<>();
      channelResults.forEach((key, value) -> {
          String channelName = key.getChannelName();
          int size = value.size();
          map.put(channelName, size);
      });

      Optional<ChartKnowledge> chartKnowledge = channelResults.entrySet().stream().flatMap(s -> s.getValue().stream()).filter(ss -> ss.getViewId().equalsIgnoreCase(knowledgeScope.getViewIds().get(0))).findAny();
      knowledgeScope.setChartInfoMap(map);
      if (chartKnowledge.isPresent()) {
        log.info("图表召回的最终图表信息：{}", JSON.toJSONString(chartKnowledge.get()));
        Map<String, String> chartKnowledgeMap = new HashMap<>();
        List<StatView> statViews = chartService.getViewListByViewIds(List.of(chartKnowledge.get().getViewId()), UserInfoConvertUtil.createUserInfo(userIdentity));
        if (CollectionUtils.isNotEmpty(statViews)) {
          chartKnowledgeMap.put("viewName", statViews.get(0).getViewName());
        }
        chartKnowledgeMap.put("chartType", StatChartTypeEnum.getName(chartKnowledge.get().getChartType()));
        chartKnowledgeMap.put("dimensions", String.join("、", chartKnowledge.get().getDimensionNames()));
        chartKnowledgeMap.put("measures", String.join("、", chartKnowledge.get().getMeasureNames()));
        chartKnowledgeMap.put("filters", String.join("、", chartKnowledge.get().getFilterNames()));
        knowledgeScope.setChartKnowledge(chartKnowledgeMap);
      }

      long endTime = System.currentTimeMillis();
      log.info("知识检索完成，总耗时: {}ms, 返回图表数: {}", (endTime - startTime), rankedViewIds.size());

      return knowledgeScope;
    } catch (Exception e) {
      log.error("检索知识过程发生异常", e);
      return createEmptyKnowledgeScope();
    }
  }

  /**
   * 创建空的知识范围对象
   */
  private KnowledgeScope createEmptyKnowledgeScope() {
    KnowledgeScope emptyScope = new KnowledgeScope();
    emptyScope.setViewIds(Collections.emptyList());
    return emptyScope;
  }
}