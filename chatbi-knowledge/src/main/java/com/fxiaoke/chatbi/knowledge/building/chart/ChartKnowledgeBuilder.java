package com.fxiaoke.chatbi.knowledge.building.chart;

import com.alibaba.fastjson.JSON;
import com.facishare.bi.metadata.context.dto.ads.StatView;
import com.facishare.bi.metadata.context.service.ads.IChartService;
import com.facishare.cep.plugin.model.UserInfo;
import com.fxiaoke.chatbi.common.config.KnowledgeProperties;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.utils.UserInfoConvertUtil;
import com.fxiaoke.chatbi.integration.dao.pg.BiMTTopologyTableMapper;
import com.fxiaoke.chatbi.integration.model.ch.ChartKnowledge;
import com.fxiaoke.chatbi.knowledge.building.core.BuildContext;
import com.fxiaoke.chatbi.knowledge.building.core.KnowledgeBuildResult;
import com.fxiaoke.chatbi.knowledge.building.core.KnowledgeBuilder;
import com.fxiaoke.chatbi.knowledge.building.metadata.ChartDesc;
import com.fxiaoke.chatbi.knowledge.building.metadata.ChartDescBuilder;
import com.fxiaoke.chatbi.knowledge.building.metadata.ViewFieldDesc;
import com.fxiaoke.chatbi.knowledge.building.metadata.ViewFilterDesc;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.common.Strings;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 图表知识构建器
 * 实现从StatView到ChartKnowledge的转换
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ChartKnowledgeBuilder implements KnowledgeBuilder<StatView, ChartKnowledge> {
    private static final Integer SYS_TENANT_ID = -1;

    private final IChartService chartService;
    private final ChartDescBuilder chartDescBuilder;
    private final BiMTTopologyTableMapper biMTTopologyTableMapper;
    private final KnowledgeProperties knowledgeProperties;

    @Override
    public KnowledgeBuildResult<ChartKnowledge> build(StatView statView, BuildContext context) {
        if (statView == null) {
            log.warn("StatView is null");
            return null;
        }

        ChartDesc chartDesc = getChartDesc(statView);
        if (chartDesc == null) {
            log.warn("ChartDesc is null for viewId: {}", statView.getViewId());
            return null;
        }

        ChartKnowledge chartKnowledge = buildChartKnowledge(statView, chartDesc, context);
        
        // 图表名称作为基础特征
        List<String> features = new ArrayList<>();
        features.add(statView.getViewName());

        return KnowledgeBuildResult.<ChartKnowledge>builder()
                .knowledge(chartKnowledge)
                .features(features)
                .build();
    }

    /**
     * 根据视图ID获取所有符合条件的StatView列表
     *
     * @param userIdentity 用户身份
     * @param viewId       视图ID，可选
     * @return StatView列表
     */
    public List<StatView> getStatViewList(UserIdentity userIdentity, String viewId) {
        UserInfo userInfo = UserInfoConvertUtil.createUserInfo(userIdentity);
        List<StatView> statViews = chartService.getViewList(userInfo);
        
        statViews = statViews.stream()
                .filter(it -> Objects.equals(it.getEi(), Integer.parseInt(userIdentity.getTenantId())))
                .filter(it -> Objects.nonNull(it.getChartType()) &&
                        knowledgeProperties.getSupportChartTypeList().contains(it.getChartType()))
                .toList();
        
        if (userInfo.getEnterpriseId() == 1) {
            List<String> normalStatusSourceId = biMTTopologyTableMapper.setTenantId(userIdentity.getTenantId())
                    .getNormalStatusSourceId(userIdentity.getTenantId());
            statViews = statViews.stream().filter(it -> normalStatusSourceId.contains(it.getViewId())).toList();
        }
        if(Objects.equals(userInfo.getEnterpriseId(), SYS_TENANT_ID)) {
            statViews  = statViews.stream().filter(it -> knowledgeProperties.getSysSupportChartIdList().contains(it.getViewId())).toList();
        }

        if (!Strings.isNullOrEmpty(viewId)) {
            statViews = statViews.stream().filter(it -> Objects.equals(it.getViewId(), viewId)).toList();
        }
        
        return statViews;
    }

    /**
     * 获取图表描述信息
     *
     * @param statView 统计视图
     * @return 图表描述
     */
    private ChartDesc getChartDesc(StatView statView) {
        List<StatView> statViews = new ArrayList<>();
        statViews.add(statView);
        List<ChartDesc> chartDescList = chartDescBuilder.convertViews(statViews);
        
        if (CollectionUtils.isEmpty(chartDescList)) {
            return null;
        }
        
        return chartDescList.get(0);
    }

    /**
     * 构建图表知识
     *
     * @param statView  统计视图
     * @param chartDesc 图表描述
     * @param context   构建上下文
     * @return 图表知识
     */
    private ChartKnowledge buildChartKnowledge(StatView statView, ChartDesc chartDesc, BuildContext context) {
        // 直接从图表描述中提取维度、指标和筛选条件
        List<String> dimensionNames = chartDesc.getDimensions().stream()
                .map(ViewFieldDesc::getFieldName)
                .filter(Objects::nonNull)
                .toList();
        
        List<String> measureNames = chartDesc.getMeasures().stream()
                .map(ViewFieldDesc::getFieldName)
                .filter(Objects::nonNull)
                .toList();
        
        List<String> filterNames = chartDesc.getFilters().stream()
                .filter(filter -> !Objects.equals(filter.getFieldId(), "0"))
                .map(ViewFilterDesc::getFieldName)
                .filter(Objects::nonNull)
                .toList();
        
        return ChartKnowledge.builder()
                .viewId(statView.getViewId())
                .viewName(statView.getViewName())
                .tenantId(String.valueOf(statView.getEi()))
                .chartType(statView.getChartType())
                .schemaId(statView.getSchemaId())
                .spec(JSON.toJSONString(chartDesc))
                .dimensionNames(dimensionNames)
                .measureNames(measureNames)
                .filterNames(filterNames)
                .fieldIds(buildFieldIds(chartDesc))
                .lastModifiedTime(Objects.nonNull(statView.getUpdateTime()) ? statView.getUpdateTime().getTime() : 0)
                .build();
    }

    /**
     * 构建字段ID列表
     *
     * @param chartDesc 图表描述
     * @return 字段ID列表
     */
    private List<String> buildFieldIds(ChartDesc chartDesc) {
        List<String> fieldIds = Lists.newArrayList();

        // 添加维度的字段ID
        fieldIds.addAll(chartDesc.getDimensions()
                .stream()
                .map(ViewFieldDesc::getFieldId)
                .filter(Objects::nonNull)
                .toList());

        // 添加指标的字段ID
        fieldIds.addAll(chartDesc.getMeasures()
                .stream()
                .map(ViewFieldDesc::getFieldId)
                .filter(Objects::nonNull)
                .toList());

        // 添加筛选条件的字段ID
        fieldIds.addAll(chartDesc.getFilters()
                .stream()
                .map(ViewFilterDesc::getFieldId)
                .filter(id -> !Objects.equals(id, "0"))
                .filter(Objects::nonNull)
                .toList());

        return fieldIds.stream().distinct().toList();
    }
} 