package com.fxiaoke.chatbi.knowledge.embedding.core.impl;

import com.alibaba.fastjson.JSON;
import com.facishare.ai.api.dto.BaseResult;
import com.facishare.ai.api.dto.OpenAIEmbeddings;
import com.fxiaoke.chatbi.common.config.EmbeddingProperties;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.integration.client.llm.PaasAiClient;
import com.fxiaoke.chatbi.integration.utils.HttpHeaderBuilder;
import com.fxiaoke.chatbi.knowledge.embedding.core.TextVectorizer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 基于PaasAiClient的文本向量化实现
 * 使用公司内部AI平台提供的向量化服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PaasAiTextVectorizerImpl implements TextVectorizer {

  private final PaasAiClient paasAiClient;
  private final EmbeddingProperties embeddingProperties;

  @Override
  public float[] vectorize(String text, UserIdentity userIdentity) {
    try {
      if (text == null || text.isEmpty()) {
        log.warn("尝试向量化空文本");
        return new float[0];
      }

      // 准备调用参数
      OpenAIEmbeddings.Arg arg = new OpenAIEmbeddings.Arg();
      arg.setInput(text);
      arg.setModel(embeddingProperties.getEmbeddingModel());

      // 调用PaasAiClient获取文本向量
      BaseResult baseResult = paasAiClient.embeddings(arg, HttpHeaderBuilder.constructHttpHeader(userIdentity));

      // 转换List<Float>为float[]
      if (Objects.nonNull(baseResult) && baseResult.getErrCode() == 0) {
        OpenAIEmbeddings.Result result = JSON.parseObject(String.valueOf(baseResult.getResult()), OpenAIEmbeddings.Result.class);
        float[] vector = convertToFloatArray(result.getEmbedding());

        log.debug("成功向量化文本，长度: {}, 文本前20字符: {}", vector.length,
          text.length() > 20 ? text.substring(0, 20) + "..." : text);

        return vector;
      } else {
        log.warn("向量化返回结果为空");
        return new float[0];
      }
    } catch (Exception e) {
      log.error("文本向量化失败: {}", text, e);
      throw new RuntimeException("文本向量化失败", e);
    }
  }

  /**
   * 将List<Float>转换为float[]
   */
  private float[] convertToFloatArray(List<Float> list) {
    if (list == null) {
      return new float[0];
    }

    float[] array = new float[list.size()];
    for (int i = 0; i < list.size(); i++) {
      array[i] = list.get(i);
    }
    return array;
  }
} 