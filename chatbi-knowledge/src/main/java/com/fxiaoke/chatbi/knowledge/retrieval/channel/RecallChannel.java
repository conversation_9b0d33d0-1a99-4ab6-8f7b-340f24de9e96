package com.fxiaoke.chatbi.knowledge.retrieval.channel;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.intent.UserIntent;
import com.fxiaoke.chatbi.common.model.knowledge.ChannelType;
import com.fxiaoke.chatbi.integration.model.ch.ChartKnowledge;

import java.util.List;
/**
 * 召回渠道接口
 * 定义单一召回渠道的行为，每个具体渠道实现此接口
 */
public interface RecallChannel {

  /**
   * 执行图表召回
   *
   * @param intent   用户意图
   * @param identity 用户身份
   * @return 召回的图表列表
   */
  List<ChartKnowledge> recall(UserIntent intent, UserIdentity identity);



  /**
   * 获取渠道名称
   * 用于日志记录和监控
   *
   * @return 渠道名称
   */
  default String getChannelName() {
    return getChannelType().name() + "-" + getRecallMethod().name();
  }

  /**
   * 获取渠道类型
   * 用于区分租户库和系统库
   *
   * @return 渠道类型
   */
  ChannelType getChannelType();

  /**
   * 获取召回方法类型
   * 用于区分不同的召回方式（字段ID/关键词/向量）
   *
   * @return 召回方法类型
   */
  RecallMethod getRecallMethod();

}