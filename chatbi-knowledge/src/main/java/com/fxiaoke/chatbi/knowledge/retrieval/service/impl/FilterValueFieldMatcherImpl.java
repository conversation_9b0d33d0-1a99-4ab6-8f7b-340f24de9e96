package com.fxiaoke.chatbi.knowledge.retrieval.service.impl;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.intent.ExtractedInfo;
import com.fxiaoke.chatbi.common.model.intent.FilterInfo;
import com.fxiaoke.chatbi.integration.model.ch.DimensionValueFull;
import com.fxiaoke.chatbi.integration.repository.ch.DimensionValueRepository;
import com.fxiaoke.chatbi.knowledge.retrieval.service.FilterValueFieldMatcher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 维度值解析服务实现
 * 根据过滤条件中的字符串类型值查找对应的字段ID
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FilterValueFieldMatcherImpl implements FilterValueFieldMatcher {

    private final DimensionValueRepository dimensionValueRepository;


    /**
     * 从提取信息中查找维度字段ID
     * 仅处理STRING类型的过滤条件值，排除NUMBER和DATE类型
     */
    @Override
    public Set<String> findFieldIdsByFilterValues(ExtractedInfo extractedInfo, UserIdentity userIdentity) {
        if (extractedInfo == null || userIdentity == null || CollectionUtils.isEmpty(extractedInfo.getFilters())) {
            return Collections.emptySet();
        }

        try {
            String tenantId = userIdentity.getTenantId();

            // 从过滤条件中提取字符串类型值，排除NUMBER和DATE类型
            Set<String> stringValueKeywords = extractedInfo.getFilters().stream().filter(f -> !isNumberOrDateFilter(f)) // 排除NUMBER和DATE类型
                    .filter(f -> CollectionUtils.isNotEmpty(f.getValues())).flatMap(f -> f.getValues().stream()).filter(StringUtils::isNotBlank).collect(Collectors.toSet());

            if (stringValueKeywords.isEmpty()) {
                return Collections.emptySet();
            }

            // 根据字符串值关键词查询维度字段ID
            return dimensionValueRepository.searchFieldIdsByKeywords(new ArrayList<>(stringValueKeywords), tenantId);
        } catch (Exception e) {
            log.error("维度字段ID查找失败", e);
            return Collections.emptySet();
        }
    }

    /**
     * 根据多个字段值批量查找匹配的字段ID和置信度
     */
    @Override
    public Map<String, Float> matchFieldsByFilterValues(List<String> fieldValues, List<String> candidateIds, UserIdentity userIdentity) {
        // 验证输入参数
        if (CollectionUtils.isEmpty(fieldValues) || userIdentity == null) {
            return Collections.emptyMap();
        }

        if (CollectionUtils.isEmpty(candidateIds)) {
            log.info("候选字段ID列表为空，无法进行匹配");
            return Collections.emptyMap();
        }

        // 过滤空值
        List<String> validValues = fieldValues.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());

        if (validValues.isEmpty()) {
            return Collections.emptyMap();
        }

        String tenantId = userIdentity.getTenantId();
        Map<String, Float> fieldIdToConfidenceMap = new HashMap<>();

        try {
            // 批量查询包含指定值的字段
            List<DimensionValueFull> matchedDimensionValues = dimensionValueRepository.exactMatchByValues(validValues, tenantId);

            if (CollectionUtils.isEmpty(matchedDimensionValues)) {
                log.info("没有找到匹配的维度值记录: values={}, tenantId={}", validValues, tenantId);
                return Collections.emptyMap();
            }

            // 过滤候选字段ID
            List<DimensionValueFull> filteredValues = matchedDimensionValues.stream().filter(dv -> candidateIds.contains(dv.getDimensionId())).toList();

            if (filteredValues.isEmpty()) {
                log.info("匹配的字段不在候选集合中: candidateIds.size={}", candidateIds.size());
                return Collections.emptyMap();
            }

            // 计算每个字段的置信度
            // 使用权重进行加权，如果没有权重则使用默认值0.8
            for (DimensionValueFull dv : filteredValues) {
                String fieldId = dv.getFieldId();
                float weight = dv.getWeight() != null ? dv.getWeight() : 0.0f;
                float confidence = 0.8f + Math.min(0.2f, weight / 10.0f); // 权重最高贡献0.2的置信度

                // 取最高置信度
                if (fieldIdToConfidenceMap.containsKey(fieldId)) {
                    float existingConfidence = fieldIdToConfidenceMap.get(fieldId);
                    if (confidence > existingConfidence) {
                        fieldIdToConfidenceMap.put(fieldId, confidence);
                    }
                } else {
                    fieldIdToConfidenceMap.put(fieldId, confidence);
                }
            }

            log.info("值匹配成功: 匹配了{}个字段", fieldIdToConfidenceMap.size());
            return fieldIdToConfidenceMap;
        } catch (Exception e) {
            log.error("值匹配字段查询异常: ", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 判断是否为数字或日期类型的过滤条件
     */
    private boolean isNumberOrDateFilter(FilterInfo filter) {
        String fieldType = filter.getFieldType();
        return "NUMBER".equals(fieldType) || "DATE".equals(fieldType);
    }
}