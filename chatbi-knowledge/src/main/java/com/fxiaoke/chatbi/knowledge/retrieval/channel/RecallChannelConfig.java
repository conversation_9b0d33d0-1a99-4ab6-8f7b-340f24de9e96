package com.fxiaoke.chatbi.knowledge.retrieval.channel;

import com.fxiaoke.chatbi.common.model.knowledge.ChannelType;
import com.fxiaoke.chatbi.integration.repository.ch.ChartKnowledgeRepository;
import com.fxiaoke.chatbi.knowledge.retrieval.ChartRetriever;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 召回渠道配置类
 * 为每种召回方式创建租户库和系统库两个实例
 */
@Configuration
public class RecallChannelConfig {
    private final ChartKnowledgeRepository repository;
    private final ChartRetriever retriever;

    public RecallChannelConfig(ChartKnowledgeRepository repository,
                             ChartRetriever retriever) {
        this.repository = repository;
        this.retriever = retriever;
    }

    /**
     * 创建FieldId召回渠道
     */
    private FieldIdRecallChannel createFieldIdChannel(ChannelType channelType) {
        FieldIdRecallChannel channel = new FieldIdRecallChannel(repository, retriever);
        channel.setChannelType(channelType);
        return channel;
    }

    /**
     * 创建Keyword召回渠道
     */
    private KeywordRecallChannel createKeywordChannel(ChannelType channelType) {
        KeywordRecallChannel channel = new KeywordRecallChannel(repository, retriever);
        channel.setChannelType(channelType);
        return channel;
    }

    /**
     * 创建Vector召回渠道
     */
    private VectorRecallChannel createVectorChannel(ChannelType channelType) {
        VectorRecallChannel channel = new VectorRecallChannel(repository, retriever);
        channel.setChannelType(channelType);
        return channel;
    }

    /**
     * 租户库字段ID召回渠道
     */
    @Bean
    @Primary
    public FieldIdRecallChannel tenantFieldIdRecallChannel() {
        return createFieldIdChannel(ChannelType.TENANT);
    }

    /**
     * 系统库字段ID召回渠道
     */
    @Bean
    public FieldIdRecallChannel systemFieldIdRecallChannel() {
        return createFieldIdChannel(ChannelType.SYSTEM);
    }

    /**
     * 租户库关键词召回渠道
     */
    @Bean
    @Primary
    public KeywordRecallChannel tenantKeywordRecallChannel() {
        return createKeywordChannel(ChannelType.TENANT);
    }

    /**
     * 系统库关键词召回渠道
     */
    @Bean
    public KeywordRecallChannel systemKeywordRecallChannel() {
        return createKeywordChannel(ChannelType.SYSTEM);
    }

    /**
     * 租户库向量召回渠道
     */
    @Bean
    @Primary
    public VectorRecallChannel tenantVectorRecallChannel() {
        return createVectorChannel(ChannelType.TENANT);
    }

    /**
     * 系统库向量召回渠道
     */
    @Bean
    public VectorRecallChannel systemVectorRecallChannel() {
        return createVectorChannel(ChannelType.SYSTEM);
    }
} 