package com.fxiaoke.chatbi.knowledge.service.impl;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.knowledge.service.ChartKnowledgeBuildingService;
import com.fxiaoke.chatbi.knowledge.service.EnterpriseKnowledgeService;
import com.fxiaoke.chatbi.knowledge.service.FieldKnowledgeService;
import com.fxiaoke.chatbi.knowledge.service.KnowledgeBuildingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 知识建设服务实现
 * 高层聚合服务，协调各种具体的知识服务
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class KnowledgeBuildingServiceImpl implements KnowledgeBuildingService {
    // 图表知识服务
    private final ChartKnowledgeBuildingService chartKnowledgeBuildingService;
    
    // 企业知识服务
    private final EnterpriseKnowledgeService enterpriseKnowledgeService;
    
    // 字段知识服务
    private final FieldKnowledgeService fieldKnowledgeService;
    
    @Override
    public void buildAllKnowledge(UserIdentity userIdentity) {
        log.info("Building all knowledge types for tenant: {}", userIdentity.getTenantId());
        
        // 构建企业知识
        buildEnterpriseKnowledge(userIdentity);
        
        // 构建字段知识
        buildFieldKnowledge(userIdentity);
        
        // 构建图表知识
        buildAllChartKnowledge(userIdentity);
        
        log.info("Completed building all knowledge types for tenant: {}", userIdentity.getTenantId());
    }
    
    @Override
    public void buildChartKnowledge(UserIdentity userIdentity, String viewId) {
        // 委托给图表知识服务
        chartKnowledgeBuildingService.buildChartKnowledge(userIdentity, viewId);
    }
    
    @Override
    public void buildAllChartKnowledge(UserIdentity userIdentity) {
        // 委托给图表知识服务
        chartKnowledgeBuildingService.buildAllChartKnowledge(userIdentity);
    }
    
    @Override
    public void buildEnterpriseKnowledge(UserIdentity userIdentity) {
        log.info("Building enterprise knowledge for tenant: {}", userIdentity.getTenantId());
        enterpriseKnowledgeService.buildEnterpriseKnowledge(userIdentity);
    }
    
    @Override
    public void buildFieldKnowledge(UserIdentity userIdentity) {
        log.info("Building field knowledge for tenant: {}", userIdentity.getTenantId());
        fieldKnowledgeService.buildFieldKnowledge(userIdentity);
    }
} 