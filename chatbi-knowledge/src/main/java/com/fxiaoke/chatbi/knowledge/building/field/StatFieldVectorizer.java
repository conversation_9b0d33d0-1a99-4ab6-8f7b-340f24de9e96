package com.fxiaoke.chatbi.knowledge.building.field;

import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.integration.model.ch.KnowledgeEmbedding;
import com.fxiaoke.chatbi.integration.model.ch.StatField;
import com.fxiaoke.chatbi.knowledge.building.core.KnowledgeVectorizer;
import com.fxiaoke.chatbi.knowledge.building.core.VectorizeContext;
import com.fxiaoke.chatbi.knowledge.embedding.core.TextVectorizer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 维度指标向量化器
 * 负责处理维度和指标的向量化
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class StatFieldVectorizer implements KnowledgeVectorizer<StatField> {
    private static final String DIM_AGG_TYPE = "dim";
    private final TextVectorizer textVectorizer;

    @Override
    public List<KnowledgeEmbedding> vectorize(StatField statField, List<String> features, VectorizeContext context) {
        if (statField == null) {
            log.warn("Cannot vectorize null statField");
            return new ArrayList<>();
        }

        if (CollectionUtils.isEmpty(features)) {
            // 如果没有提供特征，使用字段名作为特征
            if (statField.getFieldName() != null) {
                features = List.of(statField.getFieldName());
            } else {
                log.warn("No features to vectorize for statField: {}", statField.getFieldId());
                return new ArrayList<>();
            }
        }

        log.info("Vectorizing StatField: {}, features size: {}", statField.getFieldId(), features.size());

        // 创建用户身份信息，用于向量化
        UserIdentity userIdentity = UserIdentity.builder()
                .tenantId(statField.getTenantId())
                .build();

        // 并行向量化所有特征
        List<CompletableFuture<KnowledgeEmbedding>> futures = new ArrayList<>();
        for (String feature : features) {
            futures.add(CompletableFuture.supplyAsync(() -> buildKnowledgeEmbedding(statField, feature, userIdentity, context)));
        }

        // 等待所有向量化任务完成，过滤掉可能的失败结果
        return futures.stream()
                .map(CompletableFuture::join)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 为单个特征构建知识向量
     *
     * @param statField    维度/指标字段
     * @param feature      特征文本
     * @param userIdentity 用户身份
     * @param context      向量化上下文
     * @return 知识向量
     */
    private KnowledgeEmbedding buildKnowledgeEmbedding(StatField statField, String feature, UserIdentity userIdentity, VectorizeContext context) {
        try {
            // 根据聚合类型确定是维度还是指标
            String knowledgeType = statField.getKnowledgeTypeEnum().getCode();

            return KnowledgeEmbedding.builder()
                    .knowledgeType(knowledgeType)
                    .knowledgeId(statField.getFieldId())
                    .tenantId(statField.getTenantId())
                    .feature(feature)
                    .embedding(textVectorizer.vectorize(feature, userIdentity))
                    .weight(1.0f)
                    .build();
        } catch (Exception e) {
            log.error("Failed to vectorize feature: {} for statField: {}", feature, statField.getFieldId(), e);
            return null;
        }
    }
} 