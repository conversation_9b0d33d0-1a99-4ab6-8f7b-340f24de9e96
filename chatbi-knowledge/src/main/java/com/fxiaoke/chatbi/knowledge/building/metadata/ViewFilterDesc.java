package com.fxiaoke.chatbi.knowledge.building.metadata;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ViewFilterDesc {
    /**
     * 字段ID
     */
    private String fieldId;

    /**
     * 过滤操作符：EQ、GT、LT等
     */
    private String operator;

    /**
     * 日期范围ID（仅日期类过滤器使用）
     */
    private String dateRangeId;

    /**
     * 字段名称
     */
    private String fieldName;
}
