package com.fxiaoke.chatbi.knowledge.building.chart;

import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.chatbi.common.model.UserIdentity;
import com.fxiaoke.chatbi.common.model.enums.KnowledgeType;
import com.fxiaoke.chatbi.integration.model.ch.ChartKnowledge;
import com.fxiaoke.chatbi.integration.model.ch.KnowledgeEmbedding;
import com.fxiaoke.chatbi.knowledge.building.core.KnowledgeVectorizer;
import com.fxiaoke.chatbi.knowledge.building.core.VectorizeContext;
import com.fxiaoke.chatbi.knowledge.embedding.core.TextVectorizer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 图表知识向量化器
 * 负责将图表知识转化为向量表示
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChartKnowledgeVectorizer implements KnowledgeVectorizer<ChartKnowledge> {
    private final TextVectorizer textVectorizer;

    @Override
    public List<KnowledgeEmbedding> vectorize(ChartKnowledge knowledge, List<String> features, VectorizeContext context) {
        if (knowledge == null) {
            log.warn("Cannot vectorize null knowledge");
            return new ArrayList<>();
        }

        if (CollectionUtils.isEmpty(features)) {
            log.warn("No features to vectorize for knowledge: {}", knowledge.getViewId());
            return new ArrayList<>();
        }

        log.info("Vectorizing ChartKnowledge: {}, features size: {}", knowledge.getViewId(), features.size());

        // 创建用户身份信息，用于向量化
        UserIdentity userIdentity = UserIdentity.builder()
                .tenantId(knowledge.getTenantId())
                .build();

        // 并行向量化所有特征
        List<CompletableFuture<KnowledgeEmbedding>> futures = new ArrayList<>();
        for (String feature : features) {
            futures.add(CompletableFuture.supplyAsync(() -> buildKnowledgeEmbedding(knowledge, feature, userIdentity, context)));
        }

        // 等待所有向量化任务完成，过滤掉可能的失败结果
        return futures.stream()
                .map(CompletableFuture::join)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 为单个特征构建知识向量
     *
     * @param knowledge    图表知识
     * @param feature      特征文本
     * @param userIdentity 用户身份
     * @param context      向量化上下文
     * @return 知识向量
     */
    private KnowledgeEmbedding buildKnowledgeEmbedding(ChartKnowledge knowledge, String feature, UserIdentity userIdentity, VectorizeContext context) {
        try {
            return KnowledgeEmbedding.builder()
                    .id(IdGenerator.get())
                    .knowledgeType(KnowledgeType.CHART.getCode())
                    .knowledgeId(knowledge.getViewId())
                    .tenantId(knowledge.getTenantId())
                    .feature(feature)
                    .embedding(textVectorizer.vectorize(feature, userIdentity))
                    .weight(1.0f)
                    .build();
        } catch (Exception e) {
            log.error("Failed to vectorize feature: {} for knowledge: {}", feature, knowledge.getViewId(), e);
            return null;
        }
    }
} 