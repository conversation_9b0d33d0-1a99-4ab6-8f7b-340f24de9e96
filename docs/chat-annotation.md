# ChatBI 对话标注平台 - 精简版

## 📱 界面设计

```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│  ChatBI 对话标注平台 - 精简版                                                          │
├──────────────┬─────────────────────────────────┬─────────────────────────────────────┤
│              │                                 │                                     │
│   搜索&列表   │          对话展示区              │        快速标注区                    │
│              │                                 │                                     │
│  🔍 关键词搜索  │  📍 Session #S001 (3轮对话)       │   📋 当前标注: 第1轮                 │
│  🏢 租户: 全部▼│                                 │                                     │
│  👤 用户ID搜索 │                                 │                                     │
│  📅 [今日]▼   │                                 │                                     │
│  🏷️ [待标注]▼  │                                 │                                     │
│              │  💬 用户: 今天的销售数据怎么样？    │   ┌─ 📖 推理过程 ─────────────┐      │
│   Session    │                                 │   │ ⭐⭐⭐ [准确] [清晰] [+]    │      │
│  ● S001 (3轮)│  🤖 AI回答 (第1轮) [★选中]        │   │ 💬 ___________________   │      │
│  ○ S002 (2轮)│  📖 基于查询分析销售数据...         │   └─────────────────────────┘      │
│  ○ S003 (5轮)│  📊 [销售趋势图]                  │                                     │
│              │  💡 销售额呈上升趋势...            │   ┌─ 📊 图表组件 ─────────────┐      │
│   轮次       │  ✅ 建议分析各渠道数据...           │   │ ⭐⭐⭐⭐ [准确] [美观] [+]  │      │
│  ● 第1轮     │  🔧 执行日志 [6.2s] ✓              │   │ 💬 ___________________   │      │
│  ○ 第2轮     │                                 │   └─────────────────────────┘      │
│  ○ 第3轮     │  💬 用户: 华东区数据详情 (第2轮)    │                                     │
│              │  🤖 AI回答 (第2轮) [收起]          │   ┌─ 💡 图表解读 ─────────────┐      │
│              │                                 │   │ ⭐⭐ [深度不够] [需改进] [+] │      │
│              │                                 │   │ 💬 ___________________   │      │
│              │                                 │   └─────────────────────────┘      │
│              │                                 │                                     │
│              │                                 │   ┌─ ✅ 追问建议 ─────────────┐      │
│              │                                 │   │ ⭐⭐⭐⭐⭐ [实用] [相关] [+] │      │
│              │                                 │   │ 💬 ___________________   │      │
│              │                                 │   └─────────────────────────┘      │
│              │                                 │                                     │
│              │                                 │   ┌─ ✅ 追问建议 ─────────────┐      │
│              │                                 │   │ ⭐⭐⭐⭐⭐ [实用] [相关] [+] │      │
│              │                                 │   │ 💬 ___________________   │      │
│              │                                 │   └─────────────────────────┘      │
│              │                                 │                                     │
│              │                                 │   📊 第1轮整体评分                   │
│              │                                 │   ⭐⭐⭐⭐ [回答质量] [用户体验] [+]  │
│              │                                 │   💬 整体评价: ___________________  │
│              │                                 │   ─────────────────────────────────  │
│              │                                 │                                     │
│              │                                 │   📊 Session整体                    │
│              │                                 │   ⭐⭐⭐⭐ [质量评分]               │
│              │                                 │   🏷️ 标签: [逻辑连贯] [用户满意] [+] │
│              │                                 │   💬 评价: ___________________   │
│              │                                 │   ─────────────────────────────────  │
│              │                                 │                                     │
│              │                                 │   📎 标注附件                        │
│              │                                 │   [📷 截图标注] [📊 添加图表]         │
│              │                                 │                                     │
│              │                                 │   [💾 保存] [⏭️ 下一个] [📎附件]      │
└──────────────┴─────────────────────────────────┴─────────────────────────────────────┘
```

## 🎯 核心功能

### 📋 AI回答标注结构
每个AI回答包含以下标注层次：

#### 🔧 **4个Response组件独立标注**
1. **📖 ReasoningResponse**: AI的推理过程和分析思路
2. **📊 ChartDataResponse**: 图表数据和可视化展示
3. **💡 InsightResponse**: 图表解读和数据洞察分析
4. **✅ FollowUpResponse**: 追问建议和深入探索引导

#### 📊 **单轮回答整体评分**
- **整体质量**: 1-5星评分，综合评估这轮AI回答的整体表现
- **整体标签**: 回答质量、用户体验、解决程度、响应速度等
- **整体评价**: 对这轮回答的综合文字评价

#### 📊 **Session整体评分**
- **整体质量**: 1-5星评分，综合评估整个对话Session的质量
- **整体标签**: 逻辑连贯、用户满意、完整解答、需改进、体验良好等
- **整体评价**: 对整个Session的综合文字评价

### 🏷️ 自定义标签系统
每个组件及Session整体都支持：
- **快速评分**: 1-5星质量评分
- **自定义标签**: 点击[+]添加个性化标签
- **常用标签**: 
  - 组件标签: 准确性、逻辑清晰、数据准确、美观、实用等
  - Session标签: 逻辑连贯、用户满意、完整解答、需改进、体验良好等
- **备注说明**: 详细的文字评价和改进建议

### 📎 标注附件功能
- **截图标注**: 一键截图功能，支持区域选择截图
- **图表附件**: 支持PNG、JPG、GIF、WebP格式图片
- **图片管理**: 缩略图预览、点击放大、删除管理
- **使用场景**: 问题截图、对比图表、补充说明、标注草图

## 🔄 操作流程

### 🔍 搜索过滤功能
- **🔍 关键词搜索**: 支持用户问题、AI回答内容的全文搜索
- **🏢 租户筛选**: 按租户ID或租户名称筛选Session
- **👤 用户ID搜索**: 精确搜索特定用户的对话记录
- **📅 时间筛选**: 今日、本周、本月、自定义时间范围
- **🏷️ 标注状态**: 待标注、标注中、已完成、有问题等状态

### 📍 Session标注流程
1. **搜索过滤**: 使用多条件搜索找到目标Session
2. **选择Session**: 从左侧Session列表选择会话
3. **选择轮次**: 点击具体轮次的用户问题进行选中
4. **查看展开**: 中间区域展开该轮次的5个组件详情
5. **快速标注**: 右侧卡片式标注，评分+标签+备注
6. **切换轮次**: 点击其他轮次继续标注
7. **整体评估**: Session连贯性和完整性评估
8. **保存完成**: 保存所有标注数据

### 💡 交互设计
```
选中状态 (第1轮):
💬 用户问题 (第1轮) [★选中标注]
🤖 AI回答 (第1轮) - 完整展开5个组件

未选中状态 (第2轮):
💬 用户问题 (第2轮) [点击标注]
🤖 AI回答 (第2轮) [收起] - 显示组件概览

搜索交互:
🔍 关键词: "销售数据"
🏢 租户: [租户A] [租户B] [全部]
👤 用户ID: user_12345
📅 时间: [今日] [本周] [本月] [自定义]
🏷️ 状态: [待标注] [已完成] [有问题]

右侧标注区:
📋 当前标注: 第1轮
5个组件的卡片式快速标注表单
```

## 📊 数据结构

```json
{
  "sessionId": "S001",
  "conversationCount": 3,
  "currentAnnotatingRound": 1,
  "conversations": [
    {
      "conversationId": "conv_123",
      "round": 1,
      "userQuestion": "今天的销售数据怎么样？",
      "aiResponse": {
        "reasoning": "基于用户查询，我来分析今天的销售数据...",
        "chartComponent": {
          "type": "bar_chart",
          "title": "销售趋势图",
          "data": {...}
        },
        "chartInsight": "从图表可以看出，销售额呈现上升趋势...",
        "suggestions": ["建议进一步分析各渠道详细数据"],
        "executionLog": {
          "totalDuration": 6.333,
          "steps": [...]
        }
      },
      "annotations": {
        "reasoning": {
          "score": 3,
          "tags": ["推理准确", "逻辑清晰"],
          "comments": "推理逻辑清晰但可以更简洁"
        },
        "chartData": {
          "score": 4,
          "tags": ["数据准确", "图表美观"],
          "comments": "图表数据准确，展示效果好"
        },
        "insight": {
          "score": 2,
          "tags": ["解读浅显", "需改进"],
          "comments": "图表解读较浅，需要更深入分析"
        },
        "followUp": {
          "score": 5,
          "tags": ["建议实用", "引导性强"],
          "comments": "追问建议非常实用和相关"
        }
      },
      "attachments": [
        {
          "filename": "screenshot_001.png",
          "type": "image/png",
          "url": "/uploads/annotations/conv_123/screenshot_001.png",
          "description": "图表颜色问题截图"
        }
      ]
    }
  ],
  "sessionAnnotation": {
    "score": 4,
    "tags": ["逻辑连贯", "需深入", "用户满意"],
    "comments": "整体对话流畅，但第二轮回答可以更详细"
  }
}
```