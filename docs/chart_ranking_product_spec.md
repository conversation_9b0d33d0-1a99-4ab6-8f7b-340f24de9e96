# Chatbi 图表召回排序机制 

| 模块 | 子模块 | 具体说明 | 技术参数 | 业务影响 |
|------|--------|----------|-----------|-----------|
| **召回机制** | 租户库召回 | 从企业自有图表库搜索 | • 字段匹配权重: 4.0<br>• 关键词匹配权重: 3.0<br>• 语义相似度权重: 3.0 | • 优先展示企业常用图表<br>• 保证数据隔离性 |
| | 系统库召回 | 从公共图表库搜索 | • 字段匹配权重: 2.0<br>• 关键词匹配权重: 1.5<br>• 语义相似度权重: 1.5 | • 补充企业图表不足<br>• 提供通用分析模板 |
| **评分体系** | 使用频率<br>(40%) | 基于图表使用记录打分 | • 未使用: 0.5分<br>• 有使用: 对数增长计分<br>• 上限: 1.0分 | • 优先推荐常用图表<br>• 避免冷启动问题 |
| | 相关度<br>(40%) | 多维度匹配程度评估 | • 字段匹配率计算<br>• 语义相似度[0-1]<br>• 关键词分类权重:<br>&nbsp;&nbsp;- 维度词: 40%<br>&nbsp;&nbsp;- 指标词: 40%<br>&nbsp;&nbsp;- 范围词: 20% | • 提升搜索准确性<br>• 理解用户真实意图 |
| | 时效性<br>(20%) | 基于更新时间计分 | • 当天更新: 1.0分<br>• 30天内: 1.0-0.5线性衰减<br>• 超30天/无记录: 0.5分 | • 保证数据时效性<br>• 避免过期数据干扰 |
| **排序逻辑** | 多渠道处理 | 合并多渠道召回结果 | • 采用最高权重渠道信息<br>• 保留首次召回基础属性 | • 确保结果一致性<br>• 避免信息冲突 |
| | 最终排序 | 综合评分计算 | 最终得分 = 渠道权重 × (<br>&nbsp;&nbsp;使用频率 × 0.4 +<br>&nbsp;&nbsp;相关度 × 0.4 +<br>&nbsp;&nbsp;时效性 × 0.2<br>) | • 平衡各项评分因素<br>• 提供最优推荐结果 |
| **性能优化** | 并行处理 | 多渠道同时召回 | • 默认超时: 5秒<br>• 超时自动放弃该渠道 | • 保证响应速度<br>• 避免单点故障 |
| | 结果控制 | 限制返回结果数量 | • 仅返回得分最高结果<br>• 可配置返回数量 | • 保证推荐质量<br>• 优化展示体验 |

## 关键设计要点

1. **多级权重体系**
   - 租户库 > 系统库（2:1基础权重比）
   - 字段匹配 > 关键词/语义（2.0:1.5权重比）

2. **评分均衡**
   - 使用频率、相关度各占40%
   - 时效性占20%，避免过度依赖历史数据

3. **性能保障**
   - 5秒超时机制
   - 并行召回
   - 动态结果数量控制

## 业务价值

1. **精准推荐**：通过多维度评分确保推荐最合适的图表
2. **个性化**：优先考虑企业自有图表库
3. **实时响应**：并行处理+超时机制确保响应速度
4. **数据时效**：时效性评分避免过期数据
5. **可扩展**：权重体系支持动态调整 