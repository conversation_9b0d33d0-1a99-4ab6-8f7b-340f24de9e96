---
description: 编码规范
globs: 
alwaysApply: true
---
# Java与Spring Boot编码规范

## 1. 概述
本规范适用于纷享公司Java开发者，规定了基于Spring Boot技术栈的开发标准与最佳实践。**所有项目必须严格遵循此规范，以确保代码质量、一致性及可维护性。**

## 2. 内部资源
### Confluence Wiki资源
- **获取 Confluence Wiki 页面内容**:
  使用以下 `curl` 命令模板从纷享内部 Confluence (wiki.firstshare.cn) 获取指定页面的详细内容。这对于查阅最新的规范文档和技术细节非常有用。

  **命令模板**:
  `curl -X GET -H "Authorization: Bearer YOUR_PERSONAL_ACCESS_TOKEN" "https://wiki.firstshare.cn/rest/api/content/<在此处输入页面ID>?expand=body.storage"`

  **使用说明**:
    - **Tech Stack**: `curl` (命令行工具), Confluence REST API (v1), HTTP GET.
    - **Behavior**: 该命令将请求指定 Confluence 页面的内容，`expand=body.storage` 参数确保返回页面主体内容的存储格式 (通常是接近原始编辑器的HTML/XML)。
    - **Constraints/Prerequisites**:
        - 将 `YOUR_PERSONAL_ACCESS_TOKEN` 替换为您的有效纷享 Confluence 个人访问令牌。
        - 将 `<在此处输入页面ID>` 替换为您要查询的实际 Confluence 页面的数字 ID。
        - 您必须拥有访问目标页面的权限，否则请求将失败。
        - 预期输出为 JSON 格式，其中页面内容位于 `body.storage.value` 字段。

**核心参考文档页面ID:**
- `117851043`：接入CEP规范
- `209170055`：接入配置中心
- `206072841`：接入新的父POM
- `206072404`：接入健康检测和流量摘除
- `293537028`：接入日志采集上报
- `581468899`：按需关闭Spring自动配置
- `577077426`：定义全局异常处理器及支持的基础组件清单
- `293537098`：HTTP客户端使用说明（默认使用OkHttpSupport）

## 3. 环境配置
- **Java**: 21
- **Web服务器**: Tomcat 9.x
- **数据存储**:
    - **ClickHouse**: 24.8.5.115 (业务数据使用租户库，知识库数据使用系统库)
    - **PostgreSQL**: 12.4 (配置类数据)
- **详细规范参考Wiki**:
    - 配置中心: `209170055`
    - Maven父POM: `206072841`
    - 全局异常处理器: `577077426`
    - ClickHouse脚本规范: `320048575`
    - PostgreSQL脚本规范: `224022199`
    - 其他公共组件: `19666500`

### 3.1. 关键依赖示例
```xml
<dependency>
    <groupId>com.fxiaoke</groupId>
    <artifactId>fs-paas-ai-api</artifactId>
    <version>1.0.1-SNAPSHOT</version>
</dependency>
```

### 3.2. 网关服务(CEP)接入
- 遵循 `wiki-117851043` 规范。
- **用户身份 (`UserIdentify`) 处理**:
    - **Tech Stack**: Spring Framework (Dependency Injection, Spring MVC/WebFlux), CEP Gateway. 假定 `UserIdentify` 类位于 `com.fxiaoke.common.auth.UserIdentify` 或类似标准包下。
    - **Behavior**: 网关 (CEP) 会在请求头或特定上下文中传递用户身份信息。在服务内部，这些原始身份信息必须被捕获、解析，并严格映射为一个统一的 `UserIdentify` 对象实例。此 `UserIdentify` 对象随后应通过 Spring 的构造器依赖注入机制注入到相关的 Controller 方法或 Service 组件中，供业务逻辑使用。
    - **Constraints**:
        - **必须**进行转换；不允许直接在业务逻辑中使用原始的、未经处理的网关身份信息。
        - 转换逻辑应健壮，能处理身份信息缺失或格式错误的情况，并记录适当的警告或错误。
        - `UserIdentify` 对象应设计为不可变对象 (Immutable) 以保证线程安全和数据一致性。
        - 确保遵循 `wiki-117851043` 中关于身份信息传递和解析的最新规范。

## 4. 代码规范

### 4.1. 命名规范
| 类型        | 命名法                     | 示例                                  |
|-------------|------------------------------|---------------------------------------|
| 类名        | 帕斯卡命名法 (PascalCase)    | `UserController`, `OrderService`      |
| 方法/变量名 | 驼峰命名法 (camelCase)       | `findUserById`, `isOrderValid`        |
| 常量名      | 全大写下划线分隔 (ALL_CAPS) | `MAX_RETRY_ATTEMPTS`, `DEFAULT_PAGE_SIZE` |
| 包名        | 全小写，点分隔             | `com.fxiaoke.service.impl`            |

### 4.2. 注解使用
- **控制器层**: `@RestController`, `@RequestMapping` 等。
- **服务层**: `@Service`。
- **数据访问层**: `@Repository` (若使用JPA等)。
- **配置类**: `@Configuration`。
- **依赖注入**: **务必优先使用构造器注入**，而非字段注入，以保证不变性和易测性。

### 4.3. REST接口规范
- **请求方法**:
    - **Tech Stack**: HTTP/1.1 or HTTP/2, RESTful API design principles (adapted).
    - **Behavior**: 所有服务端点（API Endpoints）**必须**设计为接受 `POST` HTTP 方法。这意味着即使操作在语义上对应于读取 (GET)、更新 (PUT)、或删除 (DELETE)，接口也应通过 `POST` 请求实现。
    - **Constraints**:
        - **禁止**为新的或重构的API端点使用 `GET`, `PUT`, `DELETE`, `PATCH` 等其他HTTP方法。
        - 此约束旨在简化网关配置、统一请求处理流程，并可能有助于避免某些场景下的浏览器或代理缓存问题。
        - 操作的真实意图 (如创建、检索、更新、删除) 应通过接口路径、请求体内容或特定的动作字段在 `POST` 请求内部进行区分。

- **参数传递**:
    - **Tech Stack**: HTTP, JSON.
    - **Behavior**: API 的所有输入参数（数据）**必须**通过 HTTP 请求的 `RequestBody` 以 JSON 格式传递。
    - **Constraints**:
        - **禁止**使用 URL 查询参数 (Query Parameters) 或路径变量 (Path Variables, 超出资源定位基本需求的部分) 来传递操作性数据或复杂对象。
        - 请求体 `Content-Type` header 应为 `application/json`.
        - 所有传入的 `RequestBody` 必须有对应的Java DTO (Data Transfer Object) 类进行接收和校验 (使用如 `jakarta.validation` 注解)。

### 4.4. 日志规范
- **日志门面**: 使用SLF4J。
- **日志实现**: 使用Logback。
- **禁止行为**: **严禁使用 `System.out.println()` 或 `System.err.println()`**。
- **日志级别使用准则**:
    - `ERROR`: 严重错误，影响系统正常运行或核心功能。
    - `WARN`:  潜在问题或非严重错误，可能导致未来问题，需关注。
    - `INFO`:  关键业务流程节点、重要状态变更等信息，便于追踪和理解系统行为。
    - `DEBUG`: 开发与调试阶段使用的详细信息，生产环境默认应关闭此级别。

**正确日志记录示例:**
```java
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

// ...
private static final Logger log = LoggerFactory.getLogger(YourClass.class);

public void processBusiness(Object param) {
    log.info("业务处理开始，参数: {}", param);
    try {
        // 核心业务逻辑
        log.info("业务处理成功完成。");
    } catch (SpecificBusinessException e) {
        log.warn("业务处理出现预期异常: {}，参数: {}", e.getMessage(), param, e);
        // 根据业务需求决定是否抛出或转换异常
    } catch (Exception e) {
        log.error("业务处理发生未知严重异常，参数: {}", param, e);
        throw new BusinessException("业务处理失败，请联系管理员"); // 封装为统一业务异常
    }
}
```

## 5. 测试规范

### 5.1. 单元测试
- **强制要求**: 核心业务逻辑代码**必须编写**单元测试。
- **推荐框架**: 使用JUnit 5 和 Mockito。
- **覆盖率目标**: 核心业务模块代码的行覆盖率应 **> 80%**。

**单元测试示例:**
```java
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Optional;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class UserServiceTest {
    
    @Mock
    private UserRepository userRepository;
    
    @InjectMocks
    private UserServiceImpl userService;
    
    @Test
    void findById_shouldReturnUser_whenUserExists() {
        // Arrange
        Long userId = 1L;
        User expectedUser = new User(userId, "张三");
        when(userRepository.findById(userId)).thenReturn(Optional.of(expectedUser));
        
        // Act
        User actualUser = userService.findById(userId);
        
        // Assert
        assertEquals(expectedUser, actualUser, "返回的用户与预期不符");
        verify(userRepository).findById(userId); // 验证依赖方法是否被正确调用
    }
}
```

## 6. 业务概念定义
为确保项目成员对核心领域对象有统一认知，定义如下：

- **业务主题 (Business Domain)**: 项目或分析所围绕的宏观业务领域，例如"销售分析"、"客户服务分析"。
    - **主题 (Schema)**: 在一个业务主题下，为特定分析目的组织起来的数据集合。例如，"销售订单主题"、"客户行为主题"。
        - **维度 (Dimension)**: 用于观察和分析指标的视角或属性，通常是文本或分类数据。例如，时间、地区、产品类别。
        - **指标 (Measure)**: 可量化的数值，用于衡量业务表现。例如，销售额、订单数量、平均客单价。
        - **统计视图 (StatView)**: 预定义的、包含了特定维度和指标组合的分析视图，可以直接用于图表生成或数据查询。
    - **业务对象 (Business Object)**: 系统中实际存在的业务实体，如客户、订单、产品等。
        - **对象 (Object)**: 业务对象的具体实例。
        - **字段 (Field)**: 业务对象的属性。
        - **关系 (Relation)**: 业务对象之间的关联关系。
        - **报表视图 (RptView)**: 来源于一个或多个业务对象，经过特定加工和组织，用于报表展示的数据视图。
- **驾驶舱 (Dashboard)**: 集中展示多个相关图表（可能来自不同的统计视图或报表视图）的界面，用于综合监控和分析。
- **图表 (Chart)**: 数据的可视化表现形式。
- **图表ID (viewId)**: 图表或其背后数据视图的唯一标识符。
- **元数据 (Metadata)**: 描述数据的数据，例如字段类型、数据来源、更新时间等。

## 7. 业务组件与核心流程
Chatbi的核心工作流程可概括为：**Planning (规划)** -> **Action (执行)** -> **Memory (记忆)** -> **Knowledge (知识应用)**。
详细的 Chatbi 核心工作流程图及各阶段说明，请参考并维护核心项目文档：[`docs/4-FLOW.md`](mdc:docs/4-FLOW.md) (如果此文件尚不存在，请参照以下规范创建)。
    - **Tech Stack**: Markdown, Mermaid.js (for diagrams).
    - **Behavior**: 此文档是理解 Chatbi 核心逻辑的关键。它**必须**包含对 **Planning (规划)** -> **Action (执行)** -> **Memory (记忆)** -> **Knowledge (知识应用)** 四个阶段的详细阐述。
    - **Constraints/Content Requirements for `docs/4-FLOW.md`**:
        - **流程图**: 使用 Mermaid.js (`graph TD` or `flowchart TD`) 清晰绘制四个阶段及其主要的子步骤和流转关系。
        - **各阶段详述**:
            - **输入**: 每个阶段接收的主要数据或信号。
            - **核心处理**: 该阶段执行的关键逻辑和算法概述。
            - **输出**: 每个阶段产生的结果、数据或触发的下一动作。
            - **关键决策点**: 阶段内部或阶段转换时的重要判断逻辑。
            - **技术组件**: 涉及的主要类、模块或外部服务。
        - **维护**: 此文档**必须**与实际代码实现保持同步。任何对核心流程的重大调整都应首先更新此文档。

## 8. 性能优化最佳实践
- **缓存策略**:
    - **Tech Stack**: Spring Cache (preferred abstraction), Redis (preferred distributed cache implementation, 参考内部Wiki `页面ID待补充` 获取Redis集群详情), Caffeine (for local/in-memory caches where appropriate).
    - **Behavior**:
        1.  **识别**: 主动识别系统中符合"高频访问、低频变更、可接受一定数据延迟"特征的数据或查询结果。
        2.  **实施**: 使用 Spring Cache注解 (`@Cacheable`, `@CachePut`, `@CacheEvict`, `@Caching`) 进行声明式缓存。为缓存的读取、写入和清除定义清晰的逻辑。
        3.  **配置**: 为每个缓存区域 (Cache Region/Name) 配置合理的过期策略 (TTL, Time-to-Live) 和大小限制。
    - **Constraints**:
        - **Key命名**: 缓存键 (Cache Key) 必须有统一的、可读的命名规范，例如 `模块名:业务功能名:参数摘要`。
        - **数据一致性**: 仔细考虑缓存更新和失效策略，避免脏数据。对于强一致性要求的场景，缓存可能不适用或需要特殊处理（如read-through/write-through）。
        - **避免缓存雪崩/穿透**: 了解并采取措施防范（如随机化过期时间、空结果缓存、布隆过滤器）。
        - **监控**: 必须监控缓存命中率、内存使用、穿透情况等关键指标。

- **避免N+1查询**:
    - **Tech Stack**: JPA (Hibernate), MyBatis, JDBC. SQL.
    - **Behavior**: 在进行数据查询，特别是涉及关联实体/表时，**必须**主动避免 N+1 查询问题。
        - **JPA/Hibernate**: 使用 `JOIN FETCH` HQL/JPQL 查询，或利用 `@EntityGraph` 注解来指定预加载的关联。对于集合关联，考虑使用 `@BatchSize` 注解。
        - **MyBatis**: 精心设计 SQL 映射，使用 `<collection>` 和 `<association>` 标签进行嵌套查询或连接查询，一次性加载所需数据。
        - **JDBC/Raw SQL**: 手动构造 `JOIN` 查询语句。
    - **Constraints**:
        - **Code Review**: N+1问题是代码审查的重点关注项。
        - **Testing**: 编写测试用例，验证在加载关联数据时执行的SQL语句数量是否符合预期（例如，通过Hibernate Statistics或P6Spy等工具分析）。
        - **日志监控**: 在开发和测试阶段，开启SQL日志，观察实际执行的查询语句数量。

- **分页查询**:
    - **Tech Stack**: SQL (`LIMIT`/`OFFSET`, `ROWNUM`, `FETCH NEXT` 等数据库方言), Spring Data JPA `PagingAndSortingRepository`, MyBatis PageHelper plugin.
    - **Behavior**: 当查询可能返回大量数据记录的接口或内部服务时，**必须**实现分页逻辑。客户端应能指定请求的页码 (page number) 和每页大小 (page size)。
    - **Constraints**:
        - **默认分页**: 如果客户端未指定分页参数，应有合理的默认页码 (如第0或1页，取决于实现) 和默认每页大小 (如10或20条)。
        - **最大页大小**: 设置一个允许的最大页大小上限 (如100条，根据业务场景调整)，防止恶意请求或意外性能问题。
        - **响应格式**: 分页查询的响应体 `ApiResult<T>` 中的 `T` 应为一个包含数据列表和分页信息 (如总记录数 `totalElements`, 总页数 `totalPages`, 当前页码 `number`, 每页大小 `size`) 的对象结构。
        - **数据库优化**: 确保分页查询在数据库层面是高效的，利用索引，避免深度分页（查询偏移量过大）的性能陷阱。

- **异步处理**:
    - **Tech Stack**: Spring `@Async`, `java.util.concurrent.ExecutorService`, 消息队列 (如公司内部推荐的RocketMQ/Kafka，具体参考内部Wiki `页面ID待补充`).
    - **Behavior**: 对于执行时间不可预测或通常较长（例如，经验判断超过 500ms-1s）的操作，**必须**考虑将其设计为异步执行，以避免阻塞主处理线程（如HTTP请求处理线程）。
        - **Spring `@Async`**: 适用于服务内部的简单异步任务。**必须**配置自定义的 `TaskExecutor` (线程池)以控制线程数量、队列策略和拒绝策略。
        - **消息队列**: 对于需要持久化保证、失败重试、服务间解耦或更复杂流程编排的长时间任务，应优先使用消息队列。
    - **Constraints**:
        - **线程池配置**: Spring `@Async` 使用的线程池**必须**明确配置 (核心线程数, 最大线程数, 队列容量, 线程名前缀, 拒绝策略)，严禁依赖默认配置。
        - **异常处理**: 异步方法中的异常**必须**被捕获和妥善处理，不能被静默吞噬。考虑使用 `AsyncUncaughtExceptionHandler` 或返回 `CompletableFuture`/`ListenableFuture` 来传递和处理异常。
        - **事务边界**: 注意异步方法调用时的事务传播行为。`@Async` 方法默认在新线程执行，通常会脱离原有调用方的事务上下文。如有必要，需在新线程中手动管理事务。
        - **用户反馈**: 如果异步任务由用户请求触发，API应立即返回一个受理成功的响应（如HTTP 202 Accepted），并提供机制让用户后续查询任务状态或接收结果通知。

- **熔断与降级**:
    - **Tech Stack**: Resilience4j (公司首选), Sentinel。具体配置和最佳实践参考内部Wiki `页面ID待补充`。
    - **Behavior**: 对所有关键的外部服务依赖调用（HTTP, RPC等）以及内部可能成为瓶颈的、易失败或耗时较长的服务，**必须**实施熔断 (`CircuitBreaker`) 和降级 (`Fallback`) 机制。
        - **熔断**: 当依赖服务故障率或延迟超过预设阈值时，自动断开对该服务的调用一段时间，快速失败，防止连锁故障（雪崩效应）。
        - **降级**: 在熔断开启、服务调用超时或发生预期内错误时，执行预定义的降级逻辑，例如返回默认值、从缓存获取旧数据、或返回用户友好的提示信息，以保证核心功能的部分可用性或优雅失败。
    - **Constraints**:
        - **合理配置阈值**: 熔断器的错误率阈值、慢调用阈值、滑动窗口大小、熔断持续时间、半开状态请求数等参数，需根据依赖服务的SLA、历史表现和业务容忍度进行科学配置和持续调优。
        - **幂等性**: 降级逻辑和可能的重试机制（如果与熔断配合使用）需仔细考虑操作的幂等性。
        - **监控与告警**: **必须**监控熔断器的状态变化（Open, Half-Open, Closed）、降级执行次数等关键指标，并设置实时告警。
        - **隔离策略**: 考虑使用舱壁模式 (`Bulkhead`) 限制对不同依赖的并发调用数，防止一个慢服务或故障服务耗尽所有线程资源，拖垮整个应用。

## 9. 安全规范
- **敏感数据加密 (Encryption at Rest)**:
    - **Tech Stack**: Jasypt (用于配置文件属性加密), Spring Security Crypto (用于通用数据对称/非对称加密), AES-256 GCM (推荐的对称加密算法标准)。密钥管理服务/策略：参考内部Wiki `页面ID待补充`。
    - **Behavior**: 所有被识别为敏感数据（定义见约束）在持久化存储（数据库、文件系统、日志等）之前**必须**进行加密处理。
        - **识别与分类**: 项目启动阶段或需求分析时，需由安全负责人或架构师组织明确识别并记录应用处理的所有敏感数据字段及其敏感等级。
        - **实施**: 根据数据敏感等级和使用场景，选择合适的加密算法和库对这些字段的值进行加密存储。解密操作应仅在绝对必要时进行，并严格控制解密权限和审计日志。
    - **Constraints**:
        - **敏感数据定义**: 至少包括但不限于：用户密码、支付信息（卡号、CVV）、身份证号、手机号、详细住址、API密钥、内部服务凭证、个人生物识别信息等。具体列表和分级标准参考公司数据安全策略 `内部Wiki页面ID待补充`。
        - **密钥管理**: 加密密钥的生成、存储、分发、轮换和销毁**必须**遵循公司密钥管理规范。严禁在代码、配置文件或版本控制系统中硬编码任何加密密钥。
        - **算法标准**: **必须**采用业界公认的强加密算法和安全模式 (如对称加密 AES-256 GCM, 非对称加密 RSA-2048/ECC-256)。避免使用已知不安全的算法（如DES, MD5 for hashing passwords）。
        - **用户凭证处理**: 用户密码等身份凭证**必须**使用加盐的、自适应的单向哈希算法 (如 Argon2id, scrypt, bcrypt) 进行存储，而非可逆加密。盐值必须为每个用户独立生成。

- **禁止硬编码敏感信息**:
    - **Tech Stack**: 公司统一配置中心 (如纷享配置中心, Spring Cloud Config Server + Vault/Jasypt后端)。
    - **Behavior**: 任何形式的密钥、密码、API Tokens、数据库连接字符串（含凭证）、私有证书等敏感配置信息**严禁**直接写入代码 (Java, properties, yml, xml, shell脚本等)或提交到版本控制系统。
    - **Constraints**:
        - **唯一可信来源**: 所有此类敏感信息**必须**通过公司指定的配置中心进行统一管理和安全分发。应用启动时动态拉取。
        - **配置项加密**: 配置中心存储的敏感配置项本身也**应该**是加密的（例如，配置中心使用Jasypt加密属性值）。
        - **访问控制**: 配置中心中敏感配置的访问权限应遵循最小权限原则，仅授权给需要的应用实例或服务账户。
        - **审计与扫描**: 定期使用代码扫描工具 (如GitGuardian, Trivy) 审计代码库和CI/CD流程，确保没有意外泄露的硬编码凭证。

- **API参数校验 (Input Validation)**:
    - **Tech Stack**: `jakarta.validation` (Bean Validation API, 实现如 Hibernate Validator), Spring MVC/WebFlux Validation (`@Valid`, `@Validated`注解)。OWASP Java Encoder (用于输出编码), AntiSamy (用于富文本HTML清理)。
    - **Behavior**: 所有从外部（包括浏览器客户端、移动App、第三方服务、甚至内部其他非绝对可信的服务）传入API的参数（HTTP Headers, URL Path/Query Parameters, Request Body）**必须**在服务端进行严格、全面的校验。
        - **类型、格式、长度、范围、存在性校验**: 使用 Bean Validation 注解在DTO层面进行声明式校验。
        - **白名单校验**: 对于取值范围固定的参数（如枚举、特定代码），优先使用白名单校验。
        - **业务逻辑校验**: 更复杂的、依赖其他数据状态的校验应在Service层进行，并返回明确的业务异常。
    - **Constraints**:
        - **校验位置**: 主要校验逻辑应在Controller层（针对DTO）和Service层（针对业务规则）实施。
        - **自定义校验器**: 对标准注解无法满足的复杂校验规则，**必须**创建自定义 Bean Validation 注解和对应的校验器。
        - **防御注入攻击**:
            - **SQL注入**: **绝对禁止**拼接SQL字符串。**必须**使用参数化查询 (PreparedStatement)、JPA/Hibernate Criteria API 或 MyBatis 等ORM框架的内置机制。
            - **XSS (Cross-Site Scripting)**: 对所有将输出到HTML、JavaScript、CSS、URL等上下文的用户输入数据，**必须**进行恰当的上下文感知编码/转义。优先使用安全的UI框架和模板引擎（如Thymeleaf, React）的内置XSS防护机制。对富文本输入，使用AntiSamy等库进行清理。
            - **命令注入**: 避免调用外部进程或执行系统命令。如不可避免，对用户输入进行严格的白名单校验和转义。
            - **XML注入 (XXE)**: 解析XML时，禁用外部实体和DTD的加载。
            - **反序列化漏洞**: 谨慎处理不可信来源的序列化数据。使用安全的序列化库，并考虑对序列化的类进行白名单限制。
        - **错误处理**: 参数校验失败时，API应返回HTTP 400 (Bad Request) 状态码，并在响应体中提供清晰、具体的错误信息（指明哪个字段、何种错误），但避免泄露过多内部实现细节。

- **最小权限原则 (Principle of Least Privilege)**:
    - **Tech Stack**: Spring Security (首选), Apache Shiro, 或其他符合OAuth2/OIDC规范的认证授权框架。RBAC (Role-Based Access Control), ABAC (Attribute-Based Access Control) 模型。
    - **Behavior**: 系统中的所有接口、功能调用、数据访问和资源操作的权限控制**必须**严格遵循最小权限原则。
        - **认证 (Authentication)**: **必须**对所有访问受保护资源的请求进行身份认证，确认请求者的合法身份。
        - **授权 (Authorization)**: 在身份认证通过后，**必须**对已认证用户是否有权执行特定操作或访问特定资源进行精细化授权检查。
    - **Constraints**:
        - **默认拒绝**: 所有权限请求在未经明确授权之前，**必须**被默认拒绝。
        - **角色与权限设计**: 根据业务需求设计清晰、职责单一、粒度适当的角色 (Roles) 和权限 (Permissions)。避免创建过于宽泛的"超级管理员"角色，应细化为多个职责分离的管理角色。
        - **资源级别访问控制**: 权限控制应尽可能细化到具体的资源实例级别（例如，用户A只能查看和编辑自己的订单，不能操作用户B的订单）。
        - **操作级别访问控制**: 对同一资源的不同操作（如查看、创建、修改、删除、审批等）应定义独立的权限。
        - **定期审计与回收**: **必须**定期审查用户账户、角色分配和权限授予情况，及时移除不再需要的账户或权限。
        - **服务账户权限**: 用于服务间调用的技术账户或API凭证，也**必须**遵循最小权限原则，仅授予其完成预定任务所必需的最少权限。

- **依赖库安全 (Dependency Security Management)**:
    - **Tech Stack**: OWASP Dependency-Check (Maven/Gradle plugin), Snyk, Trivy, GitHub Dependabot/Renovatebot, JFrog Xray.
    - **Behavior**: **必须**持续管理和维护项目所使用的第三方依赖库（直接依赖和传递性依赖）的安全性。
        - **自动化扫描**: **必须**将依赖库安全扫描工具集成到CI/CD流水线中，在每次构建或每日定时对项目依赖进行自动化扫描，以识别已知的安全漏洞 (CVEs)。
        - **漏洞响应**: 一旦发现高危或严重漏洞，**必须**立即评估其对项目的影响，并制定应急响应计划。通常需要在短时间内（如24-72小时内）将受影响的库更新到已修复漏洞的安全版本，或应用官方推荐的缓解措施。中低危漏洞也应有计划地修复。
    - **Constraints**:
        - **漏洞信息来源**: 关注NVD (National Vulnerability Database), CVE Mitre, 以及项目依赖的官方安全公告。
        - **更新策略与测试**: 制定依赖库的更新策略，平衡安全需求与版本兼容性带来的风险。所有依赖更新，特别是主版本更新，**必须**经过充分的回归测试。
        - **禁止来源不明或废弃依赖**: **严禁**引入维护不活跃、来源不明或已知不再安全的第三方依赖。优先使用官方维护、社区活跃且有良好安全记录的库。
        - **软件物料清单 (SBOM)**: 鼓励生成和维护项目的软件物料清单 (SBOM)，以提高供应链透明度和安全事件响应能力。
        - **漏洞传递性**: 扫描工具应能检测传递性依赖中的漏洞。解决传递性漏洞可能需要覆盖依赖版本或升级直接依赖。
