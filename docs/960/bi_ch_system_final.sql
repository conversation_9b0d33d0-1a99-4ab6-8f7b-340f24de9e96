-- 1. 问答记录表（完整记录ChatBiRequest）
create table bi_ch_system.bi_query_log
(
    id                 String comment '问答记录唯一标识',
    trace_id           String comment '链路追踪ID，用于跟踪整个请求处理流程',
    session_id         String comment '会话ID，用于关联同一会话中的多次交互',
    tenant_id          String comment '租户ID',
    user_id            String comment '用户ID',
    request_data       String comment 'ChatBiRequest完整JSON数据，包含sessionId、instructions、history、llmModel等所有请求信息',    
    tags Array(String) comment '标签列表，包含响应类型标记、业务场景、质量标记等',
    query              String comment '用户问题文本（当前轮次的问题，冗余存储便于查询）',
    response_components String comment '响应组件信息，JSON格式，包含ReasoningResponse/ChartDataResponse/InsightResponse/FollowUpResponse四个组件内容',
    response_time      UInt32 comment '响应时间(毫秒)',
    is_context_enabled UInt8 comment '是否启用上下文：1-启用，0-不启用',
    source LowCardinality(String) comment '来源：WEB/MOBILE/API/TEST等',
    feedback           Int8     default 0 comment '用户反馈：1-点赞，-1-点踩，0-无反馈',
    feedback_comment   String comment '反馈备注',
    is_annotated       UInt8    default 0 comment '是否已标注：1-已标注，0-未标注',
    query_time         Int64 comment '问询时间戳(毫秒)',
    created_by LowCardinality(String) default '-10000' comment '创建人ID',
    create_time        Int64 comment '创建时间戳(毫秒)',
    action_logs        String comment '执行日志JSON数据，包含意图识别、知识检索、数据查询、图表生成等各个Action步骤的详细日志信息',
    sys_modified_time  Int64    default toUnixTimestamp64Micro(now64(9)) comment '时间戳-精度更高',
    is_deleted         Int16    default 0 comment '状态',
    bi_sys_flag        Int8     default 1 comment '增量计算(变更前后标记)，默认1，变更后',
    bi_sys_batch_id    Int64    default 0 comment '增量同步批次，默认0',
    bi_sys_is_deleted  UInt8    default 0 comment '为mergeTree提供标记是否删除，默认0',
    bi_sys_version     DateTime default now() comment '写入时间',
    bi_sys_ods_part    String   default 's' comment '增量计算的分区(i-增量、c-计算、s-存量)'
)
    engine = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/bi_ch_system/{uuid}/', '{replica}',
             bi_sys_version, bi_sys_is_deleted)
        PARTITION BY bi_sys_ods_part
        ORDER BY (tenant_id, bi_sys_flag, session_id, create_time)
        TTL bi_sys_version + toIntervalMonth(1);

-- 2. 问题级标注表（包含组件标注+轮次标注+附件）
create table bi_ch_system.bi_query_annotation
(
    id                     String comment '标注记录唯一标识',
    request_id             String comment '关联的问答记录ID',
    session_id             String comment '关联的会话ID',
    tenant_id              String comment '租户ID',
    
    -- 4个组件标注（JSON格式，保留可视化能力）
    component_annotations  String comment '4个组件标注详情，JSON格式：{reasoning:{score:3, tags:["推理准确"], comments:"逻辑清晰"}, chartData:{score:4, tags:["数据准确"], comments:"图表效果好"}, insight:{score:2, tags:["解读浅显"], comments:"需更深入"}, followUp:{score:5, tags:["建议实用"], comments:"很有价值"}}',
    
    -- 单轮整体标注
    round_score           UInt8 comment '单轮回答整体评分：1-5分',
    round_tags            Array(String) comment '单轮标签：["回答质量好", "用户体验佳", "解决程度高"]',
    round_comments        String comment '单轮回答的综合评价',
    
    -- 附件信息（JSON轻量化存储）
    attachments           String comment '附件信息JSON：[{id:"att_001", filename:"screenshot.png", url:"/uploads/xxx.png", type:"screenshot", description:"问题截图", size:1024000, hash:"md5hash"}]',
    
    -- 系统字段
    created_by            LowCardinality(String) default '-10000' comment '标注人ID',
    create_time           Int64 comment '标注时间戳(毫秒)',
    last_modified_time    Int64 comment '最后修改时间戳(毫秒)',
    sys_modified_time     Int64    default toUnixTimestamp64Micro(now64(9)) comment '时间戳-精度更高',
    is_deleted            Int16    default 0 comment '状态',
    bi_sys_flag           Int8     default 1 comment '增量计算(变更前后标记)，默认1，变更后',
    bi_sys_batch_id       Int64    default 0 comment '增量同步批次，默认0',
    bi_sys_is_deleted     UInt8    default 0 comment '为mergeTree提供标记是否删除，默认0',
    bi_sys_version        DateTime default now() comment '写入时间',
    bi_sys_ods_part       String   default 's' comment '增量计算的分区(i-增量、c-计算、s-存量)'
)
    engine = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/bi_ch_system/{uuid}/', '{replica}',
             bi_sys_version, bi_sys_is_deleted)
        PARTITION BY bi_sys_ods_part
        ORDER BY (tenant_id, bi_sys_flag, session_id, request_id)
        TTL bi_sys_version + toIntervalMonth(1);

-- 3. Session级标注表
create table bi_ch_system.bi_session_annotation
(
    id                    String comment 'Session标注记录唯一标识',
    session_id            String comment '关联的会话ID',
    tenant_id             String comment '租户ID',
    user_id               String comment '用户ID',
    
    -- Session整体标注
    session_score         UInt8 comment 'Session整体质量评分：1-5分',
    session_tags          Array(String) comment 'Session标签：["逻辑连贯", "用户满意", "完整解答", "需改进", "体验良好"]',
    session_comments      String comment 'Session的综合文字评价',
    
    -- 系统字段
    created_by            LowCardinality(String) default '-10000' comment '标注人ID',
    create_time           Int64 comment '创建时间戳(毫秒)',
    last_modified_by      LowCardinality(String) default '-10000' comment '最后修改人ID',
    last_modified_time    Int64 comment '最后修改时间戳(毫秒)',
    sys_modified_time     Int64    default toUnixTimestamp64Micro(now64(9)) comment '时间戳-精度更高',
    is_deleted            Int16    default 0 comment '状态',
    bi_sys_flag           Int8     default 1 comment '增量计算(变更前后标记)，默认1，变更后',
    bi_sys_batch_id       Int64    default 0 comment '增量同步批次，默认0',
    bi_sys_is_deleted     UInt8    default 0 comment '为mergeTree提供标记是否删除，默认0',
    bi_sys_version        DateTime default now() comment '写入时间',
    bi_sys_ods_part       String   default 's' comment '增量计算的分区(i-增量、c-计算、s-存量)'
)
    engine = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/bi_ch_system/{uuid}/', '{replica}',
             bi_sys_version, bi_sys_is_deleted)
        PARTITION BY bi_sys_ods_part
        ORDER BY (tenant_id, bi_sys_flag, session_id)
        TTL bi_sys_version + toIntervalMonth(1);