# ChatBI 评估测试平台产品说明


## 目标
这是一个后台评测系统，服务于开发人员，前端设计优先考虑扩展性，代码尽量少一些方便维护。

## 核心功能

### 1. 图表知识管理
**目的**: 通过调整特征，提升图表在向量召回上的效果

**页面设计**:
- 图表列表: viewId、viewName、chartType、featureCount
- 点击图表 → 右侧显示特征列表: feature、weight
- 支持特征的增删改
- 测试区域: 输入问题，显示召回结果（包含 feature、weight、tenantId，按 score 倒序）

**接口**:
- `getChartKnowledgeList`: 获取图表列表
- `getChartFeaturesByViewId`: 获取图表特征
- `saveChartFeature`: 保存特征
- `testChartFeature`: 测试召回效果

### 2. 测试执行
**目的**: 模拟用户请求，对 Agent 召回全链路进行快速测试
以RPC的方式调用Agent服务，支持配置Agent地址，指向测试ChatBiController的chat方法

**页面设计**:
- 测试输入区: 问题文本框、租户ID等
- 结果展示区: json展示接口的调用结果


### 3. 问答记录标注
**目的**: 人工标注用户问答，构建测试用例

**页面设计**:
- 记录列表: 问题、回答、用户、时间、标注状态
- 标注界面: 问题展示、回答展示、标注选项（正确/错误/部分正确）
- 标注详情: 错误类型、改进建议、期望结果
- 统计面板: 标注进度、准确率统计、问题分类

**接口**:
- `getQARecords`: 获取问答记录
- `saveAnnotation`: 保存标注结果
- `generateTestCase`: 生成测试用例

### 4. 测试集管理
**目的**: 基于测试用例构建测试集，做定量评估

**页面设计**:
- 测试集列表: 名称、用例数量、创建时间、最后执行时间
- 用例管理: 问题、期望结果、难度、分类、状态
- 批量测试: 选择测试集、执行进度、结果统计
- 结果对比: 不同版本测试结果对比、准确率趋势

**接口**:
- `getTestSets`: 获取测试集列表
- `getTestCases`: 获取测试用例
- `executeBatchTest`: 执行批量测试
- `getTestResults`: 获取测试结果

## 技术栈
- 后端: Spring Boot + ClickHouse + MySQL
- 前端: Thymeleaf + Bootstrap + jQuery
- 部署: 简单打包部署

## 前端技术栈详情
### 必需组件
- **Thymeleaf**: 模板引擎（Spring Boot自带）
- **Bootstrap 5**: CSS框架（CDN引入）
- **jQuery**: JavaScript库（CDN引入）

### 可选组件（按需添加）
- **DataTables**: 表格排序搜索功能
- **Chart.js**: 图表展示（如需要）

### 引入方式
```html
<!-- 基础CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

<!-- 基础JS -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
```

### 项目结构
```
src/main/resources/
├── static/
│   ├── css/custom.css
│   └── js/common.js
└── templates/
    ├── layout.html
    ├── chart-list.html
    ├── test.html
    └── annotation.html
```


# 表结构
 bi_ch_system.sql 
# Rest协议
必须是Post
url命名是驼峰风格
# 规范
代码精简、不要冗余、不要兼容，当全新功能开发
