graph TD
    %% 主流程
    Start[用户请求] --> Judge{请求类型}
    Judge -->|推荐请求| Recommend[推荐流程]
    Judge -->|聊天请求| Chat[聊天流程]
    Judge -->|数据加载| DataLoad[数据加载流程]

    %% 推荐流程
    Recommend --> CreateAction[创建Action上下文]
    CreateAction --> ExtractAction[获取推荐Action]
    ExtractAction --> ExecuteAction[执行推荐]
    ExecuteAction --> ReturnRecommend[返回推荐结果]

    %% 聊天流程
    Chat --> CreateContext[获取会话上下文]
    CreateContext --> PreProcess[问题预处理]

    %% 预处理子流程
    subgraph PreProcess[问题预处理]
        LoadKnowledge[加载bi_enterprise_knowledge] --> TermReplace[术语替换]
        TermReplace --> ProcessedQuery[处理后的查询]
    end

    ProcessedQuery --> IntentRecognition

    %% 意图识别子流程
    subgraph IntentRecognition[意图识别]
        Intent{意图类型判断}
        Intent -->|澄清与建议?| ClarifyAndSuggest[待实现]
        Intent -->|分析意图| AnalysisIntent[分析流程]
        
        %% 信息提取子流程
        AnalysisIntent --> InfoExtract[信息提取]
        InfoExtract --> DimExtract[维度提取]
        InfoExtract --> MetricExtract[指标提取]
        InfoExtract --> FilterExtract[过滤条件提取]
        DimExtract & MetricExtract & FilterExtract --> ExtractResult[提取结果]
    end

    %% 知识管理子流程
    subgraph KnowledgeManagement[知识管理]
        %% 全量维度表知识建设
        subgraph DimensionKnowledge[全量维度表知识建设]
            DataSource[数据源] --> FilterData[仅保留产生数据的值]
            FilterData --> EnumTypes{枚举类型}
            EnumTypes -->|文本| TextValues[文本值]
            EnumTypes -->|枚举| EnumValues[枚举值]
            EnumTypes -->|人员| StaffValues[人员值]
            EnumTypes -->|部门| DeptValues[部门值]
            EnumTypes -->|主属性| MainAttrValues[主属性值]
            TextValues & EnumValues & StaffValues & DeptValues & MainAttrValues --> DimTable[维度知识表]
        end

        %% 图表知识构建流程
        subgraph ChartKnowledge[图表知识构建]
            ChartDef[图表定义] --> FeatureExtract[特征提取]
            FeatureExtract --> GenQuestions[LLM生成问题]
            FeatureExtract --> Vectorize[向量化]
            GenQuestions --> Vectorize
            Vectorize --> ChartVectorTable[图表向量表]
        end
        
        %% API结构知识
        subgraph APIStructureKnowledge[API结构知识]
            APIMetadata[API元数据] --> FilterOps[数据范围操作符]
            APIMetadata --> DateFilter[日期快捷筛选枚举]
            APIMetadata --> ChartTypes[图表类型]
            APIMetadata --> CompareTypes[同环比类型]
            APIMetadata --> StatTypes[统计方式]
        end

        %% 知识库使用流程
        subgraph KnowledgeUsage[知识库使用]
            %% 维度值匹配
            subgraph DimValueMatching[维度值匹配]
                ExtractResult --> DimValueMatch[维度和数据范围值匹配]
                DimTable --> DimValueMatch
                DimValueMatch --> MatchedFields[匹配字段列表]
            end
            
            %% 字段关键词提取
            subgraph FieldKeywordExtraction[字段关键词提取]
                ExtractResult --> ExtractKeywords[提取字段关键词]
                ExtractKeywords --> DimensionKeywords[维度关键词]
                ExtractKeywords --> MeasureKeywords[指标关键词]
                ExtractKeywords --> FilterKeywords[过滤条件关键词]
                ExtractKeywords --> TimeRangeKeywords[时间范围关键词]
                DimensionKeywords & MeasureKeywords & FilterKeywords & TimeRangeKeywords --> KeywordsSet[关键词集合]
            end
            
            %% 知识召回流程
            subgraph KnowledgeRecall[知识召回]
                ExtractResult & MatchedFields & KeywordsSet --> MultiChannelRecall[多渠道图表召回]
                
                %% 多渠道召回策略
                subgraph MultiChannelRecall[多渠道图表召回]
                    UserIntent[用户意图] --> RecallChannels
                    MatchedFields --> FieldIDsRecall[字段ID召回]
                    KeywordsSet --> KeywordRecall[关键词召回]
                    UserIntent --> VectorRecall[向量语义召回]
                    
                    %% 召回渠道
                    subgraph RecallChannels[召回渠道]
                        TenantChannels[租户库渠道]
                        SystemChannels[系统库渠道]
                        
                        TenantChannels --> TenantFieldIDs[租户库字段ID匹配]
                        TenantChannels --> TenantKeywords[租户库关键词匹配]
                        TenantChannels --> TenantVector[租户库向量匹配]
                        
                        SystemChannels --> SystemFieldIDs[系统库字段ID匹配]
                        SystemChannels --> SystemKeywords[系统库关键词匹配]
                        SystemChannels --> SystemVector[系统库向量匹配]
                    end
                    
                    FieldIDsRecall --> TenantFieldIDs
                    KeywordRecall --> TenantKeywords
                    VectorRecall --> TenantVector
                    
                    FieldIDsRecall -.-> SystemFieldIDs
                    KeywordRecall -.-> SystemKeywords
                    VectorRecall -.-> SystemVector
                    
                    TenantFieldIDs & TenantKeywords & TenantVector & SystemFieldIDs & SystemKeywords & SystemVector --> MergeResults[合并召回结果]
                end
                
                %% 排序算法
                subgraph RankingAlgorithm[排序算法]
                    MergeResults --> RankFactors[排序因子计算]
                    
                    subgraph RankFactors[排序因子计算]
                        KeywordScore[关键词匹配得分]
                        VectorScore[向量相似度]
                        FieldMatch[字段匹配度]
                        TenantPriority[租户优先级]
                        UsageCount[使用频率]
                        LastVisitTime[最近访问时间]
                    end
                    
                    RankFactors --> WeightedScore[加权总分]
                    WeightedScore --> RankResults[结果排序]
                    RankResults --> TopCharts[选择头部图表]
                end
                
                TopCharts --> ReasoningRecord[推理数据记录]
            end
            
            %% 提示词生成流程
            subgraph PromptGeneration[提示词生成]
                ReasoningRecord --> CollectKnowledge[收集知识]
                ChartVectorTable --> ChartInfo[图表知识]
                ChartInfo --> CollectKnowledge
                FilterOps & DateFilter & ChartTypes & CompareTypes & StatTypes --> APIInfo[API结构知识]
                APIInfo --> CollectKnowledge
                CollectKnowledge --> GenPrompt[生成提示词]
                GenPrompt --> GenAPIParam[生成API参数]
            end
        end
    end

    %% 分析执行子流程
    subgraph AnalysisExecution[分析执行]
        CreateAnalysisPlan[创建分析计划]
        ExecuteAnalysisPlan[执行分析计划]
        
        subgraph ActionDispatch[动作分发]
            ActionType{动作类型}
            ActionType -->|查询动作| QueryAction[数据查询]
            ActionType -->|解读动作| ExplainAction[解读生成]
            ActionType -->|推荐动作| RecommendAction[推荐生成]
            QueryAction & ExplainAction & RecommendAction --> ResultCollect[结果收集]
        end
    end

    %% 数据加载流程
    DataLoad --> LoadData[加载数据]
    LoadData --> ReturnData[返回数据]

    %% 连接关系
    ClarifyAndSuggest -.-> DimValueMatch
    GenAPIParam --> CreateAnalysisPlan
    CreateAnalysisPlan --> ExecuteAnalysisPlan
    ExecuteAnalysisPlan --> ActionDispatch
    ResultCollect --> UpdateContext[更新会话上下文]
    UpdateContext --> ReturnAnalysis[返回分析结果]

    %% 样式定义
    classDef process fill:#f9f,stroke:#333,stroke-width:2px;
    classDef decision fill:#fda,stroke:#333,stroke-width:2px;
    classDef data fill:#bbf,stroke:#333,stroke-width:2px;
    classDef subflow fill:#dfd,stroke:#333,stroke-width:2px;
    classDef apiKnowledge fill:#fef,stroke:#333,stroke-width:2px;
    classDef pending fill:#ccc,stroke:#666,stroke-width:2px,stroke-dasharray: 5,5;
    classDef newFeature fill:#dff,stroke:#333,stroke-width:2px;

    class Start,ReturnRecommend,ReturnAnalysis,ReturnData,ProcessedQuery process;
    class Judge,Intent,ActionType,RecallStrategy,EnumTypes decision;
    class CreateContext,UpdateContext,DimTable,APIMetadata,ChartVectorTable,MatchedFields,KeywordsSet data;
    class IntentRecognition,KnowledgeManagement,AnalysisExecution,ActionDispatch,KnowledgeUsage,KnowledgeRecall,RankingAlgorithm,DimensionKnowledge,ChartKnowledge,APIStructureKnowledge,PromptGeneration,PreProcess subflow;
    class FilterOps,DateFilter,ChartTypes,CompareTypes,StatTypes apiKnowledge;
    class ClarifyAndSuggest pending;
    class MultiChannelRecall,RecallChannels,FieldKeywordExtraction,RankFactors newFeature;