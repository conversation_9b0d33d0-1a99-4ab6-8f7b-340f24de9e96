```mermaid

sequenceDiagram
    participant User
    participant API
    participant Planning
    participant Memory
    participant Knowledge
    participant Action
    participant BI
    
    User->>API: 发送查询
    API->>Memory: 获取用户上下文
    Memory-->>API: 返回用户画像和历史
    
    API->>Planning: 处理请求
    
    Planning->>Memory: 获取会话上下文
    Memory-->>Planning: 返回对话历史
    
    Planning->>Knowledge: 知识检索
    Knowledge-->>Planning: 返回相关知识
    
    Planning->>Planning: 意图识别
    Planning->>Memory: 更新意图历史
    
    Planning->>Planning: 生成执行计划
    
    Planning->>Action: 执行计划
    
    Action->>Memory: 获取执行上下文
    Memory-->>Action: 返回执行状态
    
    Action->>BI: 调用BI接口
    BI-->>Action: 返回结果
    
    Action->>Memory: 更新执行结果
    Action->>Memory: 保存中间状态
    
    Action-->>Planning: 返回执行结果
    Planning->>Memory: 更新查询模式
    
    Planning-->>API: 处理响应
    API-->>User: 返回结果


```