CREATE TABLE IF NOT EXISTS biz_account ON CLUSTER '{cluster}'
(
    `id`                 String,
    `tenant_id`          String,
    `created_by`         LowCardinality(String),
    `create_time`        Int64,
    `last_modified_by`   LowCardinality(String),
    `last_modified_time` Int64,


    `sys_modified_time`  Int64    DEFAULT toUnixTimestamp64Micro(now64(9)) comment '时间戳-精度更高',
    `is_deleted`         Int16    DEFAULT 0 comment '状态',
    `bi_sys_flag`        Int8     DEFAULT 1 comment '增量计算(变更前后标记)，默认1，变更后',
    `bi_sys_batch_id`    Int64    DEFAULT 0 comment '增量同步批次，默认0',
    `bi_sys_is_deleted`  UInt8    DEFAULT 0 comment '为mergeTree提供标记是否删除，默认0',
    `bi_sys_version`     DateTime DEFAULT now() comment '写入时间',
    `bi_sys_ods_part`    String   DEFAULT 's' comment '增量计算的分区(i-增量、c-计算、s-存量)'
)
    ENGINE = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/{database}/{uuid}/', '{replica}', bi_sys_version,
                                          bi_sys_is_deleted)
        PARTITION BY bi_sys_ods_part
        ORDER BY (tenant_id, bi_sys_flag, id)
        TTL bi_sys_version + INTERVAL 1 MONTH DELETE WHERE bi_sys_ods_part = 's' AND (bi_sys_flag = 0 OR is_deleted IN (-1, -2)),
            bi_sys_version + INTERVAL 3 DAY DELETE WHERE bi_sys_ods_part in ('i', 'c')

