-- INSERT-忽略系统列，一律采用默认值即可
-- 更新和删除-INSERT-忽略系统列
-- 查询-加FINAL、is_deleted
CREATE OR REPLACE TABLE bi_chart_knowledge ON CLUSTER '{cluster}'
(
    tenant_id           String,
    view_id             String,
    chart_type          String comment '图表类型',
    schema_id           String,
    spec                String comment '图表详细定义',
    dimension_names Array(String) comment '维度名称列表',
    measure_names Array(String) comment '指标名称列表',
    filter_names Array(String) comment '筛选条件名称列表',
    field_ids Array(String) comment '图表涉及的所有字段ID',
    usage_count Int64 comment '使用次数',

    `sys_modified_time` Int64    DEFAULT toUnixTimestamp64Micro(now64(9)) comment '时间戳-精度更高',
    `is_deleted`        Int16    DEFAULT 0 comment '状态',
    `bi_sys_flag`       Int8     DEFAULT 1 comment '增量计算(变更前后标记)，默认1，变更后',
    `bi_sys_batch_id`   Int64    DEFAULT 0 comment '增量同步批次，默认0',
    `bi_sys_is_deleted` UInt8    DEFAULT 0 comment '为mergeTree提供标记是否删除，默认0',
    `bi_sys_version`    DateTime DEFAULT now() comment '写入时间',
    `bi_sys_ods_part`   String   DEFAULT 's' comment '增量计算的分区(i-增量、c-计算、s-存量)'

) ENGINE =
              ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/{database}/{uuid}/', '{replica}', bi_sys_version,
              bi_sys_is_deleted)
      PARTITION BY bi_sys_ods_part
      ORDER BY (tenant_id, bi_sys_flag, view_id)
      TTL bi_sys_version + INTERVAL 1 MONTH DELETE WHERE bi_sys_ods_part = 's' AND (bi_sys_flag = 0 OR is_deleted IN (-1, -2)),
    bi_sys_version + INTERVAL 3 DAY DELETE WHERE bi_sys_ods_part in ('i','c')
    SETTINGS index_granularity = 8192;


-- INSERT-忽略系统列，一律采用默认值即可
-- 更新和删除-INSERT-忽略系统列
-- 查询-加FINAL、is_deleted
CREATE OR REPLACE TABLE bi_knowledge_embedding ON CLUSTER '{cluster}'
(
    tenant_id           String,
    knowledge_type      String comment '知识类型',
    knowledge_id      String comment '知识唯一标识',
    embedding Array(Float32) comment '向量值',
    feature           String comment '特征',
    weight            Float64       default 1.0,
    hash_code         UInt64        default cityHash64(feature),

    `sys_modified_time` Int64    DEFAULT toUnixTimestamp64Micro(now64(9)) comment '时间戳-精度更高',
    `is_deleted`        Int16    DEFAULT 0 comment '状态',
    `bi_sys_flag`       Int8     DEFAULT 1 comment '增量计算(变更前后标记)，默认1，变更后',
    `bi_sys_batch_id`   Int64    DEFAULT 0 comment '增量同步批次，默认0',
    `bi_sys_is_deleted` UInt8    DEFAULT 0 comment '为mergeTree提供标记是否删除，默认0',
    `bi_sys_version`    DateTime DEFAULT now() comment '写入时间',
    `bi_sys_ods_part`   String   DEFAULT 's' comment '增量计算的分区(i-增量、c-计算、s-存量)'

) ENGINE =
              ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/{database}/{uuid}/', '{replica}', bi_sys_version,
              bi_sys_is_deleted)
      PARTITION BY bi_sys_ods_part
      ORDER BY (tenant_id, bi_sys_flag, knowledge_type,knowledge_id,hash_code)
      TTL bi_sys_version + INTERVAL 1 MONTH DELETE WHERE bi_sys_ods_part = 's' AND (bi_sys_flag = 0 OR is_deleted IN (-1, -2)),
    bi_sys_version + INTERVAL 3 DAY DELETE WHERE bi_sys_ods_part in ('i','c')
    SETTINGS index_granularity = 8192;

