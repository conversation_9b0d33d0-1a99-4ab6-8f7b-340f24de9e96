 # 系统准确率影响因素交互式地图

## 整体流程图

```mermaid
graph LR
    A[用户查询] --> B[预处理阶段]
    B --> C[意图识别阶段]
    C --> D[图表匹配阶段]
    D --> E[查询构建阶段]
    E --> F[数据查询与结果]
    
    classDef critical fill:#f99,stroke:#f66,stroke-width:2px;
    classDef important fill:#ff9,stroke:#fc6,stroke-width:2px;
    classDef normal fill:#9cf,stroke:#69f,stroke-width:1px;
    
    class C,D critical;
    class B,E important;
    class A,F normal;
```

## 准确率影响因素详解

<details>
<summary><b>🔄 预处理阶段 (影响程度: ★★★☆☆)</b></summary>

### 组件及影响

| 组件 | 影响程度 | 作用描述 |
|------|---------|---------|
| 术语替换 | ★★★★☆ | 将用户输入的术语标准化，提高后续处理准确性 |
| 领域知识加载 | ★★★☆☆ | 加载领域知识库，为准确处理提供基础 |

### 关键实体结构

```json
// 术语替换结果 - 基于QueryPreProcessOutput
{
  "originalQuery": "看看这几个月的销售情况怎么样",
  "processedQuery": "分析最近三个月销售额趋势",
  "termReplacements": {
    "这几个月": "最近三个月",
    "销售情况": "销售额趋势"
  }
}

// 企业知识条目 - 基于EnterpriseKnowledge
{
  "sourceTerm": "这几个月",
  "targetTerm": "最近三个月",
  "synonymTerm": "近期,最近"
}
```
</details>

<details>
<summary><b>🔍 意图识别阶段 (影响程度: ★★★★★)</b></summary>

### 组件及影响

| 组件 | 影响程度 | 作用描述 |
|------|---------|---------|
| 维度提取 | ★★★★★ | 从用户问题中准确提取维度信息 |
| 指标提取 | ★★★★★ | 从用户问题中准确提取指标信息 |
| 过滤条件提取 | ★★★★☆ | 从用户问题中准确提取过滤条件 |

### 关键实体结构

```json
// 用户意图 - 基于UserIntent
{
  "intentId": "intent_20231225_001",
  "instructions": "分析最近三个月销售额趋势",
  "intentType": "ANALYSIS",
  "confidence": 0.95,
  "needsClarification": false,
  "clarificationQuestion": null,
  "isContinuation": false,
  "extractedInfo": {
    "analysisType": "趋势分析",
    "measures": ["销售额"],
    "dimensions": ["日期"],
    "filters": [{
      "field": "日期",
      "operator": "LAST_N_MONTHS",
      "values": ["3"]
    }],
    "timeRange": "最近三个月",
    "chartType": "折线图"
  }
}

// 提取的信息 - 基于ExtractedInfo
{
  "analysisType": "趋势分析",
  "measures": ["销售额"],
  "dimensions": ["日期"],
  "filters": [{
    "field": "日期",
    "operator": "LAST_N_MONTHS",
    "values": ["3"]
  }],
  "timeRange": "最近三个月",
  "chartType": "折线图",
  "filterFieldIds": ["date_field_id"]
}

// 过滤条件 - 基于FilterInfo
{
  "field": "日期",
  "operator": "LAST_N_MONTHS",
  "values": ["3"]
}
```

### 流程图
```mermaid
graph TD
    Query[用户查询] --> Extract[信息提取]
    Extract --> DimExtract[维度提取]
    Extract --> MeasureExtract[指标提取]
    Extract --> FilterExtract[过滤条件提取]
    DimExtract & MeasureExtract & FilterExtract --> Result[提取结果]
    
    classDef critical fill:#f99,stroke:#f66,stroke-width:2px;
    class DimExtract,MeasureExtract critical;
```
</details>

<details>
<summary><b>📚 图表匹配阶段 (影响程度: ★★★★★)</b></summary>

### 组件及影响

| 组件 | 影响程度 | 作用描述 |
|------|---------|---------|
| 维度值匹配 | ★★★★☆ | 将提取的维度与实际数据值精确匹配 |
| 字段关键词提取 | ★★★★☆ | 从用户问题中提取关键词，提高后续处理精度 |
| 多渠道图表召回 | ★★★★★ | 通过多种渠道准确找回相关图表 |
| 排序算法 | ★★★★★ | 对召回结果进行排序，提升最相关结果排名 |

### 关键实体结构

<details>
<summary>维度值匹配示例</summary>

```json
// 维度匹配上下文
{
  "dimensions": ["日期", "产品类别"],
  "dimensionMatches": [
    {
      "inputValue": "华北",
      "matchedField": "region",
      "matchedValue": "NORTH_CHINA",
      "confidence": 0.98
    }
  ],
  "filterFieldIds": ["date_field_id", "product_type_id"]
}
```
</details>

<details>
<summary>字段关键词提取示例</summary>

```json
// 关键词集
{
  "dimensionKeywords": ["区域", "产品"],
  "measureKeywords": ["销售额", "增长率"],
  "filterKeywords": ["大于1000万", "华东地区"],
  "timeRangeKeywords": ["最近三个月", "环比"]
}

// 提取信息
{
  "analysisType": "趋势分析",
  "measures": ["销售额"],
  "dimensions": ["区域", "产品"],
  "timeRange": "最近三个月"
}
```
</details>

<details>
<summary>多渠道图表召回示例</summary>

```json
// 知识范围
{
  "tenantId": "tenant_123",
  "viewIds": ["view_123", "view_456"],
  "domainId": "sales"
}

// 字段ID召回
{
  "fieldIds": ["revenue_id", "date_id", "product_id"],
  "matchingFieldIds": ["revenue_id", "date_id"]
}

// 关键词召回
{
  "analysisType": "趋势分析", 
  "measures": ["销售额"], 
  "dimensions": ["区域", "日期"]
}

// 向量语义召回
{
  "intentType": "ANALYSIS",
  "confidence": 0.89
}
```
</details>

<details>
<summary>排序算法示例</summary>

```json
// 关键词匹配得分
{
  "intent": {
    "confidence": 0.95,
    "extractedInfo": {
      "measures": ["销售额"],
      "dimensions": ["区域"]
    }
  }
}

// 向量相似度
{
  "knowledgeType": "CHART",
  "knowledgeId": "chart_005",
  "cosineSimilarity": 0.92
}

// 字段匹配度
{
  "dimensions": {
    "matched": ["区域", "日期"],
    "total": 2,
    "score": 1.0
  },
  "measures": {
    "matched": ["销售额"],
    "total": 1,
    "score": 1.0
  }
}

// 租户优先级
{
  "userIdentity": {
    "userId": "user123",
    "tenantId": "tenant_123", 
    "corpId": "corp789"
  },
  "isFromTenant": true,
  "tenantPriorityBoost": 1.2
}

// 使用频率
{
  "knowledgeId": "chart_008",
  "usageCount": 58,
  "normalizedUsageScore": 0.75
}

// 最近访问时间
{
  "sessionId": "session_20231225_001", 
  "startTime": 1703491200000,
  "lastVisitTime": "2023-12-20T14:30:25Z",
  "recencyScore": 0.85
}
```
</details>

### 流程图
```mermaid
graph TD
    Intent[意图分析结果] --> KMgmt[图表匹配]
    KMgmt --> DimMatch[维度值匹配]
    KMgmt --> KeywordExtract[字段关键词提取]
    DimMatch & KeywordExtract --> Recall[多渠道图表召回]
    
    Recall --> IdRecall[字段ID召回]
    Recall --> KeywordRecall[关键词召回]
    Recall --> VectorRecall[向量语义召回]
    
    IdRecall & KeywordRecall & VectorRecall --> Merge[合并召回结果]
    Merge --> Ranking[排序算法]
    
    Ranking --> KwScore[关键词匹配得分]
    Ranking --> VecScore[向量相似度]
    Ranking --> FieldMatch[字段匹配度]
    Ranking --> Tenant[租户优先级]
    Ranking --> Usage[使用频率]
    Ranking --> Time[最近访问时间]
    
    KwScore & VecScore & FieldMatch & Tenant & Usage & Time --> Final[最终排序结果]
    
    classDef critical fill:#f99,stroke:#f66,stroke-width:2px;
    classDef important fill:#ff9,stroke:#fc6,stroke-width:2px;
    
    class Recall,Ranking critical;
    class DimMatch,KeywordExtract important;
```
</details>

<details>
<summary><b>🔧 查询构建阶段 (影响程度: ★★★★☆)</b></summary>

### 组件及影响

| 组件 | 影响程度 | 作用描述 |
|------|---------|---------|
| 数据结构映射 | ★★★★☆ | 将用户意图和知识转换为具体查询结构 |
| 多路召回系统 | ★★★★★ | 通过多种策略精确匹配字段ID |

### QueryDSL多路召回系统详解

<details>
<summary>召回器类型</summary>

| 召回器 | 权重 | 作用描述 |
|------|---------|---------|
| 精确匹配召回器 | 1.0 | 通过字段名精确匹配确定字段ID |
| 值匹配召回器 | 0.8 | 通过字段值匹配对应的字段ID |
| 向量匹配召回器 | 0.6 | 通过语义向量相似度匹配字段ID |

```json
// QueryDSL输入
{
  "userIntent": {
    "intentId": "intent_20231225_001",
    "intentType": "ANALYSIS",
    "extractedInfo": {
      "dimensions": ["日期"],
      "measures": ["销售额"],
      "filters": [{
        "field": "日期",
        "operator": "LAST_N_MONTHS",
        "values": ["3"]
      }]
    }
  },
  "knowledgeScope": {
    "viewIds": ["view_123"],
    "tenantId": "tenant_123"
  }
}

// 精确匹配召回器上下文
{
  "recallType": "EXACT_MATCH",
  "defaultWeight": 1.0,
  "matchContext": {
    "fieldName": "销售额",
    "schemaId": "schema123",
    "viewFieldIds": ["field_1", "field_2"]
  }
}

// 值匹配召回器上下文
{
  "recallType": "VALUE_MATCH",
  "defaultWeight": 0.8,
  "matchContext": {
    "fieldValue": "1000000",
    "schemaId": "schema123",
    "viewFilterIds": ["filter_1", "filter_2"]
  }
}

// 向量匹配召回器上下文
{
  "recallType": "VECTOR_MATCH",
  "defaultWeight": 0.6,
  "matchContext": {
    "fieldName": "销售额",
    "schemaId": "schema123",
    "schemaFieldIds": ["schema_field_1", "schema_field_2"]
  }
}
```
</details>

<details>
<summary>召回范围</summary>

| 范围 | 权重 | 作用描述 |
|------|---------|---------|
| 视图字段范围 | 1.0 | 从视图的维度和指标字段中查找 |
| 视图过滤器范围 | 0.9 | 从视图的过滤器字段中查找 |
| 主题字段范围 | 0.7 | 从整个主题的所有字段中查找 |

```json
// 从QueryDSL/generator/recall/RecallScope.java
// 视图字段范围
{
  "recallScope": "VIEW_FIELD",
  "defaultWeight": 1.0
}

// 视图过滤器范围
{
  "recallScope": "VIEW_FILTER",
  "defaultWeight": 0.9
}

// 主题字段范围
{
  "recallScope": "SCHEMA_FIELD",
  "defaultWeight": 0.7
}
```
</details>

<details>
<summary>匹配结果和协调</summary>

```json
// 字段匹配结果 FieldMatchResult
{
  "fieldId": "revenue_id",
  "confidence": 0.95,
  "recallType": "EXACT_MATCH",
  "recallScope": "VIEW_FIELD",
  "totalScore": 0.92
}

// 多路召回协调器结果
{
  "timeoutMs": 1000,
  "matchResults": [
    {"fieldId": "revenue_id", "totalScore": 0.92},
    {"fieldId": "sales_id", "totalScore": 0.85}
  ],
  "bestMatchFieldId": "revenue_id"
}
```
</details>

### 流程图
```mermaid
graph TD
    Intent[用户意图] --> DSL[QueryDSL]
    DSL --> StructMap[数据结构映射]
    DSL --> MultiRecall[多路召回系统]
    
    MultiRecall --> RecallTypes[召回器类型]
    MultiRecall --> RecallScopes[召回范围]
    
    RecallTypes --> ExactMatch[精确匹配召回器]
    RecallTypes --> ValueMatch[值匹配召回器]
    RecallTypes --> VectorMatch[向量匹配召回器]
    
    RecallScopes --> ViewField[视图字段范围]
    RecallScopes --> ViewFilter[视图过滤器范围]
    RecallScopes --> SchemaField[主题字段范围]
    
    ExactMatch & ValueMatch & VectorMatch --> |组合| Matrix[九种召回渠道组合]
    ViewField & ViewFilter & SchemaField --> |组合| Matrix
    
    Matrix --> Coordinator[多路召回协调器]
    Coordinator --> Results[匹配结果排序]
    Results --> BestMatch[最佳匹配字段ID]
    
    classDef critical fill:#f99,stroke:#f66,stroke-width:2px;
    classDef important fill:#ff9,stroke:#fc6,stroke-width:2px;
    
    class MultiRecall,ExactMatch critical;
    class Matrix,Coordinator important;
```

<details>
<summary>九种召回渠道组合图</summary>

```mermaid
graph TD
    Root[多路召回系统] --> C1[视图字段 + 精确匹配]
    Root --> C2[视图字段 + 值匹配]
    Root --> C3[视图字段 + 向量匹配]
    Root --> C4[视图过滤器 + 精确匹配]
    Root --> C5[视图过滤器 + 值匹配]
    Root --> C6[视图过滤器 + 向量匹配]
    Root --> C7[主题字段 + 精确匹配]
    Root --> C8[主题字段 + 值匹配]
    Root --> C9[主题字段 + 向量匹配]
    
    C1 & C4 & C7 --> |权重最高| High[高优先级渠道]
    C2 & C5 & C8 --> |中等权重| Medium[中优先级渠道]
    C3 & C6 & C9 --> |低权重| Low[低优先级渠道]
    
    High & Medium & Low --> Merged[合并结果]
    
    classDef high fill:#f99,stroke:#f66,stroke-width:2px;
    classDef medium fill:#ff9,stroke:#fc6,stroke-width:2px;
    classDef low fill:#cdf,stroke:#acf,stroke-width:1px;
    
    class C1,C4,C7,High high;
    class C2,C5,C8,Medium medium;
    class C3,C6,C9,Low low;
```
</details>

</details>

## 影响程度分布热力图

```mermaid
%%{init: {'theme': 'forest', 'themeVariables': { 'fontFamily': 'arial' }}}%%
pie showData
    "意图识别" : 30
    "图表匹配" : 30
    "查询构建" : 25
    "预处理" : 15
```

## 准确率提升关键点

1. **提高维度和指标提取准确率**：对意图识别阶段的维度和指标提取进行优化
2. **优化多渠道图表召回**：改进召回算法，特别是精确匹配和值匹配方面
3. **完善排序算法**：优化多种因子的权重设置，确保最相关结果排名靠前
4. **增强QueryDSL多路召回**：改进九种召回渠道的协同工作机制
5. **优化术语标准化**：扩充企业知识库中的术语映射，提高术语标准化准确率