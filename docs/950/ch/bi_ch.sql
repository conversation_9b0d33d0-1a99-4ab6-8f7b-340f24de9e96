create table if not exists bi_chart_embedding ON CLUSTER '{cluster}'
(
    tenant_id         String,
    view_id           String,
    embedding         Array(Float32),
    features          Array(String) comment '向量文本',
    chart_type        String comment '图表类型',
    schema_id         String,
    bi_sys_flag       Int8          default 0,
    bi_sys_batch_id   Int64         default 0,
    bi_sys_is_deleted UInt8         default 0,
    bi_sys_version    DateTime      default now(),
    timestamp         DateTime64(3) default now64(3),
    spec Nullable(String) comment '图表详细定义',
    keywords Map(String, Array(String)) comment '关键词:{d:[维度1、维度2]，m:[指标1、指标2]，f:[数据范围1、数据范围2]}',
    field_ids         Array(String) comment '图表涉及的所有字段ID'
) ENGINE =
      ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/{database}/{uuid}/', '{replica}', timestamp,
                                   bi_sys_is_deleted)
      ORDER BY (tenant_id, view_id)
      SETTINGS index_granularity = 8192;



CREATE OR REPLACE TABLE dim_value_full ON CLUSTER '{cluster}'
(
    tenant_id         String,                                         -- 租户ID
    dimension_id      String,                                         -- 维度ID. stat_field field_id
    field_id          String,                                         -- 字段ID. udf_obj_field field_id
    value             String,                                         -- 值
    display_value     String,                                         -- 显示值
    weight            Float64,                                        -- 先用0.0 权重值,比如负责人优先创建人
    is_deleted        UInt8,                                          -- 业务删除标记
    used_count        Int32,                                          -- 被引用次数
    -- 系统字段
    bi_sys_flag       Int8,                                           -- 系统标识
    bi_sys_batch_id   Int64,                                          -- 批次ID
    bi_sys_is_deleted UInt8,                                          -- 删除标记
    bi_sys_version    DateTime      DEFAULT now(),                    -- 版本时间
    timestamp         DateTime64(9) DEFAULT now64(9, 'Asia/Shanghai') -- 时间戳
)
    ENGINE = ReplicatedSummingMergeTree('/clickhouse/tables/{shard}/{database}/{uuid}/', '{replica}',
                                        (used_count, is_deleted))
        ORDER BY (tenant_id, display_value, weight, field_id, dimension_id)
        TTL bi_sys_version + toIntervalDay(3) WHERE used_count <= 0 or is_deleted>=1
        SETTINGS index_granularity = 8192;