create table if not exists bi_chart_embedding ON CLUSTER '{cluster}'
(
    tenant_id         String,
    view_id           String,
    embedding         <PERSON><PERSON>y(Float32),
    features          Array(String) comment '向量文本',
    chart_type        String comment '图表类型',
    schema_id         String,
    bi_sys_flag       Int8          default 0,
    bi_sys_batch_id   Int64         default 0,
    bi_sys_is_deleted UInt8         default 0,
    bi_sys_version    DateTime      default now(),
    timestamp         DateTime64(3) default now64(3),
    spec Nullable(String) comment '图表详细定义',
    keywords Map(String, Array(String)) comment '关键词:{d:[维度1、维度2]，m:[指标1、指标2]，f:[数据范围1、数据范围2]}',
    field_ids         Array(String) comment '图表涉及的所有字段ID'
) ENGINE =
      ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/{database}/{uuid}/', '{replica}', timestamp,
                                   bi_sys_is_deleted)
      ORDER BY (tenant_id, view_id)
      SETTINGS index_granularity = 8192;