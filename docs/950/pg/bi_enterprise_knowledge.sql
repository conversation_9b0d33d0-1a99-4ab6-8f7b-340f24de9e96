create table if not exists bi_enterprise_knowledge
(
    id                 varchar(100) not null,
    tenant_id          varchar(32)  not null,
    knowledge_type     varchar(50)  not null,
    source_term        varchar(200) not null,
    target_term        varchar(200) not null,
    synonym_term       varchar(500),
    config             text,
    description        text,
    create_time        bigint       not null,
    last_modified_time bigint       not null,
    sys_modified_time  bigint,
    created_by         varchar(32),
    is_deleted         integer,
    constraint bi_mt_knowledge_pkey
        primary key (id, tenant_id)
);

comment on column bi_enterprise_knowledge.knowledge_type is '知识分类';

comment on column bi_enterprise_knowledge.source_term is '源术语';

comment on column bi_enterprise_knowledge.target_term is '目标术语';

comment on column bi_enterprise_knowledge.synonym_term is '同义词';

comment on column bi_enterprise_knowledge.config is '默认配置';


DROP TRIGGER IF EXISTS x_system_changes ON bi_enterprise_knowledge;
CREATE TRIGGER x_system_changes BEFORE INSERT OR UPDATE ON bi_enterprise_knowledge FOR EACH ROW EXECUTE PROCEDURE public.f_system_change();
