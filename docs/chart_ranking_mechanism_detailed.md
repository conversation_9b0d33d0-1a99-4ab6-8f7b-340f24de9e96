# Chatbi 图表排序机制

## 一、工作流程

```mermaid
graph TD
    A[用户提问] --> B[多渠道并行召回]
    B --> C[计算综合得分]
    C --> D[排序筛选]
    D --> E[返回最佳图表]
```

## 二、召回渠道配置

<details>
<summary>📊 渠道权重详细配置</summary>

| 渠道类型 | 库类型 | 召回方式 | 库基础权重 | 方式基础权重 | 最终权重 |
|---------|--------|----------|------------|--------------|----------|
| 字段匹配 | 租户库 | 字段ID精确匹配 | 2.0 | 2.0 | 4.0 |
| 字段匹配 | 系统库 | 字段ID精确匹配 | 1.0 | 2.0 | 2.0 |
| 关键词 | 租户库 | 关键词匹配 | 2.0 | 1.5 | 3.0 |
| 关键词 | 系统库 | 关键词匹配 | 1.0 | 1.5 | 1.5 |
| 向量 | 租户库 | 语义相似度 | 2.0 | 1.5 | 3.0 |
| 向量 | 系统库 | 语义相似度 | 1.0 | 1.5 | 1.5 |

> 📝 **权重计算**: 最终权重 = 库基础权重 × 方式基础权重
</details>

## 三、评分体系

<details>
<summary>🎯 评分因子详细说明</summary>

### 1. 使用频率评分 (40%)

- 基础情况：未使用过得分 0.5
- 计算公式：log(使用次数) 归一化，上限 1.0
- 说明：采用对数增长避免热门图表过度主导

### 2. 相关度评分 (40%)

#### 2.1 字段匹配相关度
- 匹配率 = 匹配上的字段数 / 总字段数
- 无匹配时基础分 0.5

#### 2.2 语义相关度
- 向量得分范围：[0,1]
- 最终相关分 = (字段匹配分 + 向量得分) / 2
- 上限：1.0

#### 2.3 关键词匹配（权重细分）
```
维度关键词：40% (地区、时间等)
指标关键词：40% (销售额、利润等)
范围关键词：20% (时间范围、TOP N等)

匹配率计算：
- 维度匹配率 = 匹配维度词数 / 总维度词数
- 指标匹配率 = 匹配指标词数 / 总指标词数
- 范围匹配率 = 匹配范围词数 / 总范围词数

综合匹配分 = 维度匹配率×0.4 + 指标匹配率×0.4 + 范围匹配率×0.2
```

### 3. 时效性评分 (20%)

| 更新时间 | 得分 |
|---------|------|
| 当天更新 | 1.0 |
| 30天内 | 1.0-0.5 线性衰减 |
| 超过30天 | 0.5 |
| 无更新记录 | 0.5 |

</details>

## 四、排序算法

<details>
<summary>📊 排序计算详解</summary>

### 1. 多渠道结果处理
- 同一图表多渠道召回时：
  - 采用最高权重渠道的核心信息（渠道标识、向量分数等）
  - 保留首次召回的基础信息（使用次数、更新时间等）

### 2. 最终得分计算
```
最终得分 = 渠道权重 × (
    使用频率得分 × 0.4 + 
    相关度得分 × 0.4 + 
    时效性得分 × 0.2
)
```

### 3. 实际案例
假设图表通过租户库字段匹配召回：
- 渠道权重：4.0
- 使用频率：0.8
- 相关度：0.9
- 时效性：1.0

计算过程：
```
最终得分 = 4.0 × (0.8×0.4 + 0.9×0.4 + 1.0×0.2)
        = 4.0 × (0.32 + 0.36 + 0.2)
        = 4.0 × 0.88
        = 3.52
```
</details>

## 五、性能优化

<details>
<summary>⚡ 系统性能保障</summary>

### 1. 并行召回
- 所有渠道同时执行
- 默认超时时间：5秒
- 超时策略：放弃该渠道结果，不影响其他渠道

### 2. 结果处理
- 对所有召回结果统一排序
- 仅返回 Top-1 结果

</details>